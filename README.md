## 航达  产销MES
### 统一
 - 审计字段填充策略
```java
    /**
    * 是否启用;默认为0,1为未启用                    --- 此字段不需要自动填充
    */
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(fill = FieldFill.INSERT)
    private Integer nDeleteMark;
```
- controller中不要写任何逻辑代码生成后将controller中的逻辑全部迁移至service中
- 自动编码使用
```java
    private final ICodeRuleClientV2 codeRuleClientV2;
    String code = 
            codeRuleClientV2
                    .generateAndUse("ruleCode","flag")
                    .getDataOrThrow();
```
- 开发环境接口文档：http://<服务地址>:<端口>>/doc.html