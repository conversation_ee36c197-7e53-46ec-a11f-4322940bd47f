<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.aitos</groupId>
    <artifactId>aitos-pss</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>aitos-service</module>
        <module>aitos-service-api</module>
    </modules>

    <name>aitos-pss</name>
    <description>aitos-pss</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>11</java.version>
        <maven.plugin.version>3.8.1</maven.plugin.version>

        <spring.cloud.version>2021.0.5</spring.cloud.version>
        <spring.cloud.alibaba.version>2021.0.5.0</spring.cloud.alibaba.version>
        <spring.boot.version>2.6.13</spring.boot.version>
        <sentinel.datasource.nacos>1.7.1</sentinel.datasource.nacos>
        <spring.cloud.seata.version>1.6.1</spring.cloud.seata.version>

        <commons.lang.version>2.6</commons.lang.version>
        <commons.fileupload.version>1.2.2</commons.fileupload.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.codec.version>1.10</commons.codec.version>
        <hutool.version>5.8.20</hutool.version>
        <satoken.version>1.29.0</satoken.version>
        <lombok.version>1.18.4</lombok.version>
        <okhttp.version>3.1.1</okhttp.version>
        <fastjson.version>1.2.83</fastjson.version>
        <joda.time.version>2.9.9</joda.time.version>
        <gson.version>2.8.5</gson.version>
        <mybatisplus.join.version>1.5.1</mybatisplus.join.version>
        <mybatisplus.version>3.5.8</mybatisplus.version>
        <mybatisplus.generator.version>3.5.8</mybatisplus.generator.version>
        <mybatisplus.dynamic.version>3.5.1</mybatisplus.dynamic.version>
        <knife4j.version>4.3.0</knife4j.version>
        <freemarker.version>2.3.30</freemarker.version>
        <hibernatevalidator.version>6.0.13.Final</hibernatevalidator.version>
        <qiniu.version>7.2.23</qiniu.version>
        <aliyun.oss.version>2.8.3</aliyun.oss.version>
        <qcloud.cos.version>4.4</qcloud.cos.version>
        <huawei.obs.version>3.19.7</huawei.obs.version>
        <minio.oss.version>7.1.0</minio.oss.version>
        <jaxb.api.version>2.3.0</jaxb.api.version>
        <log4j.core.version>2.17.2</log4j.core.version>
        <xxl.job.version>2.4.0</xxl.job.version>
        <screw.version>1.0.5</screw.version>
        <ureport.version>2.2.9</ureport.version>
        <camunda.version>7.18.0</camunda.version>
        <groovy.version>3.0.17</groovy.version>
        <javax.mail.version>1.6.2</javax.mail.version>
        <magic.api.version>2.1.1</magic.api.version>
        <keycloak.version>20.0.1</keycloak.version>
        <aliyun.core.version>4.5.1</aliyun.core.version>
        <tencentcloud.version>3.1.62</tencentcloud.version>
        <chatgpt.java.version>1.0.6</chatgpt.java.version>
        <okio.version>3.3.0</okio.version>
        <easyexcel.version>3.1.4</easyexcel.version>
        <poi.version>5.2.2</poi.version>
        <ooxml.schemas.version>4.1.2</ooxml.schemas.version>
        <javassist.version>3.29.2-GA</javassist.version>
        <liteflow.version>2.10.1</liteflow.version>
        <microsoft.version>8.4.1.jre8</microsoft.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <sms4j.version>2.1.0</sms4j.version>
        <powerjob.version>4.3.2</powerjob.version>
        <license.version>2.0</license.version>
        <justauth.version>1.16.5</justauth.version>
        <dingtalk.version>2.0.26</dingtalk.version>
        <mysql.connector.version>8.0.28</mysql.connector.version>
        <spring.boot.redis.version>2.7.5</spring.boot.redis.version>
        <spring.boot.admin.version>2.6.11</spring.boot.admin.version>
        <clickhouse.version>0.3.2</clickhouse.version>
        <p6spy.version>3.9.1</p6spy.version>
        <aitos.framework.version>1.0.0-SNAPSHOT</aitos.framework.version>
        <spotless.version>2.37.0</spotless.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-dependencies -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--引入spring boot依赖-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <!--引入spring cloud  alibaba依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <!--引入nacos依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
            </dependency>

            <!--引入spring-cloud-alibaba-nacos-config依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
            </dependency>

            <!--引入sentinel依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
            </dependency>

            <!--引入sentinel-datasource-nacos依赖-->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-nacos</artifactId>
                <version>${sentinel.datasource.nacos}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.boot.redis.version} </version>
            </dependency>

            <!--引入MySql依赖-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>

            <!--引入hutool依赖-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--引入hutool依赖 邮件依赖-->
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>${javax.mail.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-temp-jwt</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- Sa-Token 整合 Redis (使用 jackson 序列化方式) -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-dao-redis-jackson</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- Sa-Token插件：权限缓存与业务缓存分离 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-alone-redis</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证（Reactor响应式集成）, 在线文档：http://sa-token.dev33.cn/ -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-reactor-spring-boot-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!--引入Lombok依赖-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <optional>true</optional>
            </dependency>

            <!--引入mybatis-plus依赖-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatisplus.generator.version}</version>
            </dependency>

            <!-- mybatis-plus 多表关联 -->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatisplus.join.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!--引入fastjson依赖-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons.lang.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons.codec.version}</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda.time.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <!--引入模板依赖-->
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>

            <!--引入注解验证依赖-->
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernatevalidator.version}</version>
            </dependency>

            <!-- 动态数据源 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${mybatisplus.dynamic.version}</version>
            </dependency>

            <!-- 七牛 -->
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu.version}</version>
            </dependency>

            <!-- 阿里oss -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.oss.version}</version>
            </dependency>

            <!-- 华为oss -->
            <dependency>
                <groupId>com.huaweicloud</groupId>
                <artifactId>esdk-obs-java</artifactId>
                <version>${huawei.obs.version}</version>
            </dependency>

            <!-- 腾讯oss -->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${qcloud.cos.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.oss.version}</version>
            </dependency>

            <!--jdk8  可以不需要-->
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.syyai.spring.boot</groupId>
                <artifactId>ureport-spring-boot-starter</artifactId>
                <version>${ureport.version}</version>
            </dependency>

            <!-- 工作流引擎 - Camunda-->
            <dependency>
                <groupId>org.camunda.bpm.springboot</groupId>
                <artifactId>camunda-bpm-spring-boot-starter</artifactId>
                <version>${camunda.version}</version>
            </dependency>

            <dependency>
                <groupId>org.ssssssss</groupId>
                <artifactId>magic-api-spring-boot-starter</artifactId>
                <version>${magic.api.version}</version>
            </dependency>

            <!-- 阿里云短信 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun.core.version}</version>
            </dependency>

            <!--IO 处理-->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>

            <!-- excel导入导出 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${ooxml.schemas.version}</version>
            </dependency>

            <!--动态给类新增属性 的工具-->
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist.version}</version>
            </dependency>

            <!-- 规则引擎-->
            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>${liteflow.version}</version>
            </dependency>

            <!-- 第三方拼音依赖包，配合 hutool-all 包中的 PinyinUtil 拼音工具使用 start -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
            <!-- 第三方拼音依赖包，配合 hutool-all 包中的 PinyinUtil 拼音工具使用 start -->

            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>

            <dependency>
                <groupId>tech.powerjob</groupId>
                <artifactId>powerjob-worker-spring-boot-starter</artifactId>
                <version>${powerjob.version}</version>
            </dependency>

            <dependency>
                <groupId>tech.powerjob</groupId>
                <artifactId>powerjob-official-processors</artifactId>
                <version>${powerjob.version}</version>
            </dependency>

            <dependency>
                <groupId>tech.powerjob</groupId>
                <artifactId>powerjob-worker</artifactId>
                <version>${powerjob.version}</version>
            </dependency>

            <dependency>
                <groupId>org.smartboot.license</groupId>
                <artifactId>license-client</artifactId>
                <version>${license.version}</version>
            </dependency>

            <dependency>
                <groupId>me.zhyd.oauth</groupId>
                <artifactId>JustAuth</artifactId>
                <version>${justauth.version}</version>
            </dependency>

            <!-- Http请求工具 -->
            <dependency>
                <groupId>com.ejlchina</groupId>
                <artifactId>okhttps</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <!-- Admin 服务 -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring.boot.admin.version}</version>
            </dependency>

            <!-- Admin 界面 -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-server-ui</artifactId>
                <version>${spring.boot.admin.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring.boot.admin.version}</version>
            </dependency>

            <!-- 注意一定要引入对版本，要引入spring-cloud版本seata，而不是springboot版本的seata-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <!-- 排除掉springcloud默认的seata版本，以免版本不一致出现问题-->
                <exclusions>
                    <exclusion>
                        <groupId>io.seata</groupId>
                        <artifactId>seata-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.seata</groupId>
                        <artifactId>seata-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 上面排除掉了springcloud默认色seata版本，此处引入和seata-server版本对应的seata包-->
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${spring.cloud.seata.version}</version>
            </dependency>

            <dependency>
                <groupId>ru.yandex.clickhouse</groupId>
                <artifactId>clickhouse-jdbc</artifactId>
                <version>${clickhouse.version}</version>
            </dependency>

        </dependencies>

    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>${spotless.version}</version>
                <configuration>
                    <java>
                        <removeUnusedImports/>
                    </java>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <includeSystemScope>true</includeSystemScope>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
