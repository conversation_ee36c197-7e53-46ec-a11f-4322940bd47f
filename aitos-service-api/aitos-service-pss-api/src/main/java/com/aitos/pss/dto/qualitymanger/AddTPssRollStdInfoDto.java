package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 轧制工艺质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
public class AddTPssRollStdInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "订单号")
    private String cOrderNo;

    @Schema(description = "质量编码")
    private Long cQualId;

    @Schema(description = "质量编码")
    private String cQualCode;

    @Schema(description = "质量编码简称")
    private String cQualCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;
    
    @Schema(description = "控制轧制")
    private Long nControlRoll;
    
    @Schema(description = "开轧温度℃")
    private BigDecimal nStartTemp;
    
    @Schema(description = "粗轧轧制温度℃")
    private BigDecimal nFirstRollTemp;
    
    @Schema(description = "精轧轧制温度℃")
    private BigDecimal nSecRollTemp;
    
    @Schema(description = "终轧温度℃")
    private BigDecimal nFinalTemp;
    
    @Schema(description = "备用1")
    private String cReserver1;
    
    @Schema(description = "备用2")
    private String cReserver2;
    
    @Schema(description = "备用3")
    private String cReserver3;
    
    @Schema(description = "备用4")
    private String cReserver4;
    
    @Schema(description = "备用5")
    private String cReserver5;
    
    @Schema(description = "备用6")
    private String cReserver6;
    
    @Schema(description = "备用7")
    private String cReserver7;
    
    @Schema(description = "备用8")
    private String cReserver8;
    
    @Schema(description = "备用9")
    private String cReserver9;
    
    @Schema(description = "备用10")
    private String cReserver10;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
