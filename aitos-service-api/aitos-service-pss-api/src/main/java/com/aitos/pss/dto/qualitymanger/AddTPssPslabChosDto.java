package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 计划钢坯
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
public class AddTPssPslabChosDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 计划坯料号/件次号
    */
    @Schema(description = "计划坯料号/件次号")
    private String cPlanSlabId;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    private String cTaskListId;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种代码")
    private String cStlGrdDesc;
    /**
     * 质量编码
     */
    @Schema(description = "质量编码")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    private String cMatQulCd;

    @Schema(description = "质量编码")
    private String cMatQulName;
    /**
    * 板坯厚度
    */
    @Schema(description = "板坯厚度")
    private BigDecimal nSlabThk;
    /**
    * 板坯宽度
    */
    @Schema(description = "板坯宽度")
    private BigDecimal nSlabWth;
    /**
    * 板坯长度
    */
    @Schema(description = "板坯长度")
    private BigDecimal nSlabLen;
    /**
    * 是否可以头尾炉
    */
    @Schema(description = "是否可以头尾炉")
    private String cHtHeat;
    /**
    * 是否可以头尾坯
    */
    @Schema(description = "是否可以头尾坯")
    private String cHtSlab;
    /**
    * 是否热装热送
    */
    @Schema(description = "是否热装热送")
    private String cHotFlag;
    /**
    * 是否检验
    */
    @Schema(description = "是否检验")
    private String cTestFlag;
    /**
    * 板坯去向
    */
    @Schema(description = "板坯去向")
    private String cSentPlace;
    /**
    * 定尺类型
    */
    @Schema(description = "定尺类型")
    private String cDingchiType;
    /**
    * 坯料类型
    */
    @Schema(description = "坯料类型")
    private String cSlabType;
    /**
    * 执行标准
    */
    @Schema(description = "执行标准")
    private String cUseStd;
    /**
    * 连铸机号
    */
    @Schema(description = "连铸机id")
    private Long cCastId;

    @Schema(description = "连铸机code")
    private String cCastCode;

    @Schema(description = "连铸机name")
    private String cCastName;
    /**
    * 是否来料加工材
    */
    @Schema(description = "是否来料加工材")
    private String cLljg;
    /**
    * 物料编码
    */
    @Schema(description = "物料id")
    private Long cMatId;

    @Schema(description = "物料编码")
    private String cMatCd;

    @Schema(description = "物料name")
    private String cMatName;
    /**
    * 合同号
    */
    @Schema(description = "合同号")
    private String cContaId;
    /**
    * 溢短装比
    */
    @Schema(description = "溢短装比")
    private BigDecimal cFulwRad;
    /**
    * 要求完工日期
    */
    @Schema(description = "要求完工日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cWorkoutTim;
    /**
    * 特殊要求
    */
    @Schema(description = "特殊要求")
    private String cSpcNeed;
    /**
    * 发送人
    */
    @Schema(description = "发送人")
    private String cSendEmpid;
    /**
    * 发送时间
    */
    @Schema(description = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cSendDt;
    /**
    * 接受时间
    */
    @Schema(description = "接受时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cCheckInDt;
    /**
    * 提取人
    */
    @Schema(description = "提取人")
    private String cChosEmp;
    /**
    * 提取时间
    */
    @Schema(description = "提取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cChosDt;
    /**
    * 板坯重量
    */
    @Schema(description = "板坯重量")
    private BigDecimal cSlabWgt;
    /**
    * 已经编制(1:全部编制,2:部分编制)
    */
    @Schema(description = "已经编制(1:全部编制,2:部分编制)")
    private String cHasEdit;
    /**
    * 已经编制重量
    */
    @Schema(description = "已经编制重量")
    private BigDecimal cEditWgt;
    /**
    * 余材标志
    */
    @Schema(description = "余材标志")
    private String cRemSlabFlg;
    /**
    * 余材产生方式(1:已经加入计划板坯表；2:未加入计划板坯表)
    */
    @Schema(description = "余材产生方式(1:已经加入计划板坯表；2:未加入计划板坯表)")
    private String cRemCrtway;
    /**
    * 计划炉次号
    */
    @Schema(description = "计划炉次号")
    private String cPlanHeatId;
    /**
    * 炉次内序号
    */
    @Schema(description = "炉次内序号")
    private BigDecimal cHeatSeq;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
