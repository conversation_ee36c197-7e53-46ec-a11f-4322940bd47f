package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssStlMinglePageDto extends PageInput {

    
    @Schema(description = "产线")
    private Long cProLine;
    
    @Schema(description = "次浇钢种")
    private String cStlGrdCdB;

    @Schema(description = "首浇钢种")
    private String cStlGrdCdA;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    @Schema(description = "浇次内顺序")
    private Integer nCastNum;

}
