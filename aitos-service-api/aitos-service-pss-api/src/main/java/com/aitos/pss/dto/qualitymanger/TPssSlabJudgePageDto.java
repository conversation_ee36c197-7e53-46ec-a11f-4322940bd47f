package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssSlabJudgePageDto extends PageInput {

    
    @Schema(description = "坯料号")
    private String cSlabId;
    
    @Schema(description = "现钢种代码")
    private String cNewStlGrdCd;
    
    @Schema(description = "炉号")
    private String cHeatId;

    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;

    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;
}
