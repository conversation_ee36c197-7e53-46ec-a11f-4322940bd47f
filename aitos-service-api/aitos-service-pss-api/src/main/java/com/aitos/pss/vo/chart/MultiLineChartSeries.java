package com.aitos.pss.vo.chart;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 多维折线图
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/29 11:59
 */
@Data
public class MultiLineChartSeries {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "X轴")
    private List<String> xAxisList;

    @Schema(description = "数据")
    private List<BigDecimal> data;
}
