package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssCheatPlanPageDto extends PageInput {

    
    @Schema(description = "计划炉次号")
    private String cPlanHeatId;
    
    @Schema(description = "实绩钢种")
    private String cActStlGrdCd;
    
    @Schema(description = "质量编码id")
    private Long cMatQulId;
    
    @Schema(description = "计划浇次号")
    private String cPlanCastId;
    
    @Schema(description = "下达时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    
    @Schema(description = "下达时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    
    @Schema(description = "实绩炉次号")
    private String cHeatId;
    
    @Schema(description = "实绩材质")
    private String cActMatQulCd;
    
    @Schema(description = "状态")
    private String cStatus;
    
    @Schema(description = "连铸机号")
    private String cCastId;
    
    @Schema(description = "任务单号")
    private String cTaskListId;

}
