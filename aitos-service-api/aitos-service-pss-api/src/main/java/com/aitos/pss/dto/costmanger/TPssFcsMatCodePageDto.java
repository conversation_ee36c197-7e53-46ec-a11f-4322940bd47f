package com.aitos.pss.dto.costmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-26
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssFcsMatCodePageDto extends PageInput {

    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatName;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMatCode;
}
