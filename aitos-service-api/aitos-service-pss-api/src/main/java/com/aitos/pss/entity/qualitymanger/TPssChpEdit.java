package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 计划炉次表中间表
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
@TableName("t_pss_chp_edit")
@Tag(name = "计划炉次表中间表对象", description = "计划炉次表中间表")
public class TPssChpEdit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 序列号
    */
    @Schema(description = "序列号")
    @TableId
    private Long nId;
    /**
    * 炉次编制号
    */
    @Schema(description = "炉次编制号")
    @TableField(value = "n_heat_edt_seq")
    private BigDecimal nHeatEdtSeq;
    /**
    * 计划炉次号
    */
    @Schema(description = "计划炉次号")
    @TableField(value = "c_plan_heat_id")
    private String cPlanHeatId;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码id")
    @TableField(value = "c_mat_qul_id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_cd")
    private String cMatQulCd;

    @Schema(description = "质量编码name")
    @TableField(value = "c_mat_qul_name")
    private String cMatQulName;
    /**
    * 炼钢工艺流程
    */
    @Schema(description = "炼钢工艺流程")
    @TableField(value = "c_pl_route")
    private String cPlRoute;
    /**
    * 浇铸厚度
    */
    @Schema(description = "浇铸厚度")
    @TableField(value = "n_ccm_thk")
    private BigDecimal nCcmThk;
    /**
    * 浇铸宽度
    */
    @Schema(description = "浇铸宽度")
    @TableField(value = "n_ccm_wth")
    private BigDecimal nCcmWth;
    /**
    * 浇铸时长
    */
    @Schema(description = "浇铸时长")
    @TableField(value = "n_ccm_tme")
    private Long nCcmTme;
    /**
    * 订单板坯张数
    */
    @Schema(description = "订单板坯张数")
    @TableField(value = "n_ord_slab_cnt")
    private Long nOrdSlabCnt;
    /**
    * 余材板坯张数
    */
    @Schema(description = "余材板坯张数")
    @TableField(value = "n_woo_slab_cnt")
    private Long nWooSlabCnt;
    /**
    * 余材炉次代码
    */
    @Schema(description = "余材炉次代码")
    @TableField(value = "c_woo_heat_fl")
    private String cWooHeatFl;
    /**
    * 板坯块数
    */
    @Schema(description = "板坯块数")
    @TableField(value = "n_slab_cnt")
    private Long nSlabCnt;
    /**
    * 计划出钢量
    */
    @Schema(description = "计划出钢量")
    @TableField(value = "n_pre_heat_wgt")
    private BigDecimal nPreHeatWgt;
    /**
    * 炼钢作业期限
    */
    @Schema(description = "炼钢作业期限")
    @TableField(value = "c_sms_duedatetime")
    private String cSmsDuedatetime;
    /**
    * 浇次编制号
    */
    @Schema(description = "浇次编制号")
    @TableField(value = "n_cast_edt_seq")
    private String nCastEdtSeq;
    /**
    * 计划浇次号
    */
    @Schema(description = "计划浇次号")
    @TableField(value = "c_plan_cast_id")
    private String cPlanCastId;
    /**
    * 浇次内顺序号
    */
    @Schema(description = "浇次内顺序号")
    @TableField(value = "n_cast_heat_seq")
    private Long nCastHeatSeq;
    /**
    * 浇次炉数
    */
    @Schema(description = "浇次炉数")
    @TableField(value = "n_cast_heat_cnt")
    private Long nCastHeatCnt;
    /**
    * 转炉炉座号
    */
    @Schema(description = "转炉炉座号")
    @TableField(value = "c_ld_wkst")
    private String cLdWkst;
    /**
    * 计划冶炼开始时间
    */
    @Schema(description = "计划冶炼开始时间")
    @TableField(value = "dt_pre_ld_time")
    private LocalDateTime dtPreLdTime;
    /**
    * 计划出钢开始时间
    */
    @Schema(description = "计划出钢开始时间")
    @TableField(value = "dt_pre_ld_str_tme")
    private LocalDateTime dtPreLdStrTme;
    /**
    * 计划出钢结束时间
    */
    @Schema(description = "计划出钢结束时间")
    @TableField(value = "dt_pre_ld_end_tme")
    private LocalDateTime dtPreLdEndTme;
    /**
    * 计划第一次LF炉座号
    */
    @Schema(description = "计划第一次LF炉座号")
    @TableField(value = "c_fir_lf_wkst")
    private String cFirLfWkst;
    /**
    * 计划第一次 LF 开始时间
    */
    @Schema(description = "计划第一次 LF 开始时间")
    @TableField(value = "dt_fir_lf_sttime")
    private LocalDateTime dtFirLfSttime;
    /**
    * 计划第一次 LF 结束时间
    */
    @Schema(description = "计划第一次 LF 结束时间")
    @TableField(value = "dt_fir_lf_endtime")
    private LocalDateTime dtFirLfEndtime;
    /**
    * 计划第二次LF炉座号
    */
    @Schema(description = "计划第二次LF炉座号")
    @TableField(value = "c_sec_lf_wkst")
    private String cSecLfWkst;
    /**
    * 计划第二次 LF 开始时间
    */
    @Schema(description = "计划第二次 LF 开始时间")
    @TableField(value = "dt_sec_lf_sttime")
    private LocalDateTime dtSecLfSttime;
    /**
    * 计划第二次 LF 结束时间
    */
    @Schema(description = "计划第二次 LF 结束时间")
    @TableField(value = "dt_sec_lf_endtime")
    private LocalDateTime dtSecLfEndtime;
    /**
    * 计划第一次VOD炉座号
    */
    @Schema(description = "计划第一次VOD炉座号")
    @TableField(value = "c_fir_vd_wkst")
    private String cFirVdWkst;
    /**
    * 计划第一次VD开始时间
    */
    @Schema(description = "计划第一次VD开始时间")
    @TableField(value = "dt_fir_vd_sttime")
    private LocalDateTime dtFirVdSttime;
    /**
    * 计划第一次VD结束时间
    */
    @Schema(description = "计划第一次VD结束时间")
    @TableField(value = "dt_fir_vd_endtime")
    private LocalDateTime dtFirVdEndtime;
    /**
    * 计划第二次VD炉座号
    */
    @Schema(description = "计划第二次VD炉座号")
    @TableField(value = "c_sec_vd_wkst")
    private String cSecVdWkst;
    /**
    * 计划第二次VD 开始时间
    */
    @Schema(description = "计划第二次VD 开始时间")
    @TableField(value = "dt_sec_vd_sttime")
    private LocalDateTime dtSecVdSttime;
    /**
    * 计划第二次VD结束时间
    */
    @Schema(description = "计划第二次VD结束时间")
    @TableField(value = "dt_sec_vd_endtime")
    private LocalDateTime dtSecVdEndtime;
    /**
    * 大包开浇时间
    */
    @Schema(description = "大包开浇时间")
    @TableField(value = "dt_plan_ladle_open")
    private LocalDateTime dtPlanLadleOpen;
    /**
    * 连铸机号
    */
    @Schema(description = "连铸机号")
    @TableField(value = "c_ccm_wkst")
    private String cCcmWkst;
    /**
    * 计划浇铸开始时间
    */
    @Schema(description = "计划浇铸开始时间")
    @TableField(value = "dt_pre_ccm_str_tme")
    private LocalDateTime dtPreCcmStrTme;
    /**
    * 计划浇铸结束时间
    */
    @Schema(description = "计划浇铸结束时间")
    @TableField(value = "dt_pre_ccm_end_tme")
    private LocalDateTime dtPreCcmEndTme;
    /**
    * 混炉标志
    */
    @Schema(description = "混炉标志")
    @TableField(value = "c_mstlgrd_fl")
    private String cMstlgrdFl;
    /**
    * 变更程序ID
    */
    @Schema(description = "变更程序ID")
    @TableField(value = "c_upd_pgmid")
    private String cUpdPgmid;
    /**
    * 状态
    */
    @Schema(description = "状态")
    @TableField(value = "c_status")
    private String cStatus;
    /**
    * 炉号
    */
    @Schema(description = "炉号")
    @TableField(value = "c_heat_id")
    private String cHeatId;
    /**
    * 实际钢种代码
    */
    @Schema(description = "实际钢种代码")
    @TableField(value = "c_act_stl_grd_cd")
    private String cActStlGrdCd;
    /**
    * 实际质量编码
    */
    @Schema(description = "实际质量编码")
    @TableField(value = "c_act_mat_qul_cd")
    private String cActMatQulCd;
    /**
    * 连铸机号
    */
    @Schema(description = "连铸机号")
    @TableField(value = "c_cast_id")
    private String cCastId;
    /**
    * 混铁炉座次
    */
    @Schema(description = "混铁炉座次")
    @TableField(value = "c_huntie_wks")
    private String cHuntieWks;
    /**
    * 预处理座次
    */
    @Schema(description = "预处理座次")
    @TableField(value = "c_yuchuli_wks")
    private String cYuchuliWks;
    /**
    * 当前工位
    */
    @Schema(description = "当前工位")
    @TableField(value = "c_curr_station_cd")
    private String cCurrStationCd;
    /**
    * 当前事件
    */
    @Schema(description = "当前事件")
    @TableField(value = "c_curr_event_cd")
    private String cCurrEventCd;
    /**
    * 上一事件
    */
    @Schema(description = "上一事件")
    @TableField(value = "c_prev_event_cd")
    private String cPrevEventCd;
    /**
    * 坯料长度
    */
    @Schema(description = "坯料长度")
    @TableField(value = "c_slab_len")
    private BigDecimal cSlabLen;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    @TableField(value = "task_list_id")
    private String taskListId;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time")
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}