package com.aitos.pss.entity.inventory;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 退货管理
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Data
@TableName("t_pss_steel_sale_return")
@Tag(name = "退货管理对象", description = "退货管理")
public class TPssSteelSaleReturn implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键
    */
    @Schema(description = "主键")
    @TableId
    private Long nId;
    /**
    * 提货单号
    */
    @Schema(description = "提货单号")
    @TableField(value = "delivery_no")
    private String deliveryNo;
    /**
    * 库号
    */
    @Schema(description = "库号")
    @TableField(value = "stock_no")
    private String stockNo;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    @TableField(value = "batch_no")
    private String batchNo;
    /**
    * 材料号/卷号
    */
    @Schema(description = "材料号/卷号")
    @TableField(value = "material_no")
    private String materialNo;
    /**
    * 钢种名称
    */
    @Schema(description = "钢种名称")
    @TableField(value = "steel_grade")
    private String steelGrade;
    /**
    * 规格
    */
    @Schema(description = "规格")
    @TableField(value = "spec")
    private String spec;
    /**
    * 单件重量(kg)
    */
    @Schema(description = "单件重量(kg)")
    @TableField(value = "weight")
    private BigDecimal weight;
    /**
    * 退货数量(件/卷)
    */
    @Schema(description = "退货数量(件/卷)")
    @TableField(value = "return_qty")
    private Integer returnQty;
    /**
    * 退货总重量(kg)
    */
    @Schema(description = "退货总重量(kg)")
    @TableField(value = "return_weight")
    private BigDecimal returnWeight;
    /**
    * 退货申请时间
    */
    @Schema(description = "退货申请时间")
    @TableField(value = "return_time")
    private LocalDateTime returnTime;
    /**
    * 退货申请人
    */
    @Schema(description = "退货申请人")
    @TableField(value = "return_user")
    private String returnUser;
    /**
    * 退货原因
    */
    @Schema(description = "退货原因")
    @TableField(value = "return_reason")
    private String returnReason;
    /**
    * 原出库单号
    */
    @Schema(description = "原出库单号")
    @TableField(value = "out_no")
    private String outNo;
    /**
    * 状态(待退库/已退库/已驳回等)
    */
    @Schema(description = "状态(待退库/已退库/已驳回等)")
    @TableField(value = "status")
    private String status;
    /**
    * 备注
    */
    @Schema(description = "备注")
    @TableField(value = "remark")
    private String remark;
    /**
    * 是否启用;默认为0,1为未启用
    */
    @Schema(description = "是否启用;默认为0,1为未启用")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "dt_create_date_time")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "dt_modify_date_time")
    private LocalDateTime dtModifyDateTime;


}