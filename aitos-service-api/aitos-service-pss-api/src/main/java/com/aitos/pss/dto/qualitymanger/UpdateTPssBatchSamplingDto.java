package com.aitos.pss.dto.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;



/**
* @title: 轧钢组批取样
* <AUTHOR>
* @Date: 2025-06-16
* @Version 1.0
*/
@Data
public class UpdateTPssBatchSamplingDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "")
    private Long nId;
    
    @Schema(description = "炉次号")
    private String cHeatId;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称")
    private String cStlGrdDesc;
    
    @Schema(description = "规格")
    private String cSpec;
    
    @Schema(description = "检验批号")
    private String cSmpLot;
    
    @Schema(description = "质量编码")
    private String cQualityCode;
    
    @Schema(description = "产线")
    private String cLineCd;
    
    @Schema(description = "物料长度")
    private BigDecimal nThk;
    
    @Schema(description = "委托次数")
    private Integer nSampNum;
    
    @Schema(description = "委托单号")
    private String cTestcrdId;
    
    @Schema(description = "检验炉次号")
    private String cSmpHeatId;
    
    @Schema(description = "委托单状态（A：等待发送、B：等待试验、C：试验完毕、D：性能判定完毕）")
    private String cSmpStates;
    
    @Schema(description = "检验批次包含炉次号")
    private String cSampHeatIdList;
}
