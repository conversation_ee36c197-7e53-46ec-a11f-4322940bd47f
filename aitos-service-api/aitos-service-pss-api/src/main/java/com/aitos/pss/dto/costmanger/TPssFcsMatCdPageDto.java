package com.aitos.pss.dto.costmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssFcsMatCdPageDto extends PageInput {

    /**
    * 顺序号
    */
    @Schema(description = "顺序号")
    private Long nId;
    /**
    * 成本小类名称
    */
    @Schema(description = "成本小类名称")
    private String cCostItemName;
    /**
    * 成本项目名称
    */
    @Schema(description = "成本项目名称")
    private String cMatClassName;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private String cUnit;
    /**
    * 显示顺序
    */
    @Schema(description = "显示顺序")
    private Integer nCostSeq;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMatCode;
    /**
    * 是否区分座次
    */
    @Schema(description = "是否区分座次")
    private String cSeat;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 成本小类编码
    */
    @Schema(description = "成本小类编码")
    private String cCostItemCode;
    /**
    * 成本项目编码
    */
    @Schema(description = "成本项目编码")
    private String cMatClassCode;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cMemo;
    /**
    * 产线
    */
    @Schema(description = "产线")
    private Long cProLine;
    /**
    * 工序
    */
    @Schema(description = "工序")
    private Long cProcId;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatName;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

}
