package com.aitos.pss.entity.costmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 成本物料配置
* <AUTHOR>
* @Date: 2025-06-26
* @Version 1.0
*/
@Data
@TableName("t_pss_fcs_mat_code")
@Tag(name = "成本物料配置对象", description = "成本物料配置")
public class TPssFcsMatCode implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    @TableId
    private Long nId;

    @Schema(description = "物料id")
    @TableField(value = "c_mat_id")
    private Long cMatId;

    @Schema(description = "物料编码")
    @TableField(value = "c_mat_code")
    private String cMatCode;

    @Schema(description = "物料名称")
    @TableField(value = "c_mat_name")
    private String cMatName;

    @Schema(description = "物料组编码")
    @TableField(value = "c_mat_group_id")
    private String cMatGroupId;

    @Schema(description = "物料组名称")
    @TableField(value = "c_mat_group_name")
    private String cMatGroupName;

    @Schema(description = "单位")
    @TableField(value = "c_mat_unit")
    private String cMatUnit;

    @Schema(description = "是否使用")
    @TableField(value = "c_aply_std")
    private Integer cAplyStd;

    @Schema(description = "物料科目编码")
    @TableField(value = "c_mat_class_code")
    private String cMatClassCode;

    @Schema(description = "物料科目名称")
    @TableField(value = "c_mat_class_name")
    private String cMatClassName;

    @Schema(description = "项目大类代码")
    @TableField(value = "c_mat_type_b_id")
    private String cMatTypeBId;

    @Schema(description = "项目大类名称")
    @TableField(value = "c_mat_type_b_name")
    private String cMatTypeBName;

    @Schema(description = "项目中类代码")
    @TableField(value = "c_mat_type_m_id")
    private String cMatTypeMId;

    @Schema(description = "项目中类名称")
    @TableField(value = "c_mat_type_m_name")
    private String cMatTypeMName;

    @Schema(description = "项目小类代码")
    @TableField(value = "c_mat_type_s_id")
    private String cMatTypeSId;

    @Schema(description = "项目小类名称")
    @TableField(value = "c_mat_type_s_name")
    private String cMatTypeSName;

    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;

    @Schema(description = "规格1")
    @TableField(value = "c_mat_item1")
    private String cMatItem1;

    @Schema(description = "规格2")
    @TableField(value = "c_mat_item2")
    private String cMatItem2;

    @Schema(description = "规格3")
    @TableField(value = "c_mat_item3")
    private String cMatItem3;

    @Schema(description = "长度组编码")
    @TableField(value = "c_mat_lth_group_id")
    private String cMatLthGroupId;

    @Schema(description = "长度组名称")
    @TableField(value = "c_mat_lth_group_name")
    private String cMatLthGroupName;

    @Schema(description = "使用区域")
    @TableField(value = "c_area_id")
    private String cAreaId;

    @Schema(description = "备注")
    @TableField(value = "c_memo")
    private String cMemo;

    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;

    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;

    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;

    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;

    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;
}