package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-15
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssCommonCodePageDto extends PageInput {

    
    @Schema(description = "代码")
    private String cCode;
    
    @Schema(description = "代码名称")
    private String cName;
    
    @Schema(description = "代码英文名称")
    private String cFullEng;
    
    @Schema(description = "代码管理号")
    private String cManaNo;
    
    @Schema(description = "代码简称")
    private String cShortName;
    
    @Schema(description = "代码英文简称")
    private String cShortEng;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

}
