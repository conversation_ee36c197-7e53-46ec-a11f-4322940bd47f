package com.aitos.pss.dto.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 熔量重量标准-钢种收得率
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
public class AddTPssRateStdDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;
    
    @Schema(description = "成材率")
    private BigDecimal nPlateSlabRate;
    
    @Schema(description = "收得率")
    private BigDecimal nSlabStlRate;
    
    @Schema(description = "密度(t/m)")
    private BigDecimal nDensity;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
