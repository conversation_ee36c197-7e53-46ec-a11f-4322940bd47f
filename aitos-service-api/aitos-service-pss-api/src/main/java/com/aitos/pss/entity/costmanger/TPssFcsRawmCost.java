package com.aitos.pss.entity.costmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 工序成本核算
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
@TableName("t_pss_fcs_rawm_cost")
@Tag(name = "工序成本核算对象", description = "工序成本核算")
public class TPssFcsRawmCost implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 
    */
    @Schema(description = "")
    @TableId
    private Long nId;
    /**
    * 炉号
    */
    @Schema(description = "炉号")
    @TableField(value = "c_heat_id")
    private String cHeatId;
    /**
    * 钢种
    */
    @Schema(description = "钢种")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    /**
    * 产线
    */
    @Schema(description = "产线id")
    @TableField(value = "c_pro_line")
    private Long cProLine;

    @Schema(description = "产线cd")
    @TableField(value = "c_pro_line_code")
    private String cProLineCode;

    @Schema(description = "产线name")
    @TableField(value = "c_pro_line_name")
    private String cProLineName;
    /**
    * 工序
    */
    @Schema(description = "工序id")
    @TableField(value = "c_proc_id")
    private Long cProcId;

    @Schema(description = "工序cd")
    @TableField(value = "c_proc_cd")
    private String cProcCd;

    @Schema(description = "工序name")
    @TableField(value = "c_proc_name")
    private String cProcName;
    /**
    * 成本项目编码
    */
    @Schema(description = "成本项目编码")
    @TableField(value = "c_cost_item_code")
    private String cCostItemCode;
    /**
    * 成本项目名称
    */
    @Schema(description = "成本项目名称")
    @TableField(value = "c_cost_item_name")
    private String cCostItemName;
    /**
    * 物料id
    */
    @Schema(description = "物料id")
    @TableField(value = "c_mat_id")
    private Long cMatId;
    /**
    * 物料代码
    */
    @Schema(description = "物料代码")
    @TableField(value = "c_mat_code")
    private String cMatCode;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    @TableField(value = "c_mat_name")
    private String cMatName;
    /**
    * 计划价格
    */
    @Schema(description = "计划价格")
    @TableField(value = "c_plan_price")
    private BigDecimal cPlanPrice;
    /**
    * 实际价格
    */
    @Schema(description = "实际价格")
    @TableField(value = "c_act_price")
    private BigDecimal cActPrice;
    /**
    * 消耗量
    */
    @Schema(description = "消耗量")
    @TableField(value = "c_mat_qty")
    private BigDecimal cMatQty;

    @Schema(description = "实际成本量")
    @TableField(value = "c_act_mat_qty")
    private BigDecimal cActMatQty;

    /**
    * 开始使用日期
    */
    @Schema(description = "开始使用日期")
    @TableField(value = "dt_use_date")
    private LocalDateTime dtUseDate;
    /**
    * 班组
    */
    @Schema(description = "班组")
    @TableField(value = "c_crew")
    private String cCrew;
    /**
    * 班次
    */
    @Schema(description = "班次")
    @TableField(value = "c_shift")
    private String cShift;
    /**
    * 结束使用时间
    */
    @Schema(description = "结束使用时间")
    @TableField(value = "dt_use_date_end")
    private LocalDateTime dtUseDateEnd;
    /**
    * 显示顺序
    */
    @Schema(description = "显示顺序")
    @TableField(value = "n_cost_seq")
    private Integer nCostSeq;
    /**
    * 核算单位
    */
    @Schema(description = "核算单位")
    @TableField(value = "c_type")
    private String cType;
    /**
    * 定额
    */
    @Schema(description = "定额")
    @TableField(value = "c_quota")
    private BigDecimal cQuota;
    /**
    * 公摊系数
    */
    @Schema(description = "公摊系数")
    @TableField(value = "c_ratio")
    private BigDecimal cRatio;
    /**
    * 座次
    */
    @Schema(description = "座次id")
    @TableField(value = "c_seat")
    private Long cSeat;

    @Schema(description = "座次code")
    @TableField(value = "c_seat_code")
    private String cSeatCode;

    @Schema(description = "座次name")
    @TableField(value = "c_seat_name")
    private String cSeatName;
    /**
    * 成本日期
    */
    @Schema(description = "成本日期")
    @TableField(value = "c_cost_date")
    private LocalDateTime cCostDate;
    /**
    * 状态
    */
    @Schema(description = "状态")
    @TableField(value = "c_state")
    private Integer cState;
    /**
    * 审核人
    */
    @Schema(description = "审核人")
    @TableField(value = "c_pro_emp")
    private Long cProEmp;
    /**
    * 审核时间
    */
    @Schema(description = "审核时间")
    @TableField(value = "dt_pro_time")
    private LocalDateTime dtProTime;
    /**
    * 上传标志位
    */
    @Schema(description = "上传标志位")
    @TableField(value = "c_month_flag")
    private String cMonthFlag;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;
    /**
    * 单位
    */
    @Schema(description = "单位")
    @TableField(value = "c_unit_id")
    private Long cUnitId;
    /**
    * 单位编码
    */
    @Schema(description = "单位编码")
    @TableField(value = "c_unit_code")
    private String cUnitCode;
    /**
    * 单位名称
    */
    @Schema(description = "单位名称")
    @TableField(value = "c_unit_name")
    private String cUnitName;
}