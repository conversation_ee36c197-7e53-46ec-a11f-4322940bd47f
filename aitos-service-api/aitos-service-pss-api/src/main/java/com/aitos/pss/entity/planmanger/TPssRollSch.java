package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 物料匹配管理
* <AUTHOR>
* @Date: 2025-05-28
* @Version 1.0
*/
@Data
@TableName("t_pss_roll_sch")
@Tag(name = "物料匹配管理对象", description = "物料匹配管理")
public class TPssRollSch implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "轧制顺序计划id")
    @TableField(value = "c_sch_id")
    private String cSchId;
    
    @Schema(description = "前顺序计划id")
    @TableField(value = "c_prev_sch_id")
    private String cPrevSchId;
    
    @Schema(description = "同轧制队列中顺序")
    @TableField(value = "n_seq_in_mill")
    private BigDecimal nSeqInMill;
    
    @Schema(description = "是否完结(参考值)")
    @TableField(value = "c_ref_completed_fl")
    private String cRefCompletedFl;
    
    @Schema(description = "备注")
    @TableField(value = "c_memo")
    private String cMemo;
    
    @Schema(description = "调度单号")
    @TableField(value = "c_dispatch_id")
    private String cDispatchId;
    
    @Schema(description = "产线id")
    @TableField(value = "c_line_id")
    private Long cLineId;

    @Schema(description = "产线no")
    @TableField(value = "c_line_no")
    private String cLineNo;

    @Schema(description = "产线name")
    @TableField(value = "c_line_name")
    private String cLineName;
    
    @Schema(description = "炉号")
    @TableField(value = "c_heat_id")
    private String cHeatId;
    
    @Schema(description = "自动手动标志 0手动 1自动")
    @TableField(value = "c_make_fl")
    private String cMakeFl;
    
    @Schema(description = "产品类型")
    @TableField(value = "c_prod_type")
    private String cProdType;
    
    @Schema(description = "直径")
    @TableField(value = "n_spec")
    private BigDecimal nSpec;
    
    @Schema(description = "产品长度")
    @TableField(value = "n_prod_len")
    private BigDecimal nProdLen;
    
    @Schema(description = "精整路径")
    @TableField(value = "c_plan_finishing_path")
    private String cPlanFinishingPath;
    
    @Schema(description = "定尺代码")
    @TableField(value = "c_size_property")
    private String cSizeProperty;
    
    @Schema(description = "存货编码id （ERP）")
    @TableField(value = "c_matid")
    private String cMatid;

    @Schema(description = "存货编码code （ERP）")
    @TableField(value = "c_matcode")
    private String cMatcode;
    
    @Schema(description = "存货名称 （ERP）")
    @TableField(value = "c_matname")
    private String cMatname;
    
    @Schema(description = "产品钢种")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "产品材质")
    @TableField(value = "c_mat_qul_cd")
    private String cMatQulCd;
    
    @Schema(description = "产品宽")
    @TableField(value = "n_prod_wth")
    private BigDecimal nProdWth;
    
    @Schema(description = "钢坯规格")
    @TableField(value = "c_slab_spec")
    private String cSlabSpec;
    
    @Schema(description = "班次")
    @TableField(value = "c_shift")
    private String cShift;
    
    @Schema(description = "班别")
    @TableField(value = "c_crew")
    private String cCrew;
    
    @Schema(description = "V 真空,X高线,D大棒,B棒卷")
    @TableField(value = "c_heat_head")
    private String cHeatHead;
    
    @Schema(description = "优质钢判断")
    @TableField(value = "c_stl_grd_out")
    private String cStlGrdOut;
    
    @Schema(description = "精整备注")
    @TableField(value = "c_memomemo")
    private String cMemomemo;
    
    @Schema(description = "旧存货编码")
    @TableField(value = "c_oldcode")
    private String cOldcode;
    
    @Schema(description = "订单号")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    
    @Schema(description = "成品代码")
    @TableField(value = "c_item_cd")
    private String cItemCd;
    
    @Schema(description = "成品规格")
    @TableField(value = "c_mat_item")
    private String cMatItem;
    
    @Schema(description = "挂单重量")
    @TableField(value = "n_dofinal_wgt")
    private BigDecimal nDofinalWgt;
    
    @Schema(description = "状态")
    @TableField(value = "c_status")
    private String cStatus;
    
    @Schema(description = "挂单数量")
    @TableField(value = "n_dofinal_count")
    private BigDecimal nDofinalCount;
    
    @Schema(description = "执行标准")
    @TableField(value = "c_std_spec")
    private String cStdSpec;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
}