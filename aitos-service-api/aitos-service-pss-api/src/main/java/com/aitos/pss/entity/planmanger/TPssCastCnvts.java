package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 浇次标准-炉次浇次数量编制标准
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
@TableName("t_pss_cast_cnvts")
@Tag(name = "浇次标准-炉次浇次数量编制标准对象", description = "浇次标准-炉次浇次数量编制标准")
public class TPssCastCnvts implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "质量编码id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    private String cMatQulCd;

    @Schema(description = "质量编码名称")
    private String cMatQulName;
    
    @Schema(description = "钢种")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;

    @Schema(description = "产线")
    private Long cProLine;

    @Schema(description = "产线编码")
    private String cProLineCode;

    @Schema(description = "产线名称")
    private String cProLineName;
    
    @Schema(description = "炉数下限")
    private BigDecimal nHeatnumMin;
    
    @Schema(description = "炉数上限")
    private BigDecimal nHeatnumMax;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
}