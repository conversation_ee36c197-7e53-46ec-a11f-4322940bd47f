package com.aitos.pss.vo.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@Data
public class TPssPFaceVo {

    
    @Schema(description = "")
    private Long nId;
    
    @Schema(description = "线卷号/坯料号/件次号")
    private String cPlateId;
    
    @Schema(description = "检查次数")
    private Integer nChkSeq;
    
    @Schema(description = "检查工序")
    private String cChkPos;
    
    @Schema(description = "线卷面")
    private String cPlateFace;
    
    @Schema(description = "缺陷序号")
    private Long cDefectSeq;
    
    @Schema(description = "缺陷代码")
    private String cDefectType;
    
    @Schema(description = "缺陷位置")
    private String cDefectPos;
    
    @Schema(description = "缺陷描述")
    private String cDefectDesc;
    
    @Schema(description = "处理建议")
    private String cDispPro;
    
    @Schema(description = "处理方式")
    private String cDispManner;
    
    @Schema(description = "处理结果")
    private String cDispResult;
    
    @Schema(description = "班次")
    private String cShift;
    
    @Schema(description = "班别")
    private String cCrew;
    
    @Schema(description = "修磨人")
    private String cGrindOperater;
    
    @Schema(description = "修磨班次")
    private String cGrindShift;
    
    @Schema(description = "修磨班别")
    private String cGrindCrew;
    
    @Schema(description = "修磨工序")
    private String cGrindPos;
    
    @Schema(description = "修磨时间")
    private LocalDateTime dtGrindTime;
    
    @Schema(description = "表面等级")
    private String cFaceRlt;
    
    @Schema(description = "备注")
    private String cRemarks;
    
    @Schema(description = "缺陷名称")
    private String cDefectName;



}
