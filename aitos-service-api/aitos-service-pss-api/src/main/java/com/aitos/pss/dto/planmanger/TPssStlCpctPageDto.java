package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssStlCpctPageDto extends PageInput {

    
    @Schema(description = "电炉座次")
    private Long cBofId;

    @Schema(description = "钢种")
    private String cStlGrdCd;

    @Schema(description = "钢水上限")
    private Integer nHeatMax1;

    @Schema(description = "钢水下限")
    private Integer nHeatMin;

    @Schema(description = "收得率")
    private BigDecimal nHeatRacvRadio;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
}
