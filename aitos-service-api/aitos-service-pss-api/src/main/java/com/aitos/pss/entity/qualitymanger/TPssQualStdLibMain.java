package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 质检标准库管理
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Data
@TableName("t_pss_qual_std_lib_main")
@Tag(name = "质检标准库管理对象", description = "质检标准库管理")
public class TPssQualStdLibMain implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "质检标准编码")
    private String cStdcode;
    
    @Schema(description = "质检标准名称")
    private String cStdname;
    
    @Schema(description = "质检项目名称")
    private String cItemname;
    
    @Schema(description = "产线id")
    private Long cProLine;

    @Schema(description = "产线code")
    private String cProLineCode;

    @Schema(description = "产线name")
    private String cProLineName;
    
    @Schema(description = "物料id")
    private Long cMaterial;

    @Schema(description = "物料code")
    private String cMaterialCode;

    @Schema(description = "物料name")
    private String cMaterialName;
    
    @Schema(description = "质检标准类别")
    private String cStdtype;

    @Schema(description = "质检标准类别name")
    private String cStdtypeName;

    @Schema(description = "质检标准类别编码")
    private String cStdtypeCode;
    
    @Schema(description = "标准类型")
    private String cExecute;
    
    @Schema(description = "执行标准")
    private String cProjectAttributeCode;
    
    @Schema(description = "是否下发;0未下发，1下发")
    private Integer nIssued;
    
    @Schema(description = "判定等级")
    private String cValueType;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
}