package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssCastCnvtsPageDto extends PageInput {

    
    @Schema(description = "产线")
    private Long cProLine;
    
    @Schema(description = "钢种")
    private String cStlGrdCd;

    @Schema(description = "质量编码id")
    private Long cMatQulId;

    @Schema(description = "炉数下限")
    private BigDecimal nHeatnumMin;

    @Schema(description = "炉数上限")
    private BigDecimal nHeatnumMax;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

}
