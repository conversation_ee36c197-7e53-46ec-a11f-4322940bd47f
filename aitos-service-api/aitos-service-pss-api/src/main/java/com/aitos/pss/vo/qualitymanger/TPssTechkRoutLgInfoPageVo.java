package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
public class TPssTechkRoutLgInfoPageVo {

    
    @Schema(description = "主键")
    private String nId;
    
    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "质量编码id")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;

    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;

    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;

    @Schema(description = "产线id")
    private Long cProLine;

    @Schema(description = "产线code")
    private String cProLineCode;

    @Schema(description = "产线name")
    private String cProLineName;

    @Schema(description = "工序1")
    private Long cTechRoute1;

    @Schema(description = "工序1code")
    private String cTechRoute1Code;

    @Schema(description = "工序1name")
    private String cTechRoute1Name;

    @Schema(description = "工序2")
    private Long cTechRoute2;

    @Schema(description = "工序2code")
    private String cTechRoute2Code;

    @Schema(description = "工序2name")
    private String cTechRoute2Name;

    @Schema(description = "工序3")
    private Long cTechRoute3;

    @Schema(description = "工序3code")
    private String cTechRoute3Code;

    @Schema(description = "工序3name")
    private String cTechRoute3Name;

    @Schema(description = "工序4")
    private Long cTechRoute4;

    @Schema(description = "工序4code")
    private String cTechRoute4Code;

    @Schema(description = "工序4name")
    private String cTechRoute4Name;

    @Schema(description = "工序5")
    private Long cTechRoute5;

    @Schema(description = "工序5code")
    private String cTechRoute5Code;

    @Schema(description = "工序5name")
    private String cTechRoute5Name;

    @Schema(description = "工序6")
    private Long cTechRoute6;

    @Schema(description = "工序6code")
    private String cTechRoute6Code;

    @Schema(description = "工序6name")
    private String cTechRoute6Name;

    @Schema(description = "工序7")
    private Long cTechRoute7;

    @Schema(description = "工序7code")
    private String cTechRoute7Code;

    @Schema(description = "工序7name")
    private String cTechRoute7Name;

    @Schema(description = "工序8")
    private Long cTechRoute8;

    @Schema(description = "工序8code")
    private String cTechRoute8Code;

    @Schema(description = "工序8name")
    private String cTechRoute8Name;
    
    @Schema(description = "是否堆垛缓冷")
    private String cIsCold;
    
    @Schema(description = "工艺路径1")
    private String techRoute0;
    
    @Schema(description = "备注")
    private String cMemo;
    
    @Schema(description = "公司文件编号")
    private String cDocNum1;
    
    @Schema(description = "厂文件编号")
    private String cDocNum2;
    
    @Schema(description = "工艺路径2")
    private String cTechRoute;
    
    @Schema(description = "精整路径汇总")
    private String cFineRoute;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("创建时间")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("修改时间")
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
