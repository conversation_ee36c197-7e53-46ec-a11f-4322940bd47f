package com.aitos.pss.dto.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;



/**
* @title: 原辅料入库管理子表
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
public class AddTPssMaterialInspectionDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
    * 关联的主表主键ID
    */
    @Schema(description = "关联的主表的主键ID")
    private Long nAssociationId;
    /**
    * 到货记录ID
    */
    @Schema(description = "到货记录ID")
    private Long nReceiptId;
    /**
    * 检验项目(chemical:化学成分,physical:物理性能,appearance:外观质量,size:尺寸规格,weight:重量偏差)
    */
    @Schema(description = "检验项目(chemical:化学成分,physical:物理性能,appearance:外观质量,size:尺寸规格,weight:重量偏差)")
    private String cItem;
    /**
    * 检验标准
    */
    @Schema(description = "检验标准")
    private String cStandard;
    /**
    * 检验结果
    */
    @Schema(description = "检验结果")
    private String cResult;
    /**
    * 是否合格(0:不合格,1:合格)
    */
    @Schema(description = "是否合格(0:不合格,1:合格)")
    private Integer nIsQualified;
    /**
    * 检验人
    */
    @Schema(description = "检验人")
    private String cInspector;
    /**
    * 检验时间
    */
    @Schema(description = "检验时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtInspectionTime;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
