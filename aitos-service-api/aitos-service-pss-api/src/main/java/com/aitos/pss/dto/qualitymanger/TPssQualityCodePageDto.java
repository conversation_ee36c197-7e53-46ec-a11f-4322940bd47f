package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssQualityCodePageDto extends PageInput {

    
    @Schema(description = "名称")
    private String cQualCodeName;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "质量代码;质量编码")
    private String cQualCode;

    @Schema(description = "状态（新建/已转换）")
    private Integer cStatus;

}
