package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 炼钢表尺判定管理
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
public class AddTPssSlabResDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    private Long nId;

    
    @Schema(description = "计划件次号")
    private String cPlanMatId;
    
    @Schema(description = "件次号/坯料号")
    private String cMatId;
    
    @Schema(description = "数量")
    private Integer nCnt;
    
    @Schema(description = "原料号")
    private String cPreMatId;
    
    @Schema(description = "物料类型")
    private String cMatType;
    
    @Schema(description = "炉号")
    private String cHeatId;
    
    @Schema(description = "轧制批次号")
    private String cRollSchId;
    
    @Schema(description = "母板号")
    private String cMatIdMth;
    
    @Schema(description = "物料状态")
    private String cStatus;
    
    @Schema(description = "前物料状态")
    private String cPreStatus;
    
    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "质量编码")
    private Long cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;

    @Schema(description = "质量编码名称")
    private String cQualityCodeName;
    
    @Schema(description = "物料编码")
    private String cMatCode;
    
    @Schema(description = "物料名称")
    private String cMatName;
    
    @Schema(description = "规格")
    private String cMatItem;
    
    @Schema(description = "厚度")
    private BigDecimal nMatThk;
    
    @Schema(description = "宽度")
    private BigDecimal nMatWid;
    
    @Schema(description = "直径")
    private BigDecimal nMatDia;
    
    @Schema(description = "长度")
    private BigDecimal nMatLth;
    
    @Schema(description = "实际重量")
    private BigDecimal nMatWgt;
    
    @Schema(description = "计算重量")
    private BigDecimal nMatWgtCal;
    
    @Schema(description = "检斤重量")
    private BigDecimal nMatActWgt;
    
    @Schema(description = "执行标准号")
    private String cStdSpec;
    
    @Schema(description = "综合判定时间 Q")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtJudgeTime;
    
    @Schema(description = "综判等级")
    private String cProdGrd;
    
    @Schema(description = "初判等级")
    private String cPrelGrd;
    
    @Schema(description = "成分等级")
    private String cChemGrd;
    
    @Schema(description = "尺寸等级")
    private String cSizeGrd;
    
    @Schema(description = "外观等级（表面等级）")
    private String cSurfGrd;
    
    @Schema(description = "性能等级")
    private String cMtalGrd;
    
    @Schema(description = "探伤结果")
    private String cWPro;
    
    @Schema(description = "探伤日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtWDate;
    
    @Schema(description = "不合格原因")
    private String cUpdNot;
    
    @Schema(description = "处理方式")
    private String cUpdRsn;
    
    @Schema(description = "是否改配合同，否：Null 是：1")
    private String cBdFl;
    
    @Schema(description = "质量证明书号")
    private String cCertId;
    
    @Schema(description = "购入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPchTime;
    
    @Schema(description = "生产时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtProdTime;
    
    @Schema(description = "生产班次")
    private String cProdShift;
    
    @Schema(description = "生产班别")
    private String cProdGroup;
    
    @Schema(description = "信息来源")
    private String cOccrCd;
    
    @Schema(description = "产地")
    private String cFactory;
    
    @Schema(description = "供货商")
    private String cSupplier;
    
    @Schema(description = "销售订单号")
    private String cSaleNo;
    
    @Schema(description = "销售订单行号")
    private String cSaleSn;
    
    @Schema(description = "生产任务单号")
    private String cTaskListId;
    
    @Schema(description = "采购订单号")
    private String cBuyOrderNo;
    
    @Schema(description = "采购订单行号")
    private String cOrderSn;
    
    @Schema(description = "订单材/余材标志")
    private String cOrdFl;
    
    @Schema(description = "生产订单号")
    private String cOrderNo;
    
    @Schema(description = "是否多订单")
    private String cMultiOrdTag;
    
    @Schema(description = "余材原因")
    private String cWooRsn;
    
    @Schema(description = "板坯热送标志")
    private String cHcrFl;
    
    @Schema(description = "是否试样板")
    private String cSmpFl;

    @Schema(description = "当前产线")
    private Long cLineId;

    @Schema(description = "当前产线cd")
    private String cLineCd;

    @Schema(description = "当前产线name")
    private String cLineName;
    
    @Schema(description = "产销仓库代码")
    private String cNcYardCd;
    
    @Schema(description = "库位号")
    private String cLoc;
    
    @Schema(description = "层号")
    private Integer nLocLvl;
    
    @Schema(description = "计划去向")
    private String cPreOut;
    
    @Schema(description = "备注")
    private String cMemo;
    
    @Schema(description = "最后修改程序")
    private String cPgmId;
    
    @Schema(description = "上传标志")
    private String cMonthFlag;
    
    @Schema(description = "上传人员")
    private String cMonthEmp;
    
    @Schema(description = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtMonthTime;
    
    @Schema(description = "复秤标记")
    private String cReweightFl;
    
    @Schema(description = "复秤重量")
    private Integer nReweight;
    
    @Schema(description = "复秤类型")
    private String cReWgtFlag;
    
    @Schema(description = "二次复秤标记")
    private String cTwoRewgtFl;
    
    @Schema(description = "二次复秤日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtTwoWgtTime;
    
    @Schema(description = "二次复秤人员")
    private String cTwoWgtEmp;
    
    @Schema(description = "二次复秤重量")
    private Integer nTwoWgt;
    
    @Schema(description = "改判人")
    private String cChaEmpid;
    
    @Schema(description = "改判时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtChaTime;
    
    @Schema(description = "改判备注")
    private String cChaMemo;

    @Schema(description = "原质量编码")
    private Long cPreMatQulId;

    @Schema(description = "原质量编码")
    private String cPreMatQulCd;

    @Schema(description = "原质量编码name")
    private String cPreMatQulName;

    @Schema(description = "原钢种")
    private String cPreStlGrdCd;

    @Schema(description = "原钢种")
    private String cPreStlGrdDesc;
    
    @Schema(description = "原长度")
    private BigDecimal nPreMatLth;
    
    @Schema(description = "原计算重量")
    private BigDecimal nPreMatWgtCal;
    
    @Schema(description = "原物料编码")
    private String cPreMatcode;
    
    @Schema(description = "原物料名称")
    private String cPreMatname;
    
    @Schema(description = "是否锁定")
    private String cIsLock;
    
    @Schema(description = "装车车号")
    private String cCarNo;
}
