package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 炼钢表尺判定管理
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
@Tag(name = "炼钢表尺判定管理对象", description = "炼钢表尺判定管理")
public class TPssSlabSizePageDto extends PageInput {

    @Schema(description = "")
    private Long nId;
    
    @Schema(description = "坯料号/件次号")
    private String cSlabId;
    
    @Schema(description = "缺陷代码")
    private String cDefectCode;
    
    @Schema(description = "钢种代码")
    private String cStlGrd;
    
    @Schema(description = "钢种名称")
    private String cStlGrdName;
    
    @Schema(description = "板坯厚度")
    private BigDecimal nSlabThk;
    
    @Schema(description = "板坯宽度")
    private BigDecimal nSlabWid;
    
    @Schema(description = "板坯长度")
    private BigDecimal nSlabLen;
    
    @Schema(description = "板坯测量厚度")
    private BigDecimal nSlabMeaThk;
    
    @Schema(description = "板坯测量宽度")
    private BigDecimal nSlabMeaWid;
    
    @Schema(description = "板坯测量长度")
    private BigDecimal nSlabMeaLen;
    
    @Schema(description = "尺寸等级")
    private String cSizeRlt;
    
    @Schema(description = "质量描述")
    private String cRemark;
    
    @Schema(description = "创建人")
    private Long nCreateUserId;

    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    
    @Schema(description = "最后修改时间")
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "逻辑删除标记")
    private Integer nDeleteMark;
    
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;


}