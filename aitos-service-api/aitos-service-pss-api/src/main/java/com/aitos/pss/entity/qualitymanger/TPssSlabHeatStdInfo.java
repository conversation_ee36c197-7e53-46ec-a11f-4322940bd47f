package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 加热工艺质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@TableName("t_pss_slab_heat_std_info")
@Tag(name = "加热工艺质量设计结果对象", description = "加热工艺质量设计结果")
public class TPssSlabHeatStdInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "订单号")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    
    @Schema(description = "质量编码")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "坯料厚度")
    @TableField(value = "n_slab_thk_min")
    private BigDecimal nSlabThkMin;
    
    @Schema(description = "坯料厚度")
    @TableField(value = "n_slab_thk_max")
    private BigDecimal nSlabThkMax;
    
    @Schema(description = "出炉目标温度℃")
    @TableField(value = "n_fout_aim_temp")
    private BigDecimal nFoutAimTemp;
    
    @Schema(description = "出炉温度上限℃")
    @TableField(value = "n_fout_max_temp")
    private BigDecimal nFoutMaxTemp;
    
    @Schema(description = "出炉温度下限℃")
    @TableField(value = "n_fout_min_temp")
    private BigDecimal nFoutMinTemp;
    
    @Schema(description = "板坯表面/中心目标温度差℃")
    @TableField(value = "n_slab_sc_temp_diff")
    private BigDecimal nSlabScTempDiff;
    
    @Schema(description = "板坯头尾目标温度差℃")
    @TableField(value = "n_slab_ht_temp_diff")
    private BigDecimal nSlabHtTempDiff;
    
    @Schema(description = "加热一段最低温度")
    @TableField(value = "c_reserver1")
    private String cReserver1;
    
    @Schema(description = "加热一段最高温度")
    @TableField(value = "c_reserver2")
    private String cReserver2;
    
    @Schema(description = "加热二段最低温度")
    @TableField(value = "c_reserver3")
    private String cReserver3;
    
    @Schema(description = "加热二段最高温度")
    @TableField(value = "c_reserver4")
    private String cReserver4;
    
    @Schema(description = "加热段3最低温度")
    @TableField(value = "c_reserver5")
    private String cReserver5;
    
    @Schema(description = "加热段3最高温度")
    @TableField(value = "c_reserver6")
    private String cReserver6;
    
    @Schema(description = "加热段4最低温度")
    @TableField(value = "c_reserver7")
    private String cReserver7;
    
    @Schema(description = "加热段4最高温度")
    @TableField(value = "c_reserver8")
    private String cReserver8;
    
    @Schema(description = "目标加热时长")
    @TableField(value = "c_reserver9")
    private String cReserver9;
    
    @Schema(description = "备用10")
    @TableField(value = "c_reserver10")
    private String cReserver10;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}