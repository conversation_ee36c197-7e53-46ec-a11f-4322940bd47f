package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssYMatStockRecordPageDto extends PageInput {

    /**
    * 单据类型(in:入库单,out:出库单)
    */
    @Schema(description = "单据类型(in:入库单,out:出库单)")
    private String cDocType;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    private String cBatchNo;
    /**
    * 操作数量
    */
    @Schema(description = "操作数量")
    private BigDecimal nQuantity;
    /**
    * 操作后库存
    */
    @Schema(description = "操作后库存")
    private BigDecimal nAfterStock;
    /**
    * 操作人
    */
    @Schema(description = "操作人")
    private String cOperator;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 物料ID
    */
    @Schema(description = "物料ID")
    private Long materialId;
    /**
    * 单据编号
    */
    @Schema(description = "单据编号")
    private String cDocNo;
    /**
    * 操作类型(入库/出库)
    */
    @Schema(description = "操作类型(入库/出库)")
    private String cOperationType;
    /**
    * 操作前库存
    */
    @Schema(description = "操作前库存")
    private BigDecimal nBeforeStock;
    /**
    * 操作时间字段开始时间
    */
    @Schema(description = "操作时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtOperationTimeStart;
    /**
    * 操作时间字段结束时间
    */
    @Schema(description = "操作时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtOperationTimeEnd;
    /**
    * 关联单号
    */
    @Schema(description = "关联单号")
    private String cRelatedNo;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
