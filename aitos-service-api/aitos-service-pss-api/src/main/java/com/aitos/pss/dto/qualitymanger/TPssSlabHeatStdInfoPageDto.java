package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssSlabHeatStdInfoPageDto extends PageInput {

    
    @Schema(description = "质量编码id")
    private Long cQualId;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
}
