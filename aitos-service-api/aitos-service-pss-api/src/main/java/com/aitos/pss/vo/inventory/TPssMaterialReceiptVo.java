package com.aitos.pss.vo.inventory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
public class TPssMaterialReceiptVo {

    /**
    * 主键ID
    */
    @Schema(description = "主键ID")
    private Long nId;
    /**
    * 到货单号
    */
    @Schema(description = "到货单号")
    private String cReceiptNo;
    /**
    * 入库单号
    */
    @Schema(description = "入库单号")
    private String cStorageNo;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    private String cBatchNo;
    /**
    * 计量单号
    */
    @Schema(description = "计量单号")
    private String cMeasureNo;
    /**
    * 采购订单号
    */
    @Schema(description = "采购订单号")
    private String cPurchaseOrderNo;
    /**
    * 采购订单行号
    */
    @Schema(description = "采购订单行号")
    private String cPurchaseOrderLine;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMaterialCode;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMaterialName;
    /**
    * 物料类型(raw:原辅料,alloy:合金,scrap:废钢)
    */
    @Schema(description = "物料类型(raw:原辅料,alloy:合金,scrap:废钢)")
    private String cMaterialType;
    /**
    * 到货数量
    */
    @Schema(description = "到货数量")
    private BigDecimal nQuantity;
    /**
    * 入库检斤重量
    */
    @Schema(description = "入库检斤重量")
    private BigDecimal nMeasureWeight;
    /**
    * 入库确认重量
    */
    @Schema(description = "入库确认重量")
    private BigDecimal nConfirmWeight;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private String cUnit;
    /**
    * 到货日期
    */
    @Schema(description = "到货日期")
    private LocalDateTime dtReceiptDate;
    /**
    * 供应商
    */
    @Schema(description = "供应商")
    private String cSupplier;
    /**
    * 目的库位
    */
    @Schema(description = "目的库位")
    private String cStorageLocation;
    /**
    * 目的库位名
    */
    @Schema(description = "目的库位名")
    private String cStorageLocationName;
    /**
    * 状态(pending:待检验,testing:检验中,completed:已完成,rejected:不合格)
    */
    @Schema(description = "状态(pending:待检验,testing:检验中,completed:已完成,rejected:不合格)")
    private String cStatus;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    private LocalDateTime dtModifyDateTime;



}
