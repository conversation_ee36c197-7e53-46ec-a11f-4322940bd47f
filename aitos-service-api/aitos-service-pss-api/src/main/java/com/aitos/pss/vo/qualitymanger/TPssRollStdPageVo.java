package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-21
* @Version 1.0
*/
@Data
public class TPssRollStdPageVo {

    
    @ExcelIgnore
    @Schema(description = "主键")
    private String nId;

    @Schema(description = "质量编码id")
    private Long cQualityId;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量编码")
    @Schema(description = "质量编码")
    private String cQualityCode;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量编码名称")
    @Schema(description = "质量编码名称")
    private String cQualityCodeName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种代码")
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种名称/描述")
    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产品厚度")
    @Schema(description = "产品厚度")
    private BigDecimal nProdThkMin;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产品厚度")
    @Schema(description = "产品厚度")
    private BigDecimal nProdThkMax;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("控制轧制")
    @Schema(description = "控制轧制")
    private Long nControlRoll;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("开轧温度℃")
    @Schema(description = "开轧温度℃")
    private BigDecimal nStartTemp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("粗轧轧制温度℃")
    @Schema(description = "粗轧轧制温度℃")
    private BigDecimal nFirstRollTemp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("精轧轧制温度℃")
    @Schema(description = "精轧轧制温度℃")
    private BigDecimal nSecRollTemp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("终轧温度℃")
    @Schema(description = "终轧温度℃")
    private BigDecimal nFinalTemp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    /**
    * 创建时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("创建时间")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;

    /**
    * 修改时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("修改时间")
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
