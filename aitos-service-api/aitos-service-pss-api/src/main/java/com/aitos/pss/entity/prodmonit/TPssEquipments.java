package com.aitos.pss.entity.prodmonit;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 设备管理
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
@TableName("t_pss_equipments")
@Tag(name = "设备管理对象", description = "设备管理")
public class TPssEquipments implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    @Schema(description = "主键id")
    @TableId
    private Long nId;
    /**
    * 设备名称
    */
    @Schema(description = "设备名称")
    @TableField(value = "c_name")
    private String cName;
    /**
    * 设备代码
    */
    @Schema(description = "设备代码")
    @TableField(value = "c_code")
    private String cCode;
    /**
    * 设备类型
    */
    @Schema(description = "设备类型")
    @TableField(value = "c_type")
    private String cType;
    /**
    * 位置
    */
    @Schema(description = "位置")
    @TableField(value = "c_location")
    private String cLocation;
    /**
    * 状态
    */
    @Schema(description = "状态")
    @TableField(value = "c_status")
    private String cStatus;
    /**
    * 描述
    */
    @Schema(description = "描述")
    @TableField(value = "c_description")
    private String cDescription;
    /**
    * 制造商
    */
    @Schema(description = "制造商")
    @TableField(value = "c_manufacturer")
    private String cManufacturer;
    /**
    * 型号
    */
    @Schema(description = "型号")
    @TableField(value = "c_model")
    private String cModel;
    /**
    * 序列号
    */
    @Schema(description = "序列号")
    @TableField(value = "c_serial_number")
    private String cSerialNumber;
    /**
    * 购买日期
    */
    @Schema(description = "购买日期")
    @TableField(value = "dt_purchase_date")
    private LocalDateTime dtPurchaseDate;
    /**
    * 保修期（月）
    */
    @Schema(description = "保修期（月）")
    @TableField(value = "n_warranty_period")
    private Integer nWarrantyPeriod;
    /**
    * 最后维护时间
    */
    @Schema(description = "最后维护时间")
    @TableField(value = "dt_last_maintain_date_time")
    private LocalDateTime dtLastMaintainDateTime;
    /**
    * 下次维护时间
    */
    @Schema(description = "下次维护时间")
    @TableField(value = "dt_next_maintain_date_time")
    private LocalDateTime dtNextMaintainDateTime;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time",fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}
