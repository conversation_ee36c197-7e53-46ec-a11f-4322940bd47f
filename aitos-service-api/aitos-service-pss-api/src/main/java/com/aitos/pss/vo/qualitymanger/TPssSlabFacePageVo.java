package com.aitos.pss.vo.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
public class TPssSlabFacePageVo {

    
    @Schema(description = "")
    private Long nId;
    
    @Schema(description = "坯料号/件次号")
    private String cSlabId;
    
    @Schema(description = "缺陷代码")
    private String cDefectCode;
    
    @Schema(description = "钢种")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称")
    private String cStlGrdDesc;
    
    @Schema(description = "表面等级")
    private String cFaceRlt;
    
    @Schema(description = "原表面等级")
    private String cOldFaceRlt;
    
    @Schema(description = "质量描述")
    private String cRemark;

    @Schema(description = "录入时间")
    private LocalDateTime dtCreateDateTime;
}
