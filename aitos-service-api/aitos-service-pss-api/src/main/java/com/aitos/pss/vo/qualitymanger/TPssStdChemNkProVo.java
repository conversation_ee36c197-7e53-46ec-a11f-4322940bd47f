package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/
@Data
public class TPssStdChemNkProVo {

    
    @Schema(description = "主键")
    private Long nId;

    @Schema(description = "工序id")
    private Long cMacId;

    @Schema(description = "工序编码")
    private String cMacCode;
    
    @Schema(description = "工序名称")
    private String cMacName;
    
    @Schema(description = "执行标准号")
    private String cOpStdName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;

    @Schema(description = "质量编码id")
    private Long cQualityId;

    @Schema(description = "质量编码名称")
    private String cQualityCodeName;
    
    @Schema(description = "下限比较符")
    private String cCompCodeMin;
    
    @Schema(description = "上限比较符")
    private String cCompCodeMax;
    
    @Schema(description = "化学成分代码")
    private String cChemCompCd;
    
    @Schema(description = "化学成分")
    private String cChemComp;
    
    @Schema(description = "化学成分最小值")
    private BigDecimal cChemCompMin;
    
    @Schema(description = "化学成分最大值")
    private BigDecimal cChemCompMax;
    
    @Schema(description = "质量编码;质量编码")
    private String cQualityCode;
    
    @Schema(description = "质量编码简称")
    private String cQualityCodeAbbreviation;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
