package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssWareHousePageDto extends PageInput {

    /**
    * 所属公司
    */
    @Schema(description = "所属公司")
    private String cCompany;
    /**
    * 仓库名称
    */
    @Schema(description = "仓库名称")
    private String cName;
    /**
    * ERP库号
    */
    @Schema(description = "ERP库号")
    private String cErp;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 低储
    */
    @Schema(description = "低储")
    private BigDecimal nLow;
    /**
    * 电话
    */
    @Schema(description = "电话")
    private String cTel;
    /**
    * 所属产线
    */
    @Schema(description = "所属产线")
    private String cLine;
    /**
    * 仓库代码
    */
    @Schema(description = "仓库代码")
    private String cCode;
    /**
    * 仓库类型
    */
    @Schema(description = "仓库类型")
    private String cType;
    /**
    * 仓库地址
    */
    @Schema(description = "仓库地址")
    private String cAddress;
    /**
    * 高储
    */
    @Schema(description = "高储")
    private BigDecimal nHigh;
    /**
    * 安全库存
    */
    @Schema(description = "安全库存")
    private BigDecimal nSafe;
    /**
    * 负责人
    */
    @Schema(description = "负责人")
    private String cManager;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
