package com.aitos.pss.vo.planmanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-15
* @Version 1.0
*/
@Data
public class TPssCommonCodePageVo {

    
    @Schema(description = "主键")
    private String nId;
    
    @Schema(description = "代码管理号")
    private String cManaNo;
    
    @Schema(description = "代码")
    private String cCode;
    
    @Schema(description = "代码简称")
    private String cShortName;
    
    @Schema(description = "代码名称")
    private String cName;
    
    @Schema(description = "代码英文简称")
    private String cShortEng;
    
    @Schema(description = "代码英文名称")
    private String cFullEng;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;

    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;

    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;
}
