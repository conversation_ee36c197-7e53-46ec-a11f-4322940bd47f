package com.aitos.pss.dto.prodmonit;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 违规单管理
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
public class AddTPssViolationsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 指标ID
    */
    @Schema(description = "指标ID")
    private Long nIndicatorId;
    /**
    * 指标code
    */
    @Schema(description = "指标code")
    private String cIndicatorCode;
    /**
    * 指标name
    */
    @Schema(description = "指标name")
    private String cIndicatorName;
    /**
    * 设备id
    */
    @Schema(description = "设备id")
    private Long nEquipmentId;
    /**
    * 设备code
    */
    @Schema(description = "设备code")
    private String cEquipmentCode;
    /**
    * 设备name
    */
    @Schema(description = "设备name")
    private String cEquipmentName;
    /**
    * 违规单号
    */
    @Schema(description = "违规单号")
    private String cCode;
    /**
    * 违规描述
    */
    @Schema(description = "违规描述")
    private String cViolationDescription;
    /**
    * 违规时间
    */
    @Schema(description = "违规时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtViolationDateTime;
    /**
    * 关闭时间
    */
    @Schema(description = "关闭时间")
    private LocalDateTime dtCloseDateTime;
    /**
    * 违规值
    */
    @Schema(description = "违规值")
    private BigDecimal nErrorValue;
    /**
    * 标准值
    */
    @Schema(description = "标准值")
    private BigDecimal nStandValue;
    /**
    * 状态（待审核/已通过/已驳回）
    */
    @Schema(description = "状态（待审核/已通过/已驳回）")
    private String cStatus;
    /**
    * 责任人ID
    */
    @Schema(description = "责任人ID")
    private Long nResponsibleUserId;
    /**
    * 审批人ID
    */
    @Schema(description = "审批人ID")
    private Long nApproverId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    private LocalDateTime dtModifyDateTime;

}
