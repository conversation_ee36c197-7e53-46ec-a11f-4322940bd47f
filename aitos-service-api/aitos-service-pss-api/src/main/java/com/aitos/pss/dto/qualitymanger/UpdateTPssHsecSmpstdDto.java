package com.aitos.pss.dto.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;



/**
* @title: 组批规则维护
* <AUTHOR>
* @Date: 2025-06-17
* @Version 1.0
*/
@Data
public class UpdateTPssHsecSmpstdDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "")
    private Long nId;
    
    @Schema(description = "质量编码")
    private String cQualityCode;
    
    @Schema(description = "质量编码名称")
    private String cQualityCodeName;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称")
    private String cStlGrdDesc;
    
    @Schema(description = "取样类型")
    private String cSmpType;
    
    @Schema(description = "产线")
    private String cLineCd;
    
    @Schema(description = "产品长度")
    private BigDecimal nThk;
    
    @Schema(description = "产品规格")
    private String cSpec;
    
    @Schema(description = "取样次数")
    private Integer nSmpNum;

    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

}
