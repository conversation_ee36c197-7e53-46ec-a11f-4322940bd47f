package com.aitos.pss.vo.costmanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @title: 分页列表出参 <AUTHOR> @Date: 2025-06-30 @Version 1.0
 */
@Data
public class TPssFcsCdPageVo {

  /** 顺序号 */
  @Schema(description = "顺序号")
  private String nId;

  /** 项目大类编码 */
  @Schema(description = "项目大类编码")
  private String cMatTypeBCode;

  /** 项目大类名称 */
  @Schema(description = "项目大类名称")
  private String cMatTypeBName;

  /** 项目小类编码 */
  @Schema(description = "项目小类编码")
  private String cCostItemCode;

  /** 项目小类名称 */
  @Schema(description = "项目小类名称")
  private String cCostItemName;

  /** 计量单位 */
  @Schema(description = "计量单位")
  private String cMatUnitId;

  /** 计量单位编码 */
  @Schema(description = "计量单位编码")
  private String cMatUnitCode;

  /** 计量单位名称 */
  @Schema(description = "计量单位名称")
  private String cMatUnitName;

  @Schema(description = "产线id")
  private Long cProLine;

  @Schema(description = "产线")
  private String cProLineCode;

  @Schema(description = "产线")
  private String cProLineName;

  /** 显示顺序 */
  @Schema(description = "显示顺序")
  private Integer cCostSeq;

  /** 备注 */
  @Schema(description = "备注")
  private String cMemo;

  /** 是否启用;默认为0,1为未启用 */
  @Schema(description = "是否启用;默认为0,1为未启用")
  private Integer nEnabledMark;

  /** */
  @Schema(description = "")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime dtCreateDateTime;

  /** */
  @Schema(description = "")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime dtModifyDateTime;

  @Schema(description = "创建人")
  @Trans(type = TransType.USER, transForPropertyName = "nCreateUserName")
  private Long nCreateUserId;

  @Schema(description = "修改人")
  @Trans(type = TransType.USER, transForPropertyName = "nModifyUserName")
  private Long nModifyUserId;

  @Schema(description = "创建人")
  private String nCreateUserName;

  @Schema(description = "修改人")
  private String nModifyUserName;
}
