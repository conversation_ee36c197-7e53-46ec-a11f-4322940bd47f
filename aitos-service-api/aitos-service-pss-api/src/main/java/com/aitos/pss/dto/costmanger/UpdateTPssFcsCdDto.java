package com.aitos.pss.dto.costmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;



/**
* @title: 成本科目配置上表
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
public class UpdateTPssFcsCdDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "顺序号")
    private Long nId;

    @Schema(description = "项目大类编码")
    private String cMatTypeBCode;

    @Schema(description = "项目大类名称")
    private String cMatTypeBName;

    @Schema(description = "项目小类编码")
    private String cCostItemCode;

    @Schema(description = "项目小类名称")
    private String cCostItemName;

    @Schema(description = "计量单位")
    private String cMatUnitId;

    @Schema(description = "计量单位编码")
    private String cMatUnitCode;

    @Schema(description = "计量单位名称")
    private String cMatUnitName;

    @Schema(description = "产线id")
    private Long cProLine;

    @Schema(description = "产线")
    private String cProLineCode;

    @Schema(description = "产线")
    private String cProLineName;

    @Schema(description = "显示顺序")
    private Integer cCostSeq;

    @Schema(description = "备注")
    private String cMemo;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;
}
