package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 质量编码库表
* <AUTHOR>
* @Date: 2025-06-19
* @Version 1.0
*/
@Data
@TableName("t_pss_quality_code_lib")
@Tag(name = "质量编码库表对象", description = "质量编码库表")
public class TPssQualityCodeLib implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "质检标准编码")
    @TableField(value = "c_qual_std_lib_code")
    private String cQualStdLibCode;
    
    @Schema(description = "质检项目编码")
    @TableField(value = "c_qual_std_lib_item_code")
    private String cQualStdLibItemCode;
    
    @Schema(description = "质检项目名称")
    @TableField(value = "c_qual_std_lib_item_name")
    private String cQualStdLibItemName;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_name")
    private String cStlGrdName;
    
    @Schema(description = "计量单位")
    @TableField(value = "c_measures_unit")
    private String cMeasuresUnit;
    
    @Schema(description = "值类型")
    @TableField(value = "c_value_type")
    private String cValueType;
    
    @Schema(description = "试验次数")
    @TableField(value = "n_test_count")
    private Integer nTestCount;
    
    @Schema(description = "项目属性")
    @TableField(value = "c_project_attribute_code")
    private String cProjectAttributeCode;
    
    @Schema(description = "数据精度")
    @TableField(value = "n_precision")
    private String nPrecision;
    
    @Schema(description = "下限比较符")
    @TableField(value = "c_lower_symbol")
    private String cLowerSymbol;
    
    @Schema(description = "下限值")
    @TableField(value = "n_lower_value")
    private BigDecimal nLowerValue;
    
    @Schema(description = "上限比较符")
    @TableField(value = "c_upper_symbol")
    private String cUpperSymbol;
    
    @Schema(description = "上限值")
    @TableField(value = "n_upper_value")
    private BigDecimal nUpperValue;
    
    @Schema(description = "备注")
    @TableField(value = "n_remark")
    private String nRemark;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    @TableField(value = "n_enabled_mark")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
}