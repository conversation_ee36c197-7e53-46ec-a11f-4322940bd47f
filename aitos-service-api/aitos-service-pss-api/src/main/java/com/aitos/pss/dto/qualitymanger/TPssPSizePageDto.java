package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.time.LocalDateTime;


/**
* @title: 轧钢表尺判定管理
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@Data
@Tag(name = "轧钢表尺判定管理对象", description = "轧钢表尺判定管理")
public class TPssPSizePageDto extends PageInput {

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "线卷号/坯料号/件次号")
    private String cPlateId;
    
    @Schema(description = "检查次数")
    private Integer nChkSeq;
    
    @Schema(description = "检查工序")
    private Long cChkPos;
    
    @Schema(description = "线卷面")
    private String cPlateFace;
    
    @Schema(description = "缺陷序号")
    private Long nDefectSeq;
    
    @Schema(description = "缺陷代码")
    private String cDefectType;
    
    @Schema(description = "缺陷位置")
    private String cDefectPos;
    
    @Schema(description = "缺陷描述")
    private String cDefectDesc;
    
    @Schema(description = "处理建议")
    private String cDispPro;
    
    @Schema(description = "处理方式")
    private String cDispManner;
    
    @Schema(description = "处理结果")
    private String cDispResult;
    
    @Schema(description = "班次")
    private String cShift;
    
    @Schema(description = "班别")
    private String cCrew;
    
    @Schema(description = "处理人")
    private String cGrindOperater;
    
    @Schema(description = "处理班次")
    private String cGrindShift;
    
    @Schema(description = "处理班别")
    private String cGrindCrew;
    
    @Schema(description = "尺寸等级")
    private String cSizeRlt;
    
    @Schema(description = "备注")
    private String cRemarks;
    
    @Schema(description = "创建人")
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    
    @Schema(description = "最后修改时间")
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "逻辑删除标记")
    private Integer nDeleteMark;
    
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;
}