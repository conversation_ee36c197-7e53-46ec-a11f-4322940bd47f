package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;



/**
* @title: 质量编码管理
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@Data
public class UpdateTPssQualityCodeDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "质量代码;质量编码")
    private String cQualCode;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "钢种描述")
    private String cStlGrdDesc;
    
    @Schema(description = "名称")
    private String cQualCodeName;
    
    @Schema(description = "简称")
    private String cQualCodeAbb;
    
    @Schema(description = "描述;")
    private String cDescribe;
    
    @Schema(description = "标准名称（中）")
    private String cStdNameChi;
    
    @Schema(description = "标准名称（英）")
    private String cStdNameEng;
    
    @Schema(description = "执行标准")
    private String cOpStdName;
    
    @Schema(description = "成品物料名称")
    private String cMaterialName;
    
    @Schema(description = "成品物料编码")
    private String cMaterialCode;

    @Schema(description = "成品物料Id")
    private Long cMateId;
    
    @Schema(description = "状态（新建/已转换）")
    private Integer cStatus;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;
}
