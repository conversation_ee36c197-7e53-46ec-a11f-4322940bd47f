package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 轧钢组批取样
* <AUTHOR>
* @Date: 2025-06-16
* @Version 1.0
*/
@Data
@TableName("t_pss_batch_sampling")
@Tag(name = "轧钢组批取样对象", description = "轧钢组批取样")
public class TPssBatchSampling implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "")
    @TableId
    private Long nId;
    
    @Schema(description = "炉次号")
    @TableField(value = "c_heat_id")
    private String cHeatId;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "规格")
    @TableField(value = "c_spec")
    private String cSpec;
    
    @Schema(description = "检验批号")
    @TableField(value = "c_smp_lot")
    private String cSmpLot;
    
    @Schema(description = "质量编码")
    @TableField(value = "c_quality_code")
    private String cQualityCode;
    
    @Schema(description = "产线")
    @TableField(value = "c_line_cd")
    private String cLineCd;
    
    @Schema(description = "物料长度")
    @TableField(value = "n_thk")
    private BigDecimal nThk;
    
    @Schema(description = "委托次数")
    @TableField(value = "n_samp_num")
    private Integer nSampNum;
    
    @Schema(description = "委托单号")
    @TableField(value = "c_testcrd_id")
    private String cTestcrdId;
    
    @Schema(description = "检验炉次号")
    @TableField(value = "c_smp_heat_id")
    private String cSmpHeatId;
    
    @Schema(description = "委托单状态（A：等待发送、B：等待试验、C：试验完毕、D：性能判定完毕）")
    @TableField(value = "c_smp_states")
    private String cSmpStates;
    
    @Schema(description = "检验批次包含炉次号")
    @TableField(value = "c_samp_heat_id_list")
    private String cSampHeatIdList;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;

}