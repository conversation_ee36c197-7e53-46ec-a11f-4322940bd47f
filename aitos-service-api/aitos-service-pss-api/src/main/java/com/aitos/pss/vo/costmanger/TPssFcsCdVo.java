package com.aitos.pss.vo.costmanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
public class TPssFcsCdVo {

    /**
    * 顺序号
    */
    @Schema(description = "顺序号")
    private Long nId;
    /**
    * 项目大类编码
    */
    @Schema(description = "项目大类编码")
    private String cMatTypeBCode;
    /**
    * 项目大类名称
    */
    @Schema(description = "项目大类名称")
    private String cMatTypeBName;
    /**
    * 项目小类编码
    */
    @Schema(description = "项目小类编码")
    private String cCostItemCode;
    /**
    * 项目小类名称
    */
    @Schema(description = "项目小类名称")
    private String cCostItemName;
    /**
     * 计量单位
     */
    @Schema(description = "计量单位")
    private String cMatUnitId;
    /**
     * 计量单位编码
     */
    @Schema(description = "计量单位编码")
    private String cMatUnitCode;
    /**
     * 计量单位名称
     */
    @Schema(description = "计量单位名称")
    private String cMatUnitName;

    @Schema(description = "产线id")
    private Long cProLine;

    @Schema(description = "产线")
    private String cProLineCode;

    @Schema(description = "产线")
    private String cProLineName;
    /**
    * 显示顺序
    */
    @Schema(description = "显示顺序")
    private Integer cCostSeq;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cMemo;
    /**
    * 
    */
    @Schema(description = "")
    private LocalDateTime dtCreateDateTime;
    /**
    * 
    */
    @Schema(description = "")
    private LocalDateTime dtModifyDateTime;



}
