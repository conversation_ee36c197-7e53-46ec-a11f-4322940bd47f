package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
public class TPssCheSteelPageVo {

    
    @ExcelIgnore
    @Schema(description = "顺序号")
    private String nId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("式样类型")
    @Schema(description = "式样类型")
    private String cSampletype;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("炉号")
    @Schema(description = "炉号")
    private String cSampleid;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("写入时间")
    @Schema(description = "写入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSampTime;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称")
    private String cStlGrdDesc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("说明")
    @Schema(description = "说明")
    private String cCommentfld;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("取样序号/次数")
    @Schema(description = "取样序号/次数")
    private Integer nSmpNums;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("试样号")
    @Schema(description = "试样号")
    private String cSmpId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("c")
    @Schema(description = "c")
    private BigDecimal nC;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("si")
    @Schema(description = "si")
    private BigDecimal nSi;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("mn")
    @Schema(description = "mn")
    private BigDecimal nMn;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("p")
    @Schema(description = "p")
    private BigDecimal nP;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("s")
    @Schema(description = "s")
    private BigDecimal nS;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("cr")
    @Schema(description = "cr")
    private BigDecimal nCr;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ni")
    @Schema(description = "ni")
    private BigDecimal nNi;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("cu")
    @Schema(description = "cu")
    private BigDecimal nCu;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("v")
    @Schema(description = "v")
    private BigDecimal nV;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("mo")
    @Schema(description = "mo")
    private BigDecimal nMo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ti")
    @Schema(description = "ti")
    private BigDecimal nTi;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("nb")
    @Schema(description = "nb")
    private BigDecimal nNb;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ceq")
    @Schema(description = "ceq")
    private BigDecimal nCeq;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("als")
    @Schema(description = "als")
    private BigDecimal nAls;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("al")
    @Schema(description = "al")
    private BigDecimal nAl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ca")
    @Schema(description = "ca")
    private BigDecimal nCa;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("b")
    @Schema(description = "b")
    private BigDecimal nB;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("pb")
    @Schema(description = "pb")
    private BigDecimal nPb;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("zr")
    @Schema(description = "zr")
    private BigDecimal nZr;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("w")
    @Schema(description = "w")
    private BigDecimal nW;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("asfld")
    @Schema(description = "asfld")
    private BigDecimal nAsfld;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("sn")
    @Schema(description = "sn")
    private BigDecimal nSn;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("co")
    @Schema(description = "co")
    private BigDecimal nCo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("bi")
    @Schema(description = "bi")
    private BigDecimal nBi;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("zn")
    @Schema(description = "zn")
    private BigDecimal nZn;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("sb")
    @Schema(description = "sb")
    private BigDecimal nSb;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("la")
    @Schema(description = "la")
    private BigDecimal nLa;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ce")
    @Schema(description = "ce")
    private BigDecimal nCe;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("n")
    @Schema(description = "n")
    private BigDecimal nN;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("bs")
    @Schema(description = "bs")
    private BigDecimal nBs;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("aln")
    @Schema(description = "aln")
    private BigDecimal nAln;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("mg")
    @Schema(description = "mg")
    private BigDecimal nMg;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ct")
    @Schema(description = "ct")
    private BigDecimal nCt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("c_g")
    @Schema(description = "c_g")
    private Integer nCG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("si_g")
    @Schema(description = "si_g")
    private Integer nSiG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("mn_g")
    @Schema(description = "mn_g")
    private Integer nMnG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("p_g")
    @Schema(description = "p_g")
    private Integer nPG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("s_g")
    @Schema(description = "s_g")
    private Integer nSG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ceq_g")
    @Schema(description = "ceq_g")
    private Integer nCeqG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("cr_g")
    @Schema(description = "cr_g")
    private Integer nCrG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ni_g")
    @Schema(description = "ni_g")
    private Integer nNiG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("cu_g")
    @Schema(description = "cu_g")
    private Integer nCuG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("v_g")
    @Schema(description = "v_g")
    private Integer nVG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("mo_g")
    @Schema(description = "mo_g")
    private Integer nMoG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ti_g")
    @Schema(description = "ti_g")
    private Integer nTiG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("nb_g")
    @Schema(description = "nb_g")
    private Integer nNbG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("als_g")
    @Schema(description = "als_g")
    private Integer nAlsG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("al_g")
    @Schema(description = "al_g")
    private Integer nAlG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ca_g")
    @Schema(description = "ca_g")
    private Integer nCaG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("b_g")
    @Schema(description = "b_g")
    private Integer nBG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("pb_g")
    @Schema(description = "pb_g")
    private Integer nPbG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("zr_g")
    @Schema(description = "zr_g")
    private Integer nZrG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("w_g")
    @Schema(description = "w_g")
    private Integer nWG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("asfld_g")
    @Schema(description = "asfld_g")
    private Integer nAsfldG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("sn_g")
    @Schema(description = "sn_g")
    private Integer nSnG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("co_g")
    @Schema(description = "co_g")
    private Integer nCoG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("bi_g")
    @Schema(description = "bi_g")
    private Integer nBiG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("sb_g")
    @Schema(description = "sb_g")
    private Integer nSbG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("zn_g")
    @Schema(description = "zn_g")
    private Integer nZnG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("la_g")
    @Schema(description = "la_g")
    private Integer nLaG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ce_g")
    @Schema(description = "ce_g")
    private Integer nCeG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("n_g")
    @Schema(description = "n_g")
    private Integer nNG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("bs_g")
    @Schema(description = "bs_g")
    private Integer nBsG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("aln_g")
    @Schema(description = "aln_g")
    private Integer nAlnG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("mg_g")
    @Schema(description = "mg_g")
    private Integer nMgG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("ct_g")
    @Schema(description = "ct_g")
    private Integer nCtG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量等级")
    @Schema(description = "质量等级")
    private String cJudge;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("o")
    @Schema(description = "o")
    private BigDecimal nO;

    @Schema(description = "o")
    private BigDecimal nOG;

    @Schema(description = "h")
    private BigDecimal nHG;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("h")
    @Schema(description = "h")
    private BigDecimal nH;

    @Schema(description = "质量编码id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    private String cMatQulCode;

    @Schema(description = "质量编码name")
    private String cMatQulName;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

}
