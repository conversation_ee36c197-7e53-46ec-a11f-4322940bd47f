package com.aitos.pss.dto.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 熔量重量标准-炉熔量标准
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
public class AddTPssStlCpctDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "钢种")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;

    @Schema(description = "电炉座次id")
    private String cBofId;

    @Schema(description = "电炉座次编码")
    private String cBofNo;

    @Schema(description = "电炉座次名称")
    private String cBofName;
    
    @Schema(description = "钢水下限")
    private Integer nHeatMin;
    
    @Schema(description = "钢水上限")
    private Integer nHeatMax1;
    
    @Schema(description = "收得率")
    private BigDecimal nHeatRacvRadio;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
