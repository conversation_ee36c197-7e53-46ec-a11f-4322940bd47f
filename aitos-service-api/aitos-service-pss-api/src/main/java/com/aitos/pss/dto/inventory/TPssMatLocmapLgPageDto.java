package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-16
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssMatLocmapLgPageDto extends PageInput {

    /**
    * 层号/入库顺序号
    */
    @Schema(description = "层号/入库顺序号")
    private String cLocLvl;
    /**
    * 产线code
    */
    @Schema(description = "产线code")
    private String cLineCode;
    /**
    * 仓库id
    */
    @Schema(description = "仓库id")
    private Long cYardId;
    /**
    * 仓库name
    */
    @Schema(description = "仓库name")
    private String cYardName;
    /**
    * 数量
    */
    @Schema(description = "数量")
    private Integer nMatCnt;
    /**
    * 轧制批号
    */
    @Schema(description = "轧制批号")
    private String cMatLotId;
    /**
    * 物料id
    */
    @Schema(description = "物料id")
    private Long cMateId;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMateName;
    /**
    * 钢种描述
    */
    @Schema(description = "钢种描述")
    private String cStlGrdDesc;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    private String cMatQulCode;
    /**
    * 规格
    */
    @Schema(description = "规格")
    private String cMatItem;
    /**
    * 宽度
    */
    @Schema(description = "宽度")
    private BigDecimal nMatWth;
    /**
    * 长度
    */
    @Schema(description = "长度")
    private BigDecimal nMatLen;
    /**
    * 计算重量
    */
    @Schema(description = "计算重量")
    private BigDecimal nMatWgtCal;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    private String cTaskListId;
    /**
    * 销售订单号
    */
    @Schema(description = "销售订单号")
    private String cSaleNo;
    /**
    * 下游订单号
    */
    @Schema(description = "下游订单号")
    private String cOrderIdFinal;
    /**
    * 去向（产线号）
    */
    @Schema(description = "去向（产线号）")
    private String cPreOut;
    /**
    * 终判等级
    */
    @Schema(description = "终判等级")
    private String cProdGrd;
    /**
    * 装车车号
    */
    @Schema(description = "装车车号")
    private String cCarNo;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;
    /**
    * 库位号
    */
    @Schema(description = "库位号")
    private String cLoc;
    /**
    * 产线id
    */
    @Schema(description = "产线id")
    private Long cLineId;
    /**
    * 产线name
    */
    @Schema(description = "产线name")
    private String cLineName;
    /**
    * 仓库代码
    */
    @Schema(description = "仓库代码")
    private String cYardCode;
    /**
    * 件次号
    */
    @Schema(description = "件次号")
    private String cMatId;
    /**
    * 炉号
    */
    @Schema(description = "炉号")
    private String cLotId;
    /**
    * 物料类型
    */
    @Schema(description = "物料类型")
    private String cMatType;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMateCode;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    /**
    * 质量编码id
    */
    @Schema(description = "质量编码id")
    private Long cMatQulId;
    /**
    * 质量编码name
    */
    @Schema(description = "质量编码name")
    private String cMatQulName;
    /**
    * 厚度
    */
    @Schema(description = "厚度")
    private BigDecimal nMatThk;
    /**
    * 直径
    */
    @Schema(description = "直径")
    private BigDecimal nMatDia;
    /**
    * 实际重量
    */
    @Schema(description = "实际重量")
    private BigDecimal nMatWgt;
    /**
    * 订单材/余材
    */
    @Schema(description = "订单材/余材")
    private String cOrdFl;
    /**
    * 订单号
    */
    @Schema(description = "订单号")
    private String cOrderNo;
    /**
    * 销售订单行号
    */
    @Schema(description = "销售订单行号")
    private String cSaleSn;
    /**
    * 生产时间字段开始时间
    */
    @Schema(description = "生产时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtProdTimeStart;
    /**
    * 生产时间字段结束时间
    */
    @Schema(description = "生产时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtProdTimeEnd;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 盘库标识
    */
    @Schema(description = "盘库标识")
    private String cCheckerFl;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;

}
