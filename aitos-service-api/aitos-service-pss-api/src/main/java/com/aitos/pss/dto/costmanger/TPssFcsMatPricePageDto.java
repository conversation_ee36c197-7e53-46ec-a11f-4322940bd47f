package com.aitos.pss.dto.costmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssFcsMatPricePageDto extends PageInput {

    /**
    * 成本项目名称
    */
    @Schema(description = "成本项目名称")
    private String cCostItemName;
    /**
    * 物料代码
    */
    @Schema(description = "物料代码")
    private String cMatCode;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private Long cUnitId;
    /**
    * 单位名称
    */
    @Schema(description = "单位名称")
    private String cUnitName;
    /**
    * 实际价格
    */
    @Schema(description = "实际价格")
    private BigDecimal cActPrice;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private Integer cState;
    /**
    * 工序
    */
    @Schema(description = "工序")
    private Long cProcId;
    /**
    * 产线
    */
    @Schema(description = "产线")
    private Long cProLine;
    /**
    * 成本项目编码
    */
    @Schema(description = "成本项目编码")
    private String cCostItemCode;
    /**
    * 物料id
    */
    @Schema(description = "物料id")
    private Long cMatId;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatName;
    /**
    * 单位编码
    */
    @Schema(description = "单位编码")
    private String cUnitCode;
    /**
    * 计划价格
    */
    @Schema(description = "计划价格")
    private BigDecimal cPlanPrice;
    /**
    * 回收率
    */
    @Schema(description = "回收率")
    private BigDecimal cRecovery;
    /**
    * 物料类型名称
    */
    @Schema(description = "物料类型名称")
    private String cMatTypeName;
    /**
    * 物料类型代码
    */
    @Schema(description = "物料类型代码")
    private String cMatTypeCd;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cMemo;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
