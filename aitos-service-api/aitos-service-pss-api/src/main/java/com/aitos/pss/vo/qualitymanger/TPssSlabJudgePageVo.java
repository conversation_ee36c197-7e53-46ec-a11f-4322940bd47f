package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@Data
public class TPssSlabJudgePageVo {

    
    @ExcelIgnore
    @Schema(description = "")
    private String nId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("坯料号")
    @Schema(description = "坯料号")
    private String cSlabId;

    @Schema(description = "原质量编码id")
    private Long cOldMatId;

    @Schema(description = "原质量编码code")
    private String cOldMatCode;

    @Schema(description = "原质量编码name")
    private String cOldMatName;

    @Schema(description = "原钢种代码")
    private String cOldStlGrdCd;

    @Schema(description = "原钢种代码描述")
    private String cOldStlGrdDesc;

    @Schema(description = "现质量编码id")
    private Long cNewMatId;

    @Schema(description = "现质量编码code")
    private String cNewMatCode;

    @Schema(description = "现质量编码name")
    private String cNewMatName;

    @Schema(description = "现钢种代码")
    private String cNewStlGrdCd;

    @Schema(description = "现钢种代码描述")
    private String cNewStlGrdDesc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("成分等级")
    @Schema(description = "成分等级")
    @Trans(type = TransType.DIC, id = "1925394941974482945")
    private String cChemRlt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("表面等级")
    @Schema(description = "外形等级（表面等级）")
    @Trans(type = TransType.DIC, id = "1925393176789061634")
    private String cBodyRlt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("尺寸等级")
    @Schema(description = "尺寸等级")
    @Trans(type = TransType.DIC, id = "1925393512790560769")
    private String cSizeRlt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综判等级")
    @Schema(description = "综判等级")
    @Trans(type = TransType.DIC, id = "1925394108675330050")
    private String cJudgeRlt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("炉号")
    @Schema(description = "炉号")
    private String cHeatId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("申请编号")
    @Schema(description = "申请编号")
    private String cRequireNo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综判状态")
    @Schema(description = "综判状态")
    private String cJudgeStatus;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("表面等级")
    @Schema(description = "表面等级")
    @Trans(type = TransType.DIC, id = "1925393176789061634")
    private String cFaceRlt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原表面等级")
    @Schema(description = "原表面等级")
    @Trans(type = TransType.DIC, id = "1925393176789061634")
    private String cOldFaceRlt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("成分描述")
    @Schema(description = "成分描述")
    private String cChemRemark;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("表面描述")
    @Schema(description = "表面描述")
    private String cFaceRemark;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("尺寸描述")
    @Schema(description = "尺寸描述")
    private String cSizeRemark;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("性能描述")
    @Schema(description = "性能描述")
    private String cPropertyRemark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

}
