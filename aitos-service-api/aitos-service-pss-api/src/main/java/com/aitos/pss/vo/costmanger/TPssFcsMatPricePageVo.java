package com.aitos.pss.vo.costmanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
public class TPssFcsMatPricePageVo {

    /**
    * 顺序号
    */
    @Schema(description = "顺序号")
    private String nId;
    /**
    * 成本项目编码
    */
    @Schema(description = "成本项目编码")
    private String cCostItemCode;

    @Schema(description = "成本项目id")
    private Long cCostItemId;

    /**
    * 成本项目名称
    */
    @Schema(description = "成本项目名称")
    private String cCostItemName;
    /**
    * 物料id
    */
    @Schema(description = "物料id")
    private Long cMatId;
    /**
    * 物料代码
    */
    @Schema(description = "物料代码")
    private String cMatCode;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatName;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private Long cUnitId;
    /**
    * 单位编码
    */
    @Schema(description = "单位编码")
    private String cUnitCode;
    /**
    * 单位名称
    */
    @Schema(description = "单位名称")
    private String cUnitName;
    /**
    * 计划价格
    */
    @Schema(description = "计划价格")
    private BigDecimal cPlanPrice;
    /**
    * 实际价格
    */
    @Schema(description = "实际价格")
    private BigDecimal cActPrice;
    /**
    * 回收率
    */
    @Schema(description = "回收率")
    private BigDecimal cRecovery;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private Integer cState;
    /**
    * 开始启用时间
    */
    @Schema(description = "开始启用时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalDateTime dtStarttime;
    /**
    * 结束时间
    */
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalDateTime dtEndtime;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cMemo;

    @Schema(description = "产线id")
    private Long cProLine;

    @Schema(description = "产线cd")
    private String cProLineCode;

    @Schema(description = "产线name")
    private String cProLineName;
    /**
     * 工序
     */
    @Schema(description = "工序id")
    private Long cProcId;

    @Schema(description = "工序cd")
    private String cProcCd;

    @Schema(description = "工序name")
    private String cProcName;
    /**
    * 物料类型代码
    */
    @Schema(description = "物料类型代码")
    private String cMatTypeCd;
    /**
    * 物料类型名称
    */
    @Schema(description = "物料类型名称")
    private String cMatTypeName;

    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;

    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
