package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@Data
public class TPssQualityCodeSubPageVo {

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "质检标准名称")
    @TableField(value = "n_fstditem_name")
    private String nFstditemName;
    
    @Schema(description = "简称")
    @TableField(value = "n_fsimplename")
    private String nFsimplename;
    
    @Schema(description = "质检标准编码")
    @TableField(value = "n_fstditem")
    private String nFstditem;

    @Schema(description = "质检标准Id")
    private Long nFstditemId;
    
    @Schema(description = "描述")
    @TableField(value = "n_fdescription")
    private String nFdescription;
    
    @Schema(description = "标准类型")
    @TableField(value = "n_fjudgetype")
    private String nFjudgetype;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    private Integer nDeleteMark;

}
