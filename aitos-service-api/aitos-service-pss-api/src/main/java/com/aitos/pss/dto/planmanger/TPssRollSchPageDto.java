package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/19 09:17
 */
@Data
public class TPssRollSchPageDto extends PageInput {

    @Schema(description = "轧制顺序计划id")
    private String cSchId;

    @Schema(description = "调度单号")
    private String cDispatchId;

    @Schema(description = "产线")
    private String cLineNo;

    @Schema(description = "炉号")
    private String cHeatId;
}