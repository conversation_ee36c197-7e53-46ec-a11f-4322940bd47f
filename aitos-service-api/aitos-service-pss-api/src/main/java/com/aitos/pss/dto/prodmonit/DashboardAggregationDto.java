package com.aitos.pss.dto.prodmonit;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 仪表盘聚合对象
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/29 09:57
 */
@Data
public class DashboardAggregationDto {

    @Schema(description = "设备总数")
    private BigDecimal equipmentNum;

    @Schema(description = "在线设备数")
    private BigDecimal equipmentOnlineNum;

    @Schema(description = "生产指标达标率")
    private BigDecimal PCR;

    @Schema(description = "较昨日上浮")
    private BigDecimal PCRFloatUp;

    @Schema(description = "预警数")
    private Long warnNum;

    @Schema(description = "已处理预警")
    private Long ProcessWarnNum;

    @Schema(description = "违规单数")
    private Long violationNum;

    @Schema(description = "待审核违规单")
    private Long pendReviewViolationNum;

    @Schema(description = "设备id")
    private Long nEquipmentId;

    @Schema(description = "指标id")
    private Long nIndicatorId;

    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;

    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
}
