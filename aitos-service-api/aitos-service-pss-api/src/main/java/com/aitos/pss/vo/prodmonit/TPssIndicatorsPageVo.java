package com.aitos.pss.vo.prodmonit;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
public class TPssIndicatorsPageVo {

    /**
    * 主键id
    */
    @ExcelIgnore
    @Schema(description = "主键id")
    private String nId;
    /**
    * 指标名称
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("指标名称")
    @Schema(description = "指标名称")
    private String cName;
    /**
    * 指标代码
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("指标代码")
    @Schema(description = "指标代码")
    private String cCode;
    /**
    * 设备id
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("设备id")
    @Schema(description = "设备id")
    private Long nEquipmentId;
    /**
    * 设备code
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("设备code")
    @Schema(description = "设备code")
    private String cEquipmentCode;
    /**
    * 设备name
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("设备name")
    @Schema(description = "设备name")
    private String cEquipmentName;
    /**
    * 单位id
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("单位id")
    @Schema(description = "单位id")
    private Long cUnitId;
    /**
    * 单位code
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("单位code")
    @Schema(description = "单位code")
    private String cUnitCode;
    /**
    * 单位name
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("单位name")
    @Schema(description = "单位name")
    private String cUnitName;
    /**
    * 描述
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("描述")
    @Schema(description = "描述")
    private String cDescription;
    /**
    * 正常值下限
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("正常值下限")
    @Schema(description = "正常值下限")
    private BigDecimal nNormalMin;
    /**
    * 正常值上限
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("正常值上限")
    @Schema(description = "正常值上限")
    private BigDecimal nNormalMax;
    /**
    * 预警值下限
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("预警值下限")
    @Schema(description = "预警值下限")
    private BigDecimal nWarningMin;
    /**
    * 预警值上限
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("预警值上限")
    @Schema(description = "预警值上限")
    private BigDecimal nWarningMax;
    /**
    * 违规值下限
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("违规值下限")
    @Schema(description = "违规值下限")
    private BigDecimal nViolationMin;
    /**
    * 违规值上限
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("违规值上限")
    @Schema(description = "违规值上限")
    private BigDecimal nViolationMax;
    /**
    * 指标点位配置
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("指标点位配置")
    @Schema(description = "指标点位配置")
    private String cPointConfig;
    /**
    * 判断类型
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("判断类型")
    @Schema(description = "判断类型")
    @Trans(type = TransType.DIC, id = "1949633418515537921")
    private String cJudgmentType;
    /**
    * 状态（1:启用, 0:禁用）
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("状态（1:启用, 0:禁用）")
    @Schema(description = "状态（1:启用, 0:禁用）")
    private String cStatus;

    @Schema(description = "审批人ID")
    @Trans(type = TransType.USER,transForPropertyName = "nApproverName")
    private Long nApproverId;
    private String nApproverName;
    /**
    * 创建时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("创建时间")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("最后修改时间")
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("逻辑删除标记")
    @Schema(description = "逻辑删除标记")
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否有效/启用标记")
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
