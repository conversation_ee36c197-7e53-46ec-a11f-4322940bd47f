package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssQualStdLibMainPageDto extends PageInput {

    @Schema(description = "质检标准名称")
    private String cStdname;

    @Schema(description = "质检标准编码")
    private String cStdcode;
    
    @Schema(description = "产线")
    private Long cProLine;
    
    @Schema(description = "质检标准类别")
    private String cStdtype;
}
