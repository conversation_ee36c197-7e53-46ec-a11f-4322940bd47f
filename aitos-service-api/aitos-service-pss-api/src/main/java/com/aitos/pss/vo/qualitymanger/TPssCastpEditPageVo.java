package com.aitos.pss.vo.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
public class TPssCastpEditPageVo {

    /**
    * 序号
    */
    @Schema(description = "序号")
    private String nId;
    /**
    * 浇次编制号
    */
    @Schema(description = "浇次编制号")
    private String nCastEdtSeq;
    /**
    * 计划浇次号
    */
    @Schema(description = "计划浇次号")
    private String cPlanCastId;
    /**
    * 浇铸厚度
    */
    @Schema(description = "浇铸厚度")
    private BigDecimal nSlabThk;
    /**
    * 浇铸宽度
    */
    @Schema(description = "浇铸宽度")
    private BigDecimal nSlabWth;
    /**
    * 计划开始时间
    */
    @Schema(description = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtStartDt;
    /**
    * 计划结束时间
    */
    @Schema(description = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtEndDt;
    /**
    * 铸机号
    */
    @Schema(description = "铸机号")
    private String cCastMachId;
    /**
    * 炉数
    */
    @Schema(description = "炉数")
    private Long nHeatCnt;
    /**
    * 大浇次序号
    */
    @Schema(description = "大浇次序号")
    private BigDecimal nBigCastSeq;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 
    */
    @Schema(description = "")
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    private Long nModifyUserId;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    private Integer nDeleteMark;

}
