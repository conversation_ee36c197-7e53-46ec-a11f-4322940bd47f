package com.aitos.pss.dto.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 退货管理
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Data
public class UpdateTPssSteelSaleReturnDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键
    */
    @Schema(description = "主键")
    private Long nId;
    /**
    * 提货单号
    */
    @Schema(description = "提货单号")
    private String deliveryNo;
    /**
    * 库号
    */
    @Schema(description = "库号")
    private String stockNo;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    private String batchNo;
    /**
    * 材料号/卷号
    */
    @Schema(description = "材料号/卷号")
    private String materialNo;
    /**
    * 钢种名称
    */
    @Schema(description = "钢种名称")
    private String steelGrade;
    /**
    * 规格
    */
    @Schema(description = "规格")
    private String spec;
    /**
    * 单件重量(kg)
    */
    @Schema(description = "单件重量(kg)")
    private BigDecimal weight;
    /**
    * 退货数量(件/卷)
    */
    @Schema(description = "退货数量(件/卷)")
    private Integer returnQty;
    /**
    * 退货总重量(kg)
    */
    @Schema(description = "退货总重量(kg)")
    private BigDecimal returnWeight;
    /**
    * 退货申请时间
    */
    @Schema(description = "退货申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime returnTime;
    /**
    * 退货申请人
    */
    @Schema(description = "退货申请人")
    private String returnUser;
    /**
    * 退货原因
    */
    @Schema(description = "退货原因")
    private String returnReason;
    /**
    * 原出库单号
    */
    @Schema(description = "原出库单号")
    private String outNo;
    /**
    * 状态(待退库/已退库/已驳回等)
    */
    @Schema(description = "状态(待退库/已退库/已驳回等)")
    private String status;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String remark;
    /**
    * 
    */
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 
    */
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
