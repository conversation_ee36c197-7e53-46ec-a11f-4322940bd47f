package com.aitos.pss.dto.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 物料匹配管理
* <AUTHOR>
* @Date: 2025-05-28
* @Version 1.0
*/
@Data
public class AddTPssRollSchDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "轧制顺序计划id")
    private String cSchId;
    
    @Schema(description = "前顺序计划id")
    private String cPrevSchId;
    
    @Schema(description = "同轧制队列中顺序")
    private BigDecimal nSeqInMill;
    
    @Schema(description = "是否完结(参考值)")
    private String cRefCompletedFl;
    
    @Schema(description = "备注")
    private String cMemo;
    
    @Schema(description = "调度单号")
    private String cDispatchId;
    
    @Schema(description = "产线")
    private String cLineNo;
    
    @Schema(description = "炉号")
    private String cHeatId;
    
    @Schema(description = "自动手动标志 0手动 1自动")
    private String cMakeFl;
    
    @Schema(description = "产品类型")
    private String cProdType;
    
    @Schema(description = "直径")
    private BigDecimal nSpec;
    
    @Schema(description = "产品长度")
    private BigDecimal nProdLen;
    
    @Schema(description = "精整路径")
    private String cPlanFinishingPath;
    
    @Schema(description = "定尺代码")
    private String cSizeProperty;
    
    @Schema(description = "存货编码 （ERP）")
    private String cMatcode;
    
    @Schema(description = "存货名称 （ERP）")
    private String cMatname;
    
    @Schema(description = "产品钢种")
    private String cStlGrdCd;
    
    @Schema(description = "产品材质")
    private String cMatQulCd;
    
    @Schema(description = "产品宽")
    private BigDecimal nProdWth;
    
    @Schema(description = "钢坯规格")
    private String cSlabSpec;
    
    @Schema(description = "班次")
    private String cShift;
    
    @Schema(description = "班别")
    private String cCrew;
    
    @Schema(description = "V 真空,X高线,D大棒,B棒卷")
    private String cHeatHead;
    
    @Schema(description = "优质钢判断")
    private String cStlGrdOut;
    
    @Schema(description = "精整备注")
    private String cMemomemo;
    
    @Schema(description = "旧存货编码")
    private String cOldcode;
    
    @Schema(description = "订单号")
    private String cOrderNo;
    
    @Schema(description = "成品代码")
    private String cItemCd;
    
    @Schema(description = "成品规格")
    private String cMatItem;
    
    @Schema(description = "挂单重量")
    private BigDecimal nDofinalWgt;
    
    @Schema(description = "状态")
    private String cStatus;
    
    @Schema(description = "挂单数量")
    private BigDecimal nDofinalCount;
    
    @Schema(description = "执行标准")
    private String cStdSpec;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
