package com.aitos.pss.vo.planmanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
public class TPssPosMoveTimeVo {

    
    @Schema(description = "主键")
    private Long nId;

    @Schema(description = "开始工序/工位")
    private Long cStartProcess;

    @Schema(description = "开始工序/工位编码")
    private String cStartProcessCode;

    @Schema(description = "开始工序/工位名称")
    private String cStartProcessName;

    @Schema(description = "结束工序/工位")
    private Long cEndProcess;

    @Schema(description = "结束工序/工位编码")
    private String cEndProcessCode;

    @Schema(description = "结束工序/工位名称")
    private String cEndProcessName;

    @Schema(description = "产线")
    private Long cProLine;

    @Schema(description = "产线编码")
    private String cProLineCode;

    @Schema(description = "产线名称")
    private String cProLineName;
    
    @Schema(description = "移动时间-分钟")
    private BigDecimal nMoveTime;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;


}
