package com.aitos.pss.dto.prodmonit;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssViolationsPageDto extends PageInput {

    /**
    * 违规单号
    */
    @Schema(description = "违规单号")
    private String cCode;
    /**
    * 设备name
    */
    @Schema(description = "设备id")
    private Long nEquipmentId;
    /**
    * 指标name
    */
    @Schema(description = "指标id")
    private Long nIndicatorId;
    /**
    * 违规时间字段开始时间
    */
    @Schema(description = "违规时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtViolationDateTimeStart;
    /**
    * 违规时间字段结束时间
    */
    @Schema(description = "违规时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtViolationDateTimeEnd;
    /**
    * 状态（待审核/已通过/已驳回）
    */
    @Schema(description = "状态（待审核/已通过/已驳回）")
    private String cStatus;

}
