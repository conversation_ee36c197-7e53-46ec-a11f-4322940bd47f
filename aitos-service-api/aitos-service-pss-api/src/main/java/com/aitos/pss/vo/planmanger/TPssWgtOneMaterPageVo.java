package com.aitos.pss.vo.planmanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
public class TPssWgtOneMaterPageVo {

    
    @Schema(description = "主键")
    private String nId;
    
    @Schema(description = "钢种")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;
    
    @Schema(description = "规格")
    @Trans(type = TransType.DIC, id = "1925429568344756225")
    private String cSpec;
    
    @Schema(description = "密度")
    private BigDecimal nDensity;
    
    @Schema(description = "米重")
    private BigDecimal nWgtOneMater;
    
    @Schema(description = "计算方式;0为米重，1为密度")
    @Trans(type = TransType.DIC, id = "1925422177649758209")
    private String cCalculationMethod;

    @Schema(description = "铸机号id")
    private Long cCcmId;

    @Schema(description = "铸机号code")
    private String cCcmNo;

    @Schema(description = "铸机号name")
    private String cCcmName;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;

    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
