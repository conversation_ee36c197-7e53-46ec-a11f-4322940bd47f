package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 炼钢表尺判定管理
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
@TableName("t_pss_slab_face")
@Tag(name = "炼钢表尺判定管理对象", description = "炼钢表尺判定管理")
public class TPssSlabFace implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "")
    @TableId
    private Long nId;
    
    @Schema(description = "坯料号/件次号")
    @TableField(value = "c_slab_id")
    private String cSlabId;
    
    @Schema(description = "缺陷代码")
    @TableField(value = "c_defect_code")
    private String cDefectCode;
    
    @Schema(description = "钢种")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "表面等级")
    @TableField(value = "c_face_rlt")
    private String cFaceRlt;
    
    @Schema(description = "原表面等级")
    @TableField(value = "c_old_face_rlt")
    private String cOldFaceRlt;
    
    @Schema(description = "质量描述")
    @TableField(value = "c_remark")
    private String cRemark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}