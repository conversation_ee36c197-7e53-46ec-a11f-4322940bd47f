package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
public class TPssRollStdInfoVo {

    
    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "订单号")
    private String cOrderNo;

    @Schema(description = "质量编码")
    private Long cQualId;

    @Schema(description = "质量编码")
    private String cQualCode;

    @Schema(description = "质量编码简称")
    private String cQualCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;
    /**
    * 控制轧制
    */
    @Schema(description = "控制轧制")
    private Long nControlRoll;
    /**
    * 开轧温度℃
    */
    @Schema(description = "开轧温度℃")
    private BigDecimal nStartTemp;
    /**
    * 粗轧轧制温度℃
    */
    @Schema(description = "粗轧轧制温度℃")
    private BigDecimal nFirstRollTemp;
    /**
    * 精轧轧制温度℃
    */
    @Schema(description = "精轧轧制温度℃")
    private BigDecimal nSecRollTemp;
    /**
    * 终轧温度℃
    */
    @Schema(description = "终轧温度℃")
    private BigDecimal nFinalTemp;
    /**
    * 备用1
    */
    @Schema(description = "备用1")
    private String cReserver1;
    /**
    * 备用2
    */
    @Schema(description = "备用2")
    private String cReserver2;
    /**
    * 备用3
    */
    @Schema(description = "备用3")
    private String cReserver3;
    /**
    * 备用4
    */
    @Schema(description = "备用4")
    private String cReserver4;
    /**
    * 备用5
    */
    @Schema(description = "备用5")
    private String cReserver5;
    /**
    * 备用6
    */
    @Schema(description = "备用6")
    private String cReserver6;
    /**
    * 备用7
    */
    @Schema(description = "备用7")
    private String cReserver7;
    /**
    * 备用8
    */
    @Schema(description = "备用8")
    private String cReserver8;
    /**
    * 备用9
    */
    @Schema(description = "备用9")
    private String cReserver9;
    /**
    * 备用10
    */
    @Schema(description = "备用10")
    private String cReserver10;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
