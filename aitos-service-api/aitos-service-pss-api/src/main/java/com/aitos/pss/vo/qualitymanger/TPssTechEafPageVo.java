package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Data
public class TPssTechEafPageVo {

    
    @ExcelIgnore
    @Schema(description = "主键")
    private String nId;

    @Schema(description = "质量编码id")
    private Long cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;

    @Schema(description = "质量编码名称")
    private String cQualityCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("目标S")
    @Schema(description = "目标S")
    private String cTargetS;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("扒渣基准")
    @Schema(description = "扒渣基准")
    private String cSlagStd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("铁水")
    @Schema(description = "铁水")
    private String cIron;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("废钢")
    @Schema(description = "废钢")
    private String cSteelScrap;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("预熔渣")
    @Schema(description = "预熔渣")
    private String cPremelSlag;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("白灰")
    @Schema(description = "白灰")
    private String cLime;

    @Schema(description = "碱度")
    private BigDecimal cDeoxidizeCdoe;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("出钢[C]")
    @Schema(description = "出钢[C]")
    private String cDeoxidizeName;

    @Schema(description = "出钢温度")
    private BigDecimal cDeoxidizeSum;

    @Schema(description = "挂罐温度")
    private BigDecimal cHangPotTemp;

    @Schema(description = "到站温度")
    private BigDecimal cArriveTemp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否挡渣出钢")
    @Schema(description = "是否挡渣出钢")
    private String cSlagTapYn;

    @Schema(description = "钢包氩气流量")
    private BigDecimal cArFlux;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("萤石")
    @Schema(description = "萤石")
    private String cFluorite;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("渣洗剂")
    @Schema(description = "渣洗剂")
    private String cSlagLotion;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("硅灰石")
    @Schema(description = "硅灰石")
    private String cSilicaFume;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("电石")
    @Schema(description = "电石")
    private String cCalciumCarbide;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钙渣球")
    @Schema(description = "钙渣球")
    private String cCaBall;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("石英砂")
    @Schema(description = "石英砂")
    private String cSio2;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("开关组件")
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
