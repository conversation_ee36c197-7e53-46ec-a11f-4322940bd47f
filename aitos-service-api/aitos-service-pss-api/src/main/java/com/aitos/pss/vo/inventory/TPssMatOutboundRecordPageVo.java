package com.aitos.pss.vo.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@Data
public class TPssMatOutboundRecordPageVo {

    /**
    * 主键ID
    */
    @Schema(description = "主键ID")
    private String nId;
    /**
    * 炉号
    */
    @Schema(description = "炉号")
    private String cFurnaceNo;
    /**
    * 物料ID
    */
    @Schema(description = "物料ID")
    private Long nMaterialId;
    /**
    * 理论用量
    */
    @Schema(description = "理论用量")
    private BigDecimal nTheoreticalAmount;
    /**
    * 调整系数
    */
    @Schema(description = "调整系数")
    private BigDecimal nAdjustFactor;
    /**
    * 实际用量
    */
    @Schema(description = "实际用量")
    private BigDecimal nActualAmount;
    /**
    * 出库前库存
    */
    @Schema(description = "出库前库存")
    private BigDecimal nBeforeStock;
    /**
    * 出库后库存
    */
    @Schema(description = "出库后库存")
    private BigDecimal nAfterStock;
    /**
    * 出库时间
    */
    @Schema(description = "出库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtOutboundTime;
    /**
    * 状态(pending:待确认,completed:已完成,adjusted:已调整)
    */
    @Schema(description = "状态(pending:待确认,completed:已完成,adjusted:已调整)")
    private String cStatus;
    /**
    * 操作类型(auto:自动,manual:人工)
    */
    @Schema(description = "操作类型(auto:自动,manual:人工)")
    private String cOperationType;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
