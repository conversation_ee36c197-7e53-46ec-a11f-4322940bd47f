package com.aitos.pss.vo.inventory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Data
public class TPssMatYardinLgVo {

    /**
    * 顺序号
    */
    @Schema(description = "顺序号")
    private Long nId;
    /**
    * 件次号/坯料号
    */
    @Schema(description = "件次号/坯料号")
    private String cMatId;
    /**
    * 入库单号
    */
    @Schema(description = "入库单号")
    private String cYardinId;
    /**
    * 数量
    */
    @Schema(description = "数量")
    private Integer nCnt;
    /**
    * 入库状态
    */
    @Schema(description = "入库状态")
    private String cIfFlag;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMatcode;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatname;
    /**
    * 物料类型
    */
    @Schema(description = "物料类型")
    private String cMatType;
    /**
    * 产线
    */
    @Schema(description = "产线")
    private String cLineCd;
    /**
    * 仓库代码
    */
    @Schema(description = "仓库代码")
    private String cYardCd;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    private String cMatQulCd;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    private String cLotId;
    /**
    * 原料批次号
    */
    @Schema(description = "原料批次号")
    private String cMatLotId;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    private String cTaskListId;
    /**
    * 订单号（代表订单号）
    */
    @Schema(description = "订单号（代表订单号）")
    private String cOrderNo;
    /**
    * 销售订单号
    */
    @Schema(description = "销售订单号")
    private String cSaleNo;
    /**
    * 销售订单行号
    */
    @Schema(description = "销售订单行号")
    private String cSaleSn;
    /**
    * 下游订单号
    */
    @Schema(description = "下游订单号")
    private String cOrderIdFinal;
    /**
    * 规格
    */
    @Schema(description = "规格")
    private String cMatItem;
    /**
    * 厚度
    */
    @Schema(description = "厚度")
    private BigDecimal nMatThk;
    /**
    * 宽度
    */
    @Schema(description = "宽度")
    private BigDecimal nMatWth;
    /**
    * 内径
    */
    @Schema(description = "内径")
    private BigDecimal nMatDia;
    /**
    * 长度
    */
    @Schema(description = "长度")
    private BigDecimal nMatLen;
    /**
    * 件数
    */
    @Schema(description = "件数")
    private Integer nMatCnt;
    /**
    * 理论重量/发货重量
    */
    @Schema(description = "理论重量/发货重量")
    private BigDecimal nMatWgtCal;
    /**
    * 入库检斤重量
    */
    @Schema(description = "入库检斤重量")
    private BigDecimal nMatWgt;
    /**
    * 产地产线代码
    */
    @Schema(description = "产地产线代码")
    private String cSource;
    /**
    * 源地仓库代码
    */
    @Schema(description = "源地仓库代码")
    private String cFromYard;
    /**
    * 目的地仓库代码
    */
    @Schema(description = "目的地仓库代码")
    private String cToYard;
    /**
    * 库位号
    */
    @Schema(description = "库位号")
    private String cLoc;
    /**
    * 运输方式
    */
    @Schema(description = "运输方式")
    private String cTransType;
    /**
    * 车号
    */
    @Schema(description = "车号")
    private String cTransNo;
    /**
    * 计量单号
    */
    @Schema(description = "计量单号")
    private String cJilianId;
    /**
    * 入库类型
    */
    @Schema(description = "入库类型")
    private String cYardinType;
    /**
    * 入库原因
    */
    @Schema(description = "入库原因")
    private String cYardinRsn;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cMemo;
    /**
    * 入库班次
    */
    @Schema(description = "入库班次")
    private String cShift;
    /**
    * 入库班别
    */
    @Schema(description = "入库班别")
    private String cCrew;
    /**
    * 结存月份
    */
    @Schema(description = "结存月份")
    private String cMonth;
    /**
    * 数据来源
    */
    @Schema(description = "数据来源")
    private String cDataSource;
    /**
    * 最后修改程序
    */
    @Schema(description = "最后修改程序")
    private String cPgmId;
    /**
    * 是否上传
    */
    @Schema(description = "是否上传")
    private String cUploadFlag;
    /**
    * 上传消息号
    */
    @Schema(description = "上传消息号")
    private Long cMsgNo;
    /**
    * 终判等级
    */
    @Schema(description = "终判等级")
    private String cProdGrd;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    private LocalDateTime dtModifyDateTime;



}
