package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssMaterialInspectionPageDto extends PageInput {

    /**
    * 检验项目(chemical:化学成分,physical:物理性能,appearance:外观质量,size:尺寸规格,weight:重量偏差)
    */
    @Schema(description = "检验项目(chemical:化学成分,physical:物理性能,appearance:外观质量,size:尺寸规格,weight:重量偏差)")
    private String cItem;
    /**
    * 检验结果
    */
    @Schema(description = "检验结果")
    private String cResult;
    /**
    * 检验人
    */
    @Schema(description = "检验人")
    private String cInspector;
    /**
    * 到货记录ID
    */
    @Schema(description = "到货记录ID")
    private Long nReceiptId;
    /**
    * 检验标准
    */
    @Schema(description = "检验标准")
    private String cStandard;
    /**
    * 是否合格(0:不合格,1:合格)
    */
    @Schema(description = "是否合格(0:不合格,1:合格)")
    private Integer nIsQualified;
    /**
    * 检验时间字段开始时间
    */
    @Schema(description = "检验时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtInspectionTimeStart;
    /**
    * 检验时间字段结束时间
    */
    @Schema(description = "检验时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtInspectionTimeEnd;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
