package com.aitos.pss.dto.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;



/**
* @title: 炼钢综判管理
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@Data
public class UpdateTPssSlabJudgeDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "")
    private Long nId;
    
    @Schema(description = "坯料号")
    private String cSlabId;

    @Schema(description = "原质量编码id")
    private Long cOldMatId;

    @Schema(description = "原质量编码code")
    private String cOldMatCode;

    @Schema(description = "原质量编码name")
    private String cOldMatName;

    @Schema(description = "原钢种代码")
    private String cOldStlGrdCd;

    @Schema(description = "原钢种代码描述")
    private String cOldStlGrdDesc;

    @Schema(description = "现质量编码id")
    private Long cNewMatId;

    @Schema(description = "现质量编码code")
    private String cNewMatCode;

    @Schema(description = "现质量编码name")
    private String cNewMatName;

    @Schema(description = "现钢种代码")
    private String cNewStlGrdCd;

    @Schema(description = "现钢种代码描述")
    private String cNewStlGrdDesc;
    
    @Schema(description = "成分等级")
    private String cChemRlt;
    
    @Schema(description = "外形等级（表面等级）")
    private String cBodyRlt;
    
    @Schema(description = "尺寸等级")
    private String cSizeRlt;
    
    @Schema(description = "综判等级")
    private String cJudgeRlt;
    
    @Schema(description = "炉号")
    private String cHeatId;
    
    @Schema(description = "申请编号")
    private String cRequireNo;
    
    @Schema(description = "综判状态")
    private String cJudgeStatus;
    
    @Schema(description = "表面等级")
    private String cFaceRlt;
    
    @Schema(description = "原表面等级")
    private String cOldFaceRlt;
    
    @Schema(description = "删除标记")
    private String cDelFlag;
    
    @Schema(description = "成分描述")
    private String cChemRemark;
    
    @Schema(description = "表面描述")
    private String cFaceRemark;
    
    @Schema(description = "尺寸描述")
    private String cSizeRemark;
    
    @Schema(description = "性能描述")
    private String cPropertyRemark;

    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;
}
