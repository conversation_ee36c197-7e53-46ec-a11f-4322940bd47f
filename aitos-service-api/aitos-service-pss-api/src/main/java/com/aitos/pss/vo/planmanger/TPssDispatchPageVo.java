package com.aitos.pss.vo.planmanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-28
* @Version 1.0
*/
@Data
public class TPssDispatchPageVo {

    
    @ExcelIgnore
    @Schema(description = "序列号")
    private String nId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("调度单号")
    @Schema(description = "调度单号")
    private String cDispatchId;

    @Schema(description = "产线id")
    private String cLineId;

    @Schema(description = "产线编码")
    private String cLineNo;

    @Schema(description = "产线名称")
    private String cLineName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;

    @Schema(description = "产品规格")
    private String nSpec;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("总重量")
    @Schema(description = "总重量")
    private BigDecimal nTalWgt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("总支数")
    @Schema(description = "总支数")
    private BigDecimal nTalCnt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("下达人")
    @Schema(description = "下达人")
    private String cReleaseEmp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("调度单中没有轧制计划后设置1")
    @Schema(description = "调度单中没有轧制计划后设置1")
    private String cEmptyFl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("交货日期")
    @Schema(description = "交货日期")
    private String cPostDatetime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("剩余支数")
    @Schema(description = "剩余支数")
    private BigDecimal nLeftCnt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("计划精整工艺路径（精整工序编号+分隔符-逗号+精整工序编号）")
    @Schema(description = "计划精整工艺路径（精整工序编号+分隔符-逗号+精整工序编号）")
    private String cPlanFinishingPath;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综合生产计划号")
    @Schema(description = "综合生产计划号")
    private String cProductPlanNo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("销售计划号")
    @Schema(description = "销售计划号")
    private String cSalePlanNo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产品类型")
    @Schema(description = "产品类型")
    private String cProdType;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("定尺类型")
    @Schema(description = "定尺类型")
    private String cSizeProperty;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("下达时间")
    @Schema(description = "下达时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReleaseTime;

    @Schema(description = "存货id")
    private Long cMatId;

    @Schema(description = "存货编码")
    private String cMatCode;

    @Schema(description = "存货名称")
    private String cMatName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产品长度")
    @Schema(description = "产品长度")
    private BigDecimal nProdLen;

    @Schema(description = "产品材质id-物料")
    private Long cMatQulId;

    @Schema(description = "产品材质code-物料")
    private String cMatQulCd;

    @Schema(description = "产品材质name-物料")
    private String cMatQulName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("顺序")
    @Schema(description = "顺序")
    private BigDecimal nDispatchseq;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("1调度下达，0未下达,2 转去装炉,3已经生产,8生产结束,9撤销(P0019)")
    @Schema(description = "1调度下达，0未下达,2 转去装炉,3已经生产,8生产结束,9撤销(P0019)")
    private String cStatus;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("计划下达人")
    @Schema(description = "计划下达人")
    private String cSendEmp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("计划下达时间")
    @Schema(description = "计划下达时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSendTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("执行标准")
    @Schema(description = "执行标准")
    private String cStdSpec;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("执行标准名称")
    @Schema(description = "执行标准名称")
    private String cStdSpecName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综合生产计划序号")
    @Schema(description = "综合生产计划序号")
    private BigDecimal nProductPlanSn;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原始调度单号")
    @Schema(description = "原始调度单号")
    private String cOrgDispatchid;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("0原始调度单，1拆分后调度单，2合并后调度单，3合并后消失调度单")
    @Schema(description = "0原始调度单，1拆分后调度单，2合并后调度单，3合并后消失调度单")
    private String cDisSource;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综合生产计划号(任务单调整用)")
    @Schema(description = "综合生产计划号(任务单调整用)")
    private String cDisProductPlanNo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综合销售计划序号")
    @Schema(description = "综合销售计划序号")
    private BigDecimal nSalePlanSn;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("交货状态")
    @Schema(description = "交货状态")
    private String cFpoststateid;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("备注")
    @Schema(description = "备注")
    private String cMemo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("调度下达人（下达到物料匹配）")
    @Schema(description = "调度下达人（下达到物料匹配）")
    private String cDissendEmp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("调度下达时间")
    @Schema(description = "调度下达时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtDissendTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用订单生产")
    @Schema(description = "是否启用订单生产")
    private Integer cPreStatus;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否为临时计划（1：临时计划）")
    @Schema(description = "是否为临时计划（1：临时计划）")
    private String cIsTempplan;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("销售操作日志")
    @Schema(description = "销售操作日志")
    private String cMergememo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("调度备注")
    @Schema(description = "调度备注")
    private String cDispatchmemo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("调整顺序时间")
    @Schema(description = "调整顺序时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtUpdatetime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("调整顺序人")
    @Schema(description = "调整顺序人")
    private String cUpdatetime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("精整内容")
    @Schema(description = "精整内容")
    private String cClearcont;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("精整交货时间")
    @Schema(description = "精整交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtChearfisTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("精整内容编制人")
    @Schema(description = "精整内容编制人")
    private String cClearedtemp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("精整内容编制时间")
    @Schema(description = "精整内容编制时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtClearedtTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("结束人")
    @Schema(description = "结束人")
    private String cFinemp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否紧急订单（0：正常订单，1：紧急订单）")
    @Schema(description = "是否紧急订单（0：正常订单，1：紧急订单）")
    private String cExgProdLotFl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("客户名称")
    @Schema(description = "客户名称")
    private String cCustomerCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("结束时间")
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFinTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("客户重要度")
    @Schema(description = "客户重要度")
    private String cCusLevel;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否下发二级")
    @Schema(description = "是否下发二级")
    private String cSFl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("旧编码")
    @Schema(description = "旧编码")
    private String cOldcode;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢坯库存对应重量")
    @Schema(description = "钢坯库存对应重量")
    private String cKcgp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产品宽度")
    @Schema(description = "产品宽度")
    private BigDecimal nProdWth;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("订单号")
    @Schema(description = "订单号")
    private String cOrderNo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("规格代码")
    @Schema(description = "规格代码")
    private String cItemCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("成品规格")
    @Schema(description = "成品规格")
    private String cMatItem;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("厚度公差最大值")
    @Schema(description = "厚度公差最大值")
    private BigDecimal nThkBiasMax;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("厚度公差最小值")
    @Schema(description = "厚度公差最小值")
    private BigDecimal nThkBiasMin;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("宽度公差最大值")
    @Schema(description = "宽度公差最大值")
    private BigDecimal nWthBiasMax;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("宽度公差最小值")
    @Schema(description = "宽度公差最小值")
    private BigDecimal nWthBiasMin;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("成品重量最大值")
    @Schema(description = "成品重量最大值")
    private BigDecimal nProdWgtMax;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("成品重量最小值")
    @Schema(description = "成品重量最小值")
    private BigDecimal nProdWgtMin;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("长度最小")
    @Schema(description = "长度最小")
    private BigDecimal nProdLenMin;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("长度最大")
    @Schema(description = "长度最大")
    private BigDecimal nProdLenMax;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("计划单支重")
    @Schema(description = "计划单支重")
    private BigDecimal nPlanSingleWgt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("挂单重量")
    @Schema(description = "挂单重量")
    private BigDecimal nDofinalWgt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("坯料规格")
    @Schema(description = "坯料规格")
    private String cSlabItem;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;

}
