package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 公共编码维护
* <AUTHOR>
* @Date: 2025-05-15
* @Version 1.0
*/
@Data
@TableName("t_pss_common_code")
@Tag(name = "公共编码维护对象", description = "公共编码维护")
public class TPssCommonCode implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "代码管理号")
    private String cManaNo;
    
    @Schema(description = "代码")
    private String cCode;
    
    @Schema(description = "代码简称")
    private String cShortName;
    
    @Schema(description = "代码名称")
    private String cName;
    
    @Schema(description = "代码英文简称")
    private String cShortEng;
    
    @Schema(description = "代码英文名称")
    private String cFullEng;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}