package com.aitos.pss.dto.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 坯料设计临时表
* <AUTHOR>
* @Date: 2025-07-21
* @Version 1.0
*/
@Data
public class UpdateTPssFinalDesignSlabTempDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 序列号
    */
    @Schema(description = "序列号")
    private Long nId;
    /**
    * 设计钢坯id
    */
    @Schema(description = "设计钢坯id")
    private String cSlabId;
    /**
    * 代表钢坯材质代码
    */
    @Schema(description = "代表钢坯材质代码")
    private Long cSlabMatQulId;
    /**
    * 代表钢坯材质代码
    */
    @Schema(description = "代表钢坯材质代码")
    private String cSlabMatQulCd;
    /**
    * 代表钢板材质代码
    */
    @Schema(description = "代表钢板材质代码")
    private String cSlabMatQulName;
    /**
    * 钢坯厚
    */
    @Schema(description = "钢坯厚")
    private BigDecimal nThk;
    /**
    * 钢坯宽
    */
    @Schema(description = "钢坯宽")
    private BigDecimal nWth;
    /**
    * 钢坯长
    */
    @Schema(description = "钢坯长")
    private BigDecimal nLth;
    /**
    * 钢坯计算重量
    */
    @Schema(description = "钢坯计算重量")
    private BigDecimal nCalWgt;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdDesc;
    /**
    * 设计成材率
    */
    @Schema(description = "设计成材率")
    private BigDecimal nDegRatio;
    /**
    * 轧件长度
    */
    @Schema(description = "轧件长度")
    private BigDecimal nAsrollLth;
    /**
    * 轧件厚度
    */
    @Schema(description = "轧件厚度")
    private BigDecimal nAsrollThk;
    /**
    * 轧件宽度
    */
    @Schema(description = "轧件宽度")
    private BigDecimal nAsrollWth;
    /**
    * 交货状态
    */
    @Schema(description = "交货状态")
    private String cFpoststateid;
    /**
    * 探伤等级
    */
    @Schema(description = "探伤等级")
    private String cUstLev;
    /**
    * 探伤标准
    */
    @Schema(description = "探伤标准")
    private String cUstStd;
    /**
    * 切头长
    */
    @Schema(description = "切头长")
    private BigDecimal nCutHead;
    /**
    * 切尾长
    */
    @Schema(description = "切尾长")
    private BigDecimal nCutTail;
    /**
    * 长度切损
    */
    @Schema(description = "长度切损")
    private BigDecimal nCutLthLose;
    /**
    * 长度余量
    */
    @Schema(description = "长度余量")
    private BigDecimal nCutLthRem;
    /**
    * 宽度切损
    */
    @Schema(description = "宽度切损")
    private BigDecimal nCutTrimLose;
    /**
    * 宽度余量
    */
    @Schema(description = "宽度余量")
    private BigDecimal nCutWthRem;
    /**
    * 试样长度
    */
    @Schema(description = "试样长度")
    private BigDecimal nSmpLth;
    /**
    * 代表执行标准
    */
    @Schema(description = "代表执行标准")
    private String cStdSpec;
    /**
    * 订单号
    */
    @Schema(description = "订单号")
    private String cOrderNo;
    /**
    * 产品名称
    */
    @Schema(description = "产品名称")
    private String cProdName;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    private String cCtaskListId;
    /**
    * 轧制计划号
    */
    @Schema(description = "轧制计划号")
    private String cSchId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
