package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssRateStdPageDto extends PageInput {

    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "成材率")
    private BigDecimal nPlateSlabRate;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "收得率")
    private BigDecimal nSlabStlRate;
    
    @Schema(description = "密度(t/m)")
    private BigDecimal nDensity;

}
