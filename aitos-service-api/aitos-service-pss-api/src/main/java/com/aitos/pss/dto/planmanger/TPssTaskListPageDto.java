package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssTaskListPageDto extends PageInput {

    
    @Schema(description = "订单号")
    private String cOrderNo;
    
    @Schema(description = "产品类别")
    private String cProdType;
    
    @Schema(description = "编制时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCrtTimeStart;
    
    @Schema(description = "编制时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCrtTimeEnd;
    
    @Schema(description = "生产任务单号")
    private String cTaskListId;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "状态")
    private String cStatus;

}
