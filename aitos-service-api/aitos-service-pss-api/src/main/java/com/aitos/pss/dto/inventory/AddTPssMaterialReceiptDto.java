package com.aitos.pss.dto.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 原辅料入库管理
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
public class AddTPssMaterialReceiptDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 到货单号
    */
    @NotBlank(message = "到货单号不能为空")
    @Schema(description = "到货单号")
    private String cReceiptNo;
    /**
    * 入库单号
    */
    @NotBlank(message = "入库单号不能为空")
    @Schema(description = "入库单号")
    private String cStorageNo;
    /**
    * 批次号
    */
    @NotBlank(message = "批次号不能为空")
    @Schema(description = "批次号")
    private String cBatchNo;
    /**
    * 计量单号
    */
    @NotBlank(message = "计量单号不能为空")
    @Schema(description = "计量单号")
    private String cMeasureNo;
    /**
    * 采购订单号
    */
    @NotBlank(message = "采购订单号不能为空")
    @Schema(description = "采购订单号")
    private String cPurchaseOrderNo;
    /**
    * 采购订单行号
    */
    @NotBlank(message = "采购订单行号不能为空")
    @Schema(description = "采购订单行号")
    private String cPurchaseOrderLine;
    /**
    * 物料编码
    */
    @NotBlank(message = "物料编码不能为空")
    @Schema(description = "物料编码")
    private String cMaterialCode;
    /**
    * 物料名称
    */
    @NotBlank(message = "物料名称不能为空")
    @Schema(description = "物料名称")
    private String cMaterialName;
    /**
    * 物料类型(raw:原辅料,alloy:合金,scrap:废钢)
    */
    @NotBlank(message = "物料类型不能为空")
    @Schema(description = "物料类型(raw:原辅料,alloy:合金,scrap:废钢)")
    private String cMaterialType;
    /**
    * 到货数量
    */
    @NotNull(message = "到货数量不能为空")
    @Schema(description = "到货数量")
    private BigDecimal nQuantity;
    /**
    * 入库检斤重量
    */
    @NotNull(message = "入库检斤不能为空")
    @Schema(description = "入库检斤重量")
    private BigDecimal nMeasureWeight;
    /**
    * 入库确认重量
    */
    @NotNull(message = "入库确认重量不能为空")
    @Schema(description = "入库确认重量")
    private BigDecimal nConfirmWeight;
    /**
    * 单位
    */
    @NotBlank(message = "单位不能为空")
    @Schema(description = "单位")
    private String cUnit;
    /**
    * 到货日期
    */
    @NotNull(message = "到货日期不能为空")
    @Schema(description = "到货日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReceiptDate;
    /**
    * 供应商
    */
    @NotBlank(message = "供应商不能为空")
    @Schema(description = "供应商")
    private String cSupplier;
    /**
    * 目的库位
    */
    @NotBlank(message = "目的库位不能为空")
    @Schema(description = "目的库位")
    private String cStorageLocation;
    /**
    * 目的库位名
    */
    @NotBlank(message = "目的库位名不能为空")
    @Schema(description = "目的库位名")
    private String cStorageLocationName;
    /**
    * 状态(pending:待检验,testing:检验中,completed:已完成,rejected:不合格)
    */
    @NotBlank(message = "状态不能为空")
    @Schema(description = "状态(pending:待检验,testing:检验中,completed:已完成,rejected:不合格)")
    private String cStatus;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
