package com.aitos.pss.dto.costmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssFcsCdPageDto extends PageInput {

    /**
    * 顺序号
    */
    @Schema(description = "顺序号")
    private Long nId;
    /**
    * 项目大类名称
    */
    @Schema(description = "项目大类名称")
    private String cMatTypeBName;
    /**
    * 项目小类名称
    */
    @Schema(description = "项目小类名称")
    private String cCostItemName;
    /**
    * 产线
    */
    @Schema(description = "产线")
    private Long cProLine;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cMemo;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    private Long nModifyUserId;
    /**
    * 项目大类编码
    */
    @Schema(description = "项目大类编码")
    private String cMatTypeBCode;
    /**
    * 项目小类编码
    */
    @Schema(description = "项目小类编码")
    private String cCostItemCode;
    /**
     * 计量单位
     */
    @Schema(description = "计量单位")
    private String cMatUnitId;
    /**
     * 计量单位编码
     */
    @Schema(description = "计量单位编码")
    private String cMatUnitCode;
    /**
     * 计量单位名称
     */
    @Schema(description = "计量单位名称")
    private String cMatUnitName;
    /**
    * 显示顺序
    */
    @Schema(description = "显示顺序")
    private Integer cCostSeq;
    /**
    * 是否启用;默认为0,1为未启用
    */
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    /**
    * 字段开始时间
    */
    @Schema(description = "字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 字段结束时间
    */
    @Schema(description = "字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 字段开始时间
    */
    @Schema(description = "字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 字段结束时间
    */
    @Schema(description = "字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
