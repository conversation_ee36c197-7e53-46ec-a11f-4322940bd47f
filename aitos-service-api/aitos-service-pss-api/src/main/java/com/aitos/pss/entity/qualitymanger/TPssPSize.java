package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 轧钢表尺判定管理
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@Data
@TableName("t_pss_p_size")
@Tag(name = "轧钢表尺判定管理对象", description = "轧钢表尺判定管理")
public class TPssPSize implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "")
    @TableId
    private Long nId;
    
    @Schema(description = "线卷号/坯料号/件次号")
    @TableField(value = "c_plate_id")
    private String cPlateId;
    
    @Schema(description = "检查次数")
    @TableField(value = "n_chk_seq")
    private Integer nChkSeq;
    
    @Schema(description = "检查工序")
    @TableField(value = "c_chk_pos")
    private Long cChkPos;

    @Schema(description = "检查工序code")
    @TableField(value = "c_chk_pos_code")
    private String cChkPosCode;

    @Schema(description = "检查工序name")
    @TableField(value = "c_chk_pos_name")
    private String cChkPosName;

    @Schema(description = "钢种")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;
    
    @Schema(description = "线卷面")
    @TableField(value = "c_plate_face")
    private String cPlateFace;
    
    @Schema(description = "缺陷序号")
    @TableField(value = "n_defect_seq")
    private Long nDefectSeq;
    
    @Schema(description = "缺陷代码")
    @TableField(value = "c_defect_type")
    private String cDefectType;
    
    @Schema(description = "缺陷位置")
    @TableField(value = "c_defect_pos")
    private String cDefectPos;
    
    @Schema(description = "缺陷描述")
    @TableField(value = "c_defect_desc")
    private String cDefectDesc;
    
    @Schema(description = "处理建议")
    @TableField(value = "c_disp_pro")
    private String cDispPro;
    
    @Schema(description = "处理方式")
    @TableField(value = "c_disp_manner")
    private String cDispManner;
    
    @Schema(description = "处理结果")
    @TableField(value = "c_disp_result")
    private String cDispResult;
    
    @Schema(description = "班次")
    @TableField(value = "c_shift")
    private String cShift;
    
    @Schema(description = "班别")
    @TableField(value = "c_crew")
    private String cCrew;
    
    @Schema(description = "处理人")
    @TableField(value = "c_grind_operater")
    private String cGrindOperater;
    
    @Schema(description = "处理班次")
    @TableField(value = "c_grind_shift")
    private String cGrindShift;
    
    @Schema(description = "处理班别")
    @TableField(value = "c_grind_crew")
    private String cGrindCrew;
    
    @Schema(description = "尺寸等级")
    @TableField(value = "c_size_rlt")
    private String cSizeRlt;
    
    @Schema(description = "备注")
    @TableField(value = "c_remarks")
    private String cRemarks;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}