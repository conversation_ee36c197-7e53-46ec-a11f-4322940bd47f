package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 电炉工艺参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@TableName("t_pss_tech_eaf_info")
@Tag(name = "电炉工艺参数质量设计结果对象", description = "电炉工艺参数质量设计结果")
public class TPssTechEafInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "生产订单号")
    @TableField(value = "order_id")
    private String orderId;
    
    @Schema(description = "质量编码")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "目标S")
    @TableField(value = "c_target_s")
    private String cTargetS;
    
    @Schema(description = "扒渣基准")
    @TableField(value = "c_slag_std")
    private String cSlagStd;
    
    @Schema(description = "铁水")
    @TableField(value = "c_iron")
    private String cIron;
    
    @Schema(description = "废钢")
    @TableField(value = "c_steel_scrap")
    private String cSteelScrap;
    
    @Schema(description = "预熔渣")
    @TableField(value = "c_premel_slag")
    private String cPremelSlag;
    
    @Schema(description = "白灰")
    @TableField(value = "c_lime")
    private String cLime;
    
    @Schema(description = "碱度")
    @TableField(value = "c_deoxidize_cdoe")
    private BigDecimal cDeoxidizeCdoe;
    
    @Schema(description = "出钢[C]")
    @TableField(value = "c_deoxidize_name")
    private String cDeoxidizeName;
    
    @Schema(description = "出钢温度")
    @TableField(value = "c_deoxidize_sum")
    private BigDecimal cDeoxidizeSum;
    
    @Schema(description = "挂罐温度")
    @TableField(value = "c_hang_pot_temp")
    private BigDecimal cHangPotTemp;
    
    @Schema(description = "到站温度")
    @TableField(value = "c_arrive_temp")
    private BigDecimal cArriveTemp;
    
    @Schema(description = "是否挡渣出钢")
    @TableField(value = "c_slag_tap_yn")
    private String cSlagTapYn;
    
    @Schema(description = "钢包氩气流量")
    @TableField(value = "c_ar_flux")
    private BigDecimal cArFlux;
    
    @Schema(description = "萤石")
    @TableField(value = "c_fluorite")
    private String cFluorite;
    
    @Schema(description = "渣洗剂")
    @TableField(value = "c_slag_lotion")
    private String cSlagLotion;
    
    @Schema(description = "硅灰石")
    @TableField(value = "c_silica_fume")
    private String cSilicaFume;
    
    @Schema(description = "电石")
    @TableField(value = "c_calcium_carbide")
    private String cCalciumCarbide;
    
    @Schema(description = "钙渣球")
    @TableField(value = "c_ca_ball")
    private String cCaBall;
    
    @Schema(description = "石英砂")
    @TableField(value = "c_sio2")
    private String cSio2;
    
    @Schema(description = "备用1")
    @TableField(value = "c_backup1")
    private String cBackup1;
    
    @Schema(description = "备用2")
    @TableField(value = "c_backup2")
    private String cBackup2;
    
    @Schema(description = "备用3")
    @TableField(value = "c_backup3")
    private String cBackup3;
    
    @Schema(description = "备用4")
    @TableField(value = "c_backup4")
    private String cBackup4;
    
    @Schema(description = "备用5")
    @TableField(value = "c_backup5")
    private String cBackup5;
    
    @Schema(description = "备用6")
    @TableField(value = "c_backup6")
    private String cBackup6;
    
    @Schema(description = "备用7")
    @TableField(value = "c_backup7")
    private String cBackup7;
    
    @Schema(description = "备用8")
    @TableField(value = "c_backup8")
    private String cBackup8;
    
    @Schema(description = "备用9")
    @TableField(value = "c_backup9")
    private String cBackup9;
    
    @Schema(description = "备用10")
    @TableField(value = "c_backup10")
    private String cBackup10;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
}