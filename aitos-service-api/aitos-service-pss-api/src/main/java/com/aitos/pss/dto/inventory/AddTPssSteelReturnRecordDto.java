package com.aitos.pss.dto.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 退货管理子表
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Data
public class AddTPssSteelReturnRecordDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 提货单号
    */
    @Schema(description = "提货单号")
    private String cDeliveryNo;
    /**
    * 库号
    */
    @Schema(description = "库号")
    private String cStockNo;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    private String cBatchNo;
    /**
    * 材料号/卷号
    */
    @Schema(description = "材料号/卷号")
    private String cMaterialNo;
    /**
    * 钢种名称
    */
    @Schema(description = "钢种名称")
    private String cSteelGrade;
    /**
    * 规格
    */
    @Schema(description = "规格")
    private String cSpec;
    /**
    * 单件重量(kg)
    */
    @Schema(description = "单件重量(kg)")
    private BigDecimal nWeight;
    /**
    * 退货数量(件/卷)
    */
    @Schema(description = "退货数量(件/卷)")
    private Integer nReturnQty;
    /**
    * 退货总重量(kg)
    */
    @Schema(description = "退货总重量(kg)")
    private BigDecimal nReturnWeight;
    /**
    * 退货入库时间
    */
    @Schema(description = "退货入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReturnTime;
    /**
    * 退货操作人
    */
    @Schema(description = "退货操作人")
    private String cReturnUser;
    /**
    * 退货原因
    */
    @Schema(description = "退货原因")
    private String cReturnReason;
    /**
    * 原出库单号
    */
    @Schema(description = "原出库单号")
    private String cOutNo;
    /**
    * 状态(已退库/已冲销等)
    */
    @Schema(description = "状态(已退库/已冲销等)")
    private String cStatus;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 
    */
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 
    */
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
