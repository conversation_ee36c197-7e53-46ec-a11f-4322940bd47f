package com.aitos.pss.entity.costmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title:
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/
@Data
@TableName("t_pss_order_cost")
@Tag(name = "t_pss_order_cost", description = "t_pss_order_cost")
public class TPssOrderCost implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 
    */
    @Schema(description = "")
    @TableId
    private Long nId;
    /**
    * 产线
    */
    @Schema(description = "产线")
    @TableField(value = "c_line_cd")
    private String cLineCd;
    /**
    * 生产订单号
    */
    @Schema(description = "生产订单号")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    /**
    * 产品名称
    */
    @Schema(description = "产品名称")
    @TableField(value = "c_pro_name")
    private String cProName;
    /**
    * 总成本
    */
    @Schema(description = "总成本")
    @TableField(value = "n_sum_cost")
    private BigDecimal nSumCost;
    /**
    * 人工成本
    */
    @Schema(description = "人工成本")
    @TableField(value = "n_labor_cost")
    private BigDecimal nLaborCost;
    /**
    * 材料成本
    */
    @Schema(description = "材料成本")
    @TableField(value = "n_material_cost")
    private BigDecimal nMaterialCost;
    /**
    * 制造成本
    */
    @Schema(description = "制造成本")
    @TableField(value = "n_manufact_cost")
    private BigDecimal nManufactCost;
    /**
    * 单位成本
    */
    @Schema(description = "单位成本")
    @TableField(value = "n_unit_cost")
    private BigDecimal nUnitCost;
    /**
    * 预计利润
    */
    @Schema(description = "预计利润")
    @TableField(value = "n_estimated_profit")
    private BigDecimal nEstimatedProfit;
    /**
    * 订单产量
    */
    @Schema(description = "订单产量")
    @TableField(value = "n_order_wgt")
    private BigDecimal nOrderWgt;
    /**
    * 计划周期
    */
    @Schema(description = "计划周期")
    @TableField(value = "c_plan_cycle")
    private BigDecimal cPlanCycle;
    /**
    * 成本科目名称
    */
    @Schema(description = "成本科目名称")
    @TableField(value = "c_cost_account_name")
    private String cCostAccountName;
    /**
    * 成本科目代码
    */
    @Schema(description = "成本科目代码")
    @TableField(value = "c_cost_account_code")
    private String cCostAccountCode;
    /**
    * 计划金额
    */
    @Schema(description = "计划金额")
    @TableField(value = "n_planned_amount")
    private BigDecimal nPlannedAmount;
    /**
    * 实际金额
    */
    @Schema(description = "实际金额")
    @TableField(value = "n_actual_amount")
    private BigDecimal nActualAmount;
    /**
    * 差异金额
    */
    @Schema(description = "差异金额")
    @TableField(value = "n_difference_amount")
    private BigDecimal nDifferenceAmount;
    /**
    * 差异率
    */
    @Schema(description = "差异率")
    @TableField(value = "n_difference_rate")
    private BigDecimal nDifferenceRate;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;


}