package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 坯料设计临时表
* <AUTHOR>
* @Date: 2025-07-21
* @Version 1.0
*/
@Data
@TableName("t_pss_final_design_slab_temp")
@Tag(name = "坯料设计临时表对象", description = "坯料设计临时表")
public class TPssFinalDesignSlabTemp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 序列号
    */
    @Schema(description = "序列号")
    @TableId
    private Long nId;
    /**
    * 设计钢坯id
    */
    @Schema(description = "设计钢坯id")
    @TableField(value = "c_slab_id")
    private String cSlabId;
    /**
    * 代表钢坯材质代码
    */
    @Schema(description = "代表钢坯材质代码")
    @TableField(value = "c_slab_mat_qul_id")
    private Long cSlabMatQulId;
    /**
    * 代表钢坯材质代码
    */
    @Schema(description = "代表钢坯材质代码")
    @TableField(value = "c_slab_mat_qul_cd")
    private String cSlabMatQulCd;
    /**
    * 代表钢板材质代码
    */
    @Schema(description = "代表钢板材质代码")
    @TableField(value = "c_slab_mat_qul_name")
    private String cSlabMatQulName;
    /**
    * 钢坯厚
    */
    @Schema(description = "钢坯厚")
    @TableField(value = "n_thk")
    private BigDecimal nThk;
    /**
    * 钢坯宽
    */
    @Schema(description = "钢坯宽")
    @TableField(value = "n_wth")
    private BigDecimal nWth;
    /**
    * 钢坯长
    */
    @Schema(description = "钢坯长")
    @TableField(value = "n_lth")
    private BigDecimal nLth;
    /**
    * 钢坯计算重量
    */
    @Schema(description = "钢坯计算重量")
    @TableField(value = "n_cal_wgt")
    private BigDecimal nCalWgt;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    /**
    * 设计成材率
    */
    @Schema(description = "设计成材率")
    @TableField(value = "n_deg_ratio")
    private BigDecimal nDegRatio;
    /**
    * 轧件长度
    */
    @Schema(description = "轧件长度")
    @TableField(value = "n_asroll_lth")
    private BigDecimal nAsrollLth;
    /**
    * 轧件厚度
    */
    @Schema(description = "轧件厚度")
    @TableField(value = "n_asroll_thk")
    private BigDecimal nAsrollThk;
    /**
    * 轧件宽度
    */
    @Schema(description = "轧件宽度")
    @TableField(value = "n_asroll_wth")
    private BigDecimal nAsrollWth;
    /**
    * 交货状态
    */
    @Schema(description = "交货状态")
    @TableField(value = "c_fpoststateid")
    private String cFpoststateid;
    /**
    * 探伤等级
    */
    @Schema(description = "探伤等级")
    @TableField(value = "c_ust_lev")
    private String cUstLev;
    /**
    * 探伤标准
    */
    @Schema(description = "探伤标准")
    @TableField(value = "c_ust_std")
    private String cUstStd;
    /**
    * 切头长
    */
    @Schema(description = "切头长")
    @TableField(value = "n_cut_head")
    private BigDecimal nCutHead;
    /**
    * 切尾长
    */
    @Schema(description = "切尾长")
    @TableField(value = "n_cut_tail")
    private BigDecimal nCutTail;
    /**
    * 长度切损
    */
    @Schema(description = "长度切损")
    @TableField(value = "n_cut_lth_lose")
    private BigDecimal nCutLthLose;
    /**
    * 长度余量
    */
    @Schema(description = "长度余量")
    @TableField(value = "n_cut_lth_rem")
    private BigDecimal nCutLthRem;
    /**
    * 宽度切损
    */
    @Schema(description = "宽度切损")
    @TableField(value = "n_cut_trim_lose")
    private BigDecimal nCutTrimLose;
    /**
    * 宽度余量
    */
    @Schema(description = "宽度余量")
    @TableField(value = "n_cut_wth_rem")
    private BigDecimal nCutWthRem;
    /**
    * 试样长度
    */
    @Schema(description = "试样长度")
    @TableField(value = "n_smp_lth")
    private BigDecimal nSmpLth;
    /**
    * 代表执行标准
    */
    @Schema(description = "代表执行标准")
    @TableField(value = "c_std_spec")
    private String cStdSpec;
    /**
    * 订单号
    */
    @Schema(description = "订单号")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    /**
    * 产品名称
    */
    @Schema(description = "产品名称")
    @TableField(value = "c_prod_name")
    private String cProdName;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    @TableField(value = "c_ctask_list_id")
    private String cCtaskListId;
    /**
    * 轧制计划号
    */
    @Schema(description = "轧制计划号")
    @TableField(value = "c_sch_id")
    private String cSchId;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time")
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    private Integer nDeleteMark;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}