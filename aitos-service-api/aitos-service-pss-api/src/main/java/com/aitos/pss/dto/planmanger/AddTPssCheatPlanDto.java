package com.aitos.pss.dto.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 炼钢计划任务调度
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
public class AddTPssCheatPlanDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "炉次编制号")
    private BigDecimal nHeatEdtSeq;

    @Schema(description = "计划炉次号")
    private String cPlanHeatId;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;

    @Schema(description = "质量编码id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    private String cMatQulCd;

    @Schema(description = "质量编码名称")
    private String cMatQulName;

    @Schema(description = "炼钢工艺流程")
    private String cPlRoute;

    @Schema(description = "浇铸厚度")
    private BigDecimal nCcmThk;

    @Schema(description = "浇铸宽度")
    private BigDecimal nCcmWth;

    @Schema(description = "浇铸时长")
    private Long nCcmTme;

    @Schema(description = "订单板坯张数")
    private Long nOrdSlabCnt;

    @Schema(description = "余材板坯张数")
    private Long nWooSlabCnt;

    @Schema(description = "余材炉次代码")
    private String cWooHeatFl;

    @Schema(description = "板坯块数")
    private Long nSlabCnt;

    @Schema(description = "计划出钢量")
    private BigDecimal nPreHeatWgt;

    @Schema(description = "炼钢作业期限")
    private String cSmsDuedatetime;

    @Schema(description = "浇次编制号")
    private String nCastEdtSeq;

    @Schema(description = "计划浇次号")
    private String cPlanCastId;

    @Schema(description = "浇次内顺序号")
    private Long nCastHeatSeq;

    @Schema(description = "浇次炉数")
    private Long nCastHeatCnt;

    @Schema(description = "转炉炉座号")
    private Long cLdWkstId;

    @Schema(description = "转炉炉座号")
    private String cLdWkst;

    @Schema(description = "转炉炉座号名称")
    private String cLdWkstName;

    @Schema(description = "下达时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreLdTime;

    @Schema(description = "计划出钢开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreLdStrTme;

    @Schema(description = "计划出钢结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreLdEndTme;

    @Schema(description = "计划第一次LF炉座号id")
    private Long cFirLfWkstId;

    @Schema(description = "计划第一次LF炉座号")
    private String cFirLfWkst;

    @Schema(description = "计划第一次LF炉座号名称")
    private String cFirLfWkstName;

    @Schema(description = "计划第一次 LF 开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirLfSttime;

    @Schema(description = "计划第一次 LF 结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirLfEndtime;

    @Schema(description = "计划第二次LF炉座号")
    private Long cSecLfWkstId;

    @Schema(description = "计划第二次LF炉座号")
    private String cSecLfWkst;

    @Schema(description = "计划第二次LF炉座号")
    private String cSecLfWkstName;

    @Schema(description = "计划第二次 LF 开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecLfSttime;

    @Schema(description = "计划第二次 LF 结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecLfEndtime;

    @Schema(description = "计划第一次RH炉座号")
    private Long cFirVdWkstId;

    @Schema(description = "计划第一次RH炉座号")
    private String cFirVdWkst;

    @Schema(description = "计划第一次RH炉座号名称")
    private String cFirVdWkstName;

    @Schema(description = "计划第一次RH开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirVdSttime;

    @Schema(description = "计划第一次RH结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirVdEndtime;

    @Schema(description = "计划第二次RH炉座号id")
    private Long cSecVdWkstId;

    @Schema(description = "计划第二次RH炉座号")
    private String cSecVdWkst;

    @Schema(description = "计划第二次RH炉座号名称")
    private String cSecVdWkstName;

    @Schema(description = "计划第二次RH 开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecVdSttime;

    @Schema(description = "计划第二次RH结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecVdEndtime;

    @Schema(description = "大包开浇时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPlanLadleOpen;

    @Schema(description = "连铸机号")
    private String cCcmWkst;

    @Schema(description = "计划浇铸开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreCcmStrTme;

    @Schema(description = "计划浇铸结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreCcmEndTme;

    @Schema(description = "混炉标志")
    private String cMstlgrdFl;

    @Schema(description = "状态")
    private String cStatus;

    @Schema(description = "实绩炉次号")
    private String cHeatId;

    @Schema(description = "实绩钢种")
    private String cActStlGrdCd;

    @Schema(description = "实绩钢种描述/名称")
    private String cActStlGrdDesc;

    @Schema(description = "实绩材质")
    private String cActMatQulCd;

    @Schema(description = "连铸机号")
    private String cCastId;

    @Schema(description = "读取标志 N-新计划")
    private String cHuntieWks;

    @Schema(description = "米重")
    private String cYuchuliWks;

    @Schema(description = "当前工位")
    private String cCurrStationCd;

    @Schema(description = "当前事件（P0210）")
    private String cCurrEventCd;

    @Schema(description = "前一事件（P0210）")
    private String cPrevEventCd;

    @Schema(description = "坯料长度")
    private BigDecimal nSlabLen;

    @Schema(description = "任务单号")
    private String cTaskListId;

    @Schema(description = "转炉时间是否改变")
    private Long nIfChangeTime;

    @Schema(description = "精炼时间是否改变")
    private Long nIfChangeTimeLf;

    @Schema(description = "VOD时间是否改变")
    private Long nIfChangeTimeVod;

    @Schema(description = "连铸时间是否改变")
    private Long nIfChangeTimeCcm;

    @Schema(description = "产品直径")
    private BigDecimal nProdThk;

    @Schema(description = "产品长度")
    private BigDecimal nProdLen;

    @Schema(description = "电炉状态")
    private String cLdStatus;

    @Schema(description = "精炼状态")
    private String cLfStatus;

    @Schema(description = "VD状态")
    private String cVdStatus;

    @Schema(description = "连铸状态")
    private String cCcmStatus;

    @Schema(description = "电炉时间")
    private String cLdTime;

    @Schema(description = "精炼时间")
    private String cLfTime;

    @Schema(description = "VD时间")
    private String cVdTime;

    @Schema(description = "连铸时间")
    private String cCcmTime;

    @Schema(description = "连铸开始时间")
    private String cCcmStrTime;

    @Schema(description = "VD开始时间")
    private String cVdStrTime;

    @Schema(description = "精炼开始时间")
    private String cLfStrTime;

    @Schema(description = "电炉开始时间")
    private String cLdStrTime;

    @Schema(description = "炉次制造命令处理区分")
    private BigDecimal nDealFlag;

    @Schema(description = "制造命令号")
    private String cPono;

    @Schema(description = "CAST_LOT分割号")
    private String cCastLotDivNo;

    @Schema(description = "连铸机类型 PM44")
    private String cCcType;

    @Schema(description = "甘特图显示标志")
    private String cRestrandFlg;

    @Schema(description = "冶炼模式")
    private String cSmeltMode;

    @Schema(description = "材料去向 PM16")
    private String cDest;

    @Schema(description = "计划日期")
    private LocalDateTime cPlanDatetime;

    @Schema(description = "客户编码")
    private String cCustomerCd;

    @Schema(description = "客户名称")
    private String cCustomerName;

    @Schema(description = "浇铸长度")
    private String cCcmLen;

    @Schema(description = "钢水回炉目标炉号(改为支数确认标记)")
    private String cReturnHeatId;

    @Schema(description = "钢水回炉去向(改为代表样确认标记)")
    private String cReturnDirection;

    @Schema(description = "材料去向2")
    private String cDest2;

    @Schema(description = "定重")
    private String cCcmWgt;

    @Schema(description = "钢包号")
    private String cLdId;

    @Schema(description = "是否变定尺")
    private String cLenIsChange;

    @Schema(description = "是否变钢种标记")
    private String cStlGrdChange;

    @Schema(description = "月生产的第几炉")
    private String cMonProSeq;

    @Schema(description = "转炉作业时长")
    private Long nLdTme;

    @Schema(description = "是否浇次首炉")
    private String cIsFirstPlan;

    @Schema(description = "生产班次")
    private String cProdShift;

    @Schema(description = "生产班组")
    private String cProdGroup;

    @Schema(description = "转炉标准时长")
    private String cLdStdtime;

    @Schema(description = "前一炉转炉结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime stBeforeLdendtime;
    
    @Schema(description = "连铸标准时长")
    private String cCcmStdtime;
    
    @Schema(description = "前一炉连铸结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtBeforeCcmendtime;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
