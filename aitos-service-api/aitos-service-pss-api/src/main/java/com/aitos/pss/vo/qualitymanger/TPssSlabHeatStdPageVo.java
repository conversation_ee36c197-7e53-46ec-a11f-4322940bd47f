package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-21
* @Version 1.0
*/
@Data
public class TPssSlabHeatStdPageVo {


    @ExcelIgnore
    @Schema(description = "主键")
    private String nId;

    @Schema(description = "质量编码id")
    private Long cQualityId;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量编码")
    @Schema(description = "质量编码")
    private String cQualityCode;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量编码名称")
    @Schema(description = "质量编码名称")
    private String cQualityCodeName;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种代码")
    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种名称/描述")
    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("坯料厚度")
    @Schema(description = "坯料厚度")
    private BigDecimal nSlabThkMin;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("坯料厚度")
    @Schema(description = "坯料厚度")
    private BigDecimal nSlabThkMax;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("出炉目标温度℃")
    @Schema(description = "出炉目标温度℃")
    private BigDecimal nFoutAimTemp;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("出炉温度上限℃")
    @Schema(description = "出炉温度上限℃")
    private BigDecimal nFoutMaxTemp;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("出炉温度下限℃")
    @Schema(description = "出炉温度下限℃")
    private BigDecimal nFoutMinTemp;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("板坯表面/中心目标温度差℃")
    @Schema(description = "板坯表面/中心目标温度差℃")
    private BigDecimal nSlabScTempDiff;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("板坯头尾目标温度差℃")
    @Schema(description = "板坯头尾目标温度差℃")
    private BigDecimal nSlabHtTempDiff;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("加热一段最低温度")
    @Schema(description = "加热一段最低温度")
    private BigDecimal cReserver1;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("加热一段最高温度")
    @Schema(description = "加热一段最高温度")
    private BigDecimal cReserver2;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("加热二段最低温度")
    @Schema(description = "加热二段最低温度")
    private BigDecimal cReserver3;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("加热二段最高温度")
    @Schema(description = "加热二段最高温度")
    private BigDecimal cReserver4;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("加热段3最低温度")
    @Schema(description = "加热段3最低温度")
    private BigDecimal cReserver5;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("加热段3最高温度")
    @Schema(description = "加热段3最高温度")
    private BigDecimal cReserver6;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("加热段4最低温度")
    @Schema(description = "加热段4最低温度")
    private BigDecimal cReserver7;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("加热段4最高温度")
    @Schema(description = "加热段4最高温度")
    private BigDecimal cReserver8;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("目标加热时长")
    @Schema(description = "目标加热时长")
    private BigDecimal cReserver9;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;


    @ContentStyle(dataFormat = 49)
    @ExcelProperty("创建时间")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;


    @ContentStyle(dataFormat = 49)
    @ExcelProperty("修改时间")
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
