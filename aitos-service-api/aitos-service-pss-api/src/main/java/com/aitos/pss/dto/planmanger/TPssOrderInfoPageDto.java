package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssOrderInfoPageDto extends PageInput {

    
    @Schema(description = "产线id（P0001）")
    private Long cProductLineId;
    
    @Schema(description = "订单号（订单类型+年月日+3位顺序号）")
    private String cOrderNo;
    
    @Schema(description = "钢种")
    private String cStlGrdCd;
    
    @Schema(description = "订单类型（P0004）")
    private String cOrderType;

    @Schema(description = "销售合同号")
    private String cContractNo;

    @Schema(description = "订单状态代码（P0020）")
    private String cOrderState;

    @Schema(description = "产品类型（Y0006）")
    private String cProductType;
    
    @Schema(description = "质量编码id")
    private Long cQualId;
    
    @Schema(description = "综合生产计划号")
    private String cAggregatePlanId;
    
    @Schema(description = "成品规格")
    private String cMatItem;
    
    @Schema(description = "生产任务单编号")
    private String cProductTaskListId;
    
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;

}
