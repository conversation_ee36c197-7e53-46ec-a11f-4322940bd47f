package com.aitos.pss.dto.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 原辅料库存信息查询
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
public class AddTPsssYMatInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 物料编码
    */
    @NotBlank(message = "物料编码是必填字段")
    @Schema(description = "物料编码")
    private String cMaterialCode;
    /**
    * 物料名称
    */
    @NotBlank(message = "物料名称是必填字段")
    @Schema(description = "物料名称")
    private String cMaterialName;
    /**
    * 物料类型(raw:原辅料,alloy:合金,scrap:废钢)
    */
    @NotBlank(message = "物料类型是必填字段")
    @Schema(description = "物料类型(raw:原辅料,alloy:合金,scrap:废钢)")
    private String cMaterialType;
    /**
    * 规格型号
    */
    @Schema(description = "规格型号")
    private String cSpecification;
    /**
    * 单位
    */
    @NotBlank(message = "单位是必填字段")
    @Schema(description = "单位")
    private String cUnit;
    /**
    * 库存上限
    */
    @Schema(description = "库存上限")
    private BigDecimal nMaxStock;
    /**
    * 库存下限
    */
    @Schema(description = "库存下限")
    private BigDecimal nMinStock;
    /**
    * 当前库存
    */
    @Schema(description = "当前库存")
    private BigDecimal nCurrentStock;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
