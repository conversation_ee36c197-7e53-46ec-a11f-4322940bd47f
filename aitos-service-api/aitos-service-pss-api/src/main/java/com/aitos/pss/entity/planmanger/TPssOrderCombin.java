package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 合并计划表
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
@TableName("t_pss_order_combin")
@Tag(name = "合并计划表对象", description = "合并计划表")
public class TPssOrderCombin implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "序列号")
    @TableId
    private Long nId;
    /**
    * 合并计划号（产品类型+年月+四位流水）
    */
    @Schema(description = "合并计划号（产品类型+年月+四位流水）")
    @TableField(value = "c_sales_plan_id")
    private String cSalesPlanId;
    /**
    * 计划内顺序号
    */
    @Schema(description = "计划内顺序号")
    @TableField(value = "c_plan_sn")
    private BigDecimal cPlanSn;
    /**
    * 是否紧急生产订单（0：否 1：是）
    */
    @Schema(description = "是否紧急生产订单（0：否 1：是）")
    @TableField(value = "c_exg_prod_lot_fl")
    private String cExgProdLotFl;
    /**
    * 客户代码
    */
    @Schema(description = "客户代码")
    @TableField(value = "c_customer_cd")
    private String cCustomerCd;

    @Schema(description = "产线id")
    @TableField(value = "c_pro_line")
    private Long cProLine;

    @Schema(description = "产线code")
    @TableField(value = "c_pro_line_code")
    private String cProLineCode;

    @Schema(description = "产线name")
    @TableField(value = "c_pro_line_name")
    private String cProLineName;

    @Schema(description = "产品类型（Y0006）")
    @TableField(value = "c_product_type")
    private String cProductType;

    @Schema(description = "质量编码id")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;

    @Schema(description = "质量编码name")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    /**
    * 标准号
    */
    @Schema(description = "标准号")
    @TableField(value = "c_std_spec")
    private String cStdSpec;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;

    @Schema(description = "存货代码id")
    @TableField(value = "c_inventory_id")
    private String cInventoryId;

    @Schema(description = "存货代码")
    @TableField(value = "c_inventory_cd")
    private String cInventoryCd;

    @Schema(description = "存货代码名称")
    @TableField(value = "c_inventory_name")
    private String cInventoryName;
    /**
    * 定尺类型代码（P0013)
    */
    @Schema(description = "定尺类型代码（P0013)")
    @TableField(value = "c_size_property")
    private String cSizeProperty;
    /**
    * 直径
    */
    @Schema(description = "直径")
    @TableField(value = "n_prod_dia")
    private BigDecimal nProdDia;
    /**
    * 厚度(方钢)
    */
    @Schema(description = "厚度(方钢)")
    @TableField(value = "n_prod_thk")
    private BigDecimal nProdThk;
    /**
    * 宽度（方钢）
    */
    @Schema(description = "宽度（方钢）")
    @TableField(value = "n_prod_wid")
    private BigDecimal nProdWid;
    /**
    * 目标长度
    */
    @Schema(description = "目标长度")
    @TableField(value = "n_prod_lenth")
    private BigDecimal nProdLenth;
    /**
    * 订货重量
    */
    @Schema(description = "订货重量")
    @TableField(value = "n_order_wgt")
    private BigDecimal nOrderWgt;
    /**
    * 重量单位代码（P0023）
    */
    @Schema(description = "重量单位代码（P0023）")
    @TableField(value = "c_wgt_unit")
    private String cWgtUnit;
    /**
    * 交货起始日
    */
    @Schema(description = "交货起始日")
    @TableField(value = "c_del_datetime_from")
    private String cDelDatetimeFrom;
    /**
    * 交货终止日
    */
    @Schema(description = "交货终止日")
    @TableField(value = "c_del_datetime_to")
    private String cDelDatetimeTo;
    /**
    * 计划状态
    */
    @Schema(description = "计划状态")
    @TableField(value = "c_combin_state")
    private String cCombinState;
    /**
    * 综合生产计划号
    */
    @Schema(description = "综合生产计划号")
    @TableField(value = "c_aggregate_plan_id")
    private String cAggregatePlanId;
    /**
    * 综合生产计划序号
    */
    @Schema(description = "综合生产计划序号")
    @TableField(value = "n_aggregate_plan_sn")
    private BigDecimal nAggregatePlanSn;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    @TableField(value = "c_product_task_list_id")
    private String cProductTaskListId;
    /**
    * 计划有效期启
    */
    @Schema(description = "计划有效期启")
    @TableField(value = "c_plan_datetime_from")
    private String cPlanDatetimeFrom;
    /**
    * 计划有效期止
    */
    @Schema(description = "计划有效期止")
    @TableField(value = "c_plan_datetime_to")
    private String cPlanDatetimeTo;
    /**
    * 订单号（备用）
    */
    @Schema(description = "订单号（备用）")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    /**
    * 备注
    */
    @Schema(description = "备注")
    @TableField(value = "c_order_explain")
    private String cOrderExplain;
    /**
    * 操作记录
    */
    @Schema(description = "操作记录")
    @TableField(value = "c_operation_note")
    private String cOperationNote;
    /**
    * 是否追加计划
    */
    @Schema(description = "是否追加计划")
    @TableField(value = "c_is_add_plan")
    private String cIsAddPlan;
    /**
    * 上一个状态
    */
    @Schema(description = "上一个状态")
    @TableField(value = "c_old_state")
    private String cOldState;
    /**
    * 状态变更时间
    */
    @Schema(description = "状态变更时间")
    @TableField(value = "dt_state_change_time")
    private LocalDateTime dtStateChangeTime;
    /**
    * 部门编号
    */
    @Schema(description = "部门编号")
    @TableField(value = "c_dept_no")
    private String cDeptNo;
    /**
    * 上一次的操作记录
    */
    @Schema(description = "上一次的操作记录")
    @TableField(value = "c_old_operation_note")
    private String cOldOperationNote;
    /**
    * 订单客户类型
    */
    @Schema(description = "订单客户类型")
    @TableField(value = "c_order_cust_type")
    private String cOrderCustType;
    /**
    * 旧编码
    */
    @Schema(description = "旧编码")
    @TableField(value = "c_oldcode")
    private String cOldcode;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "n_enabled_mark")
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}