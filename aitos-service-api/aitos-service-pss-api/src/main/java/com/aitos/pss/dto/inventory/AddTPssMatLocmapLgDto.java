package com.aitos.pss.dto.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 铸坯库位显示
* <AUTHOR>
* @Date: 2025-07-16
* @Version 1.0
*/
@Data
public class AddTPssMatLocmapLgDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 库位号
    */
    @Schema(description = "库位号")
    private String cLoc;
    /**
    * 层号/入库顺序号
    */
    @Schema(description = "层号/入库顺序号")
    private String cLocLvl;
    /**
    * 产线id
    */
    @Schema(description = "产线id")
    private Long cLineId;
    /**
    * 产线code
    */
    @Schema(description = "产线code")
    private String cLineCode;
    /**
    * 产线name
    */
    @Schema(description = "产线name")
    private String cLineName;
    /**
    * 仓库id
    */
    @Schema(description = "仓库id")
    private Long cYardId;
    /**
    * 仓库代码
    */
    @Schema(description = "仓库代码")
    private String cYardCode;
    /**
    * 仓库name
    */
    @Schema(description = "仓库name")
    private String cYardName;
    /**
    * 件次号
    */
    @Schema(description = "件次号")
    private String cMatId;
    /**
    * 数量
    */
    @Schema(description = "数量")
    private Integer nMatCnt;
    /**
    * 炉号
    */
    @Schema(description = "炉号")
    private String cLotId;
    /**
    * 轧制批号
    */
    @Schema(description = "轧制批号")
    private String cMatLotId;
    /**
    * 物料类型
    */
    @Schema(description = "物料类型")
    private String cMatType;
    /**
    * 物料id
    */
    @Schema(description = "物料id")
    private Long cMateId;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMateCode;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMateName;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    /**
    * 钢种描述
    */
    @Schema(description = "钢种描述")
    private String cStlGrdDesc;
    /**
    * 质量编码id
    */
    @Schema(description = "质量编码id")
    private Long cMatQulId;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    private String cMatQulCode;
    /**
    * 质量编码name
    */
    @Schema(description = "质量编码name")
    private String cMatQulName;
    /**
    * 规格
    */
    @Schema(description = "规格")
    private String cMatItem;
    /**
    * 厚度
    */
    @Schema(description = "厚度")
    private BigDecimal nMatThk;
    /**
    * 宽度
    */
    @Schema(description = "宽度")
    private BigDecimal nMatWth;
    /**
    * 直径
    */
    @Schema(description = "直径")
    private BigDecimal nMatDia;
    /**
    * 长度
    */
    @Schema(description = "长度")
    private BigDecimal nMatLen;
    /**
    * 实际重量
    */
    @Schema(description = "实际重量")
    private BigDecimal nMatWgt;
    /**
    * 计算重量
    */
    @Schema(description = "计算重量")
    private BigDecimal nMatWgtCal;
    /**
    * 订单材/余材
    */
    @Schema(description = "订单材/余材")
    private String cOrdFl;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    private String cTaskListId;
    /**
    * 订单号
    */
    @Schema(description = "订单号")
    private String cOrderNo;
    /**
    * 销售订单号
    */
    @Schema(description = "销售订单号")
    private String cSaleNo;
    /**
    * 销售订单行号
    */
    @Schema(description = "销售订单行号")
    private String cSaleSn;
    /**
    * 下游订单号
    */
    @Schema(description = "下游订单号")
    private String cOrderIdFinal;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 去向（产线号）
    */
    @Schema(description = "去向（产线号）")
    private String cPreOut;
    /**
    * 生产时间
    */
    @Schema(description = "生产时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtProdTime;
    /**
    * 终判等级
    */
    @Schema(description = "终判等级")
    private String cProdGrd;
    /**
    * 盘库标识
    */
    @Schema(description = "盘库标识")
    private String cCheckerFl;
    /**
    * 装车车号
    */
    @Schema(description = "装车车号")
    private String cCarNo;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
