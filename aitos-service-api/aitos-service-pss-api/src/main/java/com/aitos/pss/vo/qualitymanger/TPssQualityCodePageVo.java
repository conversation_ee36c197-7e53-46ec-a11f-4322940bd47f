package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@Data
public class TPssQualityCodePageVo {
    @ExcelIgnore
    @Schema(description = "主键")
    private String nId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量编码")
    @Schema(description = "质量代码;质量编码")
    private String cQualCode;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种代码")
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种描述")
    @Schema(description = "钢种描述")
    private String cStlGrdDesc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("名称")
    @Schema(description = "名称")
    private String cQualCodeName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("简称")
    @Schema(description = "简称")
    private String cQualCodeAbb;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("描述")
    @Schema(description = "描述;")
    private String cDescribe;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("标准名称（中）")
    @Schema(description = "标准名称（中）")
    private String cStdNameChi;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("标准名称（英）")
    @Schema(description = "标准名称（英）")
    private String cStdNameEng;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("执行标准")
    @Schema(description = "执行标准")
    private String cOpStdName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("成品物料名称")
    @Schema(description = "成品物料名称")
    private String cMaterialName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("成品物料编码")
    @Schema(description = "成品物料编码")
    private String cMaterialCode;

    @Schema(description = "成品物料Id")
    private Long cMateId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("状态")
    @Schema(description = "状态（新建/已转换）")
    @Trans(type = TransType.DIC, id = "1935576632394444801")
    private String cStatus;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
