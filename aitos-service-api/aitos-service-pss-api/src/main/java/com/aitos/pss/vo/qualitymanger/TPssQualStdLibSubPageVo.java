package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-07-09
* @Version 1.0
*/
@Data
public class TPssQualStdLibSubPageVo {

    /**
    * 主键
    */
    @Schema(description = "主键")
    private String nId;

    /**
     * 质检标准编码
     */
    @Schema(description = "质检标准编码")
    @TableField(value = "c_Stdcode")
    private String cStdcode;

    @Schema(description = "质检标准名称")
    @TableField(value = "c_stdname")
    private String cStdname;

    @Schema(description = "质检项目id")
    private Long cQualityStandardLibItemId;

    /**
    * 质检项目编码
    */
    @Schema(description = "质检项目编码")
    private String cQualityStandardLibItemCode;
    /**
    * 质检项目名称
    */
    @Schema(description = "质检项目名称")
    private String cQualityStandardLibItemName;
    /**
     * 计量单位
     */
    @Schema(description = "计量单位id")
    @TableField(value = "c_measures_unit")
    private Long cMeasuresUnit;

    @Schema(description = "计量单位code")
    @TableField(value = "c_measures_unit_code")
    private String cMeasuresUnitCode;

    @Schema(description = "计量单位name")
    @TableField(value = "c_measures_unit_name")
    private String cMeasuresUnitName;
    /**
    * 值类型
    */
    @Schema(description = "值类型")
    @Trans(type = TransType.DIC, id = "1942950813472743426")
    private String cValueType;
    /**
    * 试验次数
    */
    @Schema(description = "试验次数")
    private Integer nTestcount;
    /**
    * 项目属性
    */
    @Schema(description = "项目属性")
    @Trans(type = TransType.DIC, id = "1942951282773417986")
    private String cProjectAttributeCode;
    /**
    * 数据精度
    */
    @Schema(description = "数据精度")
    private String nPrecision;
    /**
    * 下限比较符
    */
    @Schema(description = "下限比较符")
    @Trans(type = TransType.DIC, id = "1925391187640389633")
    private String cLowersymbol;
    /**
    * 下限值
    */
    @Schema(description = "下限值")
    private BigDecimal nLowervalue;
    /**
    * 上限比较符
    */
    @Schema(description = "上限比较符")
    @Trans(type = TransType.DIC, id = "1925391187640389633")
    private String cUppersymbol;
    /**
    * 上限值
    */
    @Schema(description = "上限值")
    private BigDecimal nUppervalue;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String nRemark;
    /**
    * 
    */
    @Schema(description = "")
    private Integer nEnabledMark;
    /**
    * 
    */
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 
    */
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
