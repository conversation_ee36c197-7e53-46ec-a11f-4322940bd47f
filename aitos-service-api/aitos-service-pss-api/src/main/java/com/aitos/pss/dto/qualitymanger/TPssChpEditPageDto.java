package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssChpEditPageDto extends PageInput {

    /**
    * 序列号
    */
    @Schema(description = "序列号")
    private Long nId;
    /**
    * 计划炉次号
    */
    @Schema(description = "计划炉次号")
    private String cPlanHeatId;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    private String cMatQulCd;
    /**
    * 浇铸厚度
    */
    @Schema(description = "浇铸厚度")
    private BigDecimal nCcmThk;
    /**
    * 浇铸时长
    */
    @Schema(description = "浇铸时长")
    private Long nCcmTme;
    /**
    * 余材板坯张数
    */
    @Schema(description = "余材板坯张数")
    private Long nWooSlabCnt;
    /**
    * 板坯块数
    */
    @Schema(description = "板坯块数")
    private Long nSlabCnt;
    /**
    * 炼钢作业期限
    */
    @Schema(description = "炼钢作业期限")
    private String cSmsDuedatetime;
    /**
    * 计划浇次号
    */
    @Schema(description = "计划浇次号")
    private String cPlanCastId;
    /**
    * 浇次炉数
    */
    @Schema(description = "浇次炉数")
    private Long nCastHeatCnt;
    /**
    * 计划冶炼开始时间字段开始时间
    */
    @Schema(description = "计划冶炼开始时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreLdTimeStart;
    /**
    * 计划冶炼开始时间字段结束时间
    */
    @Schema(description = "计划冶炼开始时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreLdTimeEnd;
    /**
    * 计划出钢结束时间字段开始时间
    */
    @Schema(description = "计划出钢结束时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreLdEndTmeStart;
    /**
    * 计划出钢结束时间字段结束时间
    */
    @Schema(description = "计划出钢结束时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreLdEndTmeEnd;
    /**
    * 计划第一次 LF 开始时间字段开始时间
    */
    @Schema(description = "计划第一次 LF 开始时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirLfSttimeStart;
    /**
    * 计划第一次 LF 开始时间字段结束时间
    */
    @Schema(description = "计划第一次 LF 开始时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirLfSttimeEnd;
    /**
    * 计划第二次LF炉座号
    */
    @Schema(description = "计划第二次LF炉座号")
    private String cSecLfWkst;
    /**
    * 计划第二次 LF 结束时间字段开始时间
    */
    @Schema(description = "计划第二次 LF 结束时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecLfEndtimeStart;
    /**
    * 计划第二次 LF 结束时间字段结束时间
    */
    @Schema(description = "计划第二次 LF 结束时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecLfEndtimeEnd;
    /**
    * 计划第一次VD开始时间字段开始时间
    */
    @Schema(description = "计划第一次VD开始时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirVdSttimeStart;
    /**
    * 计划第一次VD开始时间字段结束时间
    */
    @Schema(description = "计划第一次VD开始时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirVdSttimeEnd;
    /**
    * 计划第二次VD炉座号
    */
    @Schema(description = "计划第二次VD炉座号")
    private String cSecVdWkst;
    /**
    * 计划第二次VD结束时间字段开始时间
    */
    @Schema(description = "计划第二次VD结束时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecVdEndtimeStart;
    /**
    * 计划第二次VD结束时间字段结束时间
    */
    @Schema(description = "计划第二次VD结束时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecVdEndtimeEnd;
    /**
    * 连铸机号
    */
    @Schema(description = "连铸机号")
    private String cCcmWkst;
    /**
    * 计划浇铸结束时间字段开始时间
    */
    @Schema(description = "计划浇铸结束时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreCcmEndTmeStart;
    /**
    * 计划浇铸结束时间字段结束时间
    */
    @Schema(description = "计划浇铸结束时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreCcmEndTmeEnd;
    /**
    * 变更程序ID
    */
    @Schema(description = "变更程序ID")
    private String cUpdPgmid;
    /**
    * 炉号
    */
    @Schema(description = "炉号")
    private String cHeatId;
    /**
    * 实际质量编码
    */
    @Schema(description = "实际质量编码")
    private String cActMatQulCd;
    /**
    * 混铁炉座次
    */
    @Schema(description = "混铁炉座次")
    private String cHuntieWks;
    /**
    * 当前工位
    */
    @Schema(description = "当前工位")
    private String cCurrStationCd;
    /**
    * 上一事件
    */
    @Schema(description = "上一事件")
    private String cPrevEventCd;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    private String taskListId;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    private Long nModifyUserId;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    private Integer nDeleteMark;
    /**
    * 炉次编制号
    */
    @Schema(description = "炉次编制号")
    private BigDecimal nHeatEdtSeq;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    /**
    * 炼钢工艺流程
    */
    @Schema(description = "炼钢工艺流程")
    private String cPlRoute;
    /**
    * 浇铸宽度
    */
    @Schema(description = "浇铸宽度")
    private BigDecimal nCcmWth;
    /**
    * 订单板坯张数
    */
    @Schema(description = "订单板坯张数")
    private Long nOrdSlabCnt;
    /**
    * 余材炉次代码
    */
    @Schema(description = "余材炉次代码")
    private String cWooHeatFl;
    /**
    * 计划出钢量
    */
    @Schema(description = "计划出钢量")
    private BigDecimal nPreHeatWgt;
    /**
    * 浇次编制号
    */
    @Schema(description = "浇次编制号")
    private String nCastEdtSeq;
    /**
    * 浇次内顺序号
    */
    @Schema(description = "浇次内顺序号")
    private Long nCastHeatSeq;
    /**
    * 转炉炉座号
    */
    @Schema(description = "转炉炉座号")
    private String cLdWkst;
    /**
    * 计划出钢开始时间字段开始时间
    */
    @Schema(description = "计划出钢开始时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreLdStrTmeStart;
    /**
    * 计划出钢开始时间字段结束时间
    */
    @Schema(description = "计划出钢开始时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreLdStrTmeEnd;
    /**
    * 计划第一次LF炉座号
    */
    @Schema(description = "计划第一次LF炉座号")
    private String cFirLfWkst;
    /**
    * 计划第一次 LF 结束时间字段开始时间
    */
    @Schema(description = "计划第一次 LF 结束时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirLfEndtimeStart;
    /**
    * 计划第一次 LF 结束时间字段结束时间
    */
    @Schema(description = "计划第一次 LF 结束时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirLfEndtimeEnd;
    /**
    * 计划第二次 LF 开始时间字段开始时间
    */
    @Schema(description = "计划第二次 LF 开始时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecLfSttimeStart;
    /**
    * 计划第二次 LF 开始时间字段结束时间
    */
    @Schema(description = "计划第二次 LF 开始时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecLfSttimeEnd;
    /**
    * 计划第一次VOD炉座号
    */
    @Schema(description = "计划第一次VOD炉座号")
    private String cFirVdWkst;
    /**
    * 计划第一次VD结束时间字段开始时间
    */
    @Schema(description = "计划第一次VD结束时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirVdEndtimeStart;
    /**
    * 计划第一次VD结束时间字段结束时间
    */
    @Schema(description = "计划第一次VD结束时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFirVdEndtimeEnd;
    /**
    * 计划第二次VD 开始时间字段开始时间
    */
    @Schema(description = "计划第二次VD 开始时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecVdSttimeStart;
    /**
    * 计划第二次VD 开始时间字段结束时间
    */
    @Schema(description = "计划第二次VD 开始时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSecVdSttimeEnd;
    /**
    * 大包开浇时间字段开始时间
    */
    @Schema(description = "大包开浇时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPlanLadleOpenStart;
    /**
    * 大包开浇时间字段结束时间
    */
    @Schema(description = "大包开浇时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPlanLadleOpenEnd;
    /**
    * 计划浇铸开始时间字段开始时间
    */
    @Schema(description = "计划浇铸开始时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreCcmStrTmeStart;
    /**
    * 计划浇铸开始时间字段结束时间
    */
    @Schema(description = "计划浇铸开始时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPreCcmStrTmeEnd;
    /**
    * 混炉标志
    */
    @Schema(description = "混炉标志")
    private String cMstlgrdFl;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 实际钢种代码
    */
    @Schema(description = "实际钢种代码")
    private String cActStlGrdCd;
    /**
    * 连铸机号
    */
    @Schema(description = "连铸机号")
    private String cCastId;
    /**
    * 预处理座次
    */
    @Schema(description = "预处理座次")
    private String cYuchuliWks;
    /**
    * 当前事件
    */
    @Schema(description = "当前事件")
    private String cCurrEventCd;
    /**
    * 坯料长度
    */
    @Schema(description = "坯料长度")
    private BigDecimal cSlabLen;
    /**
    * 
    */
    @Schema(description = "")
    private Integer nEnabledMark;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 修改时间字段开始时间
    */
    @Schema(description = "修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 修改时间字段结束时间
    */
    @Schema(description = "修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
