package com.aitos.pss.dto.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 生产任务单管理
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
public class AddTPssAggregatePlanDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "综合生产计划编号(AP+合并计划号)")
    private String cAggregatePlanId;

    @Schema(description = "是否紧急")
    private String cExgProdLotFl;

    @Schema(description = "任务单号")
    private String cProductTaskListId;

    @Schema(description = "产品类型(P0003)")
    private String cProductType;

    @Schema(description = "质量编码id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    private String cMatQulCode;

    @Schema(description = "质量编码name")
    private String cMatQulName;

    @Schema(description = "定尺类型")
    private String cSizeProperty;

    @Schema(description = "产品直径")
    private BigDecimal nProdDia;

    @Schema(description = "产品厚度")
    private BigDecimal nProdThk;

    @Schema(description = "产品宽度")
    private BigDecimal nProdWid;

    @Schema(description = "产品长度（计划）")
    private BigDecimal nProdLenth;

    @Schema(description = "计划产量")
    private BigDecimal nPlanWgt;

    @Schema(description = "重量单位")
    private String cWgtUnit;

    @Schema(description = "交货期起始")
    private String cDelDatetimeFrom;

    @Schema(description = "交货期结束")
    private String cDelDatetimeEnd;

    @Schema(description = "客户编码")
    private String cCustomerCd;

    @Schema(description = "计划状态")
    private String cPlanState;

    @NotBlank(message = "计划坯料厚度不能为空")
    @Schema(description = "计划坯料厚度")
    private BigDecimal nPlanSlabThk;

    @NotBlank(message = "计划坯料宽度不能为空")
    @Schema(description = "计划坯料宽度")
    private BigDecimal nPlanSlabWid;

    @NotBlank(message = "计划坯料长不能为空")
    @Schema(description = "计划坯料长")
    private BigDecimal nPlanSlabLen;

    @Schema(description = "计划产线id(轧钢)(P0001)")
    private Long cPlanLineId;

    @Schema(description = "计划产线编码(轧钢)(P0001)")
    private String cPlanLineCd;

    @Schema(description = "计划产线名称(轧钢)(P0001)")
    private String cPlanLineName;

    @Schema(description = "计划产线id（炼钢）默认一炼钢(P0001)")
    private Long cPlanSteelLineId;

    @Schema(description = "计划产线（炼钢）默认一炼钢(P0001)")
    private String cPlanSteelLineCd;

    @Schema(description = "计划产线名称（炼钢）默认一炼钢(P0001)")
    private String cPlanSteelLineName;

    @Schema(description = "标准号")
    private String cStdSpec;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;

    @Schema(description = "存货代码id")
    private Long cInventoryId;

    @Schema(description = "存货代码")
    private String cInventoryCd;

    @Schema(description = "存货代码名称")
    private String cInventoryName;

    @Schema(description = "精整路径")
    private String cFinishMachPhCd;

    @Schema(description = "计划起始日期")
    private String cPlanDatetimeTo;

    @Schema(description = "计划终止日期")
    private String cPlanDatetimeFrom;

    @Schema(description = "计划坯料总数")
    private BigDecimal nPlanSlabCount;

    @Schema(description = "计划炉数")
    private BigDecimal nPlanFuranceCount;

    @Schema(description = "计划坯料单米重")
    private BigDecimal nPlanSlabSmw;

    @Schema(description = "计划成才率")
    private BigDecimal nPlanYeild;

    @Schema(description = "计划坯料总重量")
    private BigDecimal nPlanSlabWgtCount;

    @Schema(description = "销售计划号")
    private String cSalesPlanId;

    @Schema(description = "销售计划顺序号")
    private String cSalesPlanSn;

    @Schema(description = "计划转炉冶炼量")
    private BigDecimal nPlanSteelWgt;

    @Schema(description = "上一个状态")
    private String cOldStatus;

    @Schema(description = "操作记录")
    private String cOperationNote;

    @Schema(description = "是否追加计划")
    private String cIsAddPlan;

    @Schema(description = "订单备注")
    private String cOrderExplain;

    @Schema(description = "状态变更时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtStateChangeTime;

    @Schema(description = "上一次的操作记录")
    private String cOldOperationNote;

    @Schema(description = "订单客户类型")
    private String cOrderCustType;

    @Schema(description = "旧编码")
    private String cOldcode;

    @Schema(description = "订单号")
    private String cOrderNo;

    @Schema(description = "规格代码")
    private String cItemCd;

    @Schema(description = "成品规格")
    private String nMatItem;

    @Schema(description = "厚度公差最大值")
    private BigDecimal nThkBiasMax;

    @Schema(description = "厚度公差最小值")
    private BigDecimal nThkBiasMin;

    @Schema(description = "宽度公差最大值")
    private BigDecimal nWthBiasMax;

    @Schema(description = "宽度公差最小值")
    private BigDecimal nWthBiasMin;

    @Schema(description = "成品重量最大值")
    private BigDecimal nProdWgtMax;

    @Schema(description = "成品重量最小值")
    private BigDecimal nProdWgtMin;

    @Schema(description = "长度最小")
    private BigDecimal nProdLenMin;

    @Schema(description = "长度最大")
    private BigDecimal nProdLenMax;

    @Schema(description = "替代重量")
    private BigDecimal nPlanSingleWgt;

    @NotBlank(message = "连铸机id不能为空")
    @Schema(description = "连铸机id")
    private Long cSeat;

    @NotBlank(message = "连铸机code不能为空")
    @Schema(description = "连铸机code")
    private String cSeatCode;

    @NotBlank(message = "连铸机name不能为空")
    @Schema(description = "连铸机name")
    private String cSeatName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
