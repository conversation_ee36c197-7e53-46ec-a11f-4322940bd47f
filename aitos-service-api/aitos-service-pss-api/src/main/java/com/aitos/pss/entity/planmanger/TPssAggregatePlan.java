package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 生产任务单管理
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
@TableName("t_pss_aggregate_plan")
@Tag(name = "生产任务单管理对象", description = "生产任务单管理")
public class TPssAggregatePlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "序列号")
    @TableId
    private Long nId;
    
    @Schema(description = "综合生产计划编号(AP+合并计划号)")
    @TableField(value = "c_aggregate_plan_id")
    private String cAggregatePlanId;
    
    @Schema(description = "是否紧急")
    @TableField(value = "c_exg_prod_lot_fl")
    private String cExgProdLotFl;
    
    @Schema(description = "任务单号")
    @TableField(value = "c_product_task_list_id")
    private String cProductTaskListId;

    @Schema(description = "产品类型（Y0006）")
    @TableField(value = "c_product_type")
    private String cProductType;

    @Schema(description = "质量编码id")
    @TableField(value = "c_mat_qul_id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_code")
    private String cMatQulCode;

    @Schema(description = "质量编码name")
    @TableField(value = "c_mat_qul_name")
    private String cMatQulName;
    
    @Schema(description = "定尺类型")
    @TableField(value = "c_size_property")
    private String cSizeProperty;
    
    @Schema(description = "产品直径")
    @TableField(value = "n_prod_dia")
    private BigDecimal nProdDia;
    
    @Schema(description = "产品厚度")
    @TableField(value = "n_prod_thk")
    private BigDecimal nProdThk;
    
    @Schema(description = "产品宽度")
    @TableField(value = "n_prod_wid")
    private BigDecimal nProdWid;
    
    @Schema(description = "产品长度（计划）")
    @TableField(value = "n_prod_lenth")
    private BigDecimal nProdLenth;
    
    @Schema(description = "计划产量")
    @TableField(value = "n_plan_wgt")
    private BigDecimal nPlanWgt;
    
    @Schema(description = "重量单位")
    @TableField(value = "c_wgt_unit")
    private String cWgtUnit;
    
    @Schema(description = "计划交货日期")
    @TableField(value = "c_del_datetime_from")
    private String cDelDatetimeFrom;
    
    @Schema(description = "应答交货日期")
    @TableField(value = "c_del_datetime_end")
    private String cDelDatetimeEnd;
    
    @Schema(description = "客户编码")
    @TableField(value = "c_customer_cd")
    private String cCustomerCd;
    
    @Schema(description = "计划状态")
    @TableField(value = "c_plan_state")
    private String cPlanState;
    
    @Schema(description = "计划坯料厚度")
    @TableField(value = "n_plan_slab_thk")
    private BigDecimal nPlanSlabThk;
    
    @Schema(description = "计划坯料宽度")
    @TableField(value = "n_plan_slab_wid")
    private BigDecimal nPlanSlabWid;
    
    @Schema(description = "计划坯料长")
    @TableField(value = "n_plan_slab_len")
    private BigDecimal nPlanSlabLen;
    
    @Schema(description = "计划产线id(轧钢)(P0001)")
    @TableField(value = "c_plan_line_id")
    private Long cPlanLineId;

    @Schema(description = "计划产线编码(轧钢)(P0001)")
    @TableField(value = "c_plan_line_cd")
    private String cPlanLineCd;

    @Schema(description = "计划产线名称(轧钢)(P0001)")
    @TableField(value = "c_plan_line_name")
    private String cPlanLineName;
    
    @Schema(description = "计划产线id（炼钢）默认一炼钢(P0001)")
    @TableField(value = "c_plan_steel_line_id")
    private Long cPlanSteelLineId;

    @Schema(description = "计划产线（炼钢）默认一炼钢(P0001)")
    @TableField(value = "c_plan_steel_line_cd")
    private String cPlanSteelLineCd;

    @Schema(description = "计划产线名称（炼钢）默认一炼钢(P0001)")
    @TableField(value = "c_plan_steel_line_name")
    private String cPlanSteelLineName;
    
    @Schema(description = "标准号")
    @TableField(value = "c_std_spec")
    private String cStdSpec;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;

    @Schema(description = "存货代码id")
    @TableField(value = "c_inventory_id")
    private Long cInventoryId;

    @Schema(description = "存货代码")
    @TableField(value = "c_inventory_cd")
    private String cInventoryCd;

    @Schema(description = "存货代码名称")
    @TableField(value = "c_inventory_name")
    private String cInventoryName;
    
    @Schema(description = "精整路径")
    @TableField(value = "c_finish_mach_ph_cd")
    private String cFinishMachPhCd;
    
    @Schema(description = "计划起始日期")
    @TableField(value = "c_plan_datetime_to")
    private String cPlanDatetimeTo;
    
    @Schema(description = "计划终止日期")
    @TableField(value = "c_plan_datetime_from")
    private String cPlanDatetimeFrom;
    
    @Schema(description = "计划坯料总数")
    @TableField(value = "n_plan_slab_count")
    private BigDecimal nPlanSlabCount;
    
    @Schema(description = "计划炉数")
    @TableField(value = "n_plan_furance_count")
    private BigDecimal nPlanFuranceCount;
    
    @Schema(description = "计划坯料单米重")
    @TableField(value = "n_plan_slab_smw")
    private BigDecimal nPlanSlabSmw;
    
    @Schema(description = "计划成才率")
    @TableField(value = "n_plan_yeild")
    private BigDecimal nPlanYeild;
    
    @Schema(description = "计划坯料总重量")
    @TableField(value = "n_plan_slab_wgt_count")
    private BigDecimal nPlanSlabWgtCount;
    
    @Schema(description = "销售计划号")
    @TableField(value = "c_sales_plan_id")
    private String cSalesPlanId;
    
    @Schema(description = "销售计划顺序号")
    @TableField(value = "c_sales_plan_sn")
    private String cSalesPlanSn;
    
    @Schema(description = "订单提取量")
    @TableField(value = "n_plan_steel_wgt")
    private BigDecimal nPlanSteelWgt;
    
    @Schema(description = "上一个状态")
    @TableField(value = "c_old_status")
    private String cOldStatus;
    
    @Schema(description = "操作记录")
    @TableField(value = "c_operation_note")
    private String cOperationNote;
    
    @Schema(description = "是否追加计划")
    @TableField(value = "c_is_add_plan")
    private String cIsAddPlan;
    
    @Schema(description = "订单备注")
    @TableField(value = "c_order_explain")
    private String cOrderExplain;
    
    @Schema(description = "状态变更时间")
    @TableField(value = "dt_state_change_time")
    private LocalDateTime dtStateChangeTime;
    
    @Schema(description = "上一次的操作记录")
    @TableField(value = "c_old_operation_note")
    private String cOldOperationNote;
    
    @Schema(description = "订单客户类型")
    @TableField(value = "c_order_cust_type")
    private String cOrderCustType;
    
    @Schema(description = "旧编码")
    @TableField(value = "c_oldcode")
    private String cOldcode;
    
    @Schema(description = "订单号")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    
    @Schema(description = "规格代码")
    @TableField(value = "c_item_cd")
    private String cItemCd;
    
    @Schema(description = "成品规格")
    @TableField(value = "n_mat_item")
    private String nMatItem;
    
    @Schema(description = "厚度公差最大值")
    @TableField(value = "n_thk_bias_max")
    private BigDecimal nThkBiasMax;
    
    @Schema(description = "厚度公差最小值")
    @TableField(value = "n_thk_bias_min")
    private BigDecimal nThkBiasMin;
    
    @Schema(description = "宽度公差最大值")
    @TableField(value = "n_wth_bias_max")
    private BigDecimal nWthBiasMax;
    
    @Schema(description = "宽度公差最小值")
    @TableField(value = "n_wth_bias_min")
    private BigDecimal nWthBiasMin;
    
    @Schema(description = "成品重量最大值")
    @TableField(value = "n_prod_wgt_max")
    private BigDecimal nProdWgtMax;
    
    @Schema(description = "成品重量最小值")
    @TableField(value = "n_prod_wgt_min")
    private BigDecimal nProdWgtMin;
    
    @Schema(description = "长度最小")
    @TableField(value = "n_prod_len_min")
    private BigDecimal nProdLenMin;
    
    @Schema(description = "长度最大")
    @TableField(value = "n_prod_len_max")
    private BigDecimal nProdLenMax;
    
    @Schema(description = "替代重量")
    @TableField(value = "n_plan_single_wgt")
    private BigDecimal nPlanSingleWgt;

    @Schema(description = "连铸机id")
    @TableField(value = "c_seat")
    private Long cSeat;

    @Schema(description = "连铸机code")
    @TableField(value = "c_seat_code")
    private String cSeatCode;

    @Schema(description = "连铸机name")
    @TableField(value = "c_seat_name")
    private String cSeatName;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
}