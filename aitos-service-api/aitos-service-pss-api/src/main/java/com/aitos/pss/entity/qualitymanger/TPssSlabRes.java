package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 炼钢表尺判定管理
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
@TableName("t_pss_slab_res")
@Tag(name = "炼钢表尺判定管理对象", description = "炼钢表尺判定管理")
public class TPssSlabRes implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "")
    @TableId
    private Long nId;
    
    @Schema(description = "计划件次号")
    @TableField(value = "c_plan_mat_id")
    private String cPlanMatId;
    
    @Schema(description = "件次号/坯料号")
    @TableField(value = "c_mat_id")
    private String cMatId;
    
    @Schema(description = "数量")
    @TableField(value = "n_cnt")
    private Integer nCnt;
    
    @Schema(description = "原料号")
    @TableField(value = "c_pre_mat_id")
    private String cPreMatId;
    
    @Schema(description = "物料类型")
    @TableField(value = "c_mat_type")
    private String cMatType;

    @Schema(description = "物料id")
    @TableField(value = "c_mate_id")
    private Long cMateId;
    
    @Schema(description = "炉号")
    @TableField(value = "c_heat_id")
    private String cHeatId;
    
    @Schema(description = "轧制批次号")
    @TableField(value = "c_roll_sch_id")
    private String cRollSchId;
    
    @Schema(description = "母板号")
    @TableField(value = "c_mat_id_mth")
    private String cMatIdMth;
    
    @Schema(description = "物料状态")
    @TableField(value = "c_status")
    private String cStatus;
    
    @Schema(description = "前物料状态")
    @TableField(value = "c_pre_status")
    private String cPreStatus;
    
    @Schema(description = "钢种描述/名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "质量编码")
    @TableField(value = "c_quality_id")
    private Long cQualityId;

    @Schema(description = "质量编码")
    @TableField(value = "c_quality_code")
    private String cQualityCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_quality_code_name")
    private String cQualityCodeName;
    
    @Schema(description = "物料编码")
    @TableField(value = "c_mat_code")
    private String cMatCode;
    
    @Schema(description = "物料名称")
    @TableField(value = "c_mat_name")
    private String cMatName;
    
    @Schema(description = "规格")
    @TableField(value = "c_mat_item")
    private String cMatItem;
    
    @Schema(description = "厚度")
    @TableField(value = "n_mat_thk")
    private BigDecimal nMatThk;
    
    @Schema(description = "宽度")
    @TableField(value = "n_mat_wid")
    private BigDecimal nMatWid;
    
    @Schema(description = "直径")
    @TableField(value = "n_mat_dia")
    private BigDecimal nMatDia;
    
    @Schema(description = "长度")
    @TableField(value = "n_mat_lth")
    private BigDecimal nMatLth;
    
    @Schema(description = "实际重量")
    @TableField(value = "n_mat_wgt")
    private BigDecimal nMatWgt;
    
    @Schema(description = "计算重量")
    @TableField(value = "n_mat_wgt_cal")
    private BigDecimal nMatWgtCal;
    
    @Schema(description = "检斤重量")
    @TableField(value = "n_mat_act_wgt")
    private BigDecimal nMatActWgt;
    
    @Schema(description = "执行标准号")
    @TableField(value = "c_std_spec")
    private String cStdSpec;
    
    @Schema(description = "综合判定时间 Q")
    @TableField(value = "dt_judge_time")
    private LocalDateTime dtJudgeTime;
    
    @Schema(description = "综判等级")
    @TableField(value = "c_prod_grd")
    private String cProdGrd;
    
    @Schema(description = "初判等级")
    @TableField(value = "c_prel_grd")
    private String cPrelGrd;
    
    @Schema(description = "成分等级")
    @TableField(value = "c_chem_grd")
    private String cChemGrd;
    
    @Schema(description = "尺寸等级")
    @TableField(value = "c_size_grd")
    private String cSizeGrd;
    
    @Schema(description = "外观等级（表面等级）")
    @TableField(value = "c_surf_grd")
    private String cSurfGrd;
    
    @Schema(description = "性能等级")
    @TableField(value = "c_mtal_grd")
    private String cMtalGrd;
    
    @Schema(description = "探伤结果")
    @TableField(value = "c_w_pro")
    private String cWPro;
    
    @Schema(description = "探伤日期")
    @TableField(value = "dt_w_date")
    private LocalDateTime dtWDate;
    
    @Schema(description = "不合格原因")
    @TableField(value = "c_upd_not")
    private String cUpdNot;
    
    @Schema(description = "处理方式")
    @TableField(value = "c_upd_rsn")
    private String cUpdRsn;
    
    @Schema(description = "是否改配合同，否：Null 是：1")
    @TableField(value = "c_bd_fl")
    private String cBdFl;
    
    @Schema(description = "质量证明书号")
    @TableField(value = "c_cert_id")
    private String cCertId;
    
    @Schema(description = "购入时间")
    @TableField(value = "dt_pch_time")
    private LocalDateTime dtPchTime;
    
    @Schema(description = "生产时间")
    @TableField(value = "dt_prod_time")
    private LocalDateTime dtProdTime;
    
    @Schema(description = "生产班次")
    @TableField(value = "c_prod_shift")
    private String cProdShift;
    
    @Schema(description = "生产班别")
    @TableField(value = "c_prod_group")
    private String cProdGroup;
    
    @Schema(description = "信息来源")
    @TableField(value = "c_occr_cd")
    private String cOccrCd;
    
    @Schema(description = "产地")
    @TableField(value = "c_factory")
    private String cFactory;
    
    @Schema(description = "供货商")
    @TableField(value = "c_supplier")
    private String cSupplier;
    
    @Schema(description = "销售订单号")
    @TableField(value = "c_sale_no")
    private String cSaleNo;
    
    @Schema(description = "销售订单行号")
    @TableField(value = "c_sale_sn")
    private String cSaleSn;
    
    @Schema(description = "生产任务单号")
    @TableField(value = "c_task_list_id")
    private String cTaskListId;
    
    @Schema(description = "采购订单号")
    @TableField(value = "c_buy_order_no")
    private String cBuyOrderNo;
    
    @Schema(description = "采购订单行号")
    @TableField(value = "c_order_sn")
    private String cOrderSn;
    
    @Schema(description = "订单材/余材标志")
    @TableField(value = "c_ord_fl")
    private String cOrdFl;
    
    @Schema(description = "生产订单号")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    
    @Schema(description = "是否多订单")
    @TableField(value = "c_multi_ord_tag")
    private String cMultiOrdTag;
    
    @Schema(description = "余材原因")
    @TableField(value = "c_woo_rsn")
    private String cWooRsn;
    
    @Schema(description = "板坯热送标志")
    @TableField(value = "c_hcr_fl")
    private String cHcrFl;
    
    @Schema(description = "是否试样板")
    @TableField(value = "c_smp_fl")
    private String cSmpFl;
    
    @Schema(description = "当前产线")
    @TableField(value = "c_line_id")
    private Long cLineId;

    @Schema(description = "当前产线cd")
    @TableField(value = "c_line_cd")
    private String cLineCd;

    @Schema(description = "当前产线name")
    @TableField(value = "c_line_name")
    private String cLineName;
    
    @Schema(description = "产销仓库代码")
    @TableField(value = "c_nc_yard_cd")
    private String cNcYardCd;
    
    @Schema(description = "库位号")
    @TableField(value = "c_loc")
    private String cLoc;
    
    @Schema(description = "层号")
    @TableField(value = "n_loc_lvl")
    private Integer nLocLvl;
    
    @Schema(description = "计划去向")
    @TableField(value = "c_pre_out")
    private String cPreOut;
    
    @Schema(description = "备注")
    @TableField(value = "c_memo")
    private String cMemo;
    
    @Schema(description = "最后修改程序")
    @TableField(value = "c_pgm_id")
    private String cPgmId;
    
    @Schema(description = "上传标志")
    @TableField(value = "c_month_flag")
    private String cMonthFlag;
    
    @Schema(description = "上传人员")
    @TableField(value = "c_month_emp")
    private String cMonthEmp;
    
    @Schema(description = "上传时间")
    @TableField(value = "dt_month_time")
    private LocalDateTime dtMonthTime;
    
    @Schema(description = "复秤标记")
    @TableField(value = "c_reweight_fl")
    private String cReweightFl;
    
    @Schema(description = "复秤重量")
    @TableField(value = "n_reweight")
    private Integer nReweight;
    
    @Schema(description = "复秤类型")
    @TableField(value = "c_re_wgt_flag")
    private String cReWgtFlag;
    
    @Schema(description = "二次复秤标记")
    @TableField(value = "c_two_rewgt_fl")
    private String cTwoRewgtFl;
    
    @Schema(description = "二次复秤日期")
    @TableField(value = "dt_two_wgt_time")
    private LocalDateTime dtTwoWgtTime;
    
    @Schema(description = "二次复秤人员")
    @TableField(value = "c_two_wgt_emp")
    private String cTwoWgtEmp;
    
    @Schema(description = "二次复秤重量")
    @TableField(value = "n_two_wgt")
    private Integer nTwoWgt;
    
    @Schema(description = "改判人")
    @TableField(value = "c_cha_empid")
    private String cChaEmpid;
    
    @Schema(description = "改判时间")
    @TableField(value = "dt_cha_time")
    private LocalDateTime dtChaTime;
    
    @Schema(description = "改判备注")
    @TableField(value = "c_cha_memo")
    private String cChaMemo;

    @Schema(description = "原质量编码")
    @TableField(value = "c_pre_mat_qul_id")
    private Long cPreMatQulId;

    @Schema(description = "原质量编码")
    @TableField(value = "c_pre_mat_qul_cd")
    private String cPreMatQulCd;

    @Schema(description = "原质量编码name")
    @TableField(value = "c_pre_mat_qul_name")
    private String cPreMatQulName;
    
    @Schema(description = "原钢种")
    @TableField(value = "c_pre_stl_grd_cd")
    private String cPreStlGrdCd;

    @Schema(description = "原钢种")
    @TableField(value = "c_pre_stl_grd_desc")
    private String cPreStlGrdDesc;
    
    @Schema(description = "原长度")
    @TableField(value = "n_pre_mat_lth")
    private BigDecimal nPreMatLth;
    
    @Schema(description = "原计算重量")
    @TableField(value = "n_pre_mat_wgt_cal")
    private BigDecimal nPreMatWgtCal;
    
    @Schema(description = "原物料编码")
    @TableField(value = "c_pre_matcode")
    private String cPreMatcode;
    
    @Schema(description = "原物料名称")
    @TableField(value = "c_pre_matname")
    private String cPreMatname;
    
    @Schema(description = "是否锁定")
    @TableField(value = "c_is_lock")
    private String cIsLock;
    
    @Schema(description = "装车车号")
    @TableField(value = "c_car_no")
    private String cCarNo;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;

}