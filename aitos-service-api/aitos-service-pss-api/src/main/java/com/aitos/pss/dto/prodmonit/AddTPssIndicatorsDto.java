package com.aitos.pss.dto.prodmonit;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 指标配置
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
public class AddTPssIndicatorsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 指标名称
    */
    @Schema(description = "指标名称")
    private String cName;
    /**
    * 指标代码
    */
    @Schema(description = "指标代码")
    private String cCode;
    /**
    * 设备id
    */
    @Schema(description = "设备id")
    private Long nEquipmentId;
    /**
    * 设备code
    */
    @Schema(description = "设备code")
    private String cEquipmentCode;
    /**
    * 设备name
    */
    @Schema(description = "设备name")
    private String cEquipmentName;
    /**
    * 单位id
    */
    @Schema(description = "单位id")
    private Long cUnitId;
    /**
    * 单位code
    */
    @Schema(description = "单位code")
    private String cUnitCode;
    /**
    * 单位name
    */
    @Schema(description = "单位name")
    private String cUnitName;
    /**
    * 描述
    */
    @Schema(description = "描述")
    private String cDescription;
    /**
    * 正常值下限
    */
    @Schema(description = "正常值下限")
    private BigDecimal nNormalMin;
    /**
    * 正常值上限
    */
    @Schema(description = "正常值上限")
    private BigDecimal nNormalMax;
    /**
    * 预警值下限
    */
    @Schema(description = "预警值下限")
    private BigDecimal nWarningMin;
    /**
    * 预警值上限
    */
    @Schema(description = "预警值上限")
    private BigDecimal nWarningMax;
    /**
    * 违规值下限
    */
    @Schema(description = "违规值下限")
    private BigDecimal nViolationMin;
    /**
    * 违规值上限
    */
    @Schema(description = "违规值上限")
    private BigDecimal nViolationMax;
    /**
    * 指标点位配置
    */
    @Schema(description = "指标点位配置")
    private String cPointConfig;
    /**
    * 判断类型
    */
    @Schema(description = "判断类型")
    private String cJudgmentType;
    /**
    * 状态（1:启用, 0:禁用）
    */
    @Schema(description = "状态（1:启用, 0:禁用）")
    private String cStatus;

    @Schema(description = "审批人ID")
    private Long nApproverId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
