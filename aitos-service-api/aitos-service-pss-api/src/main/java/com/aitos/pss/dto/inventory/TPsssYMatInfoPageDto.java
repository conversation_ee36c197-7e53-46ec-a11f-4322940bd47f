package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPsssYMatInfoPageDto extends PageInput {

    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMaterialName;
    /**
    * 规格型号
    */
    @Schema(description = "规格型号")
    private String cSpecification;
    /**
    * 库存上限
    */
    @Schema(description = "库存上限")
    private BigDecimal nMaxStock;
    /**
    * 当前库存
    */
    @Schema(description = "当前库存")
    private BigDecimal nCurrentStock;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMaterialCode;
    /**
    * 物料类型(raw:原辅料,alloy:合金,scrap:废钢)
    */
    @Schema(description = "物料类型(raw:原辅料,alloy:合金,scrap:废钢)")
    private String cMaterialType;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private String cUnit;
    /**
    * 库存下限
    */
    @Schema(description = "库存下限")
    private BigDecimal nMinStock;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
