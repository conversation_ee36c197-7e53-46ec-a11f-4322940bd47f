package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 连铸质量工艺参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@TableName("t_pss_tech_ccm_info")
@Tag(name = "连铸质量工艺参数质量设计结果对象", description = "连铸质量工艺参数质量设计结果")
public class TPssTechCcmInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "订单号")
    @TableField(value = "order_id")
    private String orderId;

    @Schema(description = "质量编码id")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "液相线")
    @TableField(value = "n_liquid_phase")
    private String nLiquidPhase;
    
    @Schema(description = "中包目标温度")
    @TableField(value = "n_mid_pag_temp")
    private BigDecimal nMidPagTemp;
    
    @Schema(description = "搬出温度")
    @TableField(value = "n_moveout_time")
    private BigDecimal nMoveoutTime;
    
    @Schema(description = "起步温度")
    @TableField(value = "n_start_temp")
    private BigDecimal nStartTemp;
    
    @Schema(description = "连浇温度")
    @TableField(value = "n_cast_temp")
    private BigDecimal nCastTemp;
    
    @Schema(description = "保护渣型号")
    @TableField(value = "c_flux_model")
    private String cFluxModel;
    
    @Schema(description = "钢包铸余")
    @TableField(value = "n_sld_surplus")
    private String nSldSurplus;
    
    @Schema(description = "结晶器水量")
    @TableField(value = "n_water_mould")
    private String nWaterMould;
    
    @Schema(description = "比水量")
    @TableField(value = "n_specific_volume")
    private String nSpecificVolume;
    
    @Schema(description = "中包水口")
    @TableField(value = "c_tundish_nozzle")
    private String cTundishNozzle;
    
    @Schema(description = "连铸机号")
    @TableField(value = "c_con_no")
    private String cConNo;
    
    @Schema(description = "首端电搅")
    @TableField(value = "c_head_ele_sti")
    private String cHeadEleSti;
    
    @Schema(description = "末端电搅")
    @TableField(value = "c_tail_ele_sti")
    private String cTailEleSti;
    
    @Schema(description = "振幅")
    @TableField(value = "c_amplitude")
    private String cAmplitude;
    
    @Schema(description = "振频")
    @TableField(value = "c_vibration_frequency")
    private String cVibrationFrequency;
    
    @Schema(description = "液面波动")
    @TableField(value = "c_liquid_level_fluc")
    private String cLiquidLevelFluc;
    
    @Schema(description = "备用1")
    @TableField(value = "c_backup1")
    private String cBackup1;
    
    @Schema(description = "备用2")
    @TableField(value = "c_backup2")
    private String cBackup2;
    
    @Schema(description = "备用3")
    @TableField(value = "c_backup3")
    private String cBackup3;
    
    @Schema(description = "备用4")
    @TableField(value = "c_backup4")
    private String cBackup4;
    
    @Schema(description = "备用5")
    @TableField(value = "c_backup5")
    private String cBackup5;
    
    @Schema(description = "备用6")
    @TableField(value = "c_backup6")
    private String cBackup6;
    
    @Schema(description = "备用7")
    @TableField(value = "c_backup7")
    private String cBackup7;
    
    @Schema(description = "备用8")
    @TableField(value = "c_backup8")
    private String cBackup8;
    
    @Schema(description = "备用9")
    @TableField(value = "c_backup9")
    private String cBackup9;
    
    @Schema(description = "备用10")
    @TableField(value = "c_backup10")
    private String cBackup10;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}