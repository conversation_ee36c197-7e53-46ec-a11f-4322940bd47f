package com.aitos.pss.dto.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 订单管理
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
public class UpdateTPssOrderInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "订单号（订单类型+年月日+3位顺序号）")
    private String cOrderNo;
    
    @Schema(description = "订单类型（P0004）")
    private String cOrderType;
    
    @Schema(description = "客户编码")
    private String cCustomerCd;
    
    @Schema(description = "产品类型（Y0006）")
    private String cProductType;

    @Schema(description = "产线id（P0001）")
    private Long cProductLineId;

    @Schema(description = "产线代码（P0001）")
    private String cProductLineCd;

    @Schema(description = "产线名称（P0001）")
    private String cProductLineName;
    
    @Schema(description = "销售合同号")
    private String cContractNo;

    @Schema(description = "质量编码id")
    private Long cQualId;

    @Schema(description = "质量编码")
    private String cQualCode;

    @Schema(description = "质量编码明")
    private String cQualCodeName;
    
    @Schema(description = "标准编号")
    private String cStdcode;
    
    @Schema(description = "钢种编码")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    private String cStlGrdName;

    @Schema(description = "存货代码id")
    private String cInventoryId;

    @Schema(description = "存货代码")
    private String cInventoryCd;

    @Schema(description = "存货代码名称")
    private String cInventoryName;
    
    @Schema(description = "定尺类型（P0013）")
    private String cSizeProperty;
    
    @Schema(description = "直径（直条等）")
    private BigDecimal nProdDia;
    
    @Schema(description = "厚度（方钢等）")
    private BigDecimal nProdThk;
    
    @Schema(description = "宽度（方钢等）")
    private BigDecimal nProdWid;
    
    @Schema(description = "长度目标（卷和高线为9999999.999）")
    private BigDecimal nProdLenth;
    
    @Schema(description = "长度最小")
    private BigDecimal nProdLenMin;
    
    @Schema(description = "长度最大")
    private BigDecimal nProdLenMax;
    
    @Schema(description = "称重方式（P0016）")
    private String cWgtFl;
    
    @Schema(description = "订单重量(成品替代后订单剩余重量)")
    private BigDecimal nOrderWgt;

    @Schema(description = "拆分重量")
    private BigDecimal nSplitOrderWgt;
    
    @Schema(description = "重量单位（P0023）")
    private String cWgtUnit;
    
    @Schema(description = "允许超交比率(%)")
    private BigDecimal nExtRate;
    
    @Schema(description = "允许欠交比率(%)")
    private BigDecimal nRemRate;
    
    @Schema(description = "交货方式代码(P0008)")
    private String cFpoststateCd;
    
    @Schema(description = "目标交货日期")
    private LocalDateTime cDelDatetime;
    
    @Schema(description = "交货周期")
    private BigDecimal nDelInterval;
    
    @Schema(description = "交货周期单位（P0026)")
    private String cDelIntervalUnit;
    
    @Schema(description = "包装方式代码(P0030)")
    private String cPakWayCd;
    
    @Schema(description = "客户特殊包装要求")
    private String cCustSpecPrint;
    
    @Schema(description = "订单结束日期")
    private LocalDateTime cEndDatetime;
    
    @Schema(description = "订单状态代码（P0020）")
    private String cOrderState;
    
    @Schema(description = "是否被合并（Y/N）")
    private String cIsCombined;
    
    @Schema(description = "销售计划号")
    private String cSalesPlanId;
    
    @Schema(description = "销售计划序号")
    private BigDecimal cSalesPlanSn;
    
    @Schema(description = "综合生产计划号")
    private String cAggregatePlanId;
    
    @Schema(description = "综合生产计划序号")
    private BigDecimal nAggregatePlanSn;
    
    @Schema(description = "生产任务单编号")
    private String cProductTaskListId;
    
    @Schema(description = "销售部门")
    private String cSalesDeptCd;
    
    @Schema(description = "是否紧急订单（0/1    0-普通订单 ，1-紧急订单）")
    private String cExgProdLotFl;
    
    @Schema(description = "订单备注")
    private String cOrderExplain;
    
    @Schema(description = "订单分组页号")
    private BigDecimal nOrderPageNo;
    
    @Schema(description = "核准人")
    private String cApprovalEmp;
    
    @Schema(description = "核准时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtApprovalDatetime;
    
    @Schema(description = "订单操作说明")
    private String cOperationNote;
    
    @Schema(description = "上一个状态")
    private String cOldState;
    
    @Schema(description = "从回收站释放前的订单录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtOldInputDatetime;
    
    @Schema(description = "状态变更时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtStateChangeTime;
    
    @Schema(description = "计划扣重")
    private BigDecimal nReduceWeight;
    
    @Schema(description = "计划扣重录入人员")
    private String cReduceEmpId;
    
    @Schema(description = "计划扣重录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReduceInputTime;
    
    @Schema(description = "计划扣重审核状态（X—正常订单 W—等待审核 P—审核通过 C—审核否决）")
    private String cReduceReviewStatus;
    
    @Schema(description = "计划扣重审核人员")
    private String cReduceReviewEmp;
    
    @Schema(description = "计划扣重审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReduceReviewTime;
    
    @Schema(description = "计划扣重操作日志")
    private String cReduceOperatLog;
    
    @Schema(description = "质量设计信息日志")
    private String cQualDesLog;
    
    @Schema(description = "上一次的操作记录")
    private String cOldOperationNote;
    
    @Schema(description = "订单客户类型")
    private String cOrderCustType;
    
    @Schema(description = "二维码类型")
    private String cQrcode;
    
    @Schema(description = "数据来源 （电商接口DS）暂时作为原物料编码使用，切换完成后仍作为原字段")
    private String cInsSource;
    
    @Schema(description = "是否保性能")
    private String cSmpFl;
    
    @Schema(description = "运输方式")
    private String cShipWay;
    
    @Schema(description = "订单计划重量")
    private BigDecimal nPlanOrderWgt;
    
    @Schema(description = "已排产数量")
    private BigDecimal nSchOrderWgt;
    
    @Schema(description = "订单件数")
    private BigDecimal nOrderCnt;
    
    @Schema(description = "成品单重")
    private BigDecimal nProdWgt;
    
    @Schema(description = "厚度公差最大值")
    private BigDecimal nThkBiasMax;
    
    @Schema(description = "厚度公差最小值")
    private BigDecimal nThkBiasMin;
    
    @Schema(description = "宽度公差最大值")
    private BigDecimal nWthBiasMax;
    
    @Schema(description = "宽度公差最小值")
    private BigDecimal nWthBiasMin;
    
    @Schema(description = "是否滑纹板")
    private String nCheckerFlag;
    
    @Schema(description = "滑纹板高度")
    private Long nCheckerHeight;
    
    @Schema(description = "成品重量最大值")
    private BigDecimal nProdWgtMax;
    
    @Schema(description = "成品重量最小值")
    private BigDecimal nProdWgtMin;
    
    @Schema(description = "上级订单号")
    private String cMorderId;
    
    @Schema(description = "销售合同号")
    private String cSaleContractNo;
    
    @Schema(description = "规格代码")
    private String cItemCd;
    
    @Schema(description = "成品规格")
    private String cMatItem;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
