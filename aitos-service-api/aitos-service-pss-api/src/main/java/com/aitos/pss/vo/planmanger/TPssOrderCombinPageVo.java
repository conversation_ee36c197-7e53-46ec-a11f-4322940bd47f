package com.aitos.pss.vo.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
public class TPssOrderCombinPageVo {

    /**
    * 序列号
    */
    @Schema(description = "序列号")
    private String nId;
    /**
    * 合并计划号（产品类型+年月+四位流水）
    */
    @Schema(description = "合并计划号（产品类型+年月+四位流水）")
    private String cSalesPlanId;
    /**
    * 计划内顺序号
    */
    @Schema(description = "计划内顺序号")
    private BigDecimal cPlanSn;
    /**
    * 是否紧急生产订单（0：否 1：是）
    */
    @Schema(description = "是否紧急生产订单（0：否 1：是）")
    private String cExgProdLotFl;
    /**
    * 客户代码
    */
    @Schema(description = "客户代码")
    private String cCustomerCd;

    @Schema(description = "产线id")
    private Long cProLine;

    @Schema(description = "产线code")
    private String cProLineCode;

    @Schema(description = "产线name")
    private String cProLineName;

    @Schema(description = "产品类型（Y0006）")
    private String cProductType;

    @Schema(description = "质量编码id")
    private Long cQualId;

    @Schema(description = "质量编码")
    private String cQualCode;

    @Schema(description = "质量编码name")
    private String cQualCodeName;
    /**
    * 标准号
    */
    @Schema(description = "标准号")
    private String cStdSpec;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;

    @Schema(description = "存货代码id")
    private String cInventoryId;

    @Schema(description = "存货代码")
    private String cInventoryCd;

    @Schema(description = "存货代码名称")
    private String cInventoryName;
    /**
    * 定尺类型代码（P0013)
    */
    @Schema(description = "定尺类型代码（P0013)")
    private String cSizeProperty;
    /**
    * 直径
    */
    @Schema(description = "直径")
    private BigDecimal nProdDia;
    /**
    * 厚度(方钢)
    */
    @Schema(description = "厚度(方钢)")
    private BigDecimal nProdThk;
    /**
    * 宽度（方钢）
    */
    @Schema(description = "宽度（方钢）")
    private BigDecimal nProdWid;
    /**
    * 目标长度
    */
    @Schema(description = "目标长度")
    private BigDecimal nProdLenth;
    /**
    * 订货重量
    */
    @Schema(description = "订货重量")
    private BigDecimal nOrderWgt;
    /**
    * 重量单位代码（P0023）
    */
    @Schema(description = "重量单位代码（P0023）")
    private String cWgtUnit;
    /**
    * 交货起始日
    */
    @Schema(description = "交货起始日")
    private String cDelDatetimeFrom;
    /**
    * 交货终止日
    */
    @Schema(description = "交货终止日")
    private String cDelDatetimeTo;
    /**
    * 计划状态
    */
    @Schema(description = "计划状态")
    private String cCombinState;
    /**
    * 综合生产计划号
    */
    @Schema(description = "综合生产计划号")
    private String cAggregatePlanId;
    /**
    * 综合生产计划序号
    */
    @Schema(description = "综合生产计划序号")
    private BigDecimal nAggregatePlanSn;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    private String cProductTaskListId;
    /**
    * 计划有效期启
    */
    @Schema(description = "计划有效期启")
    private String cPlanDatetimeFrom;
    /**
    * 计划有效期止
    */
    @Schema(description = "计划有效期止")
    private String cPlanDatetimeTo;
    /**
    * 订单号（备用）
    */
    @Schema(description = "订单号（备用）")
    private String cOrderNo;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cOrderExplain;
    /**
    * 操作记录
    */
    @Schema(description = "操作记录")
    private String cOperationNote;
    /**
    * 是否追加计划
    */
    @Schema(description = "是否追加计划")
    private String cIsAddPlan;
    /**
    * 上一个状态
    */
    @Schema(description = "上一个状态")
    private String cOldState;
    /**
    * 状态变更时间
    */
    @Schema(description = "状态变更时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtStateChangeTime;
    /**
    * 部门编号
    */
    @Schema(description = "部门编号")
    private String cDeptNo;
    /**
    * 上一次的操作记录
    */
    @Schema(description = "上一次的操作记录")
    private String cOldOperationNote;
    /**
    * 订单客户类型
    */
    @Schema(description = "订单客户类型")
    private String cOrderCustType;
    /**
    * 旧编码
    */
    @Schema(description = "旧编码")
    private String cOldcode;
    /**
    * 
    */
    @Schema(description = "")
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    private Long nModifyUserId;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    private Integer nDeleteMark;

}
