package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 精炼工艺参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@TableName("t_pss_tech_lf_info")
@Tag(name = "精炼工艺参数质量设计结果对象", description = "精炼工艺参数质量设计结果")
public class TPssTechLfInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "生产任务单号")
    @TableField(value = "order_id")
    private String orderId;
    
    @Schema(description = "质量编码id")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "碱度")
    @TableField(value = "c_alcalinity")
    private BigDecimal cAlcalinity;
    
    @Schema(description = "CaSi喂丝量")
    @TableField(value = "c_casi")
    private BigDecimal cCasi;
    
    @Schema(description = "CaAl喂丝量")
    @TableField(value = "c_caal")
    private BigDecimal cCaal;
    
    @Schema(description = "Ca喂丝量")
    @TableField(value = "c_ca")
    private BigDecimal cCa;
    
    @Schema(description = "净吹氩时间")
    @TableField(value = "c_argon_blow")
    private String cArgonBlow;
    
    @Schema(description = "LF搬出温度")
    @TableField(value = "c_lf_moveout_temp")
    private BigDecimal cLfMoveoutTemp;
    
    @Schema(description = "VOD保压真空度")
    @TableField(value = "c_vod_press_vac")
    private String cVodPressVac;
    
    @Schema(description = "VOD保持时间")
    @TableField(value = "c_vod_keep_time")
    private String cVodKeepTime;
    
    @Schema(description = "VOD搬出温度")
    @TableField(value = "c_vod_moveout_time")
    private BigDecimal cVodMoveoutTime;
    
    @Schema(description = "RH保压真空度")
    @TableField(value = "c_rh_press_vac")
    private String cRhPressVac;
    
    @Schema(description = "RH保持时间")
    @TableField(value = "c_rh_keep_time")
    private String cRhKeepTime;
    
    @Schema(description = "RH搬出温度")
    @TableField(value = "c_rh_moveout_time")
    private BigDecimal cRhMoveoutTime;
    
    @Schema(description = "LF炼焦")
    @TableField(value = "c_lf_lianjiao")
    private String cLfLianjiao;
    
    @Schema(description = "VOD炼焦")
    @TableField(value = "c_vod_lianjiao")
    private String cVodLianjiao;
    
    @Schema(description = "RH炼焦")
    @TableField(value = "c_rh_lianjiao")
    private String cRhLianjiao;
    
    @Schema(description = "石灰")
    @TableField(value = "c_lime")
    private String cLime;
    
    @Schema(description = "萤石")
    @TableField(value = "c_fluorite")
    private String cFluorite;
    
    @Schema(description = "硅灰石")
    @TableField(value = "c_silica_fume")
    private String cSilicaFume;
    
    @Schema(description = "精炼渣")
    @TableField(value = "c_refining_slag")
    private String cRefiningSlag;
    
    @Schema(description = "渣洗剂")
    @TableField(value = "c_slag_lotion")
    private String cSlagLotion;
    
    @Schema(description = "石英砂")
    @TableField(value = "c_quartz_sand")
    private String cQuartzSand;
    
    @Schema(description = "电石")
    @TableField(value = "c_calcium_carbide")
    private String cCalciumCarbide;
    
    @Schema(description = "LF连浇搬出温度")
    @TableField(value = "c_lf_morecast_temp")
    private BigDecimal cLfMorecastTemp;
    
    @Schema(description = "RH连浇搬出温度")
    @TableField(value = "c_rh_moveout_cast_time")
    private BigDecimal cRhMoveoutCastTime;
    
    @Schema(description = "真空环流")
    @TableField(value = "c_vacuum_circulation")
    private String cVacuumCirculation;
    
    @Schema(description = "白渣保持时间")
    @TableField(value = "c_w_slag_h_time")
    private String cWSlagHTime;
    
    @Schema(description = "精炼标准时间")
    @TableField(value = "c_lf_time_bz")
    private String cLfTimeBz;
    
    @Schema(description = "TFE+MNO")
    @TableField(value = "c_tfemno")
    private String cTfemno;
    
    @Schema(description = "钙渣球")
    @TableField(value = "c_ca_ball")
    private String cCaBall;
    
    @Schema(description = "碱度最小")
    @TableField(value = "c_alcalinity_min")
    private BigDecimal cAlcalinityMin;
    
    @Schema(description = "碱度最大")
    @TableField(value = "c_alcalinity_max")
    private BigDecimal cAlcalinityMax;
    
    @Schema(description = "备用1")
    @TableField(value = "c_backup1")
    private String cBackup1;
    
    @Schema(description = "备用2")
    @TableField(value = "c_backup2")
    private String cBackup2;
    
    @Schema(description = "备用3")
    @TableField(value = "c_backup3")
    private String cBackup3;
    
    @Schema(description = "备用4")
    @TableField(value = "c_backup4")
    private String cBackup4;
    
    @Schema(description = "备用5")
    @TableField(value = "c_backup5")
    private String cBackup5;
    
    @Schema(description = "备用6")
    @TableField(value = "c_backup6")
    private String cBackup6;
    
    @Schema(description = "备用7")
    @TableField(value = "c_backup7")
    private String cBackup7;
    
    @Schema(description = "备用8")
    @TableField(value = "c_backup8")
    private String cBackup8;
    
    @Schema(description = "备用9")
    @TableField(value = "c_backup9")
    private String cBackup9;
    
    @Schema(description = "备用10")
    @TableField(value = "c_backup10")
    private String cBackup10;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
}