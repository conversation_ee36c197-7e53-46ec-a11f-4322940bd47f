package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssOrderCombinPageDto extends PageInput {

    @Schema(description = "质量编码")
    private Long cQualId;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "产线代码(P0001)")
    private Long cProLine;

    @Schema(description = "订单号（备用）")
    private String cOrderNo;

    @Schema(description = "合并计划号（产品类型+年月+四位流水）")
    private String cSalesPlanId;

    @Schema(description = "综合生产计划号")
    private String cAggregatePlanId;

    @Schema(description = "生产任务单号")
    private String cProductTaskListId;
}
