package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 性能标准管理
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Data
@TableName("t_pss_stdmat_zg")
@Tag(name = "性能标准管理对象", description = "性能标准管理")
public class TPssStdmatZg implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "质量等级")
    private String cStdClass;
    
    @Schema(description = "执行标准号")
    private String cOpStdName;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;
    
    @Schema(description = "厚度最小值")
    private BigDecimal nThkMin;
    
    @Schema(description = "厚度最大值")
    private BigDecimal nThkMax;
    
    @Schema(description = "质检标准名称/试验项目")
    private String cVerifyItemName;
    
    @Schema(description = "质检标准编码/实验项目编码")
    private String cVerifyItemCode;

    @Schema(description = "质检标准id")
    private Long cVerifyItemId;
    
    @Schema(description = "下限比较符")
    private String cSmpMinTag;
    
    @Schema(description = "上限比较符")
    private String cSmpMaxTag;
    
    @Schema(description = "下限")
    private BigDecimal cSmpMin;
    
    @Schema(description = "下限2")
    private BigDecimal cSmpMin2;
    
    @Schema(description = "下下限")
    private BigDecimal cSmpMinMin;
    
    @Schema(description = "下下限2")
    private BigDecimal cSmpMinMin2;
    
    @Schema(description = "上限")
    private BigDecimal cSmpMax;
    
    @Schema(description = "上限2")
    private BigDecimal cSmpMax2;
    
    @Schema(description = "上上限")
    private BigDecimal cSmpMaxMax;
    
    @Schema(description = "上上限2")
    private BigDecimal cSmpMaxMax2;
    
    @Schema(description = "平均")
    private BigDecimal cSmpAvg;
    
    @Schema(description = "平均2")
    private BigDecimal cSmpAvg2;
    
    @Schema(description = "平均下限")
    private BigDecimal cSmpAvgMin;
    
    @Schema(description = "平均下限2")
    private BigDecimal cSmpAvgMin2;
    
    @Schema(description = "平均上限")
    private BigDecimal cSmpAvgMax;
    
    @Schema(description = "平均上限2")
    private BigDecimal cSmpAvgMax2;
    
    @Schema(description = "判定方法")
    private String cSmpDcsCd;
    
    @Schema(description = "备注")
    private String cMemo;
    
    @Schema(description = "质量编码id")
    private String cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;
    
    @Schema(description = "质量编码名称")
    private String cQualityCodeName;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}