package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 计划坯料表
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
@TableName("t_pss_cut_plan")
@Tag(name = "计划坯料表对象", description = "计划坯料表")
public class TPssCutPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 序列号
    */
    @Schema(description = "序列号")
    @TableId
    private Long nId;
    /**
    * 计划钢坯号
    */
    @Schema(description = "计划钢坯号")
    @TableField(value = "c_plan_slab_id")
    private String cPlanSlabId;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    @TableField(value = "c_task_list_id")
    private String cTaskListId;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_cd")
    private String cMatQulCd;

    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_name")
    private String cMatQulName;
    /**
    * 钢坯厚度
    */
    @Schema(description = "钢坯厚度")
    @TableField(value = "n_slab_thk")
    private BigDecimal nSlabThk;
    /**
    * 钢坯宽度
    */
    @Schema(description = "钢坯宽度")
    @TableField(value = "n_slab_wth")
    private BigDecimal nSlabWth;
    /**
    * 钢坯长度
    */
    @Schema(description = "钢坯长度")
    @TableField(value = "n_slab_len")
    private BigDecimal nSlabLen;
    /**
    * 是否可以头尾炉
    */
    @Schema(description = "是否可以头尾炉")
    @TableField(value = "c_ht_heat")
    private String cHtHeat;
    /**
    * 是否可以头尾坯
    */
    @Schema(description = "是否可以头尾坯")
    @TableField(value = "c_ht_slab")
    private String cHtSlab;
    /**
    * 是否热装热送
    */
    @Schema(description = "是否热装热送")
    @TableField(value = "c_hot_flag")
    private String cHotFlag;
    /**
    * 是否检验
    */
    @Schema(description = "是否检验")
    @TableField(value = "c_test_flag")
    private String cTestFlag;
    /**
    * 钢坯去向
    */
    @Schema(description = "钢坯去向")
    @TableField(value = "c_sent_place")
    private String cSentPlace;
    /**
    * 定尺类型
    */
    @Schema(description = "定尺类型")
    @TableField(value = "c_dingchi_type")
    private String cDingchiType;
    /**
    * 坯料类型
    */
    @Schema(description = "坯料类型")
    @TableField(value = "c_slab_type")
    private String cSlabType;
    /**
    * 执行标准
    */
    @Schema(description = "执行标准")
    @TableField(value = "c_use_std")
    private String cUseStd;
    /**
    * 连铸机号
    */
    @Schema(description = "连铸机id")
    @TableField(value = "c_cast_id")
    private Long cCastId;

    @Schema(description = "连铸机code")
    @TableField(value = "c_cast_code")
    private String cCastCode;

    @Schema(description = "连铸机name")
    @TableField(value = "c_cast_name")
    private String cCastName;
    /**
    * 是否来料加工材
    */
    @Schema(description = "是否来料加工材")
    @TableField(value = "c_lljg")
    private String cLljg;
    /**
    * 物料编码
    */
    @Schema(description = "物料id")
    @TableField(value = "c_mat_id")
    private Long cMatId;

    @Schema(description = "物料编码")
    @TableField(value = "c_mat_cd")
    private String cMatCd;

    @Schema(description = "物料name")
    @TableField(value = "c_mat_name")
    private String cMatName;
    /**
    * 合同号
    */
    @Schema(description = "合同号")
    @TableField(value = "c_conta_id")
    private String cContaId;
    /**
    * 合同行号
    */
    @Schema(description = "合同行号")
    @TableField(value = "c_conta_line_id")
    private String cContaLineId;
    /**
    * 溢短装比
    */
    @Schema(description = "溢短装比")
    @TableField(value = "n_fulw_rad")
    private BigDecimal nFulwRad;
    /**
    * 要求完工日期
    */
    @Schema(description = "要求完工日期")
    @TableField(value = "c_workout_tim")
    private String cWorkoutTim;
    /**
    * 特殊要求
    */
    @Schema(description = "特殊要求")
    @TableField(value = "c_spc_need")
    private String cSpcNeed;
    /**
    * 发送人
    */
    @Schema(description = "发送人")
    @TableField(value = "c_emp_id")
    private String cEmpId;
    /**
    * 发送时间
    */
    @Schema(description = "发送时间")
    @TableField(value = "dt_send_dt")
    private LocalDateTime dtSendDt;
    /**
    * 接受时间
    */
    @Schema(description = "接受时间")
    @TableField(value = "dt_check_dt")
    private LocalDateTime dtCheckDt;
    /**
    * 状态
    */
    @Schema(description = "状态")
    @TableField(value = "c_status")
    private String cStatus;
    /**
    * 计划炉次号
    */
    @Schema(description = "计划炉次号")
    @TableField(value = "c_plan_heat_id")
    private String cPlanHeatId;
    /**
    * 炉次内序号
    */
    @Schema(description = "炉次内序号")
    @TableField(value = "n_heat_seq")
    private BigDecimal nHeatSeq;
    /**
    * 计划重量
    */
    @Schema(description = "计划重量")
    @TableField(value = "n_plan_wgt")
    private BigDecimal nPlanWgt;
    /**
    * 产线
    */
    @Schema(description = "产线")
    @TableField(value = "c_work_shop")
    private String cWorkShop;
    /**
    * 是否余材
    */
    @Schema(description = "是否余材")
    @TableField(value = "c_rem_slab_flg")
    private String cRemSlabFlg;
    /**
    * 一切分组编号
    */
    @Schema(description = "一切分组编号")
    @TableField(value = "c_first_cut_group")
    private String cFirstCutGroup;
    /**
    * 实际版批号
    */
    @Schema(description = "实际版批号")
    @TableField(value = "c_act_slab_id")
    private String cActSlabId;
    /**
    * 一切标志
    */
    @Schema(description = "一切标志")
    @TableField(value = "c_first_cut_flag")
    private String cFirstCutFlag;
    /**
    * 一切索引
    */
    @Schema(description = "一切索引")
    @TableField(value = "n_first_cut_seq")
    private BigDecimal nFirstCutSeq;
    /**
    * 成品宽度
    */
    @Schema(description = "成品宽度")
    @TableField(value = "n_prod_wth")
    private BigDecimal nProdWth;
    /**
    * 成品厚度
    */
    @Schema(description = "成品厚度")
    @TableField(value = "n_prod_thk")
    private BigDecimal nProdThk;
    /**
    * 成品长度
    */
    @Schema(description = "成品长度")
    @TableField(value = "n_prod_len")
    private BigDecimal nProdLen;
    /**
    * 钢水重量
    */
    @Schema(description = "钢水重量")
    @TableField(value = "n_left_cal_wgt")
    private BigDecimal nLeftCalWgt;
    /**
    * 炉次号
    */
    @Schema(description = "炉次号")
    @TableField(value = "c_heat_no")
    private String cHeatNo;
    /**
    * 一切长度
    */
    @Schema(description = "一切长度")
    @TableField(value = "n_first_cut_len")
    private BigDecimal nFirstCutLen;
    /**
    * 一切重量
    */
    @Schema(description = "一切重量")
    @TableField(value = "n_first_cut_wgt")
    private BigDecimal nFirstCutWgt;
    /**
    * 一切操作员
    */
    @Schema(description = "一切操作员")
    @TableField(value = "c_first_cut_emp")
    private String cFirstCutEmp;
    /**
    * 一切时间
    */
    @Schema(description = "一切时间")
    @TableField(value = "dt_first_cut_time")
    private LocalDateTime dtFirstCutTime;
    /**
    * 是否一切添加标记
    */
    @Schema(description = "是否一切添加标记")
    @TableField(value = "n_is_first_add")
    private String nIsFirstAdd;
    /**
    * 一切热坯长度
    */
    @Schema(description = "一切热坯长度")
    @TableField(value = "c_first_hot_len")
    private BigDecimal cFirstHotLen;
    /**
    * 是否确认
    */
    @Schema(description = "是否确认")
    @TableField(value = "c_has_sure_plan")
    private String cHasSurePlan;
    /**
    * 计划浇次号
    */
    @Schema(description = "计划浇次号")
    @TableField(value = "c_cast_edt_seq")
    private String cCastEdtSeq;
    /**
    * 炉号后缀
    */
    @Schema(description = "炉号后缀")
    @TableField(value = "c_heat_id_after")
    private String cHeatIdAfter;
    /**
    * 处理次数
    */
    @Schema(description = "处理次数")
    @TableField(value = "c_treat_no")
    private String cTreatNo;
    /**
    * 钢坯成分等级
    */
    @Schema(description = "钢坯成分等级")
    @TableField(value = "c_slab_qual_level")
    private String cSlabQualLevel;
    /**
    * 装车单号
    */
    @Schema(description = "装车单号")
    @TableField(value = "c_load_no")
    private String cLoadNo;
    /**
    * 计划类别0：浇次内；1：浇次外；2：计划外
    */
    @Schema(description = "计划类别0：浇次内；1：浇次外；2：计划外")
    @TableField(value = "c_plan_type")
    private String cPlanType;
    /**
    * 切割流号
    */
    @Schema(description = "切割流号")
    @TableField(value = "n_liu_no")
    private Long nLiuNo;
    /**
    * 是否启用;默认为0,1为未启用
    */
    @Schema(description = "是否启用;默认为0,1为未启用")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time")
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}