package com.aitos.pss.dto.planmanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
* @title: 炉次浇次设计管理
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
public class UpdateTPssTaskListStoveOrderDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "是否交替生产")
    private  Integer isAlternate = 0;

    @Schema(description = "转炉炉座")
    private String stoveSeat;

    @Schema(description = "炉次设计数据")
    List<UpdateTPssTaskListDto> taskListDtoList;

}
