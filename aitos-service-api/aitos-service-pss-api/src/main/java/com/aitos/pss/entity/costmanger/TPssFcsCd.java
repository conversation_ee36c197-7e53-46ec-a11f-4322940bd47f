package com.aitos.pss.entity.costmanger;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* @title: 成本科目配置上表
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
@TableName("t_pss_fcs_cd")
@Tag(name = "成本科目配置上表对象", description = "成本科目配置上表")
public class TPssFcsCd implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 顺序号 */
  @Schema(description = "顺序号")
  @TableId
  private Long nId;

  /** 项目大类编码 */
  @Schema(description = "项目大类编码")
  @TableField(value = "c_mat_type_b_code")
  private String cMatTypeBCode;

  /** 项目大类名称 */
  @Schema(description = "项目大类名称")
  @TableField(value = "c_mat_type_b_name")
  private String cMatTypeBName;

  /** 项目小类编码 */
  @Schema(description = "项目小类编码")
  @TableField(value = "c_cost_item_code")
  private String cCostItemCode;

  /** 项目小类名称 */
  @Schema(description = "项目小类名称")
  @TableField(value = "c_cost_item_name")
  private String cCostItemName;

  /** 计量单位 */
  @Schema(description = "计量单位")
  @TableField(value = "c_mat_unit_id")
  private String cMatUnitId;

  /** 计量单位编码 */
  @Schema(description = "计量单位编码")
  @TableField(value = "c_mat_unit_code")
  private String cMatUnitCode;

  /** 计量单位名称 */
  @Schema(description = "计量单位名称")
  @TableField(value = "c_mat_unit_name")
  private String cMatUnitName;

  @Schema(description = "产线id")
  @TableField(value = "c_pro_line")
  private Long cProLine;

  @Schema(description = "产线")
  @TableField(value = "c_pro_line_code")
  private String cProLineCode;

  @Schema(description = "产线")
  @TableField(value = "c_pro_line_name")
  private String cProLineName;

  /** 显示顺序 */
  @Schema(description = "显示顺序")
  @TableField(value = "c_cost_seq")
  private Integer cCostSeq;

  /** 备注 */
  @Schema(description = "备注")
  @TableField(value = "c_memo")
  private String cMemo;

  /** 是否启用;默认为0,1为未启用 */
  @Schema(description = "是否启用;默认为0,1为未启用")
  @TableField(value = "n_enabled_mark")
  private Integer nEnabledMark;

  /** 创建人 */
  @Schema(description = "创建人")
  @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
  private Long nCreateUserId;

  /** */
  @Schema(description = "")
  @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
  private LocalDateTime dtCreateDateTime;

  /** 修改人 */
  @Schema(description = "修改人")
  @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
  private Long nModifyUserId;

  /** */
  @Schema(description = "")
  @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
  private LocalDateTime dtModifyDateTime;
}
