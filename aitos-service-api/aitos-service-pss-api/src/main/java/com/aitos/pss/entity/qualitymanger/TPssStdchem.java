package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 质量设计成分结果查看
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@TableName("t_pss_stdchem")
@Tag(name = "质量设计成分结果查看对象", description = "质量设计成分结果查看")
public class TPssStdchem implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "生产订单号")
    @TableField(value = "order_id")
    private String orderId;
    
    @Schema(description = "质量等级")
    @TableField(value = "c_std_class")
    private String cStdClass;
    
    @Schema(description = "质量编码id")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "牌号代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "下限比较符")
    @TableField(value = "c_comp_code_min")
    private String cCompCodeMin;
    
    @Schema(description = "上限比较符")
    @TableField(value = "c_comp_code_max")
    private String cCompCodeMax;
    
    @Schema(description = "化学成分代码")
    @TableField(value = "c_chem_comp_cd")
    private String cChemCompCd;
    
    @Schema(description = "化学成分最小值")
    @TableField(value = "c_chem_comp_min")
    private BigDecimal cChemCompMin;
    
    @Schema(description = "化学成分最大值")
    @TableField(value = "c_chem_comp_max")
    private BigDecimal cChemCompMax;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}