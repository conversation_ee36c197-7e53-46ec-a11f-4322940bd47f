package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssStdchemLgPageDto extends PageInput {

    @Schema(description = "质量编码id")
    private Long cQualityId;
    
    @Schema(description = "质量编码")
    private String cQualityCode;

    @Schema(description = "牌号代码")
    private String cStlGrdCd;

    @Schema(description = "牌号")
    private String cStlGrdDesc;
    
    @Schema(description = "化学成分代码")
    private String cChemCompCd;
    
    @Schema(description = "化学成分最小值")
    private BigDecimal cChemCompMin;
    
    @Schema(description = "化学成分最大值")
    private BigDecimal cChemCompMax;
    
    @Schema(description = "质量编码名称")
    private String cQualityCodeName;
    
    @Schema(description = "牌号")
    private String cStlGrdName;
    
    @Schema(description = "下限比较符")
    private String cCompCodeMin;
    
    @Schema(description = "上限比较符")
    private String cCompCodeMax;
    
    @Schema(description = "质量等级")
    private String cStdClass;

}
