package com.aitos.pss.dto.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 浇次标准-混浇钢种标准
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
public class UpdateTPssStlMingleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    private Long nId;

    @Schema(description = "首浇钢种")
    private String cStlGrdCdA;

    @Schema(description = "首浇钢种描述/名称")
    private String cStlGrdDescA;

    @Schema(description = "次浇钢种")
    private String cStlGrdCdB;

    @Schema(description = "次浇钢种描述/名称")
    private String cStlGrdDescB;

    @Schema(description = "产线")
    private Long cProLine;

    @Schema(description = "产线编码")
    private String cProLineCode;

    @Schema(description = "产线名称")
    private String cProLineName;
    
    @Schema(description = "浇次内顺序")
    private Integer nCastNum;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
}
