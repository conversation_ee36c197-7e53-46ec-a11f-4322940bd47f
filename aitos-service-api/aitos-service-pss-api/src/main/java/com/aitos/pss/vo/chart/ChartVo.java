package com.aitos.pss.vo.chart;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ChartVo<T> {

    @Schema(description = "标头")
    private String title;

    @Schema(description = "分部标头")
    private List<String> legendList;

    @Schema(description = "x轴")
    private List<String> xAxisList;

    @Schema(description = "数据")
    private T data;
}
