package com.aitos.pss.vo.costmanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TPssOrderCostPageVo {

    @Schema(description = "主键")
    private Long nId;

    @Schema(description = "产线")
    private String cLineCd;

    @Schema(description = "生产订单号")
    private String cOrderNo;

    @Schema(description = "产品名称")
    private String cProName;

    @Schema(description = "总成本")
    private BigDecimal nSumCost;

    @Schema(description = "人工成本")
    private BigDecimal nLaborCost;

    @Schema(description = "材料成本")
    private BigDecimal nMaterialCost;

    @Schema(description = "制造成本")
    private BigDecimal nManufactCost;

    @Schema(description = "单位成本")
    private BigDecimal nUnitCost;

    @Schema(description = "预计利润")
    private BigDecimal nEstimatedProfit;

    @Schema(description = "订单产量")
    private BigDecimal nOrderWgt;

    @Schema(description = "计划周期")
    private BigDecimal cPlanCycle;

    @Schema(description = "成本科目名称")
    private String cCostAccountName;

    @Schema(description = "成本科目代码")
    private String cCostAccountCode;

    @Schema(description = "计划金额")
    private BigDecimal nPlannedAmount;

    @Schema(description = "实际金额")
    private BigDecimal nActualAmount;

    @Schema(description = "差异金额")
    private BigDecimal nDifferenceAmount;

    @Schema(description = "差异率")
    private BigDecimal nDifferenceRate;

    @Schema(description = "创建人")
    private Long nCreateUserId;

    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;

    @Schema(description = "最后修改人")
    private Long nModifyUserId;

    @Schema(description = "最后修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "逻辑删除标记")
    private Integer nDeleteMark;

    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;
} 