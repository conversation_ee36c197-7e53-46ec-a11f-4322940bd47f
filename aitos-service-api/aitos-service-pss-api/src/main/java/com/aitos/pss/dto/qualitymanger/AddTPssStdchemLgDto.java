package com.aitos.pss.dto.qualitymanger;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
* @title: 成分标准管理-成分标准
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/
@Data
public class AddTPssStdchemLgDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "质量等级")
    private String cStdClass;

    @Schema(description = "质量编码id")
    private Long cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;
    
    @Schema(description = "质量编码名称")
    private String cQualityCodeName;

    @Schema(description = "牌号代码")
    private String cStlGrdCd;

    @Schema(description = "牌号")
    private String cStlGrdDesc;
    
    @Schema(description = "下限比较符")
    private String cCompCodeMin;
    
    @Schema(description = "上限比较符")
    private String cCompCodeMax;
    
    @Schema(description = "化学成分代码")
    private String cChemCompCd;
    
    @Schema(description = "化学成分最小值")
    private BigDecimal cChemCompMin;
    
    @Schema(description = "化学成分最大值")
    private BigDecimal cChemCompMax;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

}
