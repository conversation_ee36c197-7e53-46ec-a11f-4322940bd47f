package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-09
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssQualStdLibSubPageDto extends PageInput {

    @Schema(description = "质检标准编码")
    private String cStdcode;

    @Schema(description = "质检项目编码")
    private String cQualityStandardLibItemCode;

    @Schema(description = "质检项目名称")
    private String cQualityStandardLibItemName;

    @Schema(description = "是否启用")
    private Integer nEnabledMark;

    @Schema(description = "创建开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;

    @Schema(description = "创建结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
}
