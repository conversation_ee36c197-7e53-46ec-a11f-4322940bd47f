package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-16
* @Version 1.0
*/
@Data
public class TPssBatchSamplingPageVo {

    
    @ExcelIgnore
    @Schema(description = "")
    private String nId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("炉次号")
    @Schema(description = "炉次号")
    private String cHeatId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种代码")
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种名称")
    @Schema(description = "钢种名称")
    private String cStlGrdDesc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("规格")
    @Schema(description = "规格")
    private String cSpec;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("检验批号")
    @Schema(description = "检验批号")
    private String cSmpLot;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量编码")
    @Schema(description = "质量编码")
    private String cQualityCode;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产线")
    @Schema(description = "产线")
    @Trans(type = TransType.DIC, id = "1925381081532010497")
    private String cLineCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("物料长度")
    @Schema(description = "物料长度")
    private BigDecimal nThk;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("委托次数")
    @Schema(description = "委托次数")
    private Integer nSampNum;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("委托单号")
    @Schema(description = "委托单号")
    private String cTestcrdId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("检验炉次号")
    @Schema(description = "检验炉次号")
    private String cSmpHeatId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("委托单状态")
    @Schema(description = "委托单状态（A：等待发送、B：等待试验、C：试验完毕、D：性能判定完毕）")
    @Trans(type = TransType.DIC, id = "1933077518335295490")
    private String cSmpStates;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("检验批次包含炉次号")
    @Schema(description = "检验批次包含炉次号")
    private String cSampHeatIdList;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;

}
