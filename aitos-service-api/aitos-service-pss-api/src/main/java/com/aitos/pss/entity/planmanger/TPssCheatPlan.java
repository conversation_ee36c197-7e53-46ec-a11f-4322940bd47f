package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 炼钢计划任务调度
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
@TableName("t_pss_cheat_plan")
@Tag(name = "炼钢计划任务调度对象", description = "炼钢计划任务调度")
public class TPssCheatPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "序列号")
    @TableId
    private Long nId;
    
    @Schema(description = "炉次编制号")
    @TableField(value = "n_heat_edt_seq")
    private BigDecimal nHeatEdtSeq;
    
    @Schema(description = "计划炉次号")
    @TableField(value = "c_plan_heat_id")
    private String cPlanHeatId;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "质量编码id")
    @TableField(value = "c_mat_qul_id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_cd")
    private String cMatQulCd;

    @Schema(description = "质量编码名称")
    @TableField(value = "c_mat_qul_name")
    private String cMatQulName;
    
    @Schema(description = "炼钢工艺流程")
    @TableField(value = "c_pl_route")
    private String cPlRoute;
    
    @Schema(description = "浇铸厚度")
    @TableField(value = "n_ccm_thk")
    private BigDecimal nCcmThk;
    
    @Schema(description = "浇铸宽度")
    @TableField(value = "n_ccm_wth")
    private BigDecimal nCcmWth;
    
    @Schema(description = "浇铸时长")
    @TableField(value = "n_ccm_tme")
    private Long nCcmTme;
    
    @Schema(description = "订单板坯张数")
    @TableField(value = "n_ord_slab_cnt")
    private Long nOrdSlabCnt;
    
    @Schema(description = "余材板坯张数")
    @TableField(value = "n_woo_slab_cnt")
    private Long nWooSlabCnt;
    
    @Schema(description = "余材炉次代码")
    @TableField(value = "c_woo_heat_fl")
    private String cWooHeatFl;
    
    @Schema(description = "板坯块数")
    @TableField(value = "n_slab_cnt")
    private Long nSlabCnt;
    
    @Schema(description = "计划出钢量")
    @TableField(value = "n_pre_heat_wgt")
    private BigDecimal nPreHeatWgt;
    
    @Schema(description = "炼钢作业期限")
    @TableField(value = "c_sms_duedatetime")
    private String cSmsDuedatetime;
    
    @Schema(description = "浇次编制号")
    @TableField(value = "n_cast_edt_seq")
    private String nCastEdtSeq;
    
    @Schema(description = "计划浇次号")
    @TableField(value = "c_plan_cast_id")
    private String cPlanCastId;
    
    @Schema(description = "浇次内顺序号")
    @TableField(value = "n_cast_heat_seq")
    private Long nCastHeatSeq;
    
    @Schema(description = "浇次炉数")
    @TableField(value = "n_cast_heat_cnt")
    private Long nCastHeatCnt;
    
    @Schema(description = "转炉炉座号id")
    @TableField(value = "c_ld_wkst_id")
    private Long cLdWkstId;

    @Schema(description = "转炉炉座号")
    @TableField(value = "c_ld_wkst")
    private String cLdWkst;

    @Schema(description = "转炉炉座号名称")
    @TableField(value = "c_ld_wkst_name")
    private String cLdWkstName;
    
    @Schema(description = "下达时间")
    @TableField(value = "dt_pre_ld_time")
    private LocalDateTime dtPreLdTime;
    
    @Schema(description = "计划出钢开始时间")
    @TableField(value = "dt_pre_ld_str_tme")
    private LocalDateTime dtPreLdStrTme;
    
    @Schema(description = "计划出钢结束时间")
    @TableField(value = "dt_pre_ld_end_tme")
    private LocalDateTime dtPreLdEndTme;
    
    @Schema(description = "计划第一次LF炉座号id")
    @TableField(value = "c_fir_lf_wkst_id")
    private Long cFirLfWkstId;

    @Schema(description = "计划第一次LF炉座号")
    @TableField(value = "c_fir_lf_wkst")
    private String cFirLfWkst;

    @Schema(description = "计划第一次LF炉座号名称")
    @TableField(value = "c_fir_lf_wkst_name")
    private String cFirLfWkstName;
    
    @Schema(description = "计划第一次 LF 开始时间")
    @TableField(value = "dt_fir_lf_sttime")
    private LocalDateTime dtFirLfSttime;
    
    @Schema(description = "计划第一次 LF 结束时间")
    @TableField(value = "dt_fir_lf_endtime")
    private LocalDateTime dtFirLfEndtime;
    
    @Schema(description = "计划第二次LF炉座号")
    @TableField(value = "c_sec_lf_wkst_id")
    private Long cSecLfWkstId;

    @Schema(description = "计划第二次LF炉座号")
    @TableField(value = "c_sec_lf_wkst")
    private String cSecLfWkst;

    @Schema(description = "计划第二次LF炉座号")
    @TableField(value = "c_sec_lf_wkst_name")
    private String cSecLfWkstName;
    
    @Schema(description = "计划第二次 LF 开始时间")
    @TableField(value = "dt_sec_lf_sttime")
    private LocalDateTime dtSecLfSttime;
    
    @Schema(description = "计划第二次 LF 结束时间")
    @TableField(value = "dt_sec_lf_endtime")
    private LocalDateTime dtSecLfEndtime;
    
    @Schema(description = "计划第一次RH炉座号")
    @TableField(value = "c_fir_vd_wkst_id")
    private Long cFirVdWkstId;

    @Schema(description = "计划第一次RH炉座号")
    @TableField(value = "c_fir_vd_wkst")
    private String cFirVdWkst;

    @Schema(description = "计划第一次RH炉座号名称")
    @TableField(value = "c_fir_vd_wkst_name")
    private String cFirVdWkstName;
    
    @Schema(description = "计划第一次RH开始时间")
    @TableField(value = "dt_fir_vd_sttime")
    private LocalDateTime dtFirVdSttime;
    
    @Schema(description = "计划第一次RH结束时间")
    @TableField(value = "dt_fir_vd_endtime")
    private LocalDateTime dtFirVdEndtime;
    
    @Schema(description = "计划第二次RH炉座号id")
    @TableField(value = "c_sec_vd_wkst_id")
    private Long cSecVdWkstId;

    @Schema(description = "计划第二次RH炉座号")
    @TableField(value = "c_sec_vd_wkst")
    private String cSecVdWkst;

    @Schema(description = "计划第二次RH炉座号名称")
    @TableField(value = "c_sec_vd_wkst_name")
    private String cSecVdWkstName;
    
    @Schema(description = "计划第二次RH 开始时间")
    @TableField(value = "dt_sec_vd_sttime")
    private LocalDateTime dtSecVdSttime;
    
    @Schema(description = "计划第二次RH结束时间")
    @TableField(value = "dt_sec_vd_endtime")
    private LocalDateTime dtSecVdEndtime;
    
    @Schema(description = "大包开浇时间")
    @TableField(value = "dt_plan_ladle_open")
    private LocalDateTime dtPlanLadleOpen;
    
    @Schema(description = "连铸机号")
    @TableField(value = "c_ccm_wkst")
    private String cCcmWkst;
    
    @Schema(description = "计划浇铸开始时间")
    @TableField(value = "dt_pre_ccm_str_tme")
    private LocalDateTime dtPreCcmStrTme;
    
    @Schema(description = "计划浇铸结束时间")
    @TableField(value = "dt_pre_ccm_end_tme")
    private LocalDateTime dtPreCcmEndTme;
    
    @Schema(description = "混炉标志")
    @TableField(value = "c_mstlgrd_fl")
    private String cMstlgrdFl;
    
    @Schema(description = "状态")
    @TableField(value = "c_status")
    private String cStatus;
    
    @Schema(description = "实绩炉次号")
    @TableField(value = "c_heat_id")
    private String cHeatId;
    
    @Schema(description = "实绩钢种")
    @TableField(value = "c_act_stl_grd_cd")
    private String cActStlGrdCd;

    @Schema(description = "实绩钢种描述/名称")
    @TableField(value = "c_act_stl_grd_desc")
    private String cActStlGrdDesc;
    
    @Schema(description = "实绩材质")
    @TableField(value = "c_act_mat_qul_cd")
    private String cActMatQulCd;
    
    @Schema(description = "连铸机号")
    @TableField(value = "c_cast_id")
    private String cCastId;
    
    @Schema(description = "读取标志 N-新计划")
    @TableField(value = "c_huntie_wks")
    private String cHuntieWks;
    
    @Schema(description = "米重")
    @TableField(value = "c_yuchuli_wks")
    private String cYuchuliWks;
    
    @Schema(description = "当前工位")
    @TableField(value = "c_curr_station_cd")
    private String cCurrStationCd;
    
    @Schema(description = "当前事件（P0210）")
    @TableField(value = "c_curr_event_cd")
    private String cCurrEventCd;
    
    @Schema(description = "前一事件（P0210）")
    @TableField(value = "c_prev_event_cd")
    private String cPrevEventCd;
    
    @Schema(description = "坯料长度")
    @TableField(value = "n_slab_len")
    private BigDecimal nSlabLen;
    
    @Schema(description = "任务单号")
    @TableField(value = "c_task_list_id")
    private String cTaskListId;
    
    @Schema(description = "转炉时间是否改变")
    @TableField(value = "n_if_change_time")
    private Long nIfChangeTime;
    
    @Schema(description = "精炼时间是否改变")
    @TableField(value = "n_if_change_time_lf")
    private Long nIfChangeTimeLf;
    
    @Schema(description = "VOD时间是否改变")
    @TableField(value = "n_if_change_time_vod")
    private Long nIfChangeTimeVod;
    
    @Schema(description = "连铸时间是否改变")
    @TableField(value = "n_if_change_time_ccm")
    private Long nIfChangeTimeCcm;
    
    @Schema(description = "产品直径")
    @TableField(value = "n_prod_thk")
    private BigDecimal nProdThk;
    
    @Schema(description = "产品长度")
    @TableField(value = "n_prod_len")
    private BigDecimal nProdLen;
    
    @Schema(description = "电炉状态")
    @TableField(value = "c_ld_status")
    private String cLdStatus;
    
    @Schema(description = "精炼状态")
    @TableField(value = "c_lf_status")
    private String cLfStatus;
    
    @Schema(description = "VD状态")
    @TableField(value = "c_vd_status")
    private String cVdStatus;
    
    @Schema(description = "连铸状态")
    @TableField(value = "c_ccm_status")
    private String cCcmStatus;
    
    @Schema(description = "电炉时间")
    @TableField(value = "c_ld_time")
    private String cLdTime;
    
    @Schema(description = "精炼时间")
    @TableField(value = "c_lf_time")
    private String cLfTime;
    
    @Schema(description = "VD时间")
    @TableField(value = "c_vd_time")
    private String cVdTime;
    
    @Schema(description = "连铸时间")
    @TableField(value = "c_ccm_time")
    private String cCcmTime;
    
    @Schema(description = "连铸开始时间")
    @TableField(value = "c_ccm_str_time")
    private String cCcmStrTime;
    
    @Schema(description = "VD开始时间")
    @TableField(value = "c_vd_str_time")
    private String cVdStrTime;
    
    @Schema(description = "精炼开始时间")
    @TableField(value = "c_lf_str_time")
    private String cLfStrTime;
    
    @Schema(description = "电炉开始时间")
    @TableField(value = "c_ld_str_time")
    private String cLdStrTime;
    
    @Schema(description = "炉次制造命令处理区分")
    @TableField(value = "n_deal_flag")
    private BigDecimal nDealFlag;
    
    @Schema(description = "制造命令号")
    @TableField(value = "c_pono")
    private String cPono;
    
    @Schema(description = "CAST_LOT分割号")
    @TableField(value = "c_cast_lot_div_no")
    private String cCastLotDivNo;
    
    @Schema(description = "连铸机类型 PM44")
    @TableField(value = "c_cc_type")
    private String cCcType;
    
    @Schema(description = "甘特图显示标志")
    @TableField(value = "c_restrand_flg")
    private String cRestrandFlg;
    
    @Schema(description = "冶炼模式")
    @TableField(value = "c_smelt_mode")
    private String cSmeltMode;
    
    @Schema(description = "材料去向 PM16")
    @TableField(value = "c_dest")
    private String cDest;
    
    @Schema(description = "计划日期")
    @TableField(value = "c_plan_datetime")
    private LocalDateTime cPlanDatetime;
    
    @Schema(description = "客户编码")
    @TableField(value = "c_customer_cd")
    private String cCustomerCd;
    
    @Schema(description = "客户名称")
    @TableField(value = "c_customer_name")
    private String cCustomerName;
    
    @Schema(description = "浇铸长度")
    @TableField(value = "c_ccm_len")
    private String cCcmLen;
    
    @Schema(description = "钢水回炉目标炉号(改为支数确认标记)")
    @TableField(value = "c_return_heat_id")
    private String cReturnHeatId;
    
    @Schema(description = "钢水回炉去向(改为代表样确认标记)")
    @TableField(value = "c_return_direction")
    private String cReturnDirection;
    
    @Schema(description = "材料去向2")
    @TableField(value = "c_dest2")
    private String cDest2;
    
    @Schema(description = "定重")
    @TableField(value = "c_ccm_wgt")
    private String cCcmWgt;
    
    @Schema(description = "钢包号")
    @TableField(value = "c_ld_id")
    private String cLdId;
    
    @Schema(description = "是否变定尺")
    @TableField(value = "c_len_is_change")
    private String cLenIsChange;
    
    @Schema(description = "是否变钢种标记")
    @TableField(value = "c_stl_grd_change")
    private String cStlGrdChange;
    
    @Schema(description = "月生产的第几炉")
    @TableField(value = "c_mon_pro_seq")
    private String cMonProSeq;
    
    @Schema(description = "转炉作业时长")
    @TableField(value = "n_ld_tme")
    private Long nLdTme;
    
    @Schema(description = "是否浇次首炉")
    @TableField(value = "c_is_first_plan")
    private String cIsFirstPlan;
    
    @Schema(description = "生产班次")
    @TableField(value = "c_prod_shift")
    private String cProdShift;
    
    @Schema(description = "生产班组")
    @TableField(value = "c_prod_group")
    private String cProdGroup;
    
    @Schema(description = "转炉标准时长")
    @TableField(value = "c_ld_stdtime")
    private String cLdStdtime;
    
    @Schema(description = "前一炉转炉结束时间")
    @TableField(value = "st_before_ldendtime")
    private LocalDateTime stBeforeLdendtime;
    
    @Schema(description = "连铸标准时长")
    @TableField(value = "c_ccm_stdtime")
    private String cCcmStdtime;
    
    @Schema(description = "前一炉连铸结束时间")
    @TableField(value = "dt_before_ccmendtime")
    private LocalDateTime dtBeforeCcmendtime;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}