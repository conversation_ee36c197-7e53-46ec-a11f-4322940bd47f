package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Data
public class TPssTechLfPageVo {

    
    @ExcelIgnore
    @Schema(description = "主键")
    private String nId;

    @Schema(description = "质量编码id")
    private Long cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;

    @Schema(description = "质量编码名称")
    private String cQualityCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;

    @Schema(description = "碱度")
    private BigDecimal cAlcalinity;

    @Schema(description = "CaSi喂丝量")
    private BigDecimal cCasi;

    @Schema(description = "CaAl喂丝量")
    private BigDecimal cCaal;

    @Schema(description = "Ca喂丝量")
    private BigDecimal cCa;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("净吹氩时间")
    @Schema(description = "净吹氩时间")
    private String cArgonBlow;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("LF搬出温度")
    @Schema(description = "LF搬出温度")
    private BigDecimal cLfMoveoutTemp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("VOD保压真空度")
    @Schema(description = "VOD保压真空度")
    private String cVodPressVac;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("VOD保持时间")
    @Schema(description = "VOD保持时间")
    private String cVodKeepTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("VOD搬出温度")
    @Schema(description = "VOD搬出温度")
    private BigDecimal cVodMoveoutTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("RH保压真空度")
    @Schema(description = "RH保压真空度")
    private String cRhPressVac;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("RH保持时间")
    @Schema(description = "RH保持时间")
    private String cRhKeepTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("RH搬出温度")
    @Schema(description = "RH搬出温度")
    private BigDecimal cRhMoveoutTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("LF炼焦")
    @Schema(description = "LF炼焦")
    private String cLfLianjiao;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("VOD炼焦")
    @Schema(description = "VOD炼焦")
    private String cVodLianjiao;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("RH炼焦")
    @Schema(description = "RH炼焦")
    private String cRhLianjiao;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("石灰")
    @Schema(description = "石灰")
    private String cLime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("萤石")
    @Schema(description = "萤石")
    private String cFluorite;
    /**
    * 硅灰石
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("硅灰石")
    @Schema(description = "硅灰石")
    private String cSilicaFume;
    /**
    * 精炼渣
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("精炼渣")
    @Schema(description = "精炼渣")
    private String cRefiningSlag;
    /**
    * 渣洗剂
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("渣洗剂")
    @Schema(description = "渣洗剂")
    private String cSlagLotion;
    /**
    * 石英砂
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("石英砂")
    @Schema(description = "石英砂")
    private String cQuartzSand;
    /**
    * 电石
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("电石")
    @Schema(description = "电石")
    private String cCalciumCarbide;
    /**
    * LF连浇搬出温度
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("LF连浇搬出温度")
    @Schema(description = "LF连浇搬出温度")
    private BigDecimal cLfMorecastTemp;
    /**
    * RH连浇搬出温度
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("RH连浇搬出温度")
    @Schema(description = "RH连浇搬出温度")
    private BigDecimal cRhMoveoutCastTime;
    /**
    * 真空环流
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("真空环流")
    @Schema(description = "真空环流")
    private String cVacuumCirculation;
    /**
    * 白渣保持时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("白渣保持时间")
    @Schema(description = "白渣保持时间")
    private String cWSlagHTime;
    /**
    * 精炼标准时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("精炼标准时间")
    @Schema(description = "精炼标准时间")
    private String cLfTimeBz;
    /**
    * TFE+MNO
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("TFE+MNO")
    @Schema(description = "TFE+MNO")
    private String cTfemno;
    /**
    * 钙渣球
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钙渣球")
    @Schema(description = "钙渣球")
    private String cCaBall;
    /**
    * 碱度最小
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("碱度最小")
    @Schema(description = "碱度最小")
    private BigDecimal cAlcalinityMin;
    /**
    * 碱度最大
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("碱度最大")
    @Schema(description = "碱度最大")
    private BigDecimal cAlcalinityMax;
    /**
    * 是否启用;默认为0,1为未启用
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
