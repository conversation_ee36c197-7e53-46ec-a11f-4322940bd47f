package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 轧制工艺质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@TableName("t_pss_roll_std_info")
@Tag(name = "轧制工艺质量设计结果对象", description = "轧制工艺质量设计结果")
public class TPssRollStdInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "订单号")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    
    @Schema(description = "质量编码")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码简称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "控制轧制")
    @TableField(value = "n_control_roll")
    private Long nControlRoll;
    
    @Schema(description = "开轧温度℃")
    @TableField(value = "n_start_temp")
    private BigDecimal nStartTemp;
    
    @Schema(description = "粗轧轧制温度℃")
    @TableField(value = "n_first_roll_temp")
    private BigDecimal nFirstRollTemp;
    
    @Schema(description = "精轧轧制温度℃")
    @TableField(value = "n_sec_roll_temp")
    private BigDecimal nSecRollTemp;
    
    @Schema(description = "终轧温度℃")
    @TableField(value = "n_final_temp")
    private BigDecimal nFinalTemp;
    
    @Schema(description = "备用1")
    @TableField(value = "c_reserver1")
    private String cReserver1;
    
    @Schema(description = "备用2")
    @TableField(value = "c_reserver2")
    private String cReserver2;
    
    @Schema(description = "备用3")
    @TableField(value = "c_reserver3")
    private String cReserver3;
    
    @Schema(description = "备用4")
    @TableField(value = "c_reserver4")
    private String cReserver4;
    
    @Schema(description = "备用5")
    @TableField(value = "c_reserver5")
    private String cReserver5;
    
    @Schema(description = "备用6")
    @TableField(value = "c_reserver6")
    private String cReserver6;
    
    @Schema(description = "备用7")
    @TableField(value = "c_reserver7")
    private String cReserver7;
    
    @Schema(description = "备用8")
    @TableField(value = "c_reserver8")
    private String cReserver8;
    
    @Schema(description = "备用9")
    @TableField(value = "c_reserver9")
    private String cReserver9;
    
    @Schema(description = "备用10")
    @TableField(value = "c_reserver10")
    private String cReserver10;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}