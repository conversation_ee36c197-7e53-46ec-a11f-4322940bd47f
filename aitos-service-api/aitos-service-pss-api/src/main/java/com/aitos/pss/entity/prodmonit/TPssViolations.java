package com.aitos.pss.entity.prodmonit;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 违规单管理
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
@TableName("t_pss_violations")
@Tag(name = "违规单管理对象", description = "违规单管理")
public class TPssViolations implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    @Schema(description = "主键id")
    @TableId
    private Long nId;
    /**
    * 指标ID
    */
    @Schema(description = "指标ID")
    @TableField(value = "n_indicator_id")
    private Long nIndicatorId;
    /**
    * 指标code
    */
    @Schema(description = "指标code")
    @TableField(value = "c_indicator_code")
    private String cIndicatorCode;
    /**
    * 指标name
    */
    @Schema(description = "指标name")
    @TableField(value = "c_indicator_name")
    private String cIndicatorName;
    /**
    * 设备id
    */
    @Schema(description = "设备id")
    @TableField(value = "n_equipment_id")
    private Long nEquipmentId;
    /**
    * 设备code
    */
    @Schema(description = "设备code")
    @TableField(value = "c_equipment_code")
    private String cEquipmentCode;
    /**
    * 设备name
    */
    @Schema(description = "设备name")
    @TableField(value = "c_equipment_name")
    private String cEquipmentName;
    /**
    * 违规单号
    */
    @Schema(description = "违规单号")
    @TableField(value = "c_code")
    private String cCode;
    /**
    * 违规描述
    */
    @Schema(description = "违规描述")
    @TableField(value = "c_violation_description")
    private String cViolationDescription;
    /**
    * 违规时间
    */
    @Schema(description = "违规时间")
    @TableField(value = "dt_violation_date_time")
    private LocalDateTime dtViolationDateTime;
    /**
    * 关闭时间
    */
    @Schema(description = "关闭时间")
    @TableField(value = "dt_close_date_time")
    private LocalDateTime dtCloseDateTime;
    /**
    * 违规值
    */
    @Schema(description = "违规值")
    @TableField(value = "n_error_value")
    private BigDecimal nErrorValue;
    /**
    * 标准值
    */
    @Schema(description = "标准值")
    @TableField(value = "n_stand_value")
    private BigDecimal nStandValue;
    /**
    * 状态（待审核/已通过/已驳回）
    */
    @Schema(description = "状态（待审核/已通过/已驳回）")
    @TableField(value = "c_status")
    private String cStatus;
    /**
    * 责任人ID
    */
    @Schema(description = "责任人ID")
    @TableField(value = "n_responsible_user_id")
    private Long nResponsibleUserId;
    /**
    * 审批人ID
    */
    @Schema(description = "审批人ID")
    @TableField(value = "n_approver_id")
    private Long nApproverId;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}