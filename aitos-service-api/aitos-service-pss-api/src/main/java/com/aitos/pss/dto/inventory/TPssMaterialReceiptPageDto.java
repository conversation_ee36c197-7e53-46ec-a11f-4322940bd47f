package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssMaterialReceiptPageDto extends PageInput {

    /**
    * 入库单号
    */
    @Schema(description = "入库单号")
    private String cStorageNo;
    /**
    * 计量单号
    */
    @Schema(description = "计量单号")
    private String cMeasureNo;
    /**
    * 采购订单行号
    */
    @Schema(description = "采购订单行号")
    private String cPurchaseOrderLine;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMaterialName;
    /**
    * 到货数量
    */
    @Schema(description = "到货数量")
    private BigDecimal nQuantity;
    /**
    * 入库确认重量
    */
    @Schema(description = "入库确认重量")
    private BigDecimal nConfirmWeight;
    /**
    * 到货日期字段开始时间
    */
    @Schema(description = "到货日期字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReceiptDateStart;
    /**
    * 到货日期字段结束时间
    */
    @Schema(description = "到货日期字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReceiptDateEnd;
    /**
    * 目的库位
    */
    @Schema(description = "目的库位")
    private String cStorageLocation;
    /**
    * 状态(pending:待检验,testing:检验中,completed:已完成,rejected:不合格)
    */
    @Schema(description = "状态(pending:待检验,testing:检验中,completed:已完成,rejected:不合格)")
    private String cStatus;
    /**
    * 到货单号
    */
    @Schema(description = "到货单号")
    private String cReceiptNo;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    private String cBatchNo;
    /**
    * 采购订单号
    */
    @Schema(description = "采购订单号")
    private String cPurchaseOrderNo;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMaterialCode;
    /**
    * 物料类型(raw:原辅料,alloy:合金,scrap:废钢)
    */
    @Schema(description = "物料类型(raw:原辅料,alloy:合金,scrap:废钢)")
    private String cMaterialType;
    /**
    * 入库检斤重量
    */
    @Schema(description = "入库检斤重量")
    private BigDecimal nMeasureWeight;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private String cUnit;
    /**
    * 供应商
    */
    @Schema(description = "供应商")
    private String cSupplier;
    /**
    * 目的库位名
    */
    @Schema(description = "目的库位名")
    private String cStorageLocationName;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
