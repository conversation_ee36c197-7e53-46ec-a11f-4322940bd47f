package com.aitos.pss.vo.planmanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
public class TPssWgtOneMaterVo {

    
    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "钢种")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;
    
    @Schema(description = "规格")
    private String cSpec;
    
    @Schema(description = "密度")
    private BigDecimal nDensity;
    
    @Schema(description = "米重")
    private BigDecimal nWgtOneMater;
    
    @Schema(description = "计算方式;0为米重，1为密度")
    private String cCalculationMethod;

    @Schema(description = "铸机号id")
    private Long cCcmId;

    @Schema(description = "铸机号code")
    private String cCcmNo;

    @Schema(description = "铸机号name")
    private String cCcmName;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    private Long nModifyUserId;
}
