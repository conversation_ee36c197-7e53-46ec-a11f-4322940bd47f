package com.aitos.pss.dto.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 连铸质量工艺参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
public class UpdateTPssTechCcmInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "质量编码id")
    private Long cQualId;

    @Schema(description = "质量编码")
    private String cQualCode;

    @Schema(description = "质量编码名称")
    private String cQualCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;
    
    @Schema(description = "液相线")
    private String nLiquidPhase;

    @Schema(description = "中包目标温度")
    private BigDecimal nMidPagTemp;

    @Schema(description = "搬出温度")
    private BigDecimal nMoveoutTime;

    @Schema(description = "起步温度")
    private BigDecimal nStartTemp;

    @Schema(description = "连浇温度")
    private BigDecimal nCastTemp;
    
    @Schema(description = "保护渣型号")
    private String cFluxModel;
    
    @Schema(description = "钢包铸余")
    private String nSldSurplus;
    
    @Schema(description = "结晶器水量")
    private String nWaterMould;
    
    @Schema(description = "比水量")
    private String nSpecificVolume;
    
    @Schema(description = "中包水口")
    private String cTundishNozzle;
    
    @Schema(description = "连铸机号")
    private String cConNo;
    
    @Schema(description = "首端电搅")
    private String cHeadEleSti;
    
    @Schema(description = "末端电搅")
    private String cTailEleSti;
    
    @Schema(description = "振幅")
    private String cAmplitude;
    
    @Schema(description = "振频")
    private String cVibrationFrequency;
    
    @Schema(description = "液面波动")
    private String cLiquidLevelFluc;
    
    @Schema(description = "备用1")
    private String cBackup1;
    
    @Schema(description = "备用2")
    private String cBackup2;
    
    @Schema(description = "备用3")
    private String cBackup3;
    
    @Schema(description = "备用4")
    private String cBackup4;
    
    @Schema(description = "备用5")
    private String cBackup5;
    
    @Schema(description = "备用6")
    private String cBackup6;
    
    @Schema(description = "备用7")
    private String cBackup7;
    
    @Schema(description = "备用8")
    private String cBackup8;
    
    @Schema(description = "备用9")
    private String cBackup9;
    
    @Schema(description = "备用10")
    private String cBackup10;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

}
