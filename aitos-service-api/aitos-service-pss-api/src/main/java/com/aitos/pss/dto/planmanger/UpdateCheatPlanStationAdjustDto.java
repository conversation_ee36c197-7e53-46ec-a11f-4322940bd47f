package com.aitos.pss.dto.planmanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
* @title: 炼钢计划任务调度
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
public class UpdateCheatPlanStationAdjustDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "是否转炉")
    private Boolean isCLd;

    @Schema(description = "转炉")
    private String cLd;

    @Schema(description = "是否LF")
    private Boolean isLF;

    @Schema(description = "LF")
    private String lF;

    @Schema(description = "是否连铸")
    private Boolean isCcm;

    @Schema(description = "连铸")
    private String ccm;

    @Schema(description = "是否移动")
    private Boolean isMove;

    @Schema(description = "是否交换")
    private Boolean isSwap;

    @Schema(description = "操作数据")
    private List<UpdateTPssCheatPlanDto> dtoList;

}
