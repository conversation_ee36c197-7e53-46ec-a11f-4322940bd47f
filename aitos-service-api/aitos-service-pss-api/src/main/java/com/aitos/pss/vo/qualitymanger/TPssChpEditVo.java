package com.aitos.pss.vo.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
public class TPssChpEditVo {

    /**
    * 序列号
    */
    @Schema(description = "序列号")
    private Long nId;
    /**
    * 炉次编制号
    */
    @Schema(description = "炉次编制号")
    private BigDecimal nHeatEdtSeq;
    /**
    * 计划炉次号
    */
    @Schema(description = "计划炉次号")
    private String cPlanHeatId;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    private String cMatQulCd;
    /**
    * 炼钢工艺流程
    */
    @Schema(description = "炼钢工艺流程")
    private String cPlRoute;
    /**
    * 浇铸厚度
    */
    @Schema(description = "浇铸厚度")
    private BigDecimal nCcmThk;
    /**
    * 浇铸宽度
    */
    @Schema(description = "浇铸宽度")
    private BigDecimal nCcmWth;
    /**
    * 浇铸时长
    */
    @Schema(description = "浇铸时长")
    private Long nCcmTme;
    /**
    * 订单板坯张数
    */
    @Schema(description = "订单板坯张数")
    private Long nOrdSlabCnt;
    /**
    * 余材板坯张数
    */
    @Schema(description = "余材板坯张数")
    private Long nWooSlabCnt;
    /**
    * 余材炉次代码
    */
    @Schema(description = "余材炉次代码")
    private String cWooHeatFl;
    /**
    * 板坯块数
    */
    @Schema(description = "板坯块数")
    private Long nSlabCnt;
    /**
    * 计划出钢量
    */
    @Schema(description = "计划出钢量")
    private BigDecimal nPreHeatWgt;
    /**
    * 炼钢作业期限
    */
    @Schema(description = "炼钢作业期限")
    private String cSmsDuedatetime;
    /**
    * 浇次编制号
    */
    @Schema(description = "浇次编制号")
    private String nCastEdtSeq;
    /**
    * 计划浇次号
    */
    @Schema(description = "计划浇次号")
    private String cPlanCastId;
    /**
    * 浇次内顺序号
    */
    @Schema(description = "浇次内顺序号")
    private Long nCastHeatSeq;
    /**
    * 浇次炉数
    */
    @Schema(description = "浇次炉数")
    private Long nCastHeatCnt;
    /**
    * 转炉炉座号
    */
    @Schema(description = "转炉炉座号")
    private String cLdWkst;
    /**
    * 计划冶炼开始时间
    */
    @Schema(description = "计划冶炼开始时间")
    private LocalDateTime dtPreLdTime;
    /**
    * 计划出钢开始时间
    */
    @Schema(description = "计划出钢开始时间")
    private LocalDateTime dtPreLdStrTme;
    /**
    * 计划出钢结束时间
    */
    @Schema(description = "计划出钢结束时间")
    private LocalDateTime dtPreLdEndTme;
    /**
    * 计划第一次LF炉座号
    */
    @Schema(description = "计划第一次LF炉座号")
    private String cFirLfWkst;
    /**
    * 计划第一次 LF 开始时间
    */
    @Schema(description = "计划第一次 LF 开始时间")
    private LocalDateTime dtFirLfSttime;
    /**
    * 计划第一次 LF 结束时间
    */
    @Schema(description = "计划第一次 LF 结束时间")
    private LocalDateTime dtFirLfEndtime;
    /**
    * 计划第二次LF炉座号
    */
    @Schema(description = "计划第二次LF炉座号")
    private String cSecLfWkst;
    /**
    * 计划第二次 LF 开始时间
    */
    @Schema(description = "计划第二次 LF 开始时间")
    private LocalDateTime dtSecLfSttime;
    /**
    * 计划第二次 LF 结束时间
    */
    @Schema(description = "计划第二次 LF 结束时间")
    private LocalDateTime dtSecLfEndtime;
    /**
    * 计划第一次VOD炉座号
    */
    @Schema(description = "计划第一次VOD炉座号")
    private String cFirVdWkst;
    /**
    * 计划第一次VD开始时间
    */
    @Schema(description = "计划第一次VD开始时间")
    private LocalDateTime dtFirVdSttime;
    /**
    * 计划第一次VD结束时间
    */
    @Schema(description = "计划第一次VD结束时间")
    private LocalDateTime dtFirVdEndtime;
    /**
    * 计划第二次VD炉座号
    */
    @Schema(description = "计划第二次VD炉座号")
    private String cSecVdWkst;
    /**
    * 计划第二次VD 开始时间
    */
    @Schema(description = "计划第二次VD 开始时间")
    private LocalDateTime dtSecVdSttime;
    /**
    * 计划第二次VD结束时间
    */
    @Schema(description = "计划第二次VD结束时间")
    private LocalDateTime dtSecVdEndtime;
    /**
    * 大包开浇时间
    */
    @Schema(description = "大包开浇时间")
    private LocalDateTime dtPlanLadleOpen;
    /**
    * 连铸机号
    */
    @Schema(description = "连铸机号")
    private String cCcmWkst;
    /**
    * 计划浇铸开始时间
    */
    @Schema(description = "计划浇铸开始时间")
    private LocalDateTime dtPreCcmStrTme;
    /**
    * 计划浇铸结束时间
    */
    @Schema(description = "计划浇铸结束时间")
    private LocalDateTime dtPreCcmEndTme;
    /**
    * 混炉标志
    */
    @Schema(description = "混炉标志")
    private String cMstlgrdFl;
    /**
    * 变更程序ID
    */
    @Schema(description = "变更程序ID")
    private String cUpdPgmid;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 炉号
    */
    @Schema(description = "炉号")
    private String cHeatId;
    /**
    * 实际钢种代码
    */
    @Schema(description = "实际钢种代码")
    private String cActStlGrdCd;
    /**
    * 实际质量编码
    */
    @Schema(description = "实际质量编码")
    private String cActMatQulCd;
    /**
    * 连铸机号
    */
    @Schema(description = "连铸机号")
    private String cCastId;
    /**
    * 混铁炉座次
    */
    @Schema(description = "混铁炉座次")
    private String cHuntieWks;
    /**
    * 预处理座次
    */
    @Schema(description = "预处理座次")
    private String cYuchuliWks;
    /**
    * 当前工位
    */
    @Schema(description = "当前工位")
    private String cCurrStationCd;
    /**
    * 当前事件
    */
    @Schema(description = "当前事件")
    private String cCurrEventCd;
    /**
    * 上一事件
    */
    @Schema(description = "上一事件")
    private String cPrevEventCd;
    /**
    * 坯料长度
    */
    @Schema(description = "坯料长度")
    private BigDecimal cSlabLen;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    private String taskListId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;



}
