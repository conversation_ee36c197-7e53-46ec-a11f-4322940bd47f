package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 精炼工艺参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
public class UpdateTPssTechLfInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "生产任务单号")
    private String orderId;

    @Schema(description = "质量编码id")
    private Long cQualId;

    @Schema(description = "质量编码")
    private String cQualCode;

    @Schema(description = "质量编码名称")
    private String cQualCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCode;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;

    @Schema(description = "碱度")
    private BigDecimal cAlcalinity;

    @Schema(description = "CaSi喂丝量")
    private BigDecimal cCasi;

    @Schema(description = "CaAl喂丝量")
    private BigDecimal cCaal;

    @Schema(description = "Ca喂丝量")
    private BigDecimal cCa;
    
    @Schema(description = "净吹氩时间")
    private String cArgonBlow;
    
    @Schema(description = "LF搬出温度")
    private BigDecimal cLfMoveoutTemp;
    
    @Schema(description = "VOD保压真空度")
    private String cVodPressVac;
    
    @Schema(description = "VOD保持时间")
    private String cVodKeepTime;
    
    @Schema(description = "VOD搬出温度")
    private BigDecimal cVodMoveoutTime;
    
    @Schema(description = "RH保压真空度")
    private String cRhPressVac;
    
    @Schema(description = "RH保持时间")
    private String cRhKeepTime;
    
    @Schema(description = "RH搬出温度")
    private BigDecimal cRhMoveoutTime;
    
    @Schema(description = "LF炼焦")
    private String cLfLianjiao;
    
    @Schema(description = "VOD炼焦")
    private String cVodLianjiao;
    
    @Schema(description = "RH炼焦")
    private String cRhLianjiao;
    
    @Schema(description = "石灰")
    private String cLime;
    
    @Schema(description = "萤石")
    private String cFluorite;
    
    @Schema(description = "硅灰石")
    private String cSilicaFume;
    
    @Schema(description = "精炼渣")
    private String cRefiningSlag;
    
    @Schema(description = "渣洗剂")
    private String cSlagLotion;
    
    @Schema(description = "石英砂")
    private String cQuartzSand;
    
    @Schema(description = "电石")
    private String cCalciumCarbide;
    
    @Schema(description = "LF连浇搬出温度")
    private BigDecimal cLfMorecastTemp;
    
    @Schema(description = "RH连浇搬出温度")
    private BigDecimal cRhMoveoutCastTime;
    
    @Schema(description = "真空环流")
    private String cVacuumCirculation;
    
    @Schema(description = "白渣保持时间")
    private String cWSlagHTime;
    
    @Schema(description = "精炼标准时间")
    private String cLfTimeBz;
    
    @Schema(description = "TFE+MNO")
    private String cTfemno;
    
    @Schema(description = "钙渣球")
    private String cCaBall;
    
    @Schema(description = "碱度最小")
    private BigDecimal cAlcalinityMin;
    
    @Schema(description = "碱度最大")
    private BigDecimal cAlcalinityMax;
    
    @Schema(description = "备用1")
    private String cBackup1;
    
    @Schema(description = "备用2")
    private String cBackup2;
    
    @Schema(description = "备用3")
    private String cBackup3;
    
    @Schema(description = "备用4")
    private String cBackup4;
    
    @Schema(description = "备用5")
    private String cBackup5;
    
    @Schema(description = "备用6")
    private String cBackup6;
    
    @Schema(description = "备用7")
    private String cBackup7;
    
    @Schema(description = "备用8")
    private String cBackup8;
    
    @Schema(description = "备用9")
    private String cBackup9;
    
    @Schema(description = "备用10")
    private String cBackup10;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
