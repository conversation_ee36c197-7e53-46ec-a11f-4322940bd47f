package com.aitos.pss.dto.planmanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
* @title: 炼钢计划任务调度
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
public class UpdateTPssCheatPlanBathDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "规格")
    private String cSpec;

    @Schema(description = "浇铸厚度")
    private BigDecimal nCcmThk;

    @Schema(description = "浇铸宽度")
    private BigDecimal nCcmWth;

    @Schema(description = "浇铸长度")
    private BigDecimal cCcmLen;

    @Schema(description = "0 下达取消  1 计划下达")
    private Integer operationType;

    @Schema(description = "更新数据")
    private List<UpdateTPssCheatPlanDto> updateCheatPlanDtoList;

}
