package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssMatStockLimitPageDto extends PageInput {

    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMaterialName;
    /**
    * 物料类型(raw:原辅料,alloy:合金,scrap:废钢)
    */
    @Schema(description = "物料类型(raw:原辅料,alloy:合金,scrap:废钢)")
    private String cMaterialType;
    /**
     * 物料类型id
     */
    @Schema(description = "物料类型id")
    private Long cMaterialTypeId;
    /**
    * 库存下限
    */
    @Schema(description = "库存下限")
    private BigDecimal nMinStock;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private String cUnit;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMaterialCode;
    /**
    * 当前库存
    */
    @Schema(description = "当前库存")
    private BigDecimal nCurrentStock;
    /**
    * 库存上限
    */
    @Schema(description = "库存上限")
    private BigDecimal nMaxStock;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;


}
