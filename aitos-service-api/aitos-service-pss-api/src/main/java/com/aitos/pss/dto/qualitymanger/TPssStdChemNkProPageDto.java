package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssStdChemNkProPageDto extends PageInput {

    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "质量编码名称")
    private Long cQualityId;
    
    @Schema(description = "上限比较符")
    private String cCompCodeMax;
    
    @Schema(description = "化学成分")
    private String cChemComp;
    
    @Schema(description = "化学成分最大值")
    private BigDecimal cChemCompMax;
    
    @Schema(description = "质量编码简称")
    private String cQualityCodeAbbreviation;
    
    @Schema(description = "执行标准号")
    private String cOpStdName;
    
    @Schema(description = "钢种名称/描述")
    private String cStlGrdName;
    
    @Schema(description = "下限比较符")
    private String cCompCodeMin;
    
    @Schema(description = "化学成分代码")
    private String cChemCompCd;
    
    @Schema(description = "化学成分最小值")
    private BigDecimal cChemCompMin;
    
    @Schema(description = "质量编码;质量编码")
    private String cQualityCode;
    
    @Schema(description = "工序id")
    private Long cMacId;

    @Schema(description = "工序编码")
    private String cMacCode;
    
    @Schema(description = "工序名称")
    private String cMacName;

}
