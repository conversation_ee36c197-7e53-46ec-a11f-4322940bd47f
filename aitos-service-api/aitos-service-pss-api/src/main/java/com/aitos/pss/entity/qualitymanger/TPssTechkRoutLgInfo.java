package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 炼钢工艺路径参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@TableName("t_pss_techk_rout_lg_info")
@Tag(name = "炼钢工艺路径参数质量设计结果对象", description = "炼钢工艺路径参数质量设计结果")
public class TPssTechkRoutLgInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "订单号")
    @TableField(value = "order_id")
    private String orderId;
    
    @Schema(description = "质量编码id")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;

    @Schema(description = "产线id")
    private Long cProLine;

    @Schema(description = "产线code")
    private String cProLineCode;

    @Schema(description = "产线name")
    private String cProLineName;

    @Schema(description = "工序1")
    private Long cTechRoute1;

    @Schema(description = "工序1code")
    private String cTechRoute1Code;

    @Schema(description = "工序1name")
    private String cTechRoute1Name;

    @Schema(description = "工序2")
    private Long cTechRoute2;

    @Schema(description = "工序2code")
    private String cTechRoute2Code;

    @Schema(description = "工序2name")
    private String cTechRoute2Name;

    @Schema(description = "工序3")
    private Long cTechRoute3;

    @Schema(description = "工序3code")
    private String cTechRoute3Code;

    @Schema(description = "工序3name")
    private String cTechRoute3Name;

    @Schema(description = "工序4")
    private Long cTechRoute4;

    @Schema(description = "工序4code")
    private String cTechRoute4Code;

    @Schema(description = "工序4name")
    private String cTechRoute4Name;

    @Schema(description = "工序5")
    private Long cTechRoute5;

    @Schema(description = "工序5code")
    private String cTechRoute5Code;

    @Schema(description = "工序5name")
    private String cTechRoute5Name;

    @Schema(description = "工序6")
    private Long cTechRoute6;

    @Schema(description = "工序6code")
    private String cTechRoute6Code;

    @Schema(description = "工序6name")
    private String cTechRoute6Name;

    @Schema(description = "工序7")
    private Long cTechRoute7;

    @Schema(description = "工序7code")
    private String cTechRoute7Code;

    @Schema(description = "工序7name")
    private String cTechRoute7Name;

    @Schema(description = "工序8")
    private Long cTechRoute8;

    @Schema(description = "工序8code")
    private String cTechRoute8Code;

    @Schema(description = "工序8name")
    private String cTechRoute8Name;
    
    @Schema(description = "是否堆垛缓冷")
    @TableField(value = "c_is_cold")
    private String cIsCold;
    
    @Schema(description = "工艺路径1")
    @TableField(value = "tech_route0")
    private String techRoute0;
    
    @Schema(description = "备注")
    @TableField(value = "c_memo")
    private String cMemo;
    
    @Schema(description = "公司文件编号")
    @TableField(value = "c_doc_num1")
    private String cDocNum1;
    
    @Schema(description = "厂文件编号")
    @TableField(value = "c_doc_num2")
    private String cDocNum2;
    
    @Schema(description = "工艺路径2")
    @TableField(value = "c_tech_route")
    private String cTechRoute;
    
    @Schema(description = "精整路径汇总")
    @TableField(value = "c_fine_route")
    private String cFineRoute;
    
    @Schema(description = "备用1")
    @TableField(value = "c_backup1")
    private String cBackup1;
    
    @Schema(description = "备用2")
    @TableField(value = "c_backup2")
    private String cBackup2;
    
    @Schema(description = "备用3")
    @TableField(value = "c_backup3")
    private String cBackup3;
    
    @Schema(description = "备用4")
    @TableField(value = "c_backup4")
    private String cBackup4;
    
    @Schema(description = "备用5")
    @TableField(value = "c_backup5")
    private String cBackup5;
    
    @Schema(description = "备用6")
    @TableField(value = "c_backup6")
    private String cBackup6;
    
    @Schema(description = "备用7")
    @TableField(value = "c_backup7")
    private String cBackup7;
    
    @Schema(description = "备用8")
    @TableField(value = "c_backup8")
    private String cBackup8;
    
    @Schema(description = "备用9")
    @TableField(value = "c_backup9")
    private String cBackup9;
    
    @Schema(description = "备用10")
    @TableField(value = "c_backup10")
    private String cBackup10;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}