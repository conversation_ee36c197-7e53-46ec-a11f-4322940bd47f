package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 轧钢工艺参数管理-加热工艺参数
* <AUTHOR>
* @Date: 2025-05-21
* @Version 1.0
*/
@Data
public class AddTPssSlabHeatStdDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "质量编码id")
    private Long cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;

    @Schema(description = "质量编码名称")
    private String cQualityCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;

    @Schema(description = "坯料厚度")
    private BigDecimal nSlabThkMin;

    @Schema(description = "坯料厚度")
    private BigDecimal nSlabThkMax;

    @Schema(description = "出炉目标温度℃")
    private BigDecimal nFoutAimTemp;

    @Schema(description = "出炉温度上限℃")
    private BigDecimal nFoutMaxTemp;

    @Schema(description = "出炉温度下限℃")
    private BigDecimal nFoutMinTemp;

    @Schema(description = "板坯表面/中心目标温度差℃")
    private BigDecimal nSlabScTempDiff;

    @Schema(description = "板坯头尾目标温度差℃")
    private BigDecimal nSlabHtTempDiff;

    @Schema(description = "加热一段最低温度")
    private BigDecimal cReserver1;

    @Schema(description = "加热一段最高温度")
    private BigDecimal cReserver2;

    @Schema(description = "加热二段最低温度")
    private BigDecimal cReserver3;

    @Schema(description = "加热二段最高温度")
    private BigDecimal cReserver4;

    @Schema(description = "加热段3最低温度")
    private BigDecimal cReserver5;

    @Schema(description = "加热段3最高温度")
    private BigDecimal cReserver6;

    @Schema(description = "加热段4最低温度")
    private BigDecimal cReserver7;

    @Schema(description = "加热段4最高温度")
    private BigDecimal cReserver8;

    @Schema(description = "目标加热时长")
    private BigDecimal cReserver9;

    @Schema(description = "备用10")
    private BigDecimal cReserver10;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
