package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 质量设计成分结果查看
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
public class UpdateTPssStdchemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "生产订单号")
    private String orderId;
    
    @Schema(description = "质量等级")
    private String cStdClass;

    @Schema(description = "质量编码id")
    private Long cQualId;

    @Schema(description = "质量编码")
    private String cQualCode;

    @Schema(description = "质量编码名称")
    private String cQualCodeName;

    @Schema(description = "牌号代码")
    private String cStlGrdCd;

    @Schema(description = "钢种代码")
    private String cStlGrdDesc;
    
    @Schema(description = "下限比较符")
    private String cCompCodeMin;
    
    @Schema(description = "上限比较符")
    private String cCompCodeMax;
    
    @Schema(description = "化学成分代码")
    private String cChemCompCd;
    
    @Schema(description = "化学成分最小值")
    private BigDecimal cChemCompMin;
    
    @Schema(description = "化学成分最大值")
    private BigDecimal cChemCompMax;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
