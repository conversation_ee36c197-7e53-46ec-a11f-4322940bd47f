package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 质量设计性能结果查看
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
public class AddTPssStdmatDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "生产订单号")
    private String orderId;
    
    @Schema(description = "质量等级")
    private String cStdClass;
    
    @Schema(description = "执行标准号")
    private String cOpStdName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;
    
    @Schema(description = "厚度最小值")
    private BigDecimal nThkMin;
    
    @Schema(description = "厚度最大值")
    private BigDecimal nThkMax;

    @Schema(description = "质检标准名称")
    private String cVerifyItemName;

    @Schema(description = "质检标准编码")
    private String cVerifyItemCode;

    @Schema(description = "质检标准编码id")
    private String cVerifyItemId;
    
    @Schema(description = "下限比较符")
    private String cSmpMinTag;
    
    @Schema(description = "上限比较符")
    private String cSmpMaxTag;

    @Schema(description = "下限")
    private BigDecimal cSmpMin;

    @Schema(description = "下限2")
    private BigDecimal cSmpMin2;

    @Schema(description = "下下限")
    private BigDecimal cSmpMinMin;

    @Schema(description = "下下限2")
    private BigDecimal cSmpMinMin2;

    @Schema(description = "上限")
    private BigDecimal cSmpMax;

    @Schema(description = "上限2")
    private BigDecimal cSmpMax2;

    @Schema(description = "上上限")
    private BigDecimal cSmpMaxMax;

    @Schema(description = "上上限2")
    private BigDecimal cSmpMaxMax2;

    @Schema(description = "平均")
    private BigDecimal cSmpAvg;

    @Schema(description = "平均2")
    private BigDecimal cSmpAvg2;

    @Schema(description = "平均下限")
    private BigDecimal cSmpAvgMin;

    @Schema(description = "平均下限2")
    private BigDecimal cSmpAvgMin2;

    @Schema(description = "平均上限")
    private BigDecimal cSmpAvgMax;

    @Schema(description = "平均上限2")
    private BigDecimal cSmpAvgMax2;
    
    @Schema(description = "判定方法")
    private String cSmpDcsCd;
    
    @Schema(description = "备注")
    private String cMemo;

    @Schema(description = "质量编码id")
    private String cQualId;

    @Schema(description = "质量编码")
    private String cQualCode;

    @Schema(description = "质量编码名称")
    private String cQualCodeName;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
