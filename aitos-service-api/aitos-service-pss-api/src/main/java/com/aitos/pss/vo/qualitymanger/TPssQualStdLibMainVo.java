package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Data
public class TPssQualStdLibMainVo {

    
    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "质检标准编码")
    private String cStdcode;
    
    @Schema(description = "质检标准名称")
    private String cStdname;
    
    @Schema(description = "质检项目名称")
    private String cItemname;

    @Schema(description = "产线")
    private Long cProLine;

    @Schema(description = "产线code")
    private String cProLineCode;

    @Schema(description = "产线name")
    private String cProLineName;

    @Schema(description = "物料id")
    private Long cMaterial;

    @Schema(description = "物料code")
    private String cMaterialCode;

    @Schema(description = "物料name")
    private String cMaterialName;
    
    @Schema(description = "质检标准类别")
    private String cStdtype;

    @Schema(description = "质检标准类别name")
    private String cStdtypeName;

    @Schema(description = "质检标准类别编码")
    private String cStdtypeCode;

    @Schema(description = "标准类型")
    private String cExecute;
    
    @Schema(description = "执行标准")
    private String cProjectAttributeCode;
    
    @Schema(description = "是否下发;0未下发，1下发")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer nIssued;
    
    @Schema(description = "判定等级")
    private String cValueType;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
