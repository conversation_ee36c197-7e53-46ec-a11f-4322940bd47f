package com.aitos.pss.vo.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
public class TPssCheSteelVo {

    
    @Schema(description = "顺序号")
    private Long nId;
    
    @Schema(description = "式样类型")
    private String cSampletype;
    
    @Schema(description = "炉号")
    private String cSampleid;
    
    @Schema(description = "写入时间")
    private LocalDateTime dtSampTime;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称")
    private String cStlGrdName;
    
    @Schema(description = "说明")
    private String cCommentfld;
    
    @Schema(description = "取样序号/次数")
    private Integer nSmpNums;
    
    @Schema(description = "试样号")
    private String cSmpId;
    
    @Schema(description = "c")
    private BigDecimal nC;
    
    @Schema(description = "si")
    private BigDecimal nSi;
    
    @Schema(description = "mn")
    private BigDecimal nMn;
    
    @Schema(description = "p")
    private BigDecimal nP;
    
    @Schema(description = "s")
    private BigDecimal nS;
    
    @Schema(description = "cr")
    private BigDecimal nCr;
    
    @Schema(description = "ni")
    private BigDecimal nNi;
    
    @Schema(description = "cu")
    private BigDecimal nCu;
    
    @Schema(description = "v")
    private BigDecimal nV;
    
    @Schema(description = "mo")
    private BigDecimal nMo;
    
    @Schema(description = "ti")
    private BigDecimal nTi;
    
    @Schema(description = "nb")
    private BigDecimal nNb;
    
    @Schema(description = "ceq")
    private BigDecimal nCeq;
    
    @Schema(description = "als")
    private BigDecimal nAls;
    
    @Schema(description = "al")
    private BigDecimal nAl;
    
    @Schema(description = "ca")
    private BigDecimal nCa;
    
    @Schema(description = "b")
    private BigDecimal nB;
    
    @Schema(description = "pb")
    private BigDecimal nPb;
    
    @Schema(description = "zr")
    private BigDecimal nZr;
    
    @Schema(description = "w")
    private BigDecimal nW;
    
    @Schema(description = "asfld")
    private BigDecimal nAsfld;
    
    @Schema(description = "sn")
    private BigDecimal nSn;
    
    @Schema(description = "co")
    private BigDecimal nCo;
    
    @Schema(description = "bi")
    private BigDecimal nBi;
    
    @Schema(description = "zn")
    private BigDecimal nZn;
    
    @Schema(description = "sb")
    private BigDecimal nSb;
    
    @Schema(description = "la")
    private BigDecimal nLa;
    
    @Schema(description = "ce")
    private BigDecimal nCe;
    
    @Schema(description = "n")
    private BigDecimal nN;
    
    @Schema(description = "bs")
    private BigDecimal nBs;
    
    @Schema(description = "aln")
    private BigDecimal nAln;
    
    @Schema(description = "mg")
    private BigDecimal nMg;
    
    @Schema(description = "ct")
    private BigDecimal nCt;
    
    @Schema(description = "c_g")
    private Integer nCG;
    
    @Schema(description = "si_g")
    private Integer nSiG;
    
    @Schema(description = "mn_g")
    private Integer nMnG;
    
    @Schema(description = "p_g")
    private Integer nPG;
    
    @Schema(description = "s_g")
    private Integer nSG;
    
    @Schema(description = "ceq_g")
    private Integer nCeqG;
    
    @Schema(description = "cr_g")
    private Integer nCrG;
    
    @Schema(description = "ni_g")
    private Integer nNiG;
    
    @Schema(description = "cu_g")
    private Integer nCuG;
    
    @Schema(description = "v_g")
    private Integer nVG;
    
    @Schema(description = "mo_g")
    private Integer nMoG;
    
    @Schema(description = "ti_g")
    private Integer nTiG;
    
    @Schema(description = "nb_g")
    private Integer nNbG;
    
    @Schema(description = "als_g")
    private Integer nAlsG;
    
    @Schema(description = "al_g")
    private Integer nAlG;
    
    @Schema(description = "ca_g")
    private Integer nCaG;
    
    @Schema(description = "b_g")
    private Integer nBG;
    
    @Schema(description = "pb_g")
    private Integer nPbG;
    
    @Schema(description = "zr_g")
    private Integer nZrG;
    
    @Schema(description = "w_g")
    private Integer nWG;
    
    @Schema(description = "asfld_g")
    private Integer nAsfldG;
    
    @Schema(description = "sn_g")
    private Integer nSnG;
    
    @Schema(description = "co_g")
    private Integer nCoG;
    
    @Schema(description = "bi_g")
    private Integer nBiG;
    
    @Schema(description = "sb_g")
    private Integer nSbG;
    
    @Schema(description = "zn_g")
    private Integer nZnG;
    /**
    * la_g
    */
    @Schema(description = "la_g")
    private Integer nLaG;
    /**
    * ce_g
    */
    @Schema(description = "ce_g")
    private Integer nCeG;
    /**
    * n_g
    */
    @Schema(description = "n_g")
    private Integer nNG;
    /**
    * bs_g
    */
    @Schema(description = "bs_g")
    private Integer nBsG;
    /**
    * aln_g
    */
    @Schema(description = "aln_g")
    private Integer nAlnG;
    /**
    * mg_g
    */
    @Schema(description = "mg_g")
    private Integer nMgG;
    /**
    * ct_g
    */
    @Schema(description = "ct_g")
    private Integer nCtG;
    /**
    * 质量等级
    */
    @Schema(description = "质量等级")
    private String cJudge;
    /**
    * o
    */
    @Schema(description = "o")
    private BigDecimal nO;
    /**
    * h
    */
    @Schema(description = "h")
    private BigDecimal nH;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    private String cMatQulCd;



}
