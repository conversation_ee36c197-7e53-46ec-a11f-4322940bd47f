package com.aitos.pss.entity.prodmonit;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 指标数据
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
@TableName("t_pss_indicator_data")
@Tag(name = "指标数据对象", description = "指标数据")
public class TPssIndicatorData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    @Schema(description = "主键id")
    @TableId
    private Long nId;
    /**
    * 指标ID
    */
    @Schema(description = "指标ID")
    @TableField(value = "n_indicator_id")
    private Long nIndicatorId;
    /**
    * 指标code
    */
    @Schema(description = "指标code")
    @TableField(value = "c_indicator_code")
    private String cIndicatorCode;
    /**
    * 指标name
    */
    @Schema(description = "指标name")
    @TableField(value = "c_indicator_name")
    private String cIndicatorName;
    /**
    * 设备id
    */
    @Schema(description = "设备id")
    @TableField(value = "n_equipment_id")
    private Long nEquipmentId;
    /**
    * 设备code
    */
    @Schema(description = "设备code")
    @TableField(value = "c_equipment_code")
    private String cEquipmentCode;
    /**
    * 设备name
    */
    @Schema(description = "设备name")
    @TableField(value = "c_equipment_name")
    private String cEquipmentName;
    /**
    * 指标值
    */
    @Schema(description = "指标值")
    @TableField(value = "n_indicator_value")
    private BigDecimal nIndicatorValue;
    /**
    * 状态（正常/预警/违规）
    */
    @Schema(description = "状态（正常/预警/违规）")
    @TableField(value = "c_status")
    private String cStatus;
    /**
    * 时间戳
    */
    @Schema(description = "时间戳")
    @TableField(value = "dt_timestamp")
    private Long dtTimestamp;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time")
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}