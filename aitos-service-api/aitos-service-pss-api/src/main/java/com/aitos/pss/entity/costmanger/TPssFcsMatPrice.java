package com.aitos.pss.entity.costmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 计划价格配置
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
@TableName("t_pss_fcs_mat_price")
@Tag(name = "计划价格配置对象", description = "计划价格配置")
public class TPssFcsMatPrice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 顺序号
    */
    @Schema(description = "顺序号")
    @TableId
    private Long nId;
    /**
    * 成本项目编码
    */
    @Schema(description = "成本项目编码")
    @TableField(value = "c_cost_item_code")
    private String cCostItemCode;

    @Schema(description = "成本项目id")
    @TableField(value = "c_cost_item_id")
    private Long cCostItemId;
    /**
    * 成本项目名称
    */
    @Schema(description = "成本项目名称")
    @TableField(value = "c_cost_item_name")
    private String cCostItemName;
    /**
    * 物料id
    */
    @Schema(description = "物料id")
    @TableField(value = "c_mat_id")
    private Long cMatId;
    /**
    * 物料代码
    */
    @Schema(description = "物料代码")
    @TableField(value = "c_mat_code")
    private String cMatCode;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    @TableField(value = "c_mat_name")
    private String cMatName;
    /**
    * 单位
    */
    @Schema(description = "单位")
    @TableField(value = "c_unit_id")
    private Long cUnitId;
    /**
    * 单位编码
    */
    @Schema(description = "单位编码")
    @TableField(value = "c_unit_code")
    private String cUnitCode;
    /**
    * 单位名称
    */
    @Schema(description = "单位名称")
    @TableField(value = "c_unit_name")
    private String cUnitName;
    /**
    * 计划价格
    */
    @Schema(description = "计划价格")
    @TableField(value = "c_plan_price")
    private BigDecimal cPlanPrice;
    /**
    * 实际价格
    */
    @Schema(description = "实际价格")
    @TableField(value = "c_act_price")
    private BigDecimal cActPrice;
    /**
    * 回收率
    */
    @Schema(description = "回收率")
    @TableField(value = "c_recovery")
    private BigDecimal cRecovery;
    /**
    * 状态
    */
    @Schema(description = "状态")
    @TableField(value = "c_state")
    private Integer cState;
    /**
    * 开始启用时间
    */
    @Schema(description = "开始启用时间")
    @TableField(value = "dt_starttime")
    private LocalDateTime dtStarttime;
    /**
    * 结束时间
    */
    @Schema(description = "结束时间")
    @TableField(value = "dt_endtime")
    private LocalDateTime dtEndtime;
    /**
    * 备注
    */
    @Schema(description = "备注")
    @TableField(value = "c_memo")
    private String cMemo;

    @Schema(description = "产线id")
    @TableField(value = "c_pro_line")
    private Long cProLine;

    @Schema(description = "产线cd")
    @TableField(value = "c_pro_line_code")
    private String cProLineCode;

    @Schema(description = "产线name")
    @TableField(value = "c_pro_line_name")
    private String cProLineName;
    /**
    * 工序
    */
    @Schema(description = "工序id")
    @TableField(value = "c_proc_id")
    private Long cProcId;

    @Schema(description = "工序cd")
    @TableField(value = "c_proc_cd")
    private String cProcCd;

    @Schema(description = "工序name")
    @TableField(value = "c_proc_name")
    private String cProcName;
    /**
    * 物料类型代码
    */
    @Schema(description = "物料类型代码")
    @TableField(value = "c_mat_type_cd")
    private String cMatTypeCd;
    /**
    * 物料类型名称
    */
    @Schema(description = "物料类型名称")
    @TableField(value = "c_mat_type_name")
    private String cMatTypeName;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;


}