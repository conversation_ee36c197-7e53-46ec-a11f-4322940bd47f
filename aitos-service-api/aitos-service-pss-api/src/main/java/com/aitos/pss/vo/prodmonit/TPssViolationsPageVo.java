package com.aitos.pss.vo.prodmonit;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
public class TPssViolationsPageVo {

    /**
    * 主键id
    */
    @ExcelIgnore
    @Schema(description = "主键id")
    private String nId;
    /**
    * 指标name
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("指标名称")
    @Schema(description = "指标name")
    private String cIndicatorName;
    /**
    * 设备name
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("设备名称")
    @Schema(description = "设备name")
    private String cEquipmentName;
    /**
    * 违规单号
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("违规单号")
    @Schema(description = "违规单号")
    private String cCode;
    /**
    * 违规描述
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("违规描述")
    @Schema(description = "违规描述")
    private String cViolationDescription;
    /**
    * 违规时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("违规时间")
    @Schema(description = "违规时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtViolationDateTime;
    /**
    * 违规值
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("违规值")
    @Schema(description = "违规值")
    private BigDecimal nErrorValue;
    /**
    * 标准值
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("标准值")
    @Schema(description = "标准值")
    private BigDecimal nStandValue;
    /**
    * 状态（待审核/已通过/已驳回）
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("状态")
    @Schema(description = "状态（待审核/已通过/已驳回）")
    @Trans(type = TransType.DIC, id = "1949635134942175233")
    private String cStatus;
    /**
    * 是否有效/启用标记
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否有效/启用标记")
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
