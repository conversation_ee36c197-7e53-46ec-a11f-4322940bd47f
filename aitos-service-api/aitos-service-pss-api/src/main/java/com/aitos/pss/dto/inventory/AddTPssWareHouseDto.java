package com.aitos.pss.dto.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 库区库位管理
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@Data
public class AddTPssWareHouseDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 仓库代码
    */
    @Schema(description = "仓库代码")
    private String cCode;
    /**
    * 仓库名称
    */
    @Schema(description = "仓库名称")
    private String cName;
    /**
    * 所属公司
    */
    @Schema(description = "所属公司")
    private String cCompany;
    /**
    * 所属产线
    */
    @Schema(description = "所属产线")
    private String cLine;
    /**
    * 仓库类型
    */
    @Schema(description = "仓库类型")
    private String cType;
    /**
    * ERP库号
    */
    @Schema(description = "ERP库号")
    private String cErp;
    /**
    * 仓库地址
    */
    @Schema(description = "仓库地址")
    private String cAddress;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 高储
    */
    @Schema(description = "高储")
    private BigDecimal nHigh;
    /**
    * 低储
    */
    @Schema(description = "低储")
    private BigDecimal nLow;
    /**
    * 安全库存
    */
    @Schema(description = "安全库存")
    private BigDecimal nSafe;
    /**
    * 电话
    */
    @Schema(description = "电话")
    private String cTel;
    /**
    * 负责人
    */
    @Schema(description = "负责人")
    private String cManager;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
