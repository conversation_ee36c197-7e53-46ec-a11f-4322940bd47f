package com.aitos.pss.vo.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-07-22
* @Version 1.0
*/
@Data
public class TPssFinalSlabPageVo {

    /**
    * 序列号
    */
    @Schema(description = "序列号")
    private String nId;
    /**
    * 最终钢坯ID
    */
    @Schema(description = "最终钢坯ID")
    private String cFinalSlabId;
    /**
    * 最终钢坯NAME
    */
    @Schema(description = "最终钢坯NAME")
    private String cFinalSlabName;
    /**
    * 目标钢坯ID
    */
    @Schema(description = "目标钢坯ID")
    private BigDecimal nTargetSlabId;
    /**
    * 目标钢坯内前一最终钢坯ID
    */
    @Schema(description = "目标钢坯内前一最终钢坯ID")
    private BigDecimal nPrevFinalSlabIdInTarget;
    /**
    * 轧制顺序计划id
    */
    @Schema(description = "轧制顺序计划id")
    private String cRollSchId;
    /**
    * 轧制顺序计划内前一最终钢坯id
    */
    @Schema(description = "轧制顺序计划内前一最终钢坯id")
    private BigDecimal nPrevFinalSlabIdInSch;
    /**
    * 代表钢坯质量编码
    */
    @Schema(description = "代表钢坯质量编码")
    private Long cSlabMatQulId;
    /**
    * 代表钢坯质量编码
    */
    @Schema(description = "代表钢坯质量编码")
    private String cSlabMatQulCd;
    /**
    * 代表钢坯质量编码
    */
    @Schema(description = "代表钢坯质量编码")
    private String cSlabMatQulName;
    /**
    * 钢坯厚
    */
    @Schema(description = "钢坯厚")
    private BigDecimal nThk;
    /**
    * 钢坯宽
    */
    @Schema(description = "钢坯宽")
    private BigDecimal nWth;
    /**
    * 钢坯长
    */
    @Schema(description = "钢坯长")
    private BigDecimal nLth;
    /**
    * 钢坯计算重量
    */
    @Schema(description = "钢坯计算重量")
    private BigDecimal nCalWgt;
    /**
    * 代表钢种
    */
    @Schema(description = "代表钢种")
    private String cStlGrdCd;
    /**
    * 代表钢种
    */
    @Schema(description = "代表钢种")
    private String cStlGrdDesc;
    /**
    * 设计成材率
    */
    @Schema(description = "设计成材率")
    private BigDecimal nDegRatio;
    /**
    * 产品长度 （方钢/开坯）
    */
    @Schema(description = "产品长度 （方钢/开坯）")
    private BigDecimal nAsrollLth;
    /**
    * 产品厚度（方钢/开坯）
    */
    @Schema(description = "产品厚度（方钢/开坯）")
    private BigDecimal nAsrollThk;
    /**
    * 产品宽度（方钢/开坯）
    */
    @Schema(description = "产品宽度（方钢/开坯）")
    private BigDecimal nAsrollWth;
    /**
    * 交货状态
    */
    @Schema(description = "交货状态")
    private String cFpostState;
    /**
    * 探伤等级
    */
    @Schema(description = "探伤等级")
    private String cUstLev;
    /**
    * 探伤标准
    */
    @Schema(description = "探伤标准")
    private String cUstStd;
    /**
    * 切头长
    */
    @Schema(description = "切头长")
    private BigDecimal nCutHead;
    /**
    * 切尾长
    */
    @Schema(description = "切尾长")
    private BigDecimal nCutTail;
    /**
    * 长度切损
    */
    @Schema(description = "长度切损")
    private BigDecimal nCutLthLose;
    /**
    * 长度余量
    */
    @Schema(description = "长度余量")
    private BigDecimal nCutLthRem;
    /**
    * 宽度切损
    */
    @Schema(description = "宽度切损")
    private BigDecimal nCutTrimLose;
    /**
    * 宽度余量
    */
    @Schema(description = "宽度余量")
    private BigDecimal nCutWthRem;
    /**
    * 试样长度
    */
    @Schema(description = "试样长度")
    private BigDecimal nSmpLth;
    /**
    * 来源
    */
    @Schema(description = "来源")
    private String cSource;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 目标钢坯内顺序
    */
    @Schema(description = "目标钢坯内顺序")
    private BigDecimal nSeqInTarget;
    /**
    * 轧制顺序计划内顺序
    */
    @Schema(description = "轧制顺序计划内顺序")
    private BigDecimal nSeqInSch;
    /**
    * 代表执行标准
    */
    @Schema(description = "代表执行标准")
    private String cStdSpec;
    /**
    * 下发作业时间
    */
    @Schema(description = "下发作业时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReleasedMoment;
    /**
    * 程序跟踪用
    */
    @Schema(description = "程序跟踪用")
    private BigDecimal nTargetBak;
    /**
    * 提料上传时间
    */
    @Schema(description = "提料上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtApplyMomentBak;
    /**
    * 创建日期
    */
    @Schema(description = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtInsTime;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cMemoBak;

    /**
    * 消息状态
    */
    @Schema(description = "消息状态")
    private String cMsgStatus;
    /**
    * 错误描述
    */
    @Schema(description = "错误描述")
    private String cErrorText;
    /**
    * 消息类别
    */
    @Schema(description = "消息类别")
    private String cMsgType;
    /**
    * 上传者
    */
    @Schema(description = "上传者")
    private String cMsgEmp;
    /**
    * 消息号
    */
    @Schema(description = "消息号")
    private BigDecimal cMsgId;
    /**
    * 是否上传
    */
    @Schema(description = "是否上传")
    private String cAppliedFl;
    /**
    * 热送标记
    */
    @Schema(description = "热送标记")
    private String cHcrFl;
    /**
    * 物料类型
    */
    @Schema(description = "物料类型")
    private String cMatType;
    /**
    * 提料单号
    */
    @Schema(description = "提料单号")
    private BigDecimal nApplyListId;
    /**
    * 调度令号
    */
    @Schema(description = "调度令号")
    private String cDispatchId;
    /**
    * 订单号
    */
    @Schema(description = "订单号")
    private String cOrderNo;
    /**
    * 直径
    */
    @Schema(description = "直径")
    private BigDecimal nLineSpec;
    /**
    * 前状态
    */
    @Schema(description = "前状态")
    private String cPreStatus;
    /**
    * 挂单时间
    */
    @Schema(description = "挂单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtAttachTime;
    /**
    * 挂单人
    */
    @Schema(description = "挂单人")
    private String cAttachEmp;
    /**
    * 产品类型P0001
    */
    @Schema(description = "产品类型P0001")
    private String cProdType;
    /**
    * 精整路径
    */
    @Schema(description = "精整路径")
    private String cPlanFinishingPath;
    /**
    * 产线
    */
    @Schema(description = "产线")
    private Long cProductionLine;

    @Schema(description = "产线code")
    private String cProductionLineCode;

    @Schema(description = "产线name")
    private String cProductionLineName;
    /**
    * 定尺代码
    */
    @Schema(description = "定尺代码")
    private String cSizeProperty;
    /**
    * 存货编码
    */
    @Schema(description = "存货编码")
    private Long cProdMatId;

    @Schema(description = "存货编码")
    private String cProdMatCode;
    /**
    * 存货名称
    */
    @Schema(description = "存货名称")
    private String cProdMatName;
    /**
    * 调度单合并用原调度单号
    */
    @Schema(description = "调度单合并用原调度单号")
    private String cOrgDipatchId;
    /**
    * 产品宽
    */
    @Schema(description = "产品宽")
    private BigDecimal nProdWid;
    /**
    * 开坯产品规格
    */
    @Schema(description = "开坯产品规格")
    private String cSlabSpec;
    /**
    * 综合生产计划主键
    */
    @Schema(description = "综合生产计划主键")
    private BigDecimal nAgPlanSeq;
    /**
    * 
    */
    @Schema(description = "")
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 
    */
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    private Long nModifyUserId;
    /**
    * 
    */
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    private Integer nDeleteMark;

}
