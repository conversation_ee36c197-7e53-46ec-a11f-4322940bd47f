package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssAggregatePlanPageDto extends PageInput {

    @Schema(description = "订单号")
    private String cOrderNo;

    @Schema(description = "任务单号")
    private String cProductTaskListId;

    @Schema(description = "计划产线（炼钢）默认一炼钢(P0001)")
    private Long cPlanSteelLineId;

    @Schema(description = "计划产线id(轧钢)(P0001)")
    private Long cPlanLineId;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "产品类型(P0003)")
    private String cProductType;
    
    @Schema(description = "计划状态")
    private String cPlanState;
}
