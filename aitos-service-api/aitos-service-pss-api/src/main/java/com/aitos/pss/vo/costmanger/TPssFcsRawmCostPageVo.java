package com.aitos.pss.vo.costmanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
public class TPssFcsRawmCostPageVo {

    /**
    * 
    */
    @Schema(description = "")
    private String nId;
    /**
    * 炉号
    */
    @Schema(description = "炉号")
    private String cHeatId;

    @Schema(description = "钢种")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;
    /**
     * 产线
     */
    @Schema(description = "产线id")
    private Long cProLineId;

    @Schema(description = "产线cd")
    private String cProLineCd;

    @Schema(description = "产线name")
    private String cProLineName;
    /**
     * 工序
     */
    @Schema(description = "工序id")
    private Long cProcId;

    @Schema(description = "工序cd")
    private String cProcCd;

    @Schema(description = "工序name")
    private String cProcName;
    /**
    * 成本项目编码
    */
    @Schema(description = "成本项目编码")
    private String cCostItemCode;
    /**
    * 成本项目名称
    */
    @Schema(description = "成本项目名称")
    private String cCostItemName;
    /**
    * 物料id
    */
    @Schema(description = "物料id")
    private Long cMatId;
    /**
    * 物料代码
    */
    @Schema(description = "物料代码")
    private String cMatCode;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatName;
    /**
    * 计划价格
    */
    @Schema(description = "计划价格")
    private BigDecimal cPlanPrice;
    /**
    * 实际价格
    */
    @Schema(description = "实际价格")
    private BigDecimal cActPrice;
    /**
    * 消耗量
    */
    @Schema(description = "消耗量")
    private BigDecimal cMatQty;

    @Schema(description = "实际成本量")
    private BigDecimal cActMatQty;
    /**
    * 开始使用日期
    */
    @Schema(description = "开始使用日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtUseDate;
    /**
    * 班组
    */
    @Schema(description = "班组")
    private String cCrew;
    /**
    * 班次
    */
    @Schema(description = "班次")
    private String cShift;
    /**
    * 结束使用时间
    */
    @Schema(description = "结束使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtUseDateEnd;
    /**
    * 显示顺序
    */
    @Schema(description = "显示顺序")
    private Integer nCostSeq;
    /**
    * 核算单位
    */
    @Schema(description = "核算单位")
    private String cType;
    /**
    * 定额
    */
    @Schema(description = "定额")
    private BigDecimal cQuota;
    /**
    * 公摊系数
    */
    @Schema(description = "公摊系数")
    private BigDecimal cRatio;

    @Schema(description = "座次id")
    private Long cSeat;

    @Schema(description = "座次code")
    private String cSeatCode;

    @Schema(description = "座次name")
    private String cSeatName;
    /**
    * 成本日期
    */
    @Schema(description = "成本日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cCostDate;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private Integer cState;
    /**
    * 审核人
    */
    @Schema(description = "审核人")
    @Trans(type = TransType.USER,transForPropertyName = "cProEmpName")
    private Long cProEmp;

    @Schema(description = "审核人")
    private String cProEmpName;
    /**
    * 审核时间
    */
    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtProTime;
    /**
    * 上传标志位
    */
    @Schema(description = "上传标志位")
    private String cMonthFlag;

    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;

    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private Long cUnitId;
    /**
    * 单位编码
    */
    @Schema(description = "单位编码")
    private String cUnitCode;
    /**
    * 单位名称
    */
    @Schema(description = "单位名称")
    private String cUnitName;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;

}
