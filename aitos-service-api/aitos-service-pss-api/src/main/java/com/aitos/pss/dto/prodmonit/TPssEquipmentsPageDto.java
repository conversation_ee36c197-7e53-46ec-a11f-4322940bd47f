package com.aitos.pss.dto.prodmonit;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssEquipmentsPageDto extends PageInput {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long nId;
    /**
    * 设备代码
    */
    @Schema(description = "设备代码")
    private String cCode;
    /**
    * 位置
    */
    @Schema(description = "位置")
    private String cLocation;
    /**
    * 设备名称
    */
    @Schema(description = "设备名称")
    private String cName;
    /**
    * 设备类型
    */
    @Schema(description = "设备类型")
    private String cType;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;

}
