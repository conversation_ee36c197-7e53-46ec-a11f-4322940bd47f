package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 成分标准管理-工序成分标准
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/
@Data
public class UpdateTPssStdChemNkProDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "工序编码")
    private String cMacCode;
    
    @Schema(description = "工序名称")
    private String cMacName;
    
    @Schema(description = "执行标准号")
    private String cOpStdName;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCode;
    
    @Schema(description = "钢种名称/描述")
    private String cStlGrdName;
    
    @Schema(description = "质量编码名称")
    private String cQualityCodeName;
    
    @Schema(description = "下限比较符")
    private String cCompCodeMin;
    
    @Schema(description = "上限比较符")
    private String cCompCodeMax;
    
    @Schema(description = "化学成分代码")
    private String cChemCompCd;
    
    @Schema(description = "化学成分")
    private String cChemComp;
    
    @Schema(description = "化学成分最小值")
    private BigDecimal cChemCompMin;
    
    @Schema(description = "化学成分最大值")
    private BigDecimal cChemCompMax;
    
    @Schema(description = "质量编码;质量编码")
    private String cQualityCode;
    
    @Schema(description = "质量编码简称")
    private String cQualityCodeAbbreviation;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
}
