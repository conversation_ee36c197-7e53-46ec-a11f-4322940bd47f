package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-05
* @Version 1.0
*/
@Data
public class TPssPJudgePageVo {

    
    @ExcelIgnore
    @Schema(description = "")
    private String nId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("件次号")
    @Schema(description = "件次号")
    private String cPlateId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("表面等级")
    @Schema(description = "表面等级")
    @Trans(type = TransType.DIC, id = "1925393176789061634")
    private String cFaceResult;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("外形等级")
    @Schema(description = "外形等级")
    @Trans(type = TransType.DIC, id = "1944674181452488705")
    private String cBodyResult;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("尺寸等级")
    @Schema(description = "尺寸等级")
    @Trans(type = TransType.DIC, id = "1925393512790560769")
    private String cSizeResult;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("初判等级")
    @Schema(description = "初判等级")
    @Trans(type = TransType.DIC, id = "1944674704012435458")
    private String cJunCheckLvl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("初判时间")
    @Schema(description = "初判时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtJunCheckTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("初判人员")
    @Schema(description = "初判人员")
    private String cJunCheckOperator;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("初判更正等级")
    @Schema(description = "初判更正等级")
    @Trans(type = TransType.DIC, id = "1944674704012435458")
    private String cModJunCheckLvl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("性能等级")
    @Schema(description = "性能等级")
    @Trans(type = TransType.DIC, id = "1925393720312139777")
    private String cMtalGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("探伤等级")
    @Schema(description = "探伤等级")
    @Trans(type = TransType.DIC, id = "1925393951594450946")
    private String cUstGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综判等级")
    @Schema(description = "综判等级")
    @Trans(type = TransType.DIC, id = "1925394108675330050")
    private String cProdGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综判人员")
    @Schema(description = "综判人员")
    private String cEndJudgeId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综判不合原因")
    @Schema(description = "综判不合原因")
    private String cProdGrdReason;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综判时间")
    @Schema(description = "综判时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtProdGrdTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综判修改人员")
    @Schema(description = "综判修改人员")
    private String cEndJudgeModId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综判修改时间")
    @Schema(description = "综判修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtEndJudgeModTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("备注")
    @Schema(description = "备注")
    private String cMemo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否性能挽救")
    @Schema(description = "是否性能挽救")
    private String cRescueFl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原表面等级")
    @Schema(description = "原表面等级")
    @Trans(type = TransType.DIC, id = "1925393176789061634")
    private String cOldFaceResult;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原外形等级")
    @Schema(description = "原外形等级")
    @Trans(type = TransType.DIC, id = "1944674181452488705")
    private String cOldBodyResult;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原尺寸等级")
    @Schema(description = "原尺寸等级")
    @Trans(type = TransType.DIC, id = "1925393512790560769")
    private String cOldSizeResult;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原长度")
    @Schema(description = "原长度")
    private BigDecimal nOldLth;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原宽度")
    @Schema(description = "原宽度")
    private BigDecimal nOldWth;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原厚度")
    @Schema(description = "原厚度")
    private BigDecimal nOldThk;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("废品标记")
    @Schema(description = "废品标记")
    private String cWPro;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原综判等级")
    @Schema(description = "原综判等级")
    @Trans(type = TransType.DIC, id = "1925394108675330050")
    private String cOrgProdGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原性能等级")
    @Schema(description = "原性能等级")
    @Trans(type = TransType.DIC, id = "1925393720312139777")
    private String cOrgMtalGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("改判等级")
    @Schema(description = "改判等级")
    @Trans(type = TransType.DIC, id = "1944674181452488705")
    private String cProdGrdCha;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否有效/启用标记")
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

}
