package com.aitos.pss.vo.costmanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
public class TPssFcsMatCdVo {

    /**
    * 顺序号
    */
    @Schema(description = "顺序号")
    private Long nId;
    /**
    * 成本小类编码
    */
    @Schema(description = "成本小类编码")
    private String cCostItemCode;
    /**
    * 成本小类名称
    */
    @Schema(description = "成本小类名称")
    private String cCostItemName;

    @Schema(description = "成本项目id")
    private String cMatClassId;

    /**
    * 成本项目编码
    */
    @Schema(description = "成本项目编码")
    private String cMatClassCode;
    /**
    * 成本项目名称
    */
    @Schema(description = "成本项目名称")
    private String cMatClassName;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cMemo;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private String cUnit;

    @Schema(description = "产线id")
    private Long cProLine;

    @Schema(description = "产线")
    private String cProLineCode;

    @Schema(description = "产线")
    private String cProLineName;
    /**
    * 显示顺序
    */
    @Schema(description = "显示顺序")
    private Integer nCostSeq;

    @Schema(description = "工序id")
    private Long cProcId;

    @Schema(description = "工序编码")
    private String cProcCd;

    @Schema(description = "工序名称")
    private String cProcName;

    @Schema(description = "物料编码")
    private Long cMatId;

    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMatCode;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatName;
    /**
    * 是否区分座次
    */
    @Schema(description = "是否区分座次")
    private String cSeat;
    /**
    * 备用字段1
    */
    @Schema(description = "备用字段1")
    private String cSpare1;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    private LocalDateTime dtModifyDateTime;



}
