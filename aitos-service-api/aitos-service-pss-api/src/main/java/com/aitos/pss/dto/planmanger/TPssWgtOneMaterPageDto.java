package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssWgtOneMaterPageDto extends PageInput {

    
    @Schema(description = "钢种")
    private String cStlGrdCd;
    
    @Schema(description = "密度")
    private BigDecimal nDensity;
    
    @Schema(description = "计算方式;0为米重，1为密度")
    private String cCalculationMethod;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "规格")
    private String cSpec;
    
    @Schema(description = "米重")
    private BigDecimal nWgtOneMater;
    
    @Schema(description = "铸机号")
    private Long cCcmId;

}
