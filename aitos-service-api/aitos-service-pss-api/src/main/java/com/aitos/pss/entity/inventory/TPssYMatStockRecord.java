package com.aitos.pss.entity.inventory;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 原辅料库存信息查询子表
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
@TableName("t_pss_y_mat_stock_record")
@Tag(name = "原辅料库存信息查询子表对象", description = "原辅料库存信息查询子表")
public class TPssYMatStockRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    */
    @Schema(description = "主键ID")
    @TableId
    private Long nId;
    /**
    * 物料ID
    */
    @Schema(description = "物料ID")
    @TableField(value = "material_id")
    private Long materialId;
    /**
    * 单据类型(in:入库单,out:出库单)
    */
    @Schema(description = "单据类型(in:入库单,out:出库单)")
    @TableField(value = "c_doc_type")
    private String cDocType;
    /**
    * 单据编号
    */
    @Schema(description = "单据编号")
    @TableField(value = "c_doc_no")
    private String cDocNo;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    @TableField(value = "c_batch_no")
    private String cBatchNo;
    /**
    * 操作类型(入库/出库)
    */
    @Schema(description = "操作类型(入库/出库)")
    @TableField(value = "c_operation_type")
    private String cOperationType;
    /**
    * 操作数量
    */
    @Schema(description = "操作数量")
    @TableField(value = "n_quantity")
    private BigDecimal nQuantity;
    /**
    * 操作前库存
    */
    @Schema(description = "操作前库存")
    @TableField(value = "n_before_stock")
    private BigDecimal nBeforeStock;
    /**
    * 操作后库存
    */
    @Schema(description = "操作后库存")
    @TableField(value = "n_after_stock")
    private BigDecimal nAfterStock;
    /**
    * 操作时间
    */
    @Schema(description = "操作时间")
    @TableField(value = "dt_operation_time")
    private LocalDateTime dtOperationTime;
    /**
    * 操作人
    */
    @Schema(description = "操作人")
    @TableField(value = "c_operator")
    private String cOperator;
    /**
    * 关联单号
    */
    @Schema(description = "关联单号")
    @TableField(value = "c_related_no")
    private String cRelatedNo;
    /**
    * 备注
    */
    @Schema(description = "备注")
    @TableField(value = "c_remark")
    private String cRemark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time",fill=FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark")
    private Integer nEnabledMark;
}
