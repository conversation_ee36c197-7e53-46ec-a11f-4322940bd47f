package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 订单管理
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@TableName("t_pss_order_info")
@Tag(name = "订单管理对象", description = "订单管理")
public class TPssOrderInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "订单号（订单类型+年月日+3位顺序号）")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    
    @Schema(description = "订单类型（P0004）")
    @TableField(value = "c_order_type")
    private String cOrderType;
    
    @Schema(description = "客户编码")
    @TableField(value = "c_customer_cd")
    private String cCustomerCd;
    
    @Schema(description = "产品类型（Y0006）")
    @TableField(value = "c_product_type")
    private String cProductType;
    
    @Schema(description = "产线id（P0001）")
    @TableField(value = "c_product_line_id")
    private Long cProductLineId;

    @Schema(description = "产线代码（P0001）")
    @TableField(value = "c_product_line_cd")
    private String cProductLineCd;

    @Schema(description = "产线名称（P0001）")
    @TableField(value = "c_product_line_name")
    private String cProductLineName;
    
    @Schema(description = "销售合同号")
    @TableField(value = "c_contract_no")
    private String cContractNo;

    @Schema(description = "质量编码id")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码name")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "标准编号")
    @TableField(value = "c_stdcode")
    private String cStdcode;
    
    @Schema(description = "钢种编码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_name")
    private String cStlGrdName;
    
    @Schema(description = "存货代码id")
    @TableField(value = "c_inventory_id")
    private Long cInventoryId;

    @Schema(description = "存货代码")
    @TableField(value = "c_inventory_cd")
    private String cInventoryCd;

    @Schema(description = "存货代码名称")
    @TableField(value = "c_inventory_name")
    private String cInventoryName;
    
    @Schema(description = "定尺类型（P0013）")
    @TableField(value = "c_size_property")
    private String cSizeProperty;
    
    @Schema(description = "直径（直条等）")
    @TableField(value = "n_prod_dia")
    private BigDecimal nProdDia;
    
    @Schema(description = "厚度（方钢等）")
    @TableField(value = "n_prod_thk")
    private BigDecimal nProdThk;
    
    @Schema(description = "宽度（方钢等）")
    @TableField(value = "n_prod_wid")
    private BigDecimal nProdWid;
    
    @Schema(description = "长度目标（卷和高线为9999999.999）")
    @TableField(value = "n_prod_lenth")
    private BigDecimal nProdLenth;
    
    @Schema(description = "长度最小")
    @TableField(value = "n_prod_len_min")
    private BigDecimal nProdLenMin;
    
    @Schema(description = "长度最大")
    @TableField(value = "n_prod_len_max")
    private BigDecimal nProdLenMax;
    
    @Schema(description = "称重方式（P0016）")
    @TableField(value = "c_wgt_fl")
    private String cWgtFl;
    
    @Schema(description = "订单重量(成品替代后订单剩余重量)")
    @TableField(value = "n_order_wgt")
    private BigDecimal nOrderWgt;
    
    @Schema(description = "重量单位（P0023）")
    @TableField(value = "c_wgt_unit")
    private String cWgtUnit;
    
    @Schema(description = "允许超交比率(%)")
    @TableField(value = "n_ext_rate")
    private BigDecimal nExtRate;
    
    @Schema(description = "允许欠交比率(%)")
    @TableField(value = "n_rem_rate")
    private BigDecimal nRemRate;
    
    @Schema(description = "交货方式代码(P0008)")
    @TableField(value = "c_fpoststate_cd")
    private String cFpoststateCd;
    
    @Schema(description = "目标交货日期")
    @TableField(value = "c_del_datetime")
    private LocalDateTime cDelDatetime;
    
    @Schema(description = "交货周期")
    @TableField(value = "n_del_interval")
    private BigDecimal nDelInterval;
    
    @Schema(description = "交货周期单位（P0026)")
    @TableField(value = "c_del_interval_unit")
    private String cDelIntervalUnit;
    
    @Schema(description = "包装方式代码(P0030)")
    @TableField(value = "c_pak_way_cd")
    private String cPakWayCd;
    
    @Schema(description = "客户特殊包装要求")
    @TableField(value = "c_cust_spec_print")
    private String cCustSpecPrint;
    
    @Schema(description = "订单结束日期")
    @TableField(value = "c_end_datetime")
    private LocalDateTime cEndDatetime;
    
    @Schema(description = "订单状态代码（P0020）")
    @TableField(value = "c_order_state")
    private String cOrderState;
    
    @Schema(description = "是否被合并（Y/N）")
    @TableField(value = "c_is_combined")
    private String cIsCombined;
    
    @Schema(description = "销售计划号")
    @TableField(value = "c_sales_plan_id")
    private String cSalesPlanId;
    
    @Schema(description = "销售计划序号")
    @TableField(value = "c_sales_plan_sn")
    private BigDecimal cSalesPlanSn;
    
    @Schema(description = "综合生产计划号")
    @TableField(value = "c_aggregate_plan_id", updateStrategy = FieldStrategy.ALWAYS)
    private String cAggregatePlanId;
    
    @Schema(description = "综合生产计划序号")
    @TableField(value = "n_aggregate_plan_sn")
    private BigDecimal nAggregatePlanSn;
    
    @Schema(description = "生产任务单编号")
    @TableField(value = "c_product_task_list_id")
    private String cProductTaskListId;
    
    @Schema(description = "销售部门")
    @TableField(value = "c_sales_dept_cd")
    private String cSalesDeptCd;
    
    @Schema(description = "是否紧急订单（0/1    0-普通订单 ，1-紧急订单）")
    @TableField(value = "c_exg_prod_lot_fl")
    private String cExgProdLotFl;
    
    @Schema(description = "订单备注")
    @TableField(value = "c_order_explain")
    private String cOrderExplain;
    
    @Schema(description = "订单分组页号")
    @TableField(value = "n_order_page_no")
    private BigDecimal nOrderPageNo;
    
    @Schema(description = "核准人")
    @TableField(value = "c_approval_emp")
    private String cApprovalEmp;
    
    @Schema(description = "核准时间")
    @TableField(value = "dt_approval_datetime")
    private LocalDateTime dtApprovalDatetime;
    
    @Schema(description = "订单操作说明")
    @TableField(value = "c_operation_note")
    private String cOperationNote;
    
    @Schema(description = "上一个状态")
    @TableField(value = "c_old_state")
    private String cOldState;
    
    @Schema(description = "从回收站释放前的订单录入时间")
    @TableField(value = "dt_old_input_datetime")
    private LocalDateTime dtOldInputDatetime;
    
    @Schema(description = "状态变更时间")
    @TableField(value = "dt_state_change_time", updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime dtStateChangeTime;
    
    @Schema(description = "计划扣重")
    @TableField(value = "n_reduce_weight")
    private BigDecimal nReduceWeight;
    
    @Schema(description = "计划扣重录入人员")
    @TableField(value = "c_reduce_emp_id")
    private String cReduceEmpId;
    
    @Schema(description = "计划扣重录入时间")
    @TableField(value = "dt_reduce_input_time")
    private LocalDateTime dtReduceInputTime;
    
    @Schema(description = "计划扣重审核状态（X—正常订单 W—等待审核 P—审核通过 C—审核否决）")
    @TableField(value = "c_reduce_review_status")
    private String cReduceReviewStatus;
    
    @Schema(description = "计划扣重审核人员")
    @TableField(value = "c_reduce_review_emp")
    private String cReduceReviewEmp;
    
    @Schema(description = "计划扣重审核时间")
    @TableField(value = "dt_reduce_review_time")
    private LocalDateTime dtReduceReviewTime;
    
    @Schema(description = "计划扣重操作日志")
    @TableField(value = "c_reduce_operat_log")
    private String cReduceOperatLog;
    
    @Schema(description = "质量设计信息日志")
    @TableField(value = "c_qual_des_log")
    private String cQualDesLog;
    
    @Schema(description = "上一次的操作记录")
    @TableField(value = "c_old_operation_note")
    private String cOldOperationNote;
    
    @Schema(description = "订单客户类型")
    @TableField(value = "c_order_cust_type")
    private String cOrderCustType;
    
    @Schema(description = "二维码类型")
    @TableField(value = "c_qrcode")
    private String cQrcode;
    
    @Schema(description = "数据来源 （电商接口DS）暂时作为原物料编码使用，切换完成后仍作为原字段")
    @TableField(value = "c_ins_source")
    private String cInsSource;
    
    @Schema(description = "是否保性能")
    @TableField(value = "c_smp_fl")
    private String cSmpFl;
    
    @Schema(description = "运输方式")
    @TableField(value = "c_ship_way")
    private String cShipWay;
    
    @Schema(description = "订单计划重量")
    @TableField(value = "n_plan_order_wgt")
    private BigDecimal nPlanOrderWgt;
    
    @Schema(description = "已排产数量")
    @TableField(value = "n_sch_order_wgt")
    private BigDecimal nSchOrderWgt;
    
    @Schema(description = "订单件数")
    @TableField(value = "n_order_cnt")
    private BigDecimal nOrderCnt;
    
    @Schema(description = "成品单重")
    @TableField(value = "n_prod_wgt")
    private BigDecimal nProdWgt;
    
    @Schema(description = "厚度公差最大值")
    @TableField(value = "n_thk_bias_max")
    private BigDecimal nThkBiasMax;
    
    @Schema(description = "厚度公差最小值")
    @TableField(value = "n_thk_bias_min")
    private BigDecimal nThkBiasMin;
    
    @Schema(description = "宽度公差最大值")
    @TableField(value = "n_wth_bias_max")
    private BigDecimal nWthBiasMax;
    
    @Schema(description = "宽度公差最小值")
    @TableField(value = "n_wth_bias_min")
    private BigDecimal nWthBiasMin;
    
    @Schema(description = "是否滑纹板")
    @TableField(value = "n_checker_flag")
    private String nCheckerFlag;
    
    @Schema(description = "滑纹板高度")
    @TableField(value = "n_checker_height")
    private Long nCheckerHeight;
    
    @Schema(description = "成品重量最大值")
    @TableField(value = "n_prod_wgt_max")
    private BigDecimal nProdWgtMax;
    
    @Schema(description = "成品重量最小值")
    @TableField(value = "n_prod_wgt_min")
    private BigDecimal nProdWgtMin;
    
    @Schema(description = "上级订单号")
    @TableField(value = "c_morder_id")
    private String cMorderId;
    
    @Schema(description = "销售合同号")
    @TableField(value = "c_sale_contract_no")
    private String cSaleContractNo;
    
    @Schema(description = "规格代码")
    @TableField(value = "c_item_cd")
    private String cItemCd;
    
    @Schema(description = "成品规格")
    @TableField(value = "c_mat_item")
    private String cMatItem;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}