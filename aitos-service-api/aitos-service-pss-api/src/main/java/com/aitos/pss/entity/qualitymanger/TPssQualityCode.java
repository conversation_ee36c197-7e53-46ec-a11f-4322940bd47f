package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 质量编码管理
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@Data
@TableName("t_pss_quality_code")
@Tag(name = "质量编码管理对象", description = "质量编码管理")
public class TPssQualityCode implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "质量代码;质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "简称")
    @TableField(value = "c_qual_code_abb")
    private String cQualCodeAbb;
    
    @Schema(description = "描述;")
    @TableField(value = "c_describe")
    private String cDescribe;
    
    @Schema(description = "标准名称（中）")
    @TableField(value = "c_std_name_chi")
    private String cStdNameChi;
    
    @Schema(description = "标准名称（英）")
    @TableField(value = "c_std_name_eng")
    private String cStdNameEng;
    
    @Schema(description = "执行标准")
    @TableField(value = "c_op_std_name")
    private String cOpStdName;
    
    @Schema(description = "成品物料名称")
    @TableField(value = "c_material_name")
    private String cMaterialName;
    
    @Schema(description = "成品物料编码")
    @TableField(value = "c_material_code")
    private String cMaterialCode;

    @Schema(description = "成品物料Id")
    @TableField(value = "c_mate_id")
    private Long cMateId;
    
    @Schema(description = "状态（新建/已转换）")
    @TableField(value = "c_status")
    private Integer cStatus;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;

}