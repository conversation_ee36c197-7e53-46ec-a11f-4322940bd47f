package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;



/**
* @title: 质量编码管理
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@Data
public class UpdateTPssQualityCodeSubDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "质量编码")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    private String cQualCodeName;
    
    @Schema(description = "质检标准名称")
    private String nFstditemName;
    
    @Schema(description = "简称")
    private String nFsimplename;
    
    @Schema(description = "质检标准编码")
    private String nFstditem;

    @Schema(description = "质检标准Id")
    private Long nFstditemId;
    
    @Schema(description = "描述")
    private String nFdescription;
    
    @Schema(description = "标准类型")
    private String nFjudgetype;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
