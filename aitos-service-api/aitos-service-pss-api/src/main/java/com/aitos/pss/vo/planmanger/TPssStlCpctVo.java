package com.aitos.pss.vo.planmanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
public class TPssStlCpctVo {

    
    @Schema(description = "主键")
    private Long nId;

    @Schema(description = "钢种")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;

    @Schema(description = "电炉座次id")
    private String cBofId;

    @Schema(description = "电炉座次编码")
    private String cBofNo;

    @Schema(description = "电炉座次名称")
    private String cBofName;
    
    @Schema(description = "钢水下限")
    private Integer nHeatMin;
    
    @Schema(description = "钢水上限")
    private Integer nHeatMax1;
    
    @Schema(description = "收得率")
    private BigDecimal nHeatRacvRadio;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
