package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 浇次设计表
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
@TableName("t_pss_castp_edit")
@Tag(name = "浇次设计表对象", description = "浇次设计表")
public class TPssCastpEdit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 序号
    */
    @Schema(description = "序号")
    @TableId
    private Long nId;
    /**
    * 浇次编制号
    */
    @Schema(description = "浇次编制号")
    @TableField(value = "n_cast_edt_seq")
    private String nCastEdtSeq;
    /**
    * 计划浇次号
    */
    @Schema(description = "计划浇次号")
    @TableField(value = "c_plan_cast_id")
    private String cPlanCastId;
    /**
    * 浇铸厚度
    */
    @Schema(description = "浇铸厚度")
    @TableField(value = "n_slab_thk")
    private BigDecimal nSlabThk;
    /**
    * 浇铸宽度
    */
    @Schema(description = "浇铸宽度")
    @TableField(value = "n_slab_wth")
    private BigDecimal nSlabWth;
    /**
    * 计划开始时间
    */
    @Schema(description = "计划开始时间")
    @TableField(value = "dt_start_dt")
    private LocalDateTime dtStartDt;
    /**
    * 计划结束时间
    */
    @Schema(description = "计划结束时间")
    @TableField(value = "dt_end_dt")
    private LocalDateTime dtEndDt;
    /**
    * 铸机号
    */
    @Schema(description = "铸机号")
    @TableField(value = "c_cast_mach_id")
    private String cCastMachId;
    /**
    * 炉数
    */
    @Schema(description = "炉数")
    @TableField(value = "n_heat_cnt")
    private Long nHeatCnt;
    /**
    * 大浇次序号
    */
    @Schema(description = "大浇次序号")
    @TableField(value = "n_big_cast_seq")
    private BigDecimal nBigCastSeq;
    /**
    * 状态
    */
    @Schema(description = "状态")
    @TableField(value = "c_status")
    private String cStatus;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time")
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}