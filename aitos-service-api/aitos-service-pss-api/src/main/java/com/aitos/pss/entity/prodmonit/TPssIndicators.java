package com.aitos.pss.entity.prodmonit;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 指标配置
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
@TableName("t_pss_indicators")
@Tag(name = "指标配置对象", description = "指标配置")
public class TPssIndicators implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    @Schema(description = "主键id")
    @TableId
    private Long nId;
    /**
    * 指标名称
    */
    @Schema(description = "指标名称")
    @TableField(value = "c_name")
    private String cName;
    /**
    * 指标代码
    */
    @Schema(description = "指标代码")
    @TableField(value = "c_code")
    private String cCode;
    /**
    * 设备id
    */
    @Schema(description = "设备id")
    @TableField(value = "n_equipment_id")
    private Long nEquipmentId;
    /**
    * 设备code
    */
    @Schema(description = "设备code")
    @TableField(value = "c_equipment_code")
    private String cEquipmentCode;
    /**
    * 设备name
    */
    @Schema(description = "设备name")
    @TableField(value = "c_equipment_name")
    private String cEquipmentName;
    /**
    * 单位id
    */
    @Schema(description = "单位id")
    @TableField(value = "c_unit_id")
    private Long cUnitId;
    /**
    * 单位code
    */
    @Schema(description = "单位code")
    @TableField(value = "c_unit_code")
    private String cUnitCode;
    /**
    * 单位name
    */
    @Schema(description = "单位name")
    @TableField(value = "c_unit_name")
    private String cUnitName;
    /**
    * 描述
    */
    @Schema(description = "描述")
    @TableField(value = "c_description")
    private String cDescription;
    /**
    * 正常值下限
    */
    @Schema(description = "正常值下限")
    @TableField(value = "n_normal_min")
    private BigDecimal nNormalMin;
    /**
    * 正常值上限
    */
    @Schema(description = "正常值上限")
    @TableField(value = "n_normal_max")
    private BigDecimal nNormalMax;
    /**
    * 预警值下限
    */
    @Schema(description = "预警值下限")
    @TableField(value = "n_warning_min")
    private BigDecimal nWarningMin;
    /**
    * 预警值上限
    */
    @Schema(description = "预警值上限")
    @TableField(value = "n_warning_max")
    private BigDecimal nWarningMax;
    /**
    * 违规值下限
    */
    @Schema(description = "违规值下限")
    @TableField(value = "n_violation_min")
    private BigDecimal nViolationMin;
    /**
    * 违规值上限
    */
    @Schema(description = "违规值上限")
    @TableField(value = "n_violation_max")
    private BigDecimal nViolationMax;
    /**
    * 指标点位配置
    */
    @Schema(description = "指标点位配置")
    @TableField(value = "c_point_config")
    private String cPointConfig;
    /**
    * 判断类型
    */
    @Schema(description = "判断类型")
    @TableField(value = "c_judgment_type")
    private String cJudgmentType;
    /**
    * 状态（1:启用, 0:禁用）
    */
    @Schema(description = "状态（1:启用, 0:禁用）")
    @TableField(value = "c_status")
    private String cStatus;

    @Schema(description = "审批人ID")
    @TableField(value = "n_approver_id")
    private Long nApproverId;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time",fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}
