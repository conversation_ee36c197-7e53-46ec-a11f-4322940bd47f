package com.aitos.pss.dto.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;



/**
* @title: 库区库位管理子表
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@Data
public class UpdateTPssWareHouseLocDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键
    */
    @Schema(description = "主键")
    private Long nId;
    /**
    * 代码
    */
    @Schema(description = "代码")
    private String cCode;
    /**
    * 设置类型
    */
    @Schema(description = "设置类型")
    private String cType;
    /**
    * 名称
    */
    @Schema(description = "名称")
    private String cName;
    /**
    * 列（数）
    */
    @Schema(description = "列（数）")
    private Integer nCol;
    /**
    * 行（数）
    */
    @Schema(description = "行（数）")
    private Integer nRow;
    /**
    * 上级节点
    */
    @Schema(description = "上级节点")
    private String cParentCode;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
