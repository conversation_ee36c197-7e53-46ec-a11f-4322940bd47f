package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-16
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssBatchSamplingPageDto extends PageInput {

    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "规格")
    private String cSpec;
    
    @Schema(description = "质量编码")
    private String cQualityCode;
    
    @Schema(description = "物料长度")
    private BigDecimal nThk;
    
    @Schema(description = "委托单号")
    private String cTestcrdId;
    
    @Schema(description = "委托单状态（A：等待发送、B：等待试验、C：试验完毕、D：性能判定完毕）")
    private String cSmpStates;
    
    @Schema(description = "创建人")
    private Long nCreateUserId;
    
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    
    @Schema(description = "炉次号")
    private String cHeatId;
    
    @Schema(description = "钢种名称")
    private String cStlGrdDesc;
    
    @Schema(description = "检验批号")
    private String cSmpLot;
    
    @Schema(description = "产线")
    private String cLineCd;
    
    @Schema(description = "委托次数")
    private Integer nSampNum;
    
    @Schema(description = "检验炉次号")
    private String cSmpHeatId;
    
    @Schema(description = "检验批次包含炉次号")
    private String cSampHeatIdList;
    
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateStart;
    
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateEnd;
    
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateStart;
    
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateEnd;

}
