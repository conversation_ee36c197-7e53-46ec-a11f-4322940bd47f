package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 炼钢化学成分
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
public class UpdateTPssCheSteelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "顺序号")
    private Long nId;
    
    @Schema(description = "式样类型")
    private String cSampletype;
    
    @Schema(description = "炉号")
    private String cSampleid;
    
    @Schema(description = "写入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSampTime;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称")
    private String cStlGrdName;
    
    @Schema(description = "说明")
    private String cCommentfld;
    
    @Schema(description = "取样序号/次数")
    private Integer nSmpNums;
    
    @Schema(description = "试样号")
    private String cSmpId;
    
    @Schema(description = "c")
    private BigDecimal nC;
    
    @Schema(description = "si")
    private BigDecimal nSi;
    
    @Schema(description = "mn")
    private BigDecimal nMn;
    
    @Schema(description = "p")
    private BigDecimal nP;
    
    @Schema(description = "s")
    private BigDecimal nS;
    
    @Schema(description = "cr")
    private BigDecimal nCr;
    
    @Schema(description = "ni")
    private BigDecimal nNi;
    
    @Schema(description = "cu")
    private BigDecimal nCu;
    
    @Schema(description = "v")
    private BigDecimal nV;
    
    @Schema(description = "mo")
    private BigDecimal nMo;
    
    @Schema(description = "ti")
    private BigDecimal nTi;
    
    @Schema(description = "nb")
    private BigDecimal nNb;
    
    @Schema(description = "ceq")
    private BigDecimal nCeq;
    /**
    * als
    */
    @Schema(description = "als")
    private BigDecimal nAls;
    /**
    * al
    */
    @Schema(description = "al")
    private BigDecimal nAl;
    /**
    * ca
    */
    @Schema(description = "ca")
    private BigDecimal nCa;
    /**
    * b
    */
    @Schema(description = "b")
    private BigDecimal nB;
    /**
    * pb
    */
    @Schema(description = "pb")
    private BigDecimal nPb;
    /**
    * zr
    */
    @Schema(description = "zr")
    private BigDecimal nZr;
    /**
    * w
    */
    @Schema(description = "w")
    private BigDecimal nW;
    /**
    * asfld
    */
    @Schema(description = "asfld")
    private BigDecimal nAsfld;
    /**
    * sn
    */
    @Schema(description = "sn")
    private BigDecimal nSn;
    /**
    * co
    */
    @Schema(description = "co")
    private BigDecimal nCo;
    /**
    * bi
    */
    @Schema(description = "bi")
    private BigDecimal nBi;
    /**
    * zn
    */
    @Schema(description = "zn")
    private BigDecimal nZn;
    /**
    * sb
    */
    @Schema(description = "sb")
    private BigDecimal nSb;
    /**
    * la
    */
    @Schema(description = "la")
    private BigDecimal nLa;
    /**
    * ce
    */
    @Schema(description = "ce")
    private BigDecimal nCe;
    /**
    * n
    */
    @Schema(description = "n")
    private BigDecimal nN;
    /**
    * bs
    */
    @Schema(description = "bs")
    private BigDecimal nBs;
    /**
    * aln
    */
    @Schema(description = "aln")
    private BigDecimal nAln;
    /**
    * mg
    */
    @Schema(description = "mg")
    private BigDecimal nMg;
    /**
    * ct
    */
    @Schema(description = "ct")
    private BigDecimal nCt;
    /**
    * c_g
    */
    @Schema(description = "c_g")
    private Integer nCG;
    /**
    * si_g
    */
    @Schema(description = "si_g")
    private Integer nSiG;
    /**
    * mn_g
    */
    @Schema(description = "mn_g")
    private Integer nMnG;
    /**
    * p_g
    */
    @Schema(description = "p_g")
    private Integer nPG;
    /**
    * s_g
    */
    @Schema(description = "s_g")
    private Integer nSG;
    /**
    * ceq_g
    */
    @Schema(description = "ceq_g")
    private Integer nCeqG;
    /**
    * cr_g
    */
    @Schema(description = "cr_g")
    private Integer nCrG;
    /**
    * ni_g
    */
    @Schema(description = "ni_g")
    private Integer nNiG;
    /**
    * cu_g
    */
    @Schema(description = "cu_g")
    private Integer nCuG;
    /**
    * v_g
    */
    @Schema(description = "v_g")
    private Integer nVG;
    /**
    * mo_g
    */
    @Schema(description = "mo_g")
    private Integer nMoG;
    /**
    * ti_g
    */
    @Schema(description = "ti_g")
    private Integer nTiG;
    /**
    * nb_g
    */
    @Schema(description = "nb_g")
    private Integer nNbG;
    /**
    * als_g
    */
    @Schema(description = "als_g")
    private Integer nAlsG;
    /**
    * al_g
    */
    @Schema(description = "al_g")
    private Integer nAlG;
    /**
    * ca_g
    */
    @Schema(description = "ca_g")
    private Integer nCaG;
    /**
    * b_g
    */
    @Schema(description = "b_g")
    private Integer nBG;
    /**
    * pb_g
    */
    @Schema(description = "pb_g")
    private Integer nPbG;
    /**
    * zr_g
    */
    @Schema(description = "zr_g")
    private Integer nZrG;
    /**
    * w_g
    */
    @Schema(description = "w_g")
    private Integer nWG;
    /**
    * asfld_g
    */
    @Schema(description = "asfld_g")
    private Integer nAsfldG;
    /**
    * sn_g
    */
    @Schema(description = "sn_g")
    private Integer nSnG;
    /**
    * co_g
    */
    @Schema(description = "co_g")
    private Integer nCoG;
    /**
    * bi_g
    */
    @Schema(description = "bi_g")
    private Integer nBiG;
    /**
    * sb_g
    */
    @Schema(description = "sb_g")
    private Integer nSbG;
    /**
    * zn_g
    */
    @Schema(description = "zn_g")
    private Integer nZnG;
    /**
    * la_g
    */
    @Schema(description = "la_g")
    private Integer nLaG;
    /**
    * ce_g
    */
    @Schema(description = "ce_g")
    private Integer nCeG;
    /**
    * n_g
    */
    @Schema(description = "n_g")
    private Integer nNG;
    /**
    * bs_g
    */
    @Schema(description = "bs_g")
    private Integer nBsG;
    /**
    * aln_g
    */
    @Schema(description = "aln_g")
    private Integer nAlnG;
    /**
    * mg_g
    */
    @Schema(description = "mg_g")
    private Integer nMgG;
    /**
    * ct_g
    */
    @Schema(description = "ct_g")
    private Integer nCtG;
    /**
    * 质量等级
    */
    @Schema(description = "质量等级")
    private String cJudge;
    /**
    * o
    */
    @Schema(description = "o")
    private BigDecimal nO;
    /**
    * h
    */
    @Schema(description = "h")
    private BigDecimal nH;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    private String cMatQulCd;

}
