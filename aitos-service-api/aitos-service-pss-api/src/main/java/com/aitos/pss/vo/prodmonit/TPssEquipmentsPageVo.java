package com.aitos.pss.vo.prodmonit;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
public class TPssEquipmentsPageVo {

    /**
    * 主键id
    */
    @ExcelIgnore
    @Schema(description = "主键id")
    private String nId;
    /**
    * 设备名称
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("设备名称")
    @Schema(description = "设备名称")
    private String cName;
    /**
    * 设备代码
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("设备代码")
    @Schema(description = "设备代码")
    private String cCode;
    /**
    * 设备类型
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("设备类型")
    @Trans(type = TransType.DIC, id = "1949634193148960769")
    @Schema(description = "设备类型")
    private String cType;
    /**
    * 位置
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("位置")
    @Schema(description = "位置")
    private String cLocation;
    /**
    * 状态
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("状态")
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 描述
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("描述")
    @Schema(description = "描述")
    private String cDescription;
    /**
    * 制造商
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("制造商")
    @Schema(description = "制造商")
    private String cManufacturer;
    /**
    * 型号
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("型号")
    @Schema(description = "型号")
    private String cModel;
    /**
    * 序列号
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("序列号")
    @Schema(description = "序列号")
    private String cSerialNumber;
    /**
    * 购买日期
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("购买日期")
    @Schema(description = "购买日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPurchaseDate;
    /**
    * 保修期（月）
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("保修期（月）")
    @Schema(description = "保修期（月）")
    private Integer nWarrantyPeriod;
    /**
    * 最后维护时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("最后维护时间")
    @Schema(description = "最后维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtLastMaintainDateTime;
    /**
    * 下次维护时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("下次维护时间")
    @Schema(description = "下次维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtNextMaintainDateTime;
    /**
    * 创建时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("创建时间")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("最后修改时间")
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("逻辑删除标记")
    @Schema(description = "逻辑删除标记")
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否有效/启用标记")
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;

    @Schema(description = "设备指标数据")
    List<TPssIndicatorDataVo> indicatorDataVoList;
}
