package com.aitos.pss.entity.inventory;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 退货管理子表
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Data
@TableName("t_pss_steel_return_record")
@Tag(name = "退货管理子表对象", description = "退货管理子表")
public class TPssSteelReturnRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    @Schema(description = "主键id")
    @TableId
    private Long nId;
    /**
    * 提货单号
    */
    @Schema(description = "提货单号")
    @TableField(value = "c_delivery_no")
    private String cDeliveryNo;
    /**
    * 库号
    */
    @Schema(description = "库号")
    @TableField(value = "c_stock_no")
    private String cStockNo;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    @TableField(value = "c_batch_no")
    private String cBatchNo;
    /**
    * 材料号/卷号
    */
    @Schema(description = "材料号/卷号")
    @TableField(value = "c_material_no")
    private String cMaterialNo;
    /**
    * 钢种名称
    */
    @Schema(description = "钢种名称")
    @TableField(value = "c_steel_grade")
    private String cSteelGrade;
    /**
    * 规格
    */
    @Schema(description = "规格")
    @TableField(value = "c_spec")
    private String cSpec;
    /**
    * 单件重量(kg)
    */
    @Schema(description = "单件重量(kg)")
    @TableField(value = "n_weight")
    private BigDecimal nWeight;
    /**
    * 退货数量(件/卷)
    */
    @Schema(description = "退货数量(件/卷)")
    @TableField(value = "n_return_qty")
    private Integer nReturnQty;
    /**
    * 退货总重量(kg)
    */
    @Schema(description = "退货总重量(kg)")
    @TableField(value = "n_return_weight")
    private BigDecimal nReturnWeight;
    /**
    * 退货入库时间
    */
    @Schema(description = "退货入库时间")
    @TableField(value = "dt_return_time")
    private LocalDateTime dtReturnTime;
    /**
    * 退货操作人
    */
    @Schema(description = "退货操作人")
    @TableField(value = "c_return_user")
    private String cReturnUser;
    /**
    * 退货原因
    */
    @Schema(description = "退货原因")
    @TableField(value = "c_return_reason")
    private String cReturnReason;
    /**
    * 原出库单号
    */
    @Schema(description = "原出库单号")
    @TableField(value = "c_out_no")
    private String cOutNo;
    /**
    * 状态(已退库/已冲销等)
    */
    @Schema(description = "状态(已退库/已冲销等)")
    @TableField(value = "c_status")
    private String cStatus;
    /**
    * 备注
    */
    @Schema(description = "备注")
    @TableField(value = "c_remark")
    private String cRemark;
    /**
    * 是否启用;默认为0,1为未启用
    */
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;


}