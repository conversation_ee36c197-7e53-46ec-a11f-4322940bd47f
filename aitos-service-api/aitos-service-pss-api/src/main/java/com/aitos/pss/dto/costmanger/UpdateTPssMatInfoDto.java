package com.aitos.pss.dto.costmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 投入产出管理
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/
@Data
public class UpdateTPssMatInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    *
    */
    @Schema(description = "")
    private Long nId;
    /**
     * 产线
     */
    @Schema(description = "产线id")
    private Long cProLine;

    @Schema(description = "产线code")
    private String cProLineCode;

    @Schema(description = "产线名称")
    private String  cProLineName;
    /**
     * 电炉座次id
     */
    @Schema(description = "电炉座次id")
    private String cBofId;

    @Schema(description = "电炉座次编码")
    private String cBofNo;

    @Schema(description = "电炉座次名称")
    private String cBofName;

    @Schema(description = "炉次号")
    private Long cHeatId;
    /**
     * 工序号
     */
    @Schema(description = "工序id")
    private Long cProcId;

    @Schema(description = "工序编码")
    private String  cProcCd;

    @Schema(description = "工序名称")
    private String  cProcName;
    /**
     * 物料id
     */
    @Schema(description = "物料id")
    private Long cMateId;
    /**
     * 物料代码
     */
    @Schema(description = "物料编码")
    private String cMaterialCode;
    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String cMaterialName;
    /**
     * 物料类型
     */
    @Schema(description = "物料类型")
    private String cMaterialType;
    /**
     * 物料类型id
     */
    @Schema(description = "物料类型")
    private Long cMaterialTypeId;
    /**
    * 投料累计/产出累计
    */
    @Schema(description = "投料累计/产出累计")
    private BigDecimal cTotVal;
    /**
    * 信息状态
    */
    @Schema(description = "信息状态")
    private Integer cMsgStatus;
    /**
    * 物料类型
    */
    @Schema(description = "物料类型")
    private String cMatType;
    /**
    * 班组
    */
    @Schema(description = "班组")
    private String cCrew;
    /**
    * 班次
    */
    @Schema(description = "班次")
    private String cShift;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
