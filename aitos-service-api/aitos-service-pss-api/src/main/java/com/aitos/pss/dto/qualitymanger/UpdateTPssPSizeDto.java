package com.aitos.pss.dto.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;



/**
* @title: 轧钢表尺判定管理
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@Data
public class UpdateTPssPSizeDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "")
    private Long nId;
    
    @Schema(description = "线卷号/坯料号/件次号")
    private String cPlateId;
    
    @Schema(description = "检查次数")
    private Integer nChkSeq;
    
    @Schema(description = "检查工序")
    private String cChkPos;
    
    @Schema(description = "线卷面")
    private String cPlateFace;
    
    @Schema(description = "缺陷序号")
    private Long nDefectSeq;
    
    @Schema(description = "缺陷代码")
    private String cDefectType;
    
    @Schema(description = "缺陷位置")
    private String cDefectPos;
    
    @Schema(description = "缺陷描述")
    private String cDefectDesc;
    
    @Schema(description = "处理建议")
    private String cDispPro;
    
    @Schema(description = "处理方式")
    private String cDispManner;
    
    @Schema(description = "处理结果")
    private String cDispResult;
    
    @Schema(description = "班次")
    private String cShift;
    
    @Schema(description = "班别")
    private String cCrew;
    
    @Schema(description = "处理人")
    private String cGrindOperater;
    
    @Schema(description = "处理班次")
    private String cGrindShift;
    
    @Schema(description = "处理班别")
    private String cGrindCrew;
    
    @Schema(description = "尺寸等级")
    private String cSizeRlt;
    
    @Schema(description = "备注")
    private String cRemarks;

}
