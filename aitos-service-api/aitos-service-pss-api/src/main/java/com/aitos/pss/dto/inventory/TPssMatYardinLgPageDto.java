package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssMatYardinLgPageDto extends PageInput {

    /**
    * 顺序号
    */
    @Schema(description = "顺序号")
    private Long nId;
    /**
    * 入库单号
    */
    @Schema(description = "入库单号")
    private String cYardinId;
    /**
    * 入库状态
    */
    @Schema(description = "入库状态")
    private String cIfFlag;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatname;
    /**
    * 产线
    */
    @Schema(description = "产线")
    private String cLineCd;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    private String cLotId;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    private String cTaskListId;
    /**
    * 销售订单号
    */
    @Schema(description = "销售订单号")
    private String cSaleNo;
    /**
    * 下游订单号
    */
    @Schema(description = "下游订单号")
    private String cOrderIdFinal;
    /**
    * 厚度
    */
    @Schema(description = "厚度")
    private BigDecimal nMatThk;
    /**
    * 内径
    */
    @Schema(description = "内径")
    private BigDecimal nMatDia;
    /**
    * 件数
    */
    @Schema(description = "件数")
    private Integer nMatCnt;
    /**
    * 入库检斤重量
    */
    @Schema(description = "入库检斤重量")
    private BigDecimal nMatWgt;
    /**
    * 源地仓库代码
    */
    @Schema(description = "源地仓库代码")
    private String cFromYard;
    /**
    * 库位号
    */
    @Schema(description = "库位号")
    private String cLoc;
    /**
    * 车号
    */
    @Schema(description = "车号")
    private String cTransNo;
    /**
    * 入库类型
    */
    @Schema(description = "入库类型")
    private String cYardinType;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cMemo;
    /**
    * 入库班别
    */
    @Schema(description = "入库班别")
    private String cCrew;
    /**
    * 数据来源
    */
    @Schema(description = "数据来源")
    private String cDataSource;
    /**
    * 是否上传
    */
    @Schema(description = "是否上传")
    private String cUploadFlag;
    /**
    * 终判等级
    */
    @Schema(description = "终判等级")
    private String cProdGrd;
    /**
    * 件次号/坯料号
    */
    @Schema(description = "件次号/坯料号")
    private String cMatId;
    /**
    * 数量
    */
    @Schema(description = "数量")
    private Integer nCnt;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMatcode;
    /**
    * 物料类型
    */
    @Schema(description = "物料类型")
    private String cMatType;
    /**
    * 仓库代码
    */
    @Schema(description = "仓库代码")
    private String cYardCd;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    private String cMatQulCd;
    /**
    * 原料批次号
    */
    @Schema(description = "原料批次号")
    private String cMatLotId;
    /**
    * 订单号（代表订单号）
    */
    @Schema(description = "订单号（代表订单号）")
    private String cOrderNo;
    /**
    * 销售订单行号
    */
    @Schema(description = "销售订单行号")
    private String cSaleSn;
    /**
    * 规格
    */
    @Schema(description = "规格")
    private String cMatItem;
    /**
    * 宽度
    */
    @Schema(description = "宽度")
    private BigDecimal nMatWth;
    /**
    * 长度
    */
    @Schema(description = "长度")
    private BigDecimal nMatLen;
    /**
    * 理论重量/发货重量
    */
    @Schema(description = "理论重量/发货重量")
    private BigDecimal nMatWgtCal;
    /**
    * 产地产线代码
    */
    @Schema(description = "产地产线代码")
    private String cSource;
    /**
    * 目的地仓库代码
    */
    @Schema(description = "目的地仓库代码")
    private String cToYard;
    /**
    * 运输方式
    */
    @Schema(description = "运输方式")
    private String cTransType;
    /**
    * 计量单号
    */
    @Schema(description = "计量单号")
    private String cJilianId;
    /**
    * 入库原因
    */
    @Schema(description = "入库原因")
    private String cYardinRsn;
    /**
    * 入库班次
    */
    @Schema(description = "入库班次")
    private String cShift;
    /**
    * 结存月份
    */
    @Schema(description = "结存月份")
    private String cMonth;
    /**
    * 最后修改程序
    */
    @Schema(description = "最后修改程序")
    private String cPgmId;
    /**
    * 上传消息号
    */
    @Schema(description = "上传消息号")
    private Long cMsgNo;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
