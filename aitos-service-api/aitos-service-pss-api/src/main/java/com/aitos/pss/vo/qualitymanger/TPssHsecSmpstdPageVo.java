package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-17
* @Version 1.0
*/
@Data
public class TPssHsecSmpstdPageVo {

    
    @ExcelIgnore
    @Schema(description = "")
    private String nId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量编码")
    @Schema(description = "质量编码")
    private String cQualityCode;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量编码名称")
    @Schema(description = "质量编码名称")
    private String cQualityCodeName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种代码")
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种名称")
    @Schema(description = "钢种名称")
    private String cStlGrdDesc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("取样类型")
    @Schema(description = "取样类型")
    @Trans(type = TransType.DIC, id = "1933072827488616449")
    private String cSmpType;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产线")
    @Schema(description = "产线")
    private String cLineCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产品长度")
    @Schema(description = "产品长度")
    private String nThk;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产品规格")
    @Schema(description = "产品规格")
    @Trans(type = TransType.DIC, id = "1925389418701066242")
    private String cSpec;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("取样次数")
    @Schema(description = "取样次数")
    private Integer nSmpNum;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;

    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

}
