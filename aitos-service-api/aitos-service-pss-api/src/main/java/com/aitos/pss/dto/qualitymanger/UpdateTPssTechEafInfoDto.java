package com.aitos.pss.dto.qualitymanger;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 电炉工艺参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
public class UpdateTPssTechEafInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "生产订单号")
    private String orderId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_id")
    private Long cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;

    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;

    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "目标S")
    private String cTargetS;
    
    @Schema(description = "扒渣基准")
    private String cSlagStd;
    
    @Schema(description = "铁水")
    private String cIron;
    
    @Schema(description = "废钢")
    private String cSteelScrap;
    
    @Schema(description = "预熔渣")
    private String cPremelSlag;
    
    @Schema(description = "白灰")
    private String cLime;
    
    @Schema(description = "碱度")
    private BigDecimal cDeoxidizeCdoe;
    
    @Schema(description = "出钢[C]")
    private String cDeoxidizeName;
    
    @Schema(description = "出钢温度")
    private BigDecimal cDeoxidizeSum;
    
    @Schema(description = "挂罐温度")
    private BigDecimal cHangPotTemp;
    
    @Schema(description = "到站温度")
    private BigDecimal cArriveTemp;
    
    @Schema(description = "是否挡渣出钢")
    private String cSlagTapYn;
    
    @Schema(description = "钢包氩气流量")
    private BigDecimal cArFlux;
    
    @Schema(description = "萤石")
    private String cFluorite;
    
    @Schema(description = "渣洗剂")
    private String cSlagLotion;
    
    @Schema(description = "硅灰石")
    private String cSilicaFume;
    
    @Schema(description = "电石")
    private String cCalciumCarbide;
    
    @Schema(description = "钙渣球")
    private String cCaBall;
    
    @Schema(description = "石英砂")
    private String cSio2;
    
    @Schema(description = "备用1")
    private String cBackup1;
    
    @Schema(description = "备用2")
    private String cBackup2;
    
    @Schema(description = "备用3")
    private String cBackup3;
    
    @Schema(description = "备用4")
    private String cBackup4;
    
    @Schema(description = "备用5")
    private String cBackup5;
    
    @Schema(description = "备用6")
    private String cBackup6;
    
    @Schema(description = "备用7")
    private String cBackup7;
    
    @Schema(description = "备用8")
    private String cBackup8;
    
    @Schema(description = "备用9")
    private String cBackup9;
    
    @Schema(description = "备用10")
    private String cBackup10;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
