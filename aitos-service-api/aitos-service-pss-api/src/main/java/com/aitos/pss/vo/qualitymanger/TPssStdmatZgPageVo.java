package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Data
public class TPssStdmatZgPageVo {

    
    @ExcelIgnore
    @Schema(description = "主键")
    private String nId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量等级")
    @Schema(description = "质量等级")
    @Trans(type = TransType.DIC, id = "1925392568048750593")
    private String cStdClass;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("执行标准号")
    @Schema(description = "执行标准号")
    private String cOpStdName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("厚度最小值")
    @Schema(description = "厚度最小值")
    private BigDecimal nThkMin;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("厚度最大值")
    @Schema(description = "厚度最大值")
    private BigDecimal nThkMax;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质检标准名称/试验项目")
    @Schema(description = "质检标准名称/试验项目")
    private String cVerifyItemName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质检标准编码/实验项目编码")
    @Schema(description = "质检标准编码/实验项目编码")
    private String cVerifyItemCode;

    @Schema(description = "质检标准id")
    private Long cVerifyItemId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("下限比较符")
    @Schema(description = "下限比较符")
    @Trans(type = TransType.DIC, id = "1925391187640389633")
    private String cSmpMinTag;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("上限比较符")
    @Schema(description = "上限比较符")
    @Trans(type = TransType.DIC, id = "1925391187640389633")
    private String cSmpMaxTag;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("下限")
    @Schema(description = "下限")
    private BigDecimal cSmpMin;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("上限")
    @Schema(description = "上限")
    private BigDecimal cSmpMax;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("平均")
    @Schema(description = "平均")
    private BigDecimal cSmpAvg;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("平均下限")
    @Schema(description = "平均下限")
    private BigDecimal cSmpAvgMin;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("平均上限")
    @Schema(description = "平均上限")
    private BigDecimal cSmpAvgMax;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("判定方法")
    @Schema(description = "判定方法")
    @Trans(type = TransType.DIC, id = "1943183332365393921")
    private String cSmpDcsCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("备注")
    @Schema(description = "备注")
    private String cMemo;

    @Schema(description = "质量编码id")
    private String cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;

    @Schema(description = "质量编码名称")
    private String cQualityCodeName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;

}
