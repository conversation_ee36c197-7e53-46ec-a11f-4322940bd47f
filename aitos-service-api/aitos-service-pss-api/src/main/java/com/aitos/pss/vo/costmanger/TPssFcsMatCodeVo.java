package com.aitos.pss.vo.costmanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-06-26
* @Version 1.0
*/
@Data
public class TPssFcsMatCodeVo {

    /**
    * 主键id
    */
    @Schema(description = "主键id")
    private Long nId;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private Long cMatId;

    @Schema(description = "物料编码")
    private String cMatCode;

    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatName;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private String cMatUnit;
    /**
    * 是否使用
    */
    @Schema(description = "是否使用")
    private Integer cAplyStd;
    /**
    * 物料科目编码
    */
    @Schema(description = "物料科目编码")
    private String cMatClassCode;
    /**
    * 物料科目名称
    */
    @Schema(description = "物料科目名称")
    private String cMatClassName;
    /**
    * 物料组编码
    */
    @Schema(description = "物料组编码")
    private String cMatGroupId;
    /**
    * 物料组名称
    */
    @Schema(description = "物料组名称")
    private String cMatGroupName;
    /**
    * 项目大类代码
    */
    @Schema(description = "项目大类代码")
    private String cMatTypeBId;
    /**
    * 项目大类名称
    */
    @Schema(description = "项目大类名称")
    private String cMatTypeBName;
    /**
    * 项目中类代码
    */
    @Schema(description = "项目中类代码")
    private String cMatTypeMId;
    /**
    * 项目中类名称
    */
    @Schema(description = "项目中类名称")
    private String cMatTypeMName;
    /**
    * 项目小类代码
    */
    @Schema(description = "项目小类代码")
    private String cMatTypeSId;
    /**
    * 项目小类名称
    */
    @Schema(description = "项目小类名称")
    private String cMatTypeSName;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdId;
    /**
    * 规格1
    */
    @Schema(description = "规格1")
    private String cMatItem1;
    /**
    * 规格2
    */
    @Schema(description = "规格2")
    private String cMatItem2;
    /**
    * 规格3
    */
    @Schema(description = "规格3")
    private String cMatItem3;
    /**
    * 长度组编码
    */
    @Schema(description = "长度组编码")
    private String cMatLthGroupId;
    /**
    * 长度组名称
    */
    @Schema(description = "长度组名称")
    private String cMatLthGroupName;
    /**
    * 使用区域
    */
    @Schema(description = "使用区域")
    private String cAreaId;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cMemo;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    private LocalDateTime dtModifyDateTime;



}
