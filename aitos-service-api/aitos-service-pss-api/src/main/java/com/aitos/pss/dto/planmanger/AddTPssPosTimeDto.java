package com.aitos.pss.dto.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 工序时间标准-钢种冶炼时间标准
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Data
public class AddTPssPosTimeDto implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "钢种")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;

    @Schema(description = "工序/工位")
    private Long cProcess;

    @Schema(description = "工序/工位编码")
    private String cProcessCode;

    @Schema(description = "工序/工位名称")
    private String cProcessName;

    @Schema(description = "产线")
    private Long cProLine;

    @Schema(description = "产线编码")
    private String cProLineCode;

    @Schema(description = "产线名称")
    private String cProLineName;
    
    @Schema(description = "冶炼时间-分钟")
    private BigDecimal nWorkTime;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
