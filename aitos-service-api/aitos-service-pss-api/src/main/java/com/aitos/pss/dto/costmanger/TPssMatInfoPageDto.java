package com.aitos.pss.dto.costmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssMatInfoPageDto extends PageInput {
    /**
     * 工序号
     */
    @Schema(description = "工序id")
    private Long cProcId;
    /**
     * 产线
     */
    @Schema(description = "产线id")
    private Long cProLine;
    /**
     * 电炉座次id
     */
    @Schema(description = "电炉座次id")
    private Long cBofId;

}
