package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 最终计划钢坯
* <AUTHOR>
* @Date: 2025-07-22
* @Version 1.0
*/
@Data
@TableName("t_pss_final_slab")
@Tag(name = "最终计划钢坯对象", description = "最终计划钢坯")
public class TPssFinalSlab implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 序列号
    */
    @Schema(description = "序列号")
    @TableId
    private Long nId;
    /**
    * 最终钢坯ID
    */
    @Schema(description = "最终钢坯ID")
    @TableField(value = "c_final_slab_id")
    private String cFinalSlabId;
    /**
    * 最终钢坯NAME
    */
    @Schema(description = "最终钢坯NAME")
    @TableField(value = "c_final_slab_name")
    private String cFinalSlabName;
    /**
    * 目标钢坯ID
    */
    @Schema(description = "目标钢坯ID")
    @TableField(value = "n_target_slab_id")
    private BigDecimal nTargetSlabId;
    /**
    * 目标钢坯内前一最终钢坯ID
    */
    @Schema(description = "目标钢坯内前一最终钢坯ID")
    @TableField(value = "n_prev_final_slab_id_in_target")
    private BigDecimal nPrevFinalSlabIdInTarget;
    /**
    * 轧制顺序计划id
    */
    @Schema(description = "轧制顺序计划id")
    @TableField(value = "c_roll_sch_id")
    private String cRollSchId;
    /**
    * 轧制顺序计划内前一最终钢坯id
    */
    @Schema(description = "轧制顺序计划内前一最终钢坯id")
    @TableField(value = "n_prev_final_slab_id_in_sch")
    private BigDecimal nPrevFinalSlabIdInSch;
    /**
    * 代表钢坯质量编码
    */
    @Schema(description = "代表钢坯质量编码")
    @TableField(value = "c_slab_mat_qul_id")
    private Long cSlabMatQulId;
    /**
    * 代表钢坯质量编码
    */
    @Schema(description = "代表钢坯质量编码cd")
    @TableField(value = "c_slab_mat_qul_cd")
    private String cSlabMatQulCd;
    /**
    * 代表钢坯质量编码
    */
    @Schema(description = "代表钢坯质量编码name")
    @TableField(value = "c_slab_mat_qul_name")
    private String cSlabMatQulName;
    /**
    * 钢坯厚
    */
    @Schema(description = "钢坯厚")
    @TableField(value = "n_thk")
    private BigDecimal nThk;
    /**
    * 钢坯宽
    */
    @Schema(description = "钢坯宽")
    @TableField(value = "n_wth")
    private BigDecimal nWth;
    /**
    * 钢坯长
    */
    @Schema(description = "钢坯长")
    @TableField(value = "n_lth")
    private BigDecimal nLth;
    /**
    * 钢坯计算重量
    */
    @Schema(description = "钢坯计算重量")
    @TableField(value = "n_cal_wgt")
    private BigDecimal nCalWgt;
    /**
    * 代表钢种
    */
    @Schema(description = "钢种")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    /**
    * 代表钢种
    */
    @Schema(description = "钢种描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    /**
    * 设计成材率
    */
    @Schema(description = "设计成材率")
    @TableField(value = "n_deg_ratio")
    private BigDecimal nDegRatio;
    /**
    * 产品长度 （方钢/开坯）
    */
    @Schema(description = "产品长度 （方钢/开坯）")
    @TableField(value = "n_asroll_lth")
    private BigDecimal nAsrollLth;
    /**
    * 产品厚度（方钢/开坯）
    */
    @Schema(description = "产品厚度（方钢/开坯）")
    @TableField(value = "n_asroll_thk")
    private BigDecimal nAsrollThk;
    /**
    * 产品宽度（方钢/开坯）
    */
    @Schema(description = "产品宽度（方钢/开坯）")
    @TableField(value = "n_asroll_wth")
    private BigDecimal nAsrollWth;
    /**
    * 交货状态
    */
    @Schema(description = "交货状态")
    @TableField(value = "c_fpost_state")
    private String cFpostState;
    /**
    * 探伤等级
    */
    @Schema(description = "探伤等级")
    @TableField(value = "c_ust_lev")
    private String cUstLev;
    /**
    * 探伤标准
    */
    @Schema(description = "探伤标准")
    @TableField(value = "c_ust_std")
    private String cUstStd;
    /**
    * 切头长
    */
    @Schema(description = "切头长")
    @TableField(value = "n_cut_head")
    private BigDecimal nCutHead;
    /**
    * 切尾长
    */
    @Schema(description = "切尾长")
    @TableField(value = "n_cut_tail")
    private BigDecimal nCutTail;
    /**
    * 长度切损
    */
    @Schema(description = "长度切损")
    @TableField(value = "n_cut_lth_lose")
    private BigDecimal nCutLthLose;
    /**
    * 长度余量
    */
    @Schema(description = "长度余量")
    @TableField(value = "n_cut_lth_rem")
    private BigDecimal nCutLthRem;
    /**
    * 宽度切损
    */
    @Schema(description = "宽度切损")
    @TableField(value = "n_cut_trim_lose")
    private BigDecimal nCutTrimLose;
    /**
    * 宽度余量
    */
    @Schema(description = "宽度余量")
    @TableField(value = "n_cut_wth_rem")
    private BigDecimal nCutWthRem;
    /**
    * 试样长度
    */
    @Schema(description = "试样长度")
    @TableField(value = "n_smp_lth")
    private BigDecimal nSmpLth;
    /**
    * 来源
    */
    @Schema(description = "来源")
    @TableField(value = "c_source")
    private String cSource;
    /**
    * 状态
    */
    @Schema(description = "状态")
    @TableField(value = "c_status")
    private String cStatus;
    /**
    * 目标钢坯内顺序
    */
    @Schema(description = "目标钢坯内顺序")
    @TableField(value = "n_seq_in_target")
    private BigDecimal nSeqInTarget;
    /**
    * 轧制顺序计划内顺序
    */
    @Schema(description = "轧制顺序计划内顺序")
    @TableField(value = "n_seq_in_sch")
    private BigDecimal nSeqInSch;
    /**
    * 代表执行标准
    */
    @Schema(description = "代表执行标准")
    @TableField(value = "c_std_spec")
    private String cStdSpec;
    /**
    * 下发作业时间
    */
    @Schema(description = "下发作业时间")
    @TableField(value = "dt_released_moment")
    private LocalDateTime dtReleasedMoment;
    /**
    * 程序跟踪用
    */
    @Schema(description = "程序跟踪用")
    @TableField(value = "n_target_bak")
    private BigDecimal nTargetBak;
    /**
    * 提料上传时间
    */
    @Schema(description = "提料上传时间")
    @TableField(value = "dt_apply_moment_bak")
    private LocalDateTime dtApplyMomentBak;
    /**
    * 创建日期
    */
    @Schema(description = "创建日期")
    @TableField(value = "dt_ins_time")
    private LocalDateTime dtInsTime;
    /**
    * 备注
    */
    @Schema(description = "备注")
    @TableField(value = "c_memo_bak")
    private String cMemoBak;
    /**
    * 消息状态
    */
    @Schema(description = "消息状态")
    @TableField(value = "c_msg_status")
    private String cMsgStatus;
    /**
    * 错误描述
    */
    @Schema(description = "错误描述")
    @TableField(value = "c_error_text")
    private String cErrorText;
    /**
    * 消息类别
    */
    @Schema(description = "消息类别")
    @TableField(value = "c_msg_type")
    private String cMsgType;
    /**
    * 上传者
    */
    @Schema(description = "上传者")
    @TableField(value = "c_msg_emp")
    private String cMsgEmp;
    /**
    * 消息号
    */
    @Schema(description = "消息号")
    @TableField(value = "c_msg_id")
    private BigDecimal cMsgId;
    /**
    * 是否上传
    */
    @Schema(description = "是否上传")
    @TableField(value = "c_applied_fl")
    private String cAppliedFl;
    /**
    * 热送标记
    */
    @Schema(description = "热送标记")
    @TableField(value = "c_hcr_fl")
    private String cHcrFl;
    /**
    * 物料类型
    */
    @Schema(description = "物料类型")
    @TableField(value = "c_mat_type")
    private String cMatType;
    /**
    * 提料单号
    */
    @Schema(description = "提料单号")
    @TableField(value = "n_apply_list_id")
    private BigDecimal nApplyListId;
    /**
    * 调度令号
    */
    @Schema(description = "调度令号")
    @TableField(value = "c_dispatch_id")
    private String cDispatchId;
    /**
    * 订单号
    */
    @Schema(description = "订单号")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    /**
    * 直径
    */
    @Schema(description = "直径")
    @TableField(value = "n_line_spec")
    private BigDecimal nLineSpec;
    /**
    * 前状态
    */
    @Schema(description = "前状态")
    @TableField(value = "c_pre_status")
    private String cPreStatus;
    /**
    * 挂单时间
    */
    @Schema(description = "挂单时间")
    @TableField(value = "dt_attach_time")
    private LocalDateTime dtAttachTime;
    /**
    * 挂单人
    */
    @Schema(description = "挂单人")
    @TableField(value = "c_attach_emp")
    private String cAttachEmp;
    /**
    * 产品类型P0001
    */
    @Schema(description = "产品类型P0001")
    @TableField(value = "c_prod_type")
    private String cProdType;
    /**
    * 精整路径
    */
    @Schema(description = "精整路径")
    @TableField(value = "c_plan_finishing_path")
    private String cPlanFinishingPath;
    /**
    * 产线
    */
    @Schema(description = "产线")
    @TableField(value = "c_production_line")
    private Long cProductionLine;

    @Schema(description = "产线code")
    @TableField(value = "c_production_line_code")
    private String cProductionLineCode;

    @Schema(description = "产线name")
    @TableField(value = "c_production_line_name")
    private String cProductionLineName;
    /**
    * 定尺代码
    */
    @Schema(description = "定尺代码")
    @TableField(value = "c_size_property")
    private String cSizeProperty;
    /**
    * 存货编码
    */
    @Schema(description = "存货编码")
    @TableField(value = "c_prod_mat_id")
    private Long cProdMatId;

    @Schema(description = "存货编码")
    @TableField(value = "c_prod_mat_code")
    private String cProdMatCode;
    /**
    * 存货名称
    */
    @Schema(description = "存货名称")
    @TableField(value = "c_prod_mat_name")
    private String cProdMatName;
    /**
    * 调度单合并用原调度单号
    */
    @Schema(description = "调度单合并用原调度单号")
    @TableField(value = "c_org_dipatch_id")
    private String cOrgDipatchId;
    /**
    * 产品宽
    */
    @Schema(description = "产品宽")
    @TableField(value = "n_prod_wid")
    private BigDecimal nProdWid;
    /**
    * 开坯产品规格
    */
    @Schema(description = "开坯产品规格")
    @TableField(value = "c_slab_spec")
    private String cSlabSpec;
    /**
    * 综合生产计划主键
    */
    @Schema(description = "综合生产计划主键")
    @TableField(value = "n_ag_plan_seq")
    private BigDecimal nAgPlanSeq;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "n_enabled_mark")
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    private Integer nDeleteMark;


}