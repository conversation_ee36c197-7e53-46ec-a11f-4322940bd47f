package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;



/**
* @title: 炼钢工艺参数管理-炼钢工艺路径
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Data
public class UpdateTPssTechkRoutLgDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    private Long nId;

    @Schema(description = "质量编码id")
    private Long cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;

    @Schema(description = "质量编码名称")
    private String cQualityCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;

    @Schema(description = "产线id")
    private Long cProLine;

    @Schema(description = "产线code")
    private String cProLineCode;

    @Schema(description = "产线name")
    private String cProLineName;

    @Schema(description = "工序1")
    private Long cTechRoute1;

    @Schema(description = "工序1code")
    private String cTechRoute1Code;

    @Schema(description = "工序1name")
    private String cTechRoute1Name;

    @Schema(description = "工序2")
    private Long cTechRoute2;

    @Schema(description = "工序2code")
    private String cTechRoute2Code;

    @Schema(description = "工序2name")
    private String cTechRoute2Name;

    @Schema(description = "工序3")
    private Long cTechRoute3;

    @Schema(description = "工序3code")
    private String cTechRoute3Code;

    @Schema(description = "工序3name")
    private String cTechRoute3Name;

    @Schema(description = "工序4")
    private Long cTechRoute4;

    @Schema(description = "工序4code")
    private String cTechRoute4Code;

    @Schema(description = "工序4name")
    private String cTechRoute4Name;

    @Schema(description = "工序5")
    private Long cTechRoute5;

    @Schema(description = "工序5code")
    private String cTechRoute5Code;

    @Schema(description = "工序5name")
    private String cTechRoute5Name;

    @Schema(description = "工序6")
    private Long cTechRoute6;

    @Schema(description = "工序6code")
    private String cTechRoute6Code;

    @Schema(description = "工序6name")
    private String cTechRoute6Name;

    @Schema(description = "工序7")
    private Long cTechRoute7;

    @Schema(description = "工序7code")
    private String cTechRoute7Code;

    @Schema(description = "工序7name")
    private String cTechRoute7Name;

    @Schema(description = "工序8")
    private Long cTechRoute8;

    @Schema(description = "工序8code")
    private String cTechRoute8Code;

    @Schema(description = "工序8name")
    private String cTechRoute8Name;
    
    @Schema(description = "是否堆垛缓冷")
    private String cIsCold;
    
    @Schema(description = "工艺路径1")
    private String techRoute0;
    
    @Schema(description = "备注")
    private String cMemo;
    
    @Schema(description = "公司文件编号")
    private String cDocNum1;
    
    @Schema(description = "厂文件编号")
    private String cDocNum2;
    
    @Schema(description = "工艺路径2")
    private String cTechRoute;
    
    @Schema(description = "精整路径汇总")
    private String cFineRoute;
    
    @Schema(description = "备用1")
    private String cBackup1;
    
    @Schema(description = "备用2")
    private String cBackup2;
    
    @Schema(description = "备用3")
    private String cBackup3;
    
    @Schema(description = "备用4")
    private String cBackup4;
    
    @Schema(description = "备用5")
    private String cBackup5;
    
    @Schema(description = "备用6")
    private String cBackup6;
    
    @Schema(description = "备用7")
    private String cBackup7;
    
    @Schema(description = "备用8")
    private String cBackup8;
    
    @Schema(description = "备用9")
    private String cBackup9;
    
    @Schema(description = "备用10")
    private String cBackup10;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
}
