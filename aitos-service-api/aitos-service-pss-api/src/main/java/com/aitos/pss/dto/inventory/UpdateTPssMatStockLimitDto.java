package com.aitos.pss.dto.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 库存量上下限维护
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Data
public class UpdateTPssMatStockLimitDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    */
    @Schema(description = "主键ID")
    private Long nId;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMaterialCode;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMaterialName;
    /**
    * 物料类型(raw:原辅料,alloy:合金,scrap:废钢)
    */
    @Schema(description = "物料类型(raw:原辅料,alloy:合金,scrap:废钢)")
    private String cMaterialType;
    /**
    * 当前库存
    */
    @Schema(description = "当前库存")
    private BigDecimal nCurrentStock;
    /**
    * 库存下限
    */
    @Schema(description = "库存下限")
    private BigDecimal nMinStock;
    /**
    * 库存上限
    */
    @Schema(description = "库存上限")
    private BigDecimal nMaxStock;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private String cUnit;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;
}
