package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssCastpEditPageDto extends PageInput {

    /**
    * 序号
    */
    @Schema(description = "序号")
    private Long nId;
    /**
    * 计划浇次号
    */
    @Schema(description = "计划浇次号")
    private String cPlanCastId;
    /**
    * 浇铸宽度
    */
    @Schema(description = "浇铸宽度")
    private BigDecimal nSlabWth;
    /**
    * 计划结束时间字段开始时间
    */
    @Schema(description = "计划结束时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtEndDtStart;
    /**
    * 计划结束时间字段结束时间
    */
    @Schema(description = "计划结束时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtEndDtEnd;
    /**
    * 炉数
    */
    @Schema(description = "炉数")
    private Long nHeatCnt;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    private Long nModifyUserId;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    private Integer nDeleteMark;
    /**
    * 浇次编制号
    */
    @Schema(description = "浇次编制号")
    private String nCastEdtSeq;
    /**
    * 浇铸厚度
    */
    @Schema(description = "浇铸厚度")
    private BigDecimal nSlabThk;
    /**
    * 计划开始时间字段开始时间
    */
    @Schema(description = "计划开始时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtStartDtStart;
    /**
    * 计划开始时间字段结束时间
    */
    @Schema(description = "计划开始时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtStartDtEnd;
    /**
    * 铸机号
    */
    @Schema(description = "铸机号")
    private String cCastMachId;
    /**
    * 大浇次序号
    */
    @Schema(description = "大浇次序号")
    private BigDecimal nBigCastSeq;
    /**
    * 
    */
    @Schema(description = "")
    private Integer nEnabledMark;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 修改时间字段开始时间
    */
    @Schema(description = "修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 修改时间字段结束时间
    */
    @Schema(description = "修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
