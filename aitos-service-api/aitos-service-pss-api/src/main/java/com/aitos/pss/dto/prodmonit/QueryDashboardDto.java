package com.aitos.pss.dto.prodmonit;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 仪表盘查询对象
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/29 09:57
 */
@Data
public class QueryDashboardDto {

    @Schema(description = "设备id")
    private Long nEquipmentId;

    @Schema(description = "指标id")
    private Long nIndicatorId;

    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;

    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
}
