package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 轧钢综判管理
* <AUTHOR>
* @Date: 2025-06-05
* @Version 1.0
*/
@Data
public class AddTPssPJudgeDto implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "件次号")
    private String cPlateId;
    
    @Schema(description = "表面等级")
    private String cFaceResult;
    
    @Schema(description = "外形等级")
    private String cBodyResult;
    
    @Schema(description = "尺寸等级")
    private String cSizeResult;
    
    @Schema(description = "初判等级")
    private String cJunCheckLvl;
    
    @Schema(description = "初判时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtJunCheckTime;
    
    @Schema(description = "初判人员")
    private String cJunCheckOperator;
    
    @Schema(description = "初判更正等级")
    private String cModJunCheckLvl;
    
    @Schema(description = "性能等级")
    private String cMtalGrd;
    
    @Schema(description = "探伤等级")
    private String cUstGrd;
    
    @Schema(description = "综判等级")
    private String cProdGrd;
    
    @Schema(description = "综判人员")
    private String cEndJudgeId;
    
    @Schema(description = "综判不合原因")
    private String cProdGrdReason;
    
    @Schema(description = "综判时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtProdGrdTime;
    
    @Schema(description = "综判修改人员")
    private String cEndJudgeModId;
    
    @Schema(description = "综判修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtEndJudgeModTime;
    
    @Schema(description = "备注")
    private String cMemo;
    
    @Schema(description = "是否性能挽救")
    private String cRescueFl;
    
    @Schema(description = "原表面等级")
    private String cOldFaceResult;
    
    @Schema(description = "原外形等级")
    private String cOldBodyResult;
    
    @Schema(description = "原尺寸等级")
    private String cOldSizeResult;
    
    @Schema(description = "原长度")
    private BigDecimal nOldLth;
    
    @Schema(description = "原宽度")
    private BigDecimal nOldWth;
    
    @Schema(description = "原厚度")
    private BigDecimal nOldThk;
    
    @Schema(description = "废品标记")
    private String cWPro;
    
    @Schema(description = "原综判等级")
    private String cOrgProdGrd;
    
    @Schema(description = "原性能等级")
    private String cOrgMtalGrd;
    
    @Schema(description = "改判等级")
    private String cProdGrdCha;

}
