package com.aitos.pss.vo.costmanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-26
* @Version 1.0
*/
@Data
public class TPssFcsMatCodePageVo {

    /**
    * 主键id
    */
    @Schema(description = "主键id")
    private String nId;
    /**
    * 物料id
    */
    @Schema(description = "物料id")
    private Long cMatId;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMatCode;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatName;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private String cMatUnit;
    /**
    * 是否使用
    */
    @Schema(description = "是否使用")
    private Integer cAplyStd;
    /**
    * 物料科目编码
    */
    @Schema(description = "物料科目编码")
    private String cMatClassCode;
    /**
    * 物料科目名称
    */
    @Schema(description = "物料科目名称")
    private String cMatClassName;

    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;

    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;

}
