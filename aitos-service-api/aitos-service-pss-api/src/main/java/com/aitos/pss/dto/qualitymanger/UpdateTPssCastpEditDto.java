package com.aitos.pss.dto.qualitymanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 浇次设计表
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
public class UpdateTPssCastpEditDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 序号
    */
    @Schema(description = "序号")
    private Long nId;
    /**
    * 浇次编制号
    */
    @Schema(description = "浇次编制号")
    private String nCastEdtSeq;
    /**
    * 计划浇次号
    */
    @Schema(description = "计划浇次号")
    private String cPlanCastId;
    /**
    * 浇铸厚度
    */
    @Schema(description = "浇铸厚度")
    private BigDecimal nSlabThk;
    /**
    * 浇铸宽度
    */
    @Schema(description = "浇铸宽度")
    private BigDecimal nSlabWth;
    /**
    * 计划开始时间
    */
    @Schema(description = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtStartDt;
    /**
    * 计划结束时间
    */
    @Schema(description = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtEndDt;
    /**
    * 铸机号
    */
    @Schema(description = "铸机号")
    private String cCastMachId;
    /**
    * 炉数
    */
    @Schema(description = "炉数")
    private Long nHeatCnt;
    /**
    * 大浇次序号
    */
    @Schema(description = "大浇次序号")
    private BigDecimal nBigCastSeq;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
