package com.aitos.pss.dto.prodmonit;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssIndicatorsPageDto extends PageInput {

    @Schema(description = "指标名称")
    private String cName;

    @Schema(description = "设备id")
    private Long nEquipmentId;

    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;

    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
}
