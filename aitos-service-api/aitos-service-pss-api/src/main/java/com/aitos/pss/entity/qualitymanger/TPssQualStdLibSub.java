package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 质检标准库子表
* <AUTHOR>
* @Date: 2025-07-09
* @Version 1.0
*/
@Data
@TableName("t_pss_qual_std_lib_sub")
@Tag(name = "质检标准库子表对象", description = "质检标准库子表")
public class TPssQualStdLibSub implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键
    */
    @Schema(description = "主键")
    @TableId
    private Long nId;
    /**
    * 质检标准编码
    */
    @Schema(description = "质检标准编码")
    @TableField(value = "c_Stdcode")
    private String cStdcode;

    @Schema(description = "质检标准名称")
    @TableField(value = "c_stdname")
    private String cStdname;
    /**
    * 质检项目编码
    */
    @Schema(description = "质检项目id")
    @TableField(value = "c_quality_standard_lib_item_id")
    private Long cQualityStandardLibItemId;

    /**
    * 质检项目编码
    */
    @Schema(description = "质检项目编码")
    @TableField(value = "c_quality_standard_lib_item_code")
    private String cQualityStandardLibItemCode;
    /**
    * 质检项目名称
    */
    @Schema(description = "质检项目名称")
    @TableField(value = "c_quality_standard_lib_item_name")
    private String cQualityStandardLibItemName;
    /**
    * 计量单位
    */
    @Schema(description = "计量单位id")
    @TableField(value = "c_measures_unit")
    private Long cMeasuresUnit;

    @Schema(description = "计量单位code")
    @TableField(value = "c_measures_unit_code")
    private String cMeasuresUnitCode;

    @Schema(description = "计量单位name")
    @TableField(value = "c_measures_unit_name")
    private String cMeasuresUnitName;
    /**
    * 值类型
    */
    @Schema(description = "值类型")
    @TableField(value = "c_value_type")
    private String cValueType;
    /**
    * 试验次数
    */
    @Schema(description = "试验次数")
    @TableField(value = "n_testcount")
    private Integer nTestcount;
    /**
    * 项目属性
    */
    @Schema(description = "项目属性")
    @TableField(value = "c_project_attribute_code")
    private String cProjectAttributeCode;

    @Schema(description = "质检标准类别编码")
    private String cStdtypeCode;
    /**
    * 数据精度
    */
    @Schema(description = "数据精度")
    @TableField(value = "n_precision")
    private String nPrecision;
    /**
    * 下限比较符
    */
    @Schema(description = "下限比较符")
    @TableField(value = "c_lowersymbol")
    private String cLowersymbol;
    /**
    * 下限值
    */
    @Schema(description = "下限值")
    @TableField(value = "n_lowervalue")
    private BigDecimal nLowervalue;
    /**
    * 上限比较符
    */
    @Schema(description = "上限比较符")
    @TableField(value = "c_uppersymbol")
    private String cUppersymbol;
    /**
    * 上限值
    */
    @Schema(description = "上限值")
    @TableField(value = "n_uppervalue")
    private BigDecimal nUppervalue;
    /**
    * 备注
    */
    @Schema(description = "备注")
    @TableField(value = "n_remark")
    private String nRemark;
    /**
    * 
    */
    @Schema(description = "")
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}