package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
public class TPssSlabHeatStdInfoPageVo {

    
    @Schema(description = "主键")
    private String nId;
    
    @Schema(description = "订单号")
    private String cOrderNo;

    @Schema(description = "质量编码")
    private Long cQualId;

    @Schema(description = "质量编码")
    private String cQualCode;

    @Schema(description = "质量编码名称")
    private String cQualCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;
    
    @Schema(description = "坯料厚度")
    private BigDecimal nSlabThkMin;
    
    @Schema(description = "坯料厚度")
    private BigDecimal nSlabThkMax;
    
    @Schema(description = "出炉目标温度℃")
    private BigDecimal nFoutAimTemp;
    
    @Schema(description = "出炉温度上限℃")
    private BigDecimal nFoutMaxTemp;
    
    @Schema(description = "出炉温度下限℃")
    private BigDecimal nFoutMinTemp;
    
    @Schema(description = "板坯表面/中心目标温度差℃")
    private BigDecimal nSlabScTempDiff;
    
    @Schema(description = "板坯头尾目标温度差℃")
    private BigDecimal nSlabHtTempDiff;
    
    @Schema(description = "加热一段最低温度")
    private String cReserver1;
    
    @Schema(description = "加热一段最高温度")
    private String cReserver2;
    
    @Schema(description = "加热二段最低温度")
    private String cReserver3;
    
    @Schema(description = "加热二段最高温度")
    private String cReserver4;
    
    @Schema(description = "加热段3最低温度")
    private String cReserver5;
    
    @Schema(description = "加热段3最高温度")
    private String cReserver6;
    
    @Schema(description = "加热段4最低温度")
    private String cReserver7;
    
    @Schema(description = "加热段4最高温度")
    private String cReserver8;
    
    @Schema(description = "目标加热时长")
    private String cReserver9;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("创建时间")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("修改时间")
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
