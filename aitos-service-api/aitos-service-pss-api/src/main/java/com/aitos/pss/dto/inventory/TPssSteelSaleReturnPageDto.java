package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssSteelSaleReturnPageDto extends PageInput {

    /**
    * 库号
    */
    @Schema(description = "库号")
    private String stockNo;
    /**
    * 材料号/卷号
    */
    @Schema(description = "材料号/卷号")
    private String materialNo;
    /**
    * 规格
    */
    @Schema(description = "规格")
    private String spec;
    /**
    * 退货数量(件/卷)
    */
    @Schema(description = "退货数量(件/卷)")
    private Integer returnQty;
    /**
    * 退货申请时间字段开始时间
    */
    @Schema(description = "退货申请时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime returnTimeStart;
    /**
    * 退货申请时间字段结束时间
    */
    @Schema(description = "退货申请时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime returnTimeEnd;
    /**
    * 退货原因
    */
    @Schema(description = "退货原因")
    private String returnReason;
    /**
    * 状态(待退库/已退库/已驳回等)
    */
    @Schema(description = "状态(待退库/已退库/已驳回等)")
    private String status;
    /**
    * 是否启用;默认为0,1为未启用
    */
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    /**
    * 提货单号
    */
    @Schema(description = "提货单号")
    private String deliveryNo;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    private String batchNo;
    /**
    * 钢种名称
    */
    @Schema(description = "钢种名称")
    private String steelGrade;
    /**
    * 单件重量(kg)
    */
    @Schema(description = "单件重量(kg)")
    private BigDecimal weight;
    /**
    * 退货总重量(kg)
    */
    @Schema(description = "退货总重量(kg)")
    private BigDecimal returnWeight;
    /**
    * 退货申请人
    */
    @Schema(description = "退货申请人")
    private String returnUser;
    /**
    * 原出库单号
    */
    @Schema(description = "原出库单号")
    private String outNo;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String remark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 字段开始时间
    */
    @Schema(description = "字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 字段结束时间
    */
    @Schema(description = "字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    private Long nModifyUserId;
    /**
    * 字段开始时间
    */
    @Schema(description = "字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 字段结束时间
    */
    @Schema(description = "字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
