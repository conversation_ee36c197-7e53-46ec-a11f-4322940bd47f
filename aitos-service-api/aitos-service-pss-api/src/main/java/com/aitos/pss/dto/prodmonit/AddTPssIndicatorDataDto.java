package com.aitos.pss.dto.prodmonit;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 指标数据
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
public class AddTPssIndicatorDataDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 指标ID
    */
    @Schema(description = "指标ID")
    private Long nIndicatorId;
    /**
    * 指标code
    */
    @Schema(description = "指标code")
    private String cIndicatorCode;
    /**
    * 指标name
    */
    @Schema(description = "指标name")
    private String cIndicatorName;
    /**
    * 设备id
    */
    @Schema(description = "设备id")
    private Long nEquipmentId;
    /**
    * 设备code
    */
    @Schema(description = "设备code")
    private String cEquipmentCode;
    /**
    * 设备name
    */
    @Schema(description = "设备name")
    private String cEquipmentName;
    /**
    * 指标值
    */
    @Schema(description = "指标值")
    private BigDecimal nIndicatorValue;
    /**
    * 状态（正常/预警/违规）
    */
    @Schema(description = "状态（正常/预警/违规）")
    private String cStatus;
    /**
    * 时间戳
    */
    @Schema(description = "时间戳")
    private Long dtTimestamp;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
