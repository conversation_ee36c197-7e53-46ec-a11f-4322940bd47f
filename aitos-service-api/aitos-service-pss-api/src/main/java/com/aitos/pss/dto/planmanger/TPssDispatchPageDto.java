package com.aitos.pss.dto.planmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-05-28
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssDispatchPageDto extends PageInput {

    
    @Schema(description = "产线")
    private Long cLineId;
    
    @Schema(description = "规格")
    private BigDecimal nSpec;
    
    @Schema(description = "总支数")
    private BigDecimal nTalCnt;
    
    @Schema(description = "调度单中没有轧制计划后设置1")
    private String cEmptyFl;
    
    @Schema(description = "剩余支数")
    private BigDecimal nLeftCnt;
    
    @Schema(description = "综合生产计划号")
    private String cProductPlanNo;
    
    @Schema(description = "产品类型")
    private String cProdType;
    
    @Schema(description = "下达时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReleaseTimeStart;
    
    @Schema(description = "下达时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReleaseTimeEnd;
    
    @Schema(description = "存货名称")
    private String cMatname;
    
    @Schema(description = "产品材质")
    private String cMatQulCd;
    
    @Schema(description = "1调度下达，0未下达,2 转去装炉,3已经生产,8生产结束,9撤销(P0019)")
    private String cStatus;
    
    @Schema(description = "计划下达时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSendTimeStart;
    
    @Schema(description = "计划下达时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSendTimeEnd;
    
    @Schema(description = "执行标准名称")
    private String cStdSpecName;
    
    @Schema(description = "原始调度单号")
    private String cOrgDispatchid;
    
    @Schema(description = "综合生产计划号(任务单调整用)")
    private String cDisProductPlanNo;
    
    @Schema(description = "交货状态")
    private String cFpoststateid;
    
    @Schema(description = "调度下达人（下达到物料匹配）")
    private String cDissendEmp;
    
    @Schema(description = "是否启用订单生产")
    private Integer cPreStatus;
    
    @Schema(description = "销售操作日志")
    private String cMergememo;
    
    @Schema(description = "调整顺序时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtUpdatetimeStart;
    
    @Schema(description = "调整顺序时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtUpdatetimeEnd;
    
    @Schema(description = "精整内容")
    private String cClearcont;
    
    @Schema(description = "精整内容编制人")
    private String cClearedtemp;
    
    @Schema(description = "结束人")
    private String cFinemp;
    
    @Schema(description = "客户名称")
    private String cCustomerCd;
    
    @Schema(description = "客户重要度")
    private String cCusLevel;
    
    @Schema(description = "旧编码")
    private String cOldcode;
    
    @Schema(description = "产品宽度")
    private BigDecimal nProdWth;
    
    @Schema(description = "规格代码")
    private String cItemCd;
    
    @Schema(description = "厚度公差最大值")
    private BigDecimal nThkBiasMax;
    
    @Schema(description = "宽度公差最大值")
    private BigDecimal nWthBiasMax;
    
    @Schema(description = "成品重量最大值")
    private BigDecimal nProdWgtMax;
    
    @Schema(description = "长度最小")
    private BigDecimal nProdLenMin;
    
    @Schema(description = "计划单支重")
    private BigDecimal nPlanSingleWgt;
    
    @Schema(description = "坯料规格")
    private String cSlabItem;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    private Long nCreateUserId;
    
    @Schema(description = "修改人")
    private Long nModifyUserId;
    
    @Schema(description = "调度单号")
    private String cDispatchId;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "总重量")
    private BigDecimal nTalWgt;
    
    @Schema(description = "下达人")
    private String cReleaseEmp;
    
    @Schema(description = "交货日期")
    private String cPostDatetime;
    
    @Schema(description = "计划精整工艺路径（精整工序编号+分隔符-逗号+精整工序编号）")
    private String cPlanFinishingPath;
    
    @Schema(description = "销售计划号")
    private String cSalePlanNo;
    
    @Schema(description = "定尺类型")
    private String cSizeProperty;
    
    @Schema(description = "存货编码")
    private String cMatcode;
    
    @Schema(description = "产品长度")
    private BigDecimal nProdLen;
    
    @Schema(description = "顺序")
    private BigDecimal nDispatchseq;
    
    @Schema(description = "计划下达人")
    private String cSendEmp;
    
    @Schema(description = "执行标准")
    private String cStdSpec;
    
    @Schema(description = "综合生产计划序号")
    private BigDecimal nProductPlanSn;
    
    @Schema(description = "0原始调度单，1拆分后调度单，2合并后调度单，3合并后消失调度单")
    private String cDisSource;
    
    @Schema(description = "综合销售计划序号")
    private BigDecimal nSalePlanSn;
    
    @Schema(description = "备注")
    private String cMemo;
    
    @Schema(description = "调度下达时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtDissendTimeStart;
    
    @Schema(description = "调度下达时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtDissendTimeEnd;
    
    @Schema(description = "是否为临时计划（1：临时计划）")
    private String cIsTempplan;
    
    @Schema(description = "调度备注")
    private String cDispatchmemo;
    
    @Schema(description = "调整顺序人")
    private String cUpdatetime;
    
    @Schema(description = "精整交货时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtChearfisTimeStart;
    
    @Schema(description = "精整交货时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtChearfisTimeEnd;
    
    @Schema(description = "精整内容编制时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtClearedtTimeStart;
    
    @Schema(description = "精整内容编制时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtClearedtTimeEnd;
    
    @Schema(description = "是否紧急订单（0：正常订单，1：紧急订单）")
    private String cExgProdLotFl;
    
    @Schema(description = "结束时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFinTimeStart;
    
    @Schema(description = "结束时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtFinTimeEnd;
    
    @Schema(description = "是否下发二级")
    private String cSFl;
    
    @Schema(description = "钢坯库存对应重量")
    private String cKcgp;
    
    @Schema(description = "订单号")
    private String cOrderNo;
    
    @Schema(description = "成品规格")
    private String cMatItem;
    
    @Schema(description = "厚度公差最小值")
    private BigDecimal nThkBiasMin;
    
    @Schema(description = "宽度公差最小值")
    private BigDecimal nWthBiasMin;
    
    @Schema(description = "成品重量最小值")
    private BigDecimal nProdWgtMin;
    
    @Schema(description = "长度最大")
    private BigDecimal nProdLenMax;
    
    @Schema(description = "挂单重量")
    private BigDecimal nDofinalWgt;
    
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    
    @Schema(description = "修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    
    @Schema(description = "修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
