package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssSteelReturnRecordPageDto extends PageInput {

    /**
    * 库号
    */
    @Schema(description = "库号")
    private String cStockNo;
    /**
    * 材料号/卷号
    */
    @Schema(description = "材料号/卷号")
    private String cMaterialNo;
    /**
    * 规格
    */
    @Schema(description = "规格")
    private String cSpec;
    /**
    * 退货数量(件/卷)
    */
    @Schema(description = "退货数量(件/卷)")
    private Integer nReturnQty;
    /**
    * 退货入库时间字段开始时间
    */
    @Schema(description = "退货入库时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReturnTimeStart;
    /**
    * 退货入库时间字段结束时间
    */
    @Schema(description = "退货入库时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtReturnTimeEnd;
    /**
    * 退货原因
    */
    @Schema(description = "退货原因")
    private String cReturnReason;
    /**
    * 状态(已退库/已冲销等)
    */
    @Schema(description = "状态(已退库/已冲销等)")
    private String cStatus;
    /**
    * 是否启用;默认为0,1为未启用
    */
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    /**
    * 提货单号
    */
    @Schema(description = "提货单号")
    private String cDeliveryNo;
    /**
    * 批次号
    */
    @Schema(description = "批次号")
    private String cBatchNo;
    /**
    * 钢种名称
    */
    @Schema(description = "钢种名称")
    private String cSteelGrade;
    /**
    * 单件重量(kg)
    */
    @Schema(description = "单件重量(kg)")
    private BigDecimal nWeight;
    /**
    * 退货总重量(kg)
    */
    @Schema(description = "退货总重量(kg)")
    private BigDecimal nReturnWeight;
    /**
    * 退货操作人
    */
    @Schema(description = "退货操作人")
    private String cReturnUser;
    /**
    * 原出库单号
    */
    @Schema(description = "原出库单号")
    private String cOutNo;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 字段开始时间
    */
    @Schema(description = "字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 字段结束时间
    */
    @Schema(description = "字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    private Long nModifyUserId;
    /**
    * 字段开始时间
    */
    @Schema(description = "字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 字段结束时间
    */
    @Schema(description = "字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
