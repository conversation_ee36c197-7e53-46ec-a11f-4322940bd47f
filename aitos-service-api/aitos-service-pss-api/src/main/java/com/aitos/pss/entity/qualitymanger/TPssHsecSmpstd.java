package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 组批规则维护
* <AUTHOR>
* @Date: 2025-06-17
* @Version 1.0
*/
@Data
@TableName("t_pss_hsec_smpstd")
@Tag(name = "组批规则维护对象", description = "组批规则维护")
public class TPssHsecSmpstd implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "")
    @TableId
    private Long nId;
    
    @Schema(description = "质量编码")
    @TableField(value = "c_quality_code")
    private String cQualityCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_quality_code_name")
    private String cQualityCodeName;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "取样类型")
    @TableField(value = "c_smp_type")
    private String cSmpType;
    
    @Schema(description = "产线")
    @TableField(value = "c_line_cd")
    private String cLineCd;
    
    @Schema(description = "产品长度")
    @TableField(value = "n_thk")
    private BigDecimal nThk;
    
    @Schema(description = "产品规格")
    @TableField(value = "c_spec")
    private String cSpec;
    
    @Schema(description = "取样次数")
    @TableField(value = "n_smp_num")
    private Integer nSmpNum;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}