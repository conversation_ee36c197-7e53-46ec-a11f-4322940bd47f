package com.aitos.pss.dto.planmanger;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
* @title: 炉次浇次设计管理
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
public class UpdateTPssTaskListDto implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "顺序号")
    private Long nId;
    
    @Schema(description = "生产任务单号")
    private String cTaskListId;
    
    @Schema(description = "产品类别")
    private String cProdType;
    
    @Schema(description = "编制部门")
    private String cCrtDept;
    
    @Schema(description = "编制时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCrtTime;
    
    @Schema(description = "编制人")
    private String cCrtEmp;
    
    @Schema(description = "生产部门")
    private String cPrcDept;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;

    @Schema(description = "质量编码")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    private String cMatQulCd;

    @Schema(description = "质量编码名称")
    private String cMatQulName;
    
    @Schema(description = "板坯厚度")
    private BigDecimal nSlabThk;
    
    @Schema(description = "板坯宽度")
    private BigDecimal nSlabWth;
    
    @Schema(description = "板坯长度")
    private BigDecimal nSlabLen;
    
    @Schema(description = "块数")
    private Long nSlabCnt;
    
    @Schema(description = "总重量")
    private BigDecimal nSlabWgt;
    
    @Schema(description = "发送人")
    private String cEmpId;
    
    @Schema(description = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtSendDt;
    
    @Schema(description = "接受时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCheckDt;
    
    @Schema(description = "状态")
    private String cStatus;
    
    @Schema(description = "溢短装比")
    private BigDecimal nFulwRad;
    
    @Schema(description = "板坯去向")
    private String cSentPlace;
    
    @Schema(description = "质量设计错误信息")
    private String cQulDsgErr;
    
    @Schema(description = "XT标识")
    private String cPositionDemand;
    
    @Schema(description = "订单号")
    private String cOrderNo;
    
    @Schema(description = "是否保性能")
    private String cSmpFl;
    
    @Schema(description = "回收重量")
    private BigDecimal nBackWgt;

    @Schema(description = "连铸机id")
    private Long cSeat;

    @Schema(description = "连铸机code")
    private String cSeatCode;

    @Schema(description = "连铸机name")
    private String cSeatName;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
