package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 轧钢综判管理
* <AUTHOR>
* @Date: 2025-06-05
* @Version 1.0
*/
@Data
@TableName("t_pss_p_judge")
@Tag(name = "轧钢综判管理对象", description = "轧钢综判管理")
public class TPssPJudge implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "")
    @TableId
    private Long nId;
    
    @Schema(description = "件次号")
    @TableField(value = "c_plate_id")
    private String cPlateId;
    
    @Schema(description = "表面等级")
    @TableField(value = "c_face_result")
    private String cFaceResult;
    
    @Schema(description = "外形等级")
    @TableField(value = "c_body_result")
    private String cBodyResult;
    
    @Schema(description = "尺寸等级")
    @TableField(value = "c_size_result")
    private String cSizeResult;
    
    @Schema(description = "初判等级")
    @TableField(value = "c_jun_check_lvl")
    private String cJunCheckLvl;
    
    @Schema(description = "初判时间")
    @TableField(value = "dt_jun_check_time")
    private LocalDateTime dtJunCheckTime;
    
    @Schema(description = "初判人员")
    @TableField(value = "c_jun_check_operator")
    private String cJunCheckOperator;
    
    @Schema(description = "初判更正等级")
    @TableField(value = "c_mod_jun_check_lvl")
    private String cModJunCheckLvl;
    
    @Schema(description = "性能等级")
    @TableField(value = "c_mtal_grd")
    private String cMtalGrd;
    
    @Schema(description = "探伤等级")
    @TableField(value = "c_ust_grd")
    private String cUstGrd;
    
    @Schema(description = "综判等级")
    @TableField(value = "c_prod_grd")
    private String cProdGrd;
    
    @Schema(description = "综判人员")
    @TableField(value = "c_end_judge_id")
    private String cEndJudgeId;
    
    @Schema(description = "综判不合原因")
    @TableField(value = "c_prod_grd_reason")
    private String cProdGrdReason;
    
    @Schema(description = "综判时间")
    @TableField(value = "dt_prod_grd_time")
    private LocalDateTime dtProdGrdTime;
    
    @Schema(description = "综判修改人员")
    @TableField(value = "c_end_judge_mod_id")
    private String cEndJudgeModId;
    
    @Schema(description = "综判修改时间")
    @TableField(value = "dt_end_judge_mod_time")
    private LocalDateTime dtEndJudgeModTime;
    
    @Schema(description = "备注")
    @TableField(value = "c_memo")
    private String cMemo;
    
    @Schema(description = "是否性能挽救")
    @TableField(value = "c_rescue_fl")
    private String cRescueFl;
    
    @Schema(description = "原表面等级")
    @TableField(value = "c_old_face_result")
    private String cOldFaceResult;
    
    @Schema(description = "原外形等级")
    @TableField(value = "c_old_body_result")
    private String cOldBodyResult;
    
    @Schema(description = "原尺寸等级")
    @TableField(value = "c_old_size_result")
    private String cOldSizeResult;
    
    @Schema(description = "原长度")
    @TableField(value = "n_old_lth")
    private BigDecimal nOldLth;
    
    @Schema(description = "原宽度")
    @TableField(value = "n_old_wth")
    private BigDecimal nOldWth;
    
    @Schema(description = "原厚度")
    @TableField(value = "n_old_thk")
    private BigDecimal nOldThk;
    
    @Schema(description = "废品标记")
    @TableField(value = "c_w_pro")
    private String cWPro;
    
    @Schema(description = "原综判等级")
    @TableField(value = "c_org_prod_grd")
    private String cOrgProdGrd;
    
    @Schema(description = "原性能等级")
    @TableField(value = "c_org_mtal_grd")
    private String cOrgMtalGrd;
    
    @Schema(description = "改判等级")
    @TableField(value = "c_prod_grd_cha")
    private String cProdGrdCha;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}