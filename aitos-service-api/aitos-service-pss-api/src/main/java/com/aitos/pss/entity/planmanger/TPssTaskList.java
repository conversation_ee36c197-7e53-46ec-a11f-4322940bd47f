package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 炉次浇次设计管理
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Data
@TableName("t_pss_task_list")
@Tag(name = "炉次浇次设计管理对象", description = "炉次浇次设计管理")
public class TPssTaskList implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "顺序号")
    @TableId
    private Long nId;
    
    @Schema(description = "生产任务单号")
    @TableField(value = "c_task_list_id")
    private String cTaskListId;
    
    @Schema(description = "产品类别")
    @TableField(value = "c_prod_type")
    private String cProdType;
    
    @Schema(description = "编制部门")
    @TableField(value = "c_crt_dept")
    private String cCrtDept;
    
    @Schema(description = "编制时间")
    @TableField(value = "dt_crt_time")
    private LocalDateTime dtCrtTime;
    
    @Schema(description = "编制人")
    @TableField(value = "c_crt_emp")
    private String cCrtEmp;
    
    @Schema(description = "生产部门")
    @TableField(value = "c_prc_dept")
    private String cPrcDept;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_cd")
    private String cMatQulCd;

    @Schema(description = "质量编码名称")
    @TableField(value = "c_mat_qul_name")
    private String cMatQulName;
    
    @Schema(description = "板坯厚度")
    @TableField(value = "n_slab_thk")
    private BigDecimal nSlabThk;
    
    @Schema(description = "板坯宽度")
    @TableField(value = "n_slab_wth")
    private BigDecimal nSlabWth;
    
    @Schema(description = "板坯长度")
    @TableField(value = "n_slab_len")
    private BigDecimal nSlabLen;
    
    @Schema(description = "块数")
    @TableField(value = "n_slab_cnt")
    private Long nSlabCnt;
    
    @Schema(description = "总重量")
    @TableField(value = "n_slab_wgt")
    private BigDecimal nSlabWgt;
    
    @Schema(description = "发送人")
    @TableField(value = "c_emp_id")
    private String cEmpId;
    
    @Schema(description = "发送时间")
    @TableField(value = "dt_send_dt")
    private LocalDateTime dtSendDt;
    
    @Schema(description = "接受时间")
    @TableField(value = "dt_check_dt")
    private LocalDateTime dtCheckDt;
    
    @Schema(description = "状态")
    @TableField(value = "c_status")
    private String cStatus;
    
    @Schema(description = "溢短装比")
    @TableField(value = "n_fulw_rad")
    private BigDecimal nFulwRad;
    
    @Schema(description = "板坯去向")
    @TableField(value = "c_sent_place")
    private String cSentPlace;
    
    @Schema(description = "质量设计错误信息")
    @TableField(value = "c_qul_dsg_err")
    private String cQulDsgErr;
    
    @Schema(description = "XT标识")
    @TableField(value = "c_position_demand")
    private String cPositionDemand;
    
    @Schema(description = "订单号")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    
    @Schema(description = "是否保性能")
    @TableField(value = "c_smp_fl")
    private String cSmpFl;
    
    @Schema(description = "回收重量")
    @TableField(value = "n_back_wgt")
    private BigDecimal nBackWgt;

    @Schema(description = "连铸机id")
    @TableField(value = "c_seat")
    private Long cSeat;

    @Schema(description = "连铸机code")
    @TableField(value = "c_seat_code")
    private String cSeatCode;

    @Schema(description = "连铸机name")
    @TableField(value = "c_seat_name")
    private String cSeatName;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}