package com.aitos.pss.entity.costmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @title: 成本科目配置下表 <AUTHOR> @Date: 2025-06-30 @Version 1.0
 */
@Data
@TableName("t_pss_fcs_mat_cd")
@Tag(name = "成本科目配置下表对象", description = "成本科目配置下表")
public class TPssFcsMatCd implements Serializable {

  private static final long serialVersionUID = 1L;

  @Schema(description = "顺序号")
  @TableId
  private Long nId;

  @Schema(description = "成本小类编码")
  @TableField(value = "c_cost_item_code")
  private String cCostItemCode;

  @Schema(description = "成本小类名称")
  @TableField(value = "c_cost_item_name")
  private String cCostItemName;

  @Schema(description = "成本项目编码")
  @TableField(value = "c_mat_class_id")
  private String cMatClassId;

  @Schema(description = "成本项目编码")
  @TableField(value = "c_mat_class_code")
  private String cMatClassCode;

  @Schema(description = "成本项目名称")
  @TableField(value = "c_mat_class_name")
  private String cMatClassName;

  @Schema(description = "备注")
  @TableField(value = "c_memo")
  private String cMemo;

  @Schema(description = "单位")
  @TableField(value = "c_unit")
  private String cUnit;

  @Schema(description = "产线id")
  @TableField(value = "c_pro_line")
  private Long cProLine;

  @Schema(description = "产线")
  @TableField(value = "c_pro_line_code")
  private String cProLineCode;

  @Schema(description = "产线")
  @TableField(value = "c_pro_line_name")
  private String cProLineName;

  @Schema(description = "显示顺序")
  @TableField(value = "n_cost_seq")
  private Integer nCostSeq;

  @Schema(description = "工序id")
  @TableField(value = "c_proc_id")
  private Long cProcId;

  @Schema(description = "工序编码")
  @TableField(value = "c_proc_cd")
  private String cProcCd;

  @Schema(description = "工序名称")
  @TableField(value = "c_proc_name")
  private String cProcName;

  @Schema(description = "物料编码")
  @TableField(value = "c_mat_id")
  private Long cMatId;

  @Schema(description = "物料编码")
  @TableField(value = "c_mat_code")
  private String cMatCode;

  @Schema(description = "物料名称")
  @TableField(value = "c_mat_name")
  private String cMatName;

  @Schema(description = "是否区分座次")
  @TableField(value = "c_seat")
  private String cSeat;

  @Schema(description = "备用字段1")
  @TableField(value = "c_spare1")
  private String cSpare1;

  @Schema(description = "创建人")
  @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
  private Long nCreateUserId;

  @Schema(description = "创建时间")
  @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
  private LocalDateTime dtCreateDateTime;

  @Schema(description = "最后修改人")
  @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
  private Long nModifyUserId;

  @Schema(description = "最后修改时间")
  @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
  private LocalDateTime dtModifyDateTime;

  @Schema(description = "逻辑删除标记")
  @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
  @TableLogic
  private Integer nDeleteMark;

  @Schema(description = "是否有效/启用标记")
  @TableField(value = "n_enabled_mark")
  private Integer nEnabledMark;
}
