package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 质量编码管理
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@Data
@TableName("t_pss_quality_code_sub")
@Tag(name = "质量编码管理对象", description = "质量编码管理")
public class TPssQualityCodeSub implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "质检标准名称")
    @TableField(value = "n_fstditem_name")
    private String nFstditemName;

    @Schema(description = "质检标准Id")
    @TableField(value = "n_fstditem_id")
    private Long nFstditemId;
    
    @Schema(description = "简称")
    @TableField(value = "n_fsimplename")
    private String nFsimplename;
    
    @Schema(description = "质检标准编码")
    @TableField(value = "n_fstditem")
    private String nFstditem;
    
    @Schema(description = "描述")
    @TableField(value = "n_fdescription")
    private String nFdescription;
    
    @Schema(description = "标准类型")
    @TableField(value = "n_fjudgetype")
    private String nFjudgetype;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}