package com.aitos.pss.vo.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
public class TPssCutPlanVo {

    /**
    * 序列号
    */
    @Schema(description = "序列号")
    private Long nId;
    /**
    * 计划钢坯号
    */
    @Schema(description = "计划钢坯号")
    private String cPlanSlabId;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    private String cTaskListId;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    private String cMatQulCd;
    /**
    * 钢坯厚度
    */
    @Schema(description = "钢坯厚度")
    private BigDecimal nSlabThk;
    /**
    * 钢坯宽度
    */
    @Schema(description = "钢坯宽度")
    private BigDecimal nSlabWth;
    /**
    * 钢坯长度
    */
    @Schema(description = "钢坯长度")
    private BigDecimal nSlabLen;
    /**
    * 是否可以头尾炉
    */
    @Schema(description = "是否可以头尾炉")
    private String cHtHeat;
    /**
    * 是否可以头尾坯
    */
    @Schema(description = "是否可以头尾坯")
    private String cHtSlab;
    /**
    * 是否热装热送
    */
    @Schema(description = "是否热装热送")
    private String cHotFlag;
    /**
    * 是否检验
    */
    @Schema(description = "是否检验")
    private String cTestFlag;
    /**
    * 钢坯去向
    */
    @Schema(description = "钢坯去向")
    private String cSentPlace;
    /**
    * 定尺类型
    */
    @Schema(description = "定尺类型")
    private String cDingchiType;
    /**
    * 坯料类型
    */
    @Schema(description = "坯料类型")
    private String cSlabType;
    /**
    * 执行标准
    */
    @Schema(description = "执行标准")
    private String cUseStd;
    /**
    * 连铸机号
    */
    @Schema(description = "连铸机号")
    private String cCastId;
    /**
    * 是否来料加工材
    */
    @Schema(description = "是否来料加工材")
    private String cLljg;
    /**
    * 物料编码
    */
    @Schema(description = "物料编码")
    private String cMatCd;
    /**
    * 合同号
    */
    @Schema(description = "合同号")
    private String cContaId;
    /**
    * 合同行号
    */
    @Schema(description = "合同行号")
    private String cContaLineId;
    /**
    * 溢短装比
    */
    @Schema(description = "溢短装比")
    private BigDecimal nFulwRad;
    /**
    * 要求完工日期
    */
    @Schema(description = "要求完工日期")
    private String cWorkoutTim;
    /**
    * 特殊要求
    */
    @Schema(description = "特殊要求")
    private String cSpcNeed;
    /**
    * 发送人
    */
    @Schema(description = "发送人")
    private String cEmpId;
    /**
    * 发送时间
    */
    @Schema(description = "发送时间")
    private LocalDateTime dtSendDt;
    /**
    * 接受时间
    */
    @Schema(description = "接受时间")
    private LocalDateTime dtCheckDt;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 计划炉次号
    */
    @Schema(description = "计划炉次号")
    private String cPlanHeatId;
    /**
    * 炉次内序号
    */
    @Schema(description = "炉次内序号")
    private BigDecimal nHeatSeq;
    /**
    * 计划重量
    */
    @Schema(description = "计划重量")
    private BigDecimal nPlanWgt;
    /**
    * 产线
    */
    @Schema(description = "产线")
    private String cWorkShop;
    /**
    * 是否余材
    */
    @Schema(description = "是否余材")
    private String cRemSlabFlg;
    /**
    * 一切分组编号
    */
    @Schema(description = "一切分组编号")
    private String cFirstCutGroup;
    /**
    * 实际版批号
    */
    @Schema(description = "实际版批号")
    private String cActSlabId;
    /**
    * 一切标志
    */
    @Schema(description = "一切标志")
    private String cFirstCutFlag;
    /**
    * 一切索引
    */
    @Schema(description = "一切索引")
    private BigDecimal nFirstCutSeq;
    /**
    * 成品宽度
    */
    @Schema(description = "成品宽度")
    private BigDecimal nProdWth;
    /**
    * 成品厚度
    */
    @Schema(description = "成品厚度")
    private BigDecimal nProdThk;
    /**
    * 成品长度
    */
    @Schema(description = "成品长度")
    private BigDecimal nProdLen;
    /**
    * 钢水重量
    */
    @Schema(description = "钢水重量")
    private BigDecimal nLeftCalWgt;
    /**
    * 炉次号
    */
    @Schema(description = "炉次号")
    private String cHeatNo;
    /**
    * 一切长度
    */
    @Schema(description = "一切长度")
    private BigDecimal nFirstCutLen;
    /**
    * 一切重量
    */
    @Schema(description = "一切重量")
    private BigDecimal nFirstCutWgt;
    /**
    * 一切操作员
    */
    @Schema(description = "一切操作员")
    private String cFirstCutEmp;
    /**
    * 一切时间
    */
    @Schema(description = "一切时间")
    private LocalDateTime dtFirstCutTime;
    /**
    * 是否一切添加标记
    */
    @Schema(description = "是否一切添加标记")
    private String nIsFirstAdd;
    /**
    * 一切热坯长度
    */
    @Schema(description = "一切热坯长度")
    private BigDecimal cFirstHotLen;
    /**
    * 是否确认
    */
    @Schema(description = "是否确认")
    private String cHasSurePlan;
    /**
    * 计划浇次号
    */
    @Schema(description = "计划浇次号")
    private String cCastEdtSeq;
    /**
    * 炉号后缀
    */
    @Schema(description = "炉号后缀")
    private String cHeatIdAfter;
    /**
    * 处理次数
    */
    @Schema(description = "处理次数")
    private String cTreatNo;
    /**
    * 钢坯成分等级
    */
    @Schema(description = "钢坯成分等级")
    private String cSlabQualLevel;
    /**
    * 装车单号
    */
    @Schema(description = "装车单号")
    private String cLoadNo;
    /**
    * 计划类别0：浇次内；1：浇次外；2：计划外
    */
    @Schema(description = "计划类别0：浇次内；1：浇次外；2：计划外")
    private String cPlanType;
    /**
    * 切割流号
    */
    @Schema(description = "切割流号")
    private Long nLiuNo;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;



}
