package com.aitos.pss.entity.planmanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 物料匹配管理
* <AUTHOR>
* @Date: 2025-05-28
* @Version 1.0
*/
@Data
@TableName("t_pss_dispatch")
@Tag(name = "物料匹配管理对象", description = "物料匹配管理")
public class TPssDispatch implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "序列号")
    @TableId
    private Long nId;
    
    @Schema(description = "调度单号")
    @TableField(value = "c_dispatch_id")
    private String cDispatchId;
    
    @Schema(description = "产线id")
    @TableField(value = "c_line_id")
    private String cLineId;

    @Schema(description = "产线编码")
    @TableField(value = "c_line_no")
    private String cLineNo;

    @Schema(description = "产线名称")
    @TableField(value = "c_line_name")
    private String cLineName;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "产品规格")
    @TableField(value = "n_spec")
    private String nSpec;
    
    @Schema(description = "总重量")
    @TableField(value = "n_tal_wgt")
    private BigDecimal nTalWgt;
    
    @Schema(description = "总支数")
    @TableField(value = "n_tal_cnt")
    private BigDecimal nTalCnt;
    
    @Schema(description = "下达人")
    @TableField(value = "c_release_emp")
    private String cReleaseEmp;
    
    @Schema(description = "调度单中没有轧制计划后设置1")
    @TableField(value = "c_empty_fl")
    private String cEmptyFl;
    
    @Schema(description = "交货日期")
    @TableField(value = "c_post_datetime")
    private String cPostDatetime;
    
    @Schema(description = "剩余支数")
    @TableField(value = "n_left_cnt")
    private BigDecimal nLeftCnt;
    
    @Schema(description = "计划精整工艺路径（精整工序编号+分隔符-逗号+精整工序编号）")
    @TableField(value = "c_plan_finishing_path")
    private String cPlanFinishingPath;
    
    @Schema(description = "综合生产计划号")
    @TableField(value = "c_product_plan_no")
    private String cProductPlanNo;
    
    @Schema(description = "销售计划号")
    @TableField(value = "c_sale_plan_no")
    private String cSalePlanNo;
    
    @Schema(description = "产品类型")
    @TableField(value = "c_prod_type")
    private String cProdType;
    
    @Schema(description = "定尺类型")
    @TableField(value = "c_size_property")
    private String cSizeProperty;
    
    @Schema(description = "下达时间")
    @TableField(value = "dt_release_time")
    private LocalDateTime dtReleaseTime;
    
    @Schema(description = "存货id")
    @TableField(value = "c_mat_id")
    private Long cMatId;

    @Schema(description = "存货编码")
    @TableField(value = "c_mat_code")
    private String cMatCode;
    
    @Schema(description = "存货名称")
    @TableField(value = "c_mat_name")
    private String cMatName;
    
    @Schema(description = "产品长度")
    @TableField(value = "n_prod_len")
    private BigDecimal nProdLen;
    
    @Schema(description = "产品材质id-物料")
    @TableField(value = "c_mat_qul_id")
    private Long cMatQulId;

    @Schema(description = "产品材质code-物料")
    @TableField(value = "c_mat_qul_cd")
    private String cMatQulCd;

    @Schema(description = "产品材质name-物料")
    @TableField(value = "c_mat_qul_name")
    private String cMatQulName;
    
    @Schema(description = "顺序")
    @TableField(value = "n_dispatchseq")
    private BigDecimal nDispatchseq;
    
    @Schema(description = "1调度下达，0未下达,2 转去装炉,3已经生产,8生产结束,9撤销(P0019)")
    @TableField(value = "c_status")
    private String cStatus;
    
    @Schema(description = "计划下达人")
    @TableField(value = "c_send_emp")
    private String cSendEmp;
    
    @Schema(description = "计划下达时间")
    @TableField(value = "dt_send_time")
    private LocalDateTime dtSendTime;
    
    @Schema(description = "执行标准")
    @TableField(value = "c_std_spec")
    private String cStdSpec;
    
    @Schema(description = "执行标准名称")
    @TableField(value = "c_std_spec_name")
    private String cStdSpecName;
    
    @Schema(description = "综合生产计划序号")
    @TableField(value = "n_product_plan_sn")
    private BigDecimal nProductPlanSn;
    
    @Schema(description = "原始调度单号")
    @TableField(value = "c_org_dispatchid")
    private String cOrgDispatchid;
    
    @Schema(description = "0原始调度单，1拆分后调度单，2合并后调度单，3合并后消失调度单")
    @TableField(value = "c_dis_source")
    private String cDisSource;
    
    @Schema(description = "综合生产计划号(任务单调整用)")
    @TableField(value = "c_dis_product_plan_no")
    private String cDisProductPlanNo;
    
    @Schema(description = "综合销售计划序号")
    @TableField(value = "n_sale_plan_sn")
    private BigDecimal nSalePlanSn;
    
    @Schema(description = "交货状态")
    @TableField(value = "c_fpoststateid")
    private String cFpoststateid;
    
    @Schema(description = "备注")
    @TableField(value = "c_memo")
    private String cMemo;
    
    @Schema(description = "调度下达人（下达到物料匹配）")
    @TableField(value = "c_dissend_emp")
    private String cDissendEmp;
    
    @Schema(description = "调度下达时间")
    @TableField(value = "dt_dissend_time")
    private LocalDateTime dtDissendTime;
    
    @Schema(description = "是否启用订单生产 0下线，1上线")
    @TableField(value = "c_pre_status")
    private Integer cPreStatus;
    
    @Schema(description = "是否为临时计划（1：临时计划）")
    @TableField(value = "c_is_tempplan")
    private String cIsTempplan;
    
    @Schema(description = "销售操作日志")
    @TableField(value = "c_mergememo")
    private String cMergememo;
    
    @Schema(description = "调度备注")
    @TableField(value = "c_dispatchmemo")
    private String cDispatchmemo;
    
    @Schema(description = "调整顺序时间")
    @TableField(value = "dt_updatetime")
    private LocalDateTime dtUpdatetime;
    
    @Schema(description = "调整顺序人")
    @TableField(value = "c_updatetime")
    private String cUpdatetime;
    
    @Schema(description = "精整内容")
    @TableField(value = "c_clearcont")
    private String cClearcont;
    
    @Schema(description = "精整交货时间")
    @TableField(value = "dt_chearfis_time")
    private LocalDateTime dtChearfisTime;
    
    @Schema(description = "精整内容编制人")
    @TableField(value = "c_clearedtemp")
    private String cClearedtemp;
    
    @Schema(description = "精整内容编制时间")
    @TableField(value = "dt_clearedt_time")
    private LocalDateTime dtClearedtTime;
    
    @Schema(description = "结束人")
    @TableField(value = "c_finemp")
    private String cFinemp;
    
    @Schema(description = "是否紧急订单（0：正常订单，1：紧急订单）")
    @TableField(value = "c_exg_prod_lot_fl")
    private String cExgProdLotFl;
    
    @Schema(description = "客户名称")
    @TableField(value = "c_customer_cd")
    private String cCustomerCd;
    
    @Schema(description = "结束时间")
    @TableField(value = "dt_fin_time")
    private LocalDateTime dtFinTime;
    
    @Schema(description = "客户重要度")
    @TableField(value = "c_cus_level")
    private String cCusLevel;
    
    @Schema(description = "是否下发二级")
    @TableField(value = "c_s_fl")
    private String cSFl;
    
    @Schema(description = "旧编码")
    @TableField(value = "c_oldcode")
    private String cOldcode;
    
    @Schema(description = "钢坯库存对应重量")
    @TableField(value = "c_kcgp")
    private String cKcgp;
    
    @Schema(description = "产品宽度")
    @TableField(value = "n_prod_wth")
    private BigDecimal nProdWth;
    
    @Schema(description = "订单号")
    @TableField(value = "c_order_no")
    private String cOrderNo;
    
    @Schema(description = "规格代码")
    @TableField(value = "c_item_cd")
    private String cItemCd;
    
    @Schema(description = "成品规格")
    @TableField(value = "c_mat_item")
    private String cMatItem;
    
    @Schema(description = "厚度公差最大值")
    @TableField(value = "n_thk_bias_max")
    private BigDecimal nThkBiasMax;
    
    @Schema(description = "厚度公差最小值")
    @TableField(value = "n_thk_bias_min")
    private BigDecimal nThkBiasMin;
    
    @Schema(description = "宽度公差最大值")
    @TableField(value = "n_wth_bias_max")
    private BigDecimal nWthBiasMax;
    
    @Schema(description = "宽度公差最小值")
    @TableField(value = "n_wth_bias_min")
    private BigDecimal nWthBiasMin;
    
    @Schema(description = "成品重量最大值")
    @TableField(value = "n_prod_wgt_max")
    private BigDecimal nProdWgtMax;
    
    @Schema(description = "成品重量最小值")
    @TableField(value = "n_prod_wgt_min")
    private BigDecimal nProdWgtMin;
    
    @Schema(description = "长度最小")
    @TableField(value = "n_prod_len_min")
    private BigDecimal nProdLenMin;
    
    @Schema(description = "长度最大")
    @TableField(value = "n_prod_len_max")
    private BigDecimal nProdLenMax;
    
    @Schema(description = "计划单支重")
    @TableField(value = "n_plan_single_wgt")
    private BigDecimal nPlanSingleWgt;
    
    @Schema(description = "挂单重量")
    @TableField(value = "n_dofinal_wgt")
    private BigDecimal nDofinalWgt;
    
    @Schema(description = "坯料规格")
    @TableField(value = "c_slab_item")
    private String cSlabItem;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;

}