package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssMatOutboundRecordPageDto extends PageInput {

    /**
    * 物料ID
    */
    @Schema(description = "物料ID")
    private Long nMaterialId;
    /**
    * 调整系数
    */
    @Schema(description = "调整系数")
    private BigDecimal nAdjustFactor;
    /**
    * 出库前库存
    */
    @Schema(description = "出库前库存")
    private BigDecimal nBeforeStock;
    /**
    * 出库时间字段开始时间
    */
    @Schema(description = "出库时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtOutboundTimeStart;
    /**
    * 出库时间字段结束时间
    */
    @Schema(description = "出库时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtOutboundTimeEnd;
    /**
    * 操作类型(auto:自动,manual:人工)
    */
    @Schema(description = "操作类型(auto:自动,manual:人工)")
    private String cOperationType;
    /**
    * 炉号
    */
    @Schema(description = "炉号")
    private String cFurnaceNo;
    /**
    * 理论用量
    */
    @Schema(description = "理论用量")
    private BigDecimal nTheoreticalAmount;
    /**
    * 实际用量
    */
    @Schema(description = "实际用量")
    private BigDecimal nActualAmount;
    /**
    * 出库后库存
    */
    @Schema(description = "出库后库存")
    private BigDecimal nAfterStock;
    /**
    * 状态(pending:待确认,completed:已完成,adjusted:已调整)
    */
    @Schema(description = "状态(pending:待确认,completed:已完成,adjusted:已调整)")
    private String cStatus;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
