package com.aitos.pss.dto.inventory;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssWareHouseLocPageDto extends PageInput {

    /**
    * 代码
    */
    @Schema(description = "代码")
    private String cCode;
    /**
    * 名称
    */
    @Schema(description = "名称")
    private String cName;
    /**
    * 行（数）
    */
    @Schema(description = "行（数）")
    private Integer nRow;
    /**
    * 设置类型
    */
    @Schema(description = "设置类型")
    private String cType;
    /**
    * 上级节点
    */
    @Schema(description = "上级节点")
    private String cParentCode;
    /**
    * 列（数）
    */
    @Schema(description = "列（数）")
    private Integer nCol;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
