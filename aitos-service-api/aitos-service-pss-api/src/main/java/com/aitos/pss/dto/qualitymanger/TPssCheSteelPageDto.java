package com.aitos.pss.dto.qualitymanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssCheSteelPageDto extends PageInput {

    
    @Schema(description = "炉号")
    private String cSampleid;
    
    @Schema(description = "钢种代码")
    private String cStlGrdCd;
    
    @Schema(description = "式样类型")
    private String cSampletype;
    
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;
    
    @Schema(description = "试样号")
    private String cSmpId;
    
    @Schema(description = "质量等级")
    private String cJudge;

}
