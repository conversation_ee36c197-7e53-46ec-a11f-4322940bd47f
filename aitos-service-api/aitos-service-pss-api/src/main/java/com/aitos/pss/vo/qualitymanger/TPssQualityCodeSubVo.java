package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@Data
public class TPssQualityCodeSubVo {

    
    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "质量编码")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    private String cQualCodeName;
    
    @Schema(description = "质检标准名称")
    private String nFstditemName;
    
    @Schema(description = "简称")
    private String nFsimplename;
    
    @Schema(description = "质检标准编码")
    private String nFstditem;

    @Schema(description = "质检标准Id")
    private Long nFstditemId;
    
    @Schema(description = "描述")
    private String nFdescription;
    
    @Schema(description = "标准类型")
    private String nFjudgetype;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
