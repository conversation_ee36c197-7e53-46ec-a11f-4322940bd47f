package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 计划钢坯
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Data
@TableName("t_pss_pslab_chos")
@Tag(name = "计划钢坯对象", description = "计划钢坯")
public class TPssPslabChos implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 顺序号
    */
    @Schema(description = "顺序号")
    @TableId
    private Long nId;
    /**
    * 计划坯料号/件次号
    */
    @Schema(description = "计划坯料号/件次号")
    @TableField(value = "c_plan_slab_id")
    private String cPlanSlabId;
    /**
    * 生产任务单号
    */
    @Schema(description = "生产任务单号")
    @TableField(value = "c_task_list_id")
    private String cTaskListId;
    /**
    * 钢种代码
    */
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;

    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    /**
    * 质量编码
    */
    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_cd")
    private String cMatQulCd;

    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_name")
    private String cMatQulName;
    /**
    * 板坯厚度
    */
    @Schema(description = "板坯厚度")
    @TableField(value = "n_slab_thk")
    private BigDecimal nSlabThk;
    /**
    * 板坯宽度
    */
    @Schema(description = "板坯宽度")
    @TableField(value = "n_slab_wth")
    private BigDecimal nSlabWth;
    /**
    * 板坯长度
    */
    @Schema(description = "板坯长度")
    @TableField(value = "n_slab_len")
    private BigDecimal nSlabLen;
    /**
    * 是否可以头尾炉
    */
    @Schema(description = "是否可以头尾炉")
    @TableField(value = "c_ht_heat")
    private String cHtHeat;
    /**
    * 是否可以头尾坯
    */
    @Schema(description = "是否可以头尾坯")
    @TableField(value = "c_ht_slab")
    private String cHtSlab;
    /**
    * 是否热装热送
    */
    @Schema(description = "是否热装热送")
    @TableField(value = "c_hot_flag")
    private String cHotFlag;
    /**
    * 是否检验
    */
    @Schema(description = "是否检验")
    @TableField(value = "c_test_flag")
    private String cTestFlag;
    /**
    * 板坯去向
    */
    @Schema(description = "板坯去向")
    @TableField(value = "c_sent_place")
    private String cSentPlace;
    /**
    * 定尺类型
    */
    @Schema(description = "定尺类型")
    @TableField(value = "c_dingchi_type")
    private String cDingchiType;
    /**
    * 坯料类型
    */
    @Schema(description = "坯料类型")
    @TableField(value = "c_slab_type")
    private String cSlabType;
    /**
    * 执行标准
    */
    @Schema(description = "执行标准")
    @TableField(value = "c_use_std")
    private String cUseStd;
    /**
    * 连铸机号
    */
    @Schema(description = "连铸机id")
    @TableField(value = "c_cast_id")
    private Long cCastId;

    @Schema(description = "连铸机code")
    @TableField(value = "c_cast_code")
    private String cCastCode;

    @Schema(description = "连铸机name")
    @TableField(value = "c_cast_name")
    private String cCastName;
    /**
    * 是否来料加工材
    */
    @Schema(description = "是否来料加工材")
    @TableField(value = "c_lljg")
    private String cLljg;
    /**
    * 物料编码
    */
    @Schema(description = "物料id")
    @TableField(value = "c_mat_id")
    private Long cMatId;

    @Schema(description = "物料编码")
    @TableField(value = "c_mat_cd")
    private String cMatCd;

    @Schema(description = "物料name")
    @TableField(value = "c_mat_name")
    private String cMatName;
    /**
    * 合同号
    */
    @Schema(description = "合同号")
    @TableField(value = "c_conta_id")
    private String cContaId;
    /**
    * 溢短装比
    */
    @Schema(description = "溢短装比")
    @TableField(value = "c_fulw_rad")
    private BigDecimal cFulwRad;
    /**
    * 要求完工日期
    */
    @Schema(description = "要求完工日期")
    @TableField(value = "c_workout_tim")
    private LocalDateTime cWorkoutTim;
    /**
    * 特殊要求
    */
    @Schema(description = "特殊要求")
    @TableField(value = "c_spc_need")
    private String cSpcNeed;
    /**
    * 发送人
    */
    @Schema(description = "发送人")
    @TableField(value = "c_send_empid")
    private String cSendEmpid;
    /**
    * 发送时间
    */
    @Schema(description = "发送时间")
    @TableField(value = "c_send_dt")
    private LocalDateTime cSendDt;
    /**
    * 接受时间
    */
    @Schema(description = "接受时间")
    @TableField(value = "c_check_in_dt")
    private LocalDateTime cCheckInDt;
    /**
    * 提取人
    */
    @Schema(description = "提取人")
    @TableField(value = "c_chos_emp")
    private String cChosEmp;
    /**
    * 提取时间
    */
    @Schema(description = "提取时间")
    @TableField(value = "c_chos_dt")
    private LocalDateTime cChosDt;
    /**
    * 板坯重量
    */
    @Schema(description = "板坯重量")
    @TableField(value = "c_slab_wgt")
    private BigDecimal cSlabWgt;
    /**
    * 已经编制(1:全部编制,2:部分编制)
    */
    @Schema(description = "已经编制(1:全部编制,2:部分编制)")
    @TableField(value = "c_has_edit")
    private String cHasEdit;
    /**
    * 已经编制重量
    */
    @Schema(description = "已经编制重量")
    @TableField(value = "c_edit_wgt")
    private BigDecimal cEditWgt;
    /**
    * 余材标志
    */
    @Schema(description = "余材标志")
    @TableField(value = "c_rem_slab_flg")
    private String cRemSlabFlg;
    /**
    * 余材产生方式(1:已经加入计划板坯表；2:未加入计划板坯表)
    */
    @Schema(description = "余材产生方式(1:已经加入计划板坯表；2:未加入计划板坯表)")
    @TableField(value = "c_rem_crtway")
    private String cRemCrtway;
    /**
    * 计划炉次号
    */
    @Schema(description = "计划炉次号")
    @TableField(value = "c_plan_heat_id")
    private String cPlanHeatId;
    /**
    * 炉次内序号
    */
    @Schema(description = "炉次内序号")
    @TableField(value = "c_heat_seq")
    private BigDecimal cHeatSeq;
    /**
    * 
    */
    @Schema(description = "")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time")
    private LocalDateTime dtCreateDateTime;
    /**
    * 修改人
    */
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    /**
    * 修改时间
    */
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time")
    private LocalDateTime dtModifyDateTime;
    /**
    * 删除标记;默认为0,1为删除
    */
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}