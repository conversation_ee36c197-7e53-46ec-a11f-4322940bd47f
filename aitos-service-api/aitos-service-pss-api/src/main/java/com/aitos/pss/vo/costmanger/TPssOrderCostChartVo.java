package com.aitos.pss.vo.costmanger;

import com.aitos.pss.vo.chart.ChartVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class TPssOrderCostChartVo {

    @Schema(description = "主键")
    private Long nId;

    @Schema(description = "成本汇总")
    private Map<String, BigDecimal> costMap;

    @Schema(description = "总成本")
    private BigDecimal nSumCost;

    @Schema(description = "人工成本")
    private BigDecimal nLaborCost;

    @Schema(description = "材料成本")
    private BigDecimal nMaterialCost;

    @Schema(description = "制造成本")
    private BigDecimal nManufactCost;

    @Schema(description = "单位成本")
    private BigDecimal nUnitCost;

    @Schema(description = "预计利润")
    private BigDecimal nEstimatedProfit;

    @Schema(description = "chartVoList")
    private List<TPssOrderCostVo> costDetailList;

    @Schema(description = "chartVoList")
    private List<ChartVo> chartVoList;
} 