package com.aitos.pss.vo.qualitymanger;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
public class TPssSlabSizeVo {

    
    @Schema(description = "")
    private Long nId;
    
    @Schema(description = "坯料号/件次号")
    private String cSlabId;
    
    @Schema(description = "缺陷代码")
    private String cDefectCode;
    
    @Schema(description = "钢种代码")
    private String cStlGrd;
    
    @Schema(description = "钢种名称")
    private String cStlGrdDesc;
    
    @Schema(description = "板坯厚度")
    private BigDecimal nSlabThk;
    
    @Schema(description = "板坯宽度")
    private BigDecimal nSlabWid;
    
    @Schema(description = "板坯长度")
    private BigDecimal nSlabLen;
    
    @Schema(description = "板坯测量厚度")
    private BigDecimal nSlabMeaThk;
    
    @Schema(description = "板坯测量宽度")
    private BigDecimal nSlabMeaWid;
    
    @Schema(description = "板坯测量长度")
    private BigDecimal nSlabMeaLen;
    
    @Schema(description = "尺寸等级")
    private String cSizeRlt;
    
    @Schema(description = "质量描述")
    private String cRemark;



}
