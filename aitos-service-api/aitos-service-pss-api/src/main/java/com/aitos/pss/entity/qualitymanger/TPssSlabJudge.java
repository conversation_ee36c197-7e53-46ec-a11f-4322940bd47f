package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 炼钢综判管理
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@Data
@TableName("t_pss_slab_judge")
@Tag(name = "炼钢综判管理对象", description = "炼钢综判管理")
public class TPssSlabJudge implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "")
    @TableId
    private Long nId;
    
    @Schema(description = "坯料号")
    @TableField(value = "c_slab_id")
    private String cSlabId;
    
    @Schema(description = "原质量编码id")
    @TableField(value = "c_old_mat_id")
    private Long cOldMatId;

    @Schema(description = "原质量编码code")
    @TableField(value = "c_old_mat_code")
    private String cOldMatCode;

    @Schema(description = "原质量编码name")
    @TableField(value = "c_old_mat_name")
    private String cOldMatName;
    
    @Schema(description = "原钢种代码")
    @TableField(value = "c_old_stl_grd_cd")
    private String cOldStlGrdCd;

    @Schema(description = "原钢种代码描述")
    @TableField(value = "c_old_stl_grd_desc")
    private String cOldStlGrdDesc;
    
    @Schema(description = "现质量编码id")
    @TableField(value = "c_new_mat_id")
    private Long cNewMatId;

    @Schema(description = "现质量编码code")
    @TableField(value = "c_new_mat_code")
    private String cNewMatCode;

    @Schema(description = "现质量编码name")
    @TableField(value = "c_new_mat_name")
    private String cNewMatName;
    
    @Schema(description = "现钢种代码")
    @TableField(value = "c_new_stl_grd_cd")
    private String cNewStlGrdCd;

    @Schema(description = "现钢种代码描述")
    @TableField(value = "c_new_stl_grd_desc")
    private String cNewStlGrdDesc;
    
    @Schema(description = "成分等级")
    @TableField(value = "c_chem_rlt")
    private String cChemRlt;
    
    @Schema(description = "外形等级（表面等级）")
    @TableField(value = "c_body_rlt")
    private String cBodyRlt;
    
    @Schema(description = "尺寸等级")
    @TableField(value = "c_size_rlt")
    private String cSizeRlt;
    
    @Schema(description = "综判等级")
    @TableField(value = "c_judge_rlt")
    private String cJudgeRlt;
    
    @Schema(description = "炉号")
    @TableField(value = "c_heat_id")
    private String cHeatId;
    
    @Schema(description = "申请编号")
    @TableField(value = "c_require_no")
    private String cRequireNo;
    
    @Schema(description = "综判状态")
    @TableField(value = "c_judge_status")
    private String cJudgeStatus;
    
    @Schema(description = "表面等级")
    @TableField(value = "c_face_rlt")
    private String cFaceRlt;
    
    @Schema(description = "原表面等级")
    @TableField(value = "c_old_face_rlt")
    private String cOldFaceRlt;
    
    @Schema(description = "删除标记")
    @TableField(value = "c_del_flag")
    private String cDelFlag;
    
    @Schema(description = "成分描述")
    @TableField(value = "c_chem_remark")
    private String cChemRemark;
    
    @Schema(description = "表面描述")
    @TableField(value = "c_face_remark")
    private String cFaceRemark;
    
    @Schema(description = "尺寸描述")
    @TableField(value = "c_size_remark")
    private String cSizeRemark;
    
    @Schema(description = "性能描述")
    @TableField(value = "c_property_remark")
    private String cPropertyRemark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}