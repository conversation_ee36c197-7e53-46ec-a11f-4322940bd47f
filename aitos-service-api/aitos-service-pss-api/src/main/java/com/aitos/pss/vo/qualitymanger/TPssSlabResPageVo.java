package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
public class TPssSlabResPageVo {

    
    @ExcelIgnore
    @Schema(description = "")
    private String nId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("计划件次号")
    @Schema(description = "计划件次号")
    private String cPlanMatId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("件次号/坯料号")
    @Schema(description = "件次号/坯料号")
    private String cMatId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("数量")
    @Schema(description = "数量")
    private Integer nCnt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原料号")
    @Schema(description = "原料号")
    private String cPreMatId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("物料类型")
    @Schema(description = "物料类型")
    private String cMatType;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("炉号")
    @Schema(description = "炉号")
    private String cHeatId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("轧制批次号")
    @Schema(description = "轧制批次号")
    private String cRollSchId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("母板号")
    @Schema(description = "母板号")
    private String cMatIdMth;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("物料状态")
    @Schema(description = "物料状态")
    private String cStatus;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("前物料状态")
    @Schema(description = "前物料状态")
    private String cPreStatus;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢种代码")
    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;


    @Schema(description = "质量编码")
    private Long cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;

    @Schema(description = "质量编码名称")
    private String cQualityCodeName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("物料编码")
    @Schema(description = "物料编码")
    private String cMatCode;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("物料名称")
    @Schema(description = "物料名称")
    private String cMatName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("规格")
    @Schema(description = "规格")
    private String cMatItem;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("厚度")
    @Schema(description = "厚度")
    private BigDecimal nMatThk;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("宽度")
    @Schema(description = "宽度")
    private BigDecimal nMatWid;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("直径")
    @Schema(description = "直径")
    private BigDecimal nMatDia;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("长度")
    @Schema(description = "长度")
    private BigDecimal nMatLth;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("实际重量")
    @Schema(description = "实际重量")
    private BigDecimal nMatWgt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("计算重量")
    @Schema(description = "计算重量")
    private BigDecimal nMatWgtCal;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("检斤重量")
    @Schema(description = "检斤重量")
    private BigDecimal nMatActWgt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("执行标准号")
    @Schema(description = "执行标准号")
    private String cStdSpec;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综合判定时间 Q")
    @Schema(description = "综合判定时间 Q")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime dtJudgeTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("综判等级")
    @Schema(description = "综判等级")
    private String cProdGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("初判等级")
    @Schema(description = "初判等级")
    private String cPrelGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("成分等级")
    @Schema(description = "成分等级")
    private String cChemGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("尺寸等级")
    @Schema(description = "尺寸等级")
    private String cSizeGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("外观等级（表面等级）")
    @Schema(description = "外观等级（表面等级）")
    private String cSurfGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("性能等级")
    @Schema(description = "性能等级")
    private String cMtalGrd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("探伤结果")
    @Schema(description = "探伤结果")
    private String cWPro;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("探伤日期")
    @Schema(description = "探伤日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtWDate;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("不合格原因")
    @Schema(description = "不合格原因")
    private String cUpdNot;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("处理方式")
    @Schema(description = "处理方式")
    private String cUpdRsn;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否改配合同")
    @Schema(description = "是否改配合同，否：Null 是：1")
    private String cBdFl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("质量证明书号")
    @Schema(description = "质量证明书号")
    private String cCertId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("购入时间")
    @Schema(description = "购入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPchTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("生产时间")
    @Schema(description = "生产时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtProdTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("生产班次")
    @Schema(description = "生产班次")
    private String cProdShift;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("生产班别")
    @Schema(description = "生产班别")
    private String cProdGroup;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("信息来源")
    @Schema(description = "信息来源")
    private String cOccrCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产地")
    @Schema(description = "产地")
    private String cFactory;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("供货商")
    @Schema(description = "供货商")
    private String cSupplier;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("销售订单号")
    @Schema(description = "销售订单号")
    private String cSaleNo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("销售订单行号")
    @Schema(description = "销售订单行号")
    private String cSaleSn;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("生产任务单号")
    @Schema(description = "生产任务单号")
    private String cTaskListId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("采购订单号")
    @Schema(description = "采购订单号")
    private String cBuyOrderNo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("采购订单行号")
    @Schema(description = "采购订单行号")
    private String cOrderSn;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("订单材/余材标志")
    @Schema(description = "订单材/余材标志")
    private String cOrdFl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("生产订单号")
    @Schema(description = "生产订单号")
    private String cOrderNo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否多订单")
    @Schema(description = "是否多订单")
    private String cMultiOrdTag;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("余材原因")
    @Schema(description = "余材原因")
    private String cWooRsn;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("板坯热送标志")
    @Schema(description = "板坯热送标志")
    private String cHcrFl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否试样板")
    @Schema(description = "是否试样板")
    private String cSmpFl;

    @Schema(description = "当前产线")
    private Long cLineId;

    @Schema(description = "当前产线cd")
    private String cLineCd;

    @Schema(description = "当前产线name")
    private String cLineName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("产销仓库代码")
    @Schema(description = "产销仓库代码")
    private String cNcYardCd;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("库位号")
    @Schema(description = "库位号")
    private String cLoc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("层号")
    @Schema(description = "层号")
    private Integer nLocLvl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("计划去向")
    @Schema(description = "计划去向")
    private String cPreOut;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("备注")
    @Schema(description = "备注")
    private String cMemo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("最后修改程序")
    @Schema(description = "最后修改程序")
    private String cPgmId;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("上传标志")
    @Schema(description = "上传标志")
    private String cMonthFlag;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("上传人员")
    @Schema(description = "上传人员")
    private String cMonthEmp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("上传时间")
    @Schema(description = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtMonthTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("复秤标记")
    @Schema(description = "复秤标记")
    private String cReweightFl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("复秤重量")
    @Schema(description = "复秤重量")
    private Integer nReweight;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("复秤类型")
    @Schema(description = "复秤类型")
    private String cReWgtFlag;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("二次复秤标记")
    @Schema(description = "二次复秤标记")
    private String cTwoRewgtFl;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("二次复秤日期")
    @Schema(description = "二次复秤日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtTwoWgtTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("二次复秤人员")
    @Schema(description = "二次复秤人员")
    private String cTwoWgtEmp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("二次复秤重量")
    @Schema(description = "二次复秤重量")
    private Integer nTwoWgt;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("改判人")
    @Schema(description = "改判人")
    private String cChaEmpid;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("改判时间")
    @Schema(description = "改判时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtChaTime;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("改判备注")
    @Schema(description = "改判备注")
    private String cChaMemo;

    @Schema(description = "原质量编码")
    private Long cPreMatQulId;

    @Schema(description = "原质量编码")
    private String cPreMatQulCd;

    @Schema(description = "原质量编码name")
    private String cPreMatQulName;

    @Schema(description = "原钢种")
    private String cPreStlGrdCd;

    @Schema(description = "原钢种")
    private String cPreStlGrdDesc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原长度")
    @Schema(description = "原长度")
    private BigDecimal nPreMatLth;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原计算重量")
    @Schema(description = "原计算重量")
    private BigDecimal nPreMatWgtCal;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原物料编码")
    @Schema(description = "原物料编码")
    private String cPreMatcode;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("原物料名称")
    @Schema(description = "原物料名称")
    private String cPreMatname;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否锁定")
    @Schema(description = "是否锁定")
    private String cIsLock;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("装车车号")
    @Schema(description = "装车车号")
    private String cCarNo;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

}
