package com.aitos.pss.dto.costmanger;

import com.aitos.common.core.domain.page.PageInput;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class TPssFcsRawmCostPageDto extends PageInput {

    /**
    * 钢种
    */
    @Schema(description = "钢种")
    private String cStlGrdCd;
    /**
    * 工序
    */
    @Schema(description = "工序")
    private Long cProcId;
    /**
    * 成本项目名称
    */
    @Schema(description = "成本项目名称")
    private String cCostItemName;
    /**
    * 物料代码
    */
    @Schema(description = "物料代码")
    private String cMatCode;
    /**
    * 计划价格
    */
    @Schema(description = "计划价格")
    private BigDecimal cPlanPrice;
    /**
    * 消耗量
    */
    @Schema(description = "消耗量")
    private BigDecimal cMatQty;
    /**
    * 班组
    */
    @Schema(description = "班组")
    private String cCrew;
    /**
    * 结束使用时间字段开始时间
    */
    @Schema(description = "结束使用时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtUseDateEndStart;
    /**
    * 结束使用时间字段结束时间
    */
    @Schema(description = "结束使用时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtUseDateEndEnd;
    /**
    * 核算单位
    */
    @Schema(description = "核算单位")
    private String cType;
    /**
    * 公摊系数
    */
    @Schema(description = "公摊系数")
    private BigDecimal cRatio;
    /**
    * 成本日期字段开始时间
    */
    @Schema(description = "成本日期字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cCostDateStart;
    /**
    * 成本日期字段结束时间
    */
    @Schema(description = "成本日期字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cCostDateEnd;
    /**
    * 审核人
    */
    @Schema(description = "审核人")
    private Long cProEmp;
    /**
    * 上传标志位
    */
    @Schema(description = "上传标志位")
    private String cMonthFlag;
    /**
    * 是否有效/启用标记
    */
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;
    /**
    * 单位编码
    */
    @Schema(description = "单位编码")
    private String cUnitCode;
    /**
    * 炉号
    */
    @Schema(description = "炉号")
    private String cHeatId;
    /**
    * 产线
    */
    @Schema(description = "产线")
    private Long cProLine;
    /**
    * 成本项目编码
    */
    @Schema(description = "成本项目编码")
    private String cCostItemCode;
    /**
    * 物料id
    */
    @Schema(description = "物料id")
    private Long cMatId;
    /**
    * 物料名称
    */
    @Schema(description = "物料名称")
    private String cMatName;
    /**
    * 实际价格
    */
    @Schema(description = "实际价格")
    private BigDecimal cActPrice;
    /**
    * 开始使用日期字段开始时间
    */
    @Schema(description = "开始使用日期字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtUseDateStart;
    /**
    * 开始使用日期字段结束时间
    */
    @Schema(description = "开始使用日期字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtUseDateEnd;
    /**
    * 班次
    */
    @Schema(description = "班次")
    private String cShift;
    /**
    * 显示顺序
    */
    @Schema(description = "显示顺序")
    private Integer nCostSeq;
    /**
    * 定额
    */
    @Schema(description = "定额")
    private BigDecimal cQuota;
    /**
    * 座次
    */
    @Schema(description = "座次")
    private Long cSeat;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private Integer cState;
    /**
    * 审核时间字段开始时间
    */
    @Schema(description = "审核时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtProTimeStart;
    /**
    * 审核时间字段结束时间
    */
    @Schema(description = "审核时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtProTimeEnd;
    /**
    * 单位
    */
    @Schema(description = "单位")
    private Long cUnitId;
    /**
    * 单位名称
    */
    @Schema(description = "单位名称")
    private String cUnitName;
    /**
    * 最后修改人
    */
    @Schema(description = "最后修改人")
    private Long nModifyUserId;
    /**
    * 创建人
    */
    @Schema(description = "创建人")
    private Long nCreateUserId;
    /**
    * 创建时间字段开始时间
    */
    @Schema(description = "创建时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeStart;
    /**
    * 创建时间字段结束时间
    */
    @Schema(description = "创建时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTimeEnd;
    /**
    * 最后修改时间字段开始时间
    */
    @Schema(description = "最后修改时间字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeStart;
    /**
    * 最后修改时间字段结束时间
    */
    @Schema(description = "最后修改时间字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTimeEnd;

}
