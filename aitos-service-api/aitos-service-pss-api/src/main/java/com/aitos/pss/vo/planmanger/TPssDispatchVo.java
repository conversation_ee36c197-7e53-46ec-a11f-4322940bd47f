package com.aitos.pss.vo.planmanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-05-28
* @Version 1.0
*/
@Data
public class TPssDispatchVo {

    
    @Schema(description = "序列号")
    private Long nId;
    
    @Schema(description = "调度单号")
    private String cDispatchId;

    @Schema(description = "产线id")
    private String cLineId;

    @Schema(description = "产线编码")
    private String cLineNo;

    @Schema(description = "产线名称")
    private String cLineName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种描述/名称")
    private String cStlGrdDesc;
    
    @Schema(description = "规格")
    private BigDecimal nSpec;
    
    @Schema(description = "总重量")
    private BigDecimal nTalWgt;
    
    @Schema(description = "总支数")
    private BigDecimal nTalCnt;
    
    @Schema(description = "下达人")
    private String cReleaseEmp;
    
    @Schema(description = "调度单中没有轧制计划后设置1")
    private String cEmptyFl;
    
    @Schema(description = "交货日期")
    private String cPostDatetime;
    
    @Schema(description = "剩余支数")
    private BigDecimal nLeftCnt;
    
    @Schema(description = "计划精整工艺路径（精整工序编号+分隔符-逗号+精整工序编号）")
    private String cPlanFinishingPath;
    
    @Schema(description = "综合生产计划号")
    private String cProductPlanNo;
    
    @Schema(description = "销售计划号")
    private String cSalePlanNo;
    
    @Schema(description = "产品类型")
    private String cProdType;
    
    @Schema(description = "定尺类型")
    private String cSizeProperty;
    
    @Schema(description = "下达时间")
    private LocalDateTime dtReleaseTime;
    
    @Schema(description = "存货编码")
    private String cMatcode;
    
    @Schema(description = "存货名称")
    private String cMatname;
    
    @Schema(description = "产品长度")
    private BigDecimal nProdLen;
    
    @Schema(description = "产品材质")
    private String cMatQulCd;
    
    @Schema(description = "顺序")
    private BigDecimal nDispatchseq;
    
    @Schema(description = "1调度下达，0未下达,2 转去装炉,3已经生产,8生产结束,9撤销(P0019)")
    private String cStatus;
    
    @Schema(description = "计划下达人")
    private String cSendEmp;
    
    @Schema(description = "计划下达时间")
    private LocalDateTime dtSendTime;
    
    @Schema(description = "执行标准")
    private String cStdSpec;
    
    @Schema(description = "执行标准名称")
    private String cStdSpecName;
    
    @Schema(description = "综合生产计划序号")
    private BigDecimal nProductPlanSn;
    
    @Schema(description = "原始调度单号")
    private String cOrgDispatchid;
    
    @Schema(description = "0原始调度单，1拆分后调度单，2合并后调度单，3合并后消失调度单")
    private String cDisSource;
    
    @Schema(description = "综合生产计划号(任务单调整用)")
    private String cDisProductPlanNo;
    
    @Schema(description = "综合销售计划序号")
    private BigDecimal nSalePlanSn;
    
    @Schema(description = "交货状态")
    private String cFpoststateid;
    
    @Schema(description = "备注")
    private String cMemo;
    
    @Schema(description = "调度下达人（下达到物料匹配）")
    private String cDissendEmp;
    
    @Schema(description = "调度下达时间")
    private LocalDateTime dtDissendTime;
    
    @Schema(description = "是否启用订单生产")
    private Integer cPreStatus;
    
    @Schema(description = "是否为临时计划（1：临时计划）")
    private String cIsTempplan;
    
    @Schema(description = "销售操作日志")
    private String cMergememo;
    
    @Schema(description = "调度备注")
    private String cDispatchmemo;
    
    @Schema(description = "调整顺序时间")
    private LocalDateTime dtUpdatetime;
    
    @Schema(description = "调整顺序人")
    private String cUpdatetime;
    
    @Schema(description = "精整内容")
    private String cClearcont;
    
    @Schema(description = "精整交货时间")
    private LocalDateTime dtChearfisTime;
    
    @Schema(description = "精整内容编制人")
    private String cClearedtemp;
    
    @Schema(description = "精整内容编制时间")
    private LocalDateTime dtClearedtTime;
    
    @Schema(description = "结束人")
    private String cFinemp;
    
    @Schema(description = "是否紧急订单（0：正常订单，1：紧急订单）")
    private String cExgProdLotFl;
    
    @Schema(description = "客户名称")
    private String cCustomerCd;
    
    @Schema(description = "结束时间")
    private LocalDateTime dtFinTime;
    
    @Schema(description = "客户重要度")
    private String cCusLevel;
    
    @Schema(description = "是否下发二级")
    private String cSFl;
    
    @Schema(description = "旧编码")
    private String cOldcode;
    
    @Schema(description = "钢坯库存对应重量")
    private String cKcgp;
    
    @Schema(description = "产品宽度")
    private BigDecimal nProdWth;
    
    @Schema(description = "订单号")
    private String cOrderNo;
    
    @Schema(description = "规格代码")
    private String cItemCd;
    
    @Schema(description = "成品规格")
    private String cMatItem;
    
    @Schema(description = "厚度公差最大值")
    private BigDecimal nThkBiasMax;
    
    @Schema(description = "厚度公差最小值")
    private BigDecimal nThkBiasMin;
    
    @Schema(description = "宽度公差最大值")
    private BigDecimal nWthBiasMax;
    
    @Schema(description = "宽度公差最小值")
    private BigDecimal nWthBiasMin;
    
    @Schema(description = "成品重量最大值")
    private BigDecimal nProdWgtMax;
    
    @Schema(description = "成品重量最小值")
    private BigDecimal nProdWgtMin;
    
    @Schema(description = "长度最小")
    private BigDecimal nProdLenMin;
    
    @Schema(description = "长度最大")
    private BigDecimal nProdLenMax;
    
    @Schema(description = "计划单支重")
    private BigDecimal nPlanSingleWgt;
    
    @Schema(description = "挂单重量")
    private BigDecimal nDofinalWgt;
    
    @Schema(description = "坯料规格")
    private String cSlabItem;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;


    
    @Schema(description = "tPssRollSch子表")
    private List<TPssRollSchVo> tPssRollSchList;
    
    @Schema(description = "tPssMatRes子表")
    private List<TPssMatResVo> tPssMatResList;

}
