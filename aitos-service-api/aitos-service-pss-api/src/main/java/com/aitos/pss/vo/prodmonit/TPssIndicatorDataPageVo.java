package com.aitos.pss.vo.prodmonit;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
public class TPssIndicatorDataPageVo {

    /**
    * 主键id
    */
    @ExcelIgnore
    @Schema(description = "主键id")
    private String nId;
    /**
    * 指标ID
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("指标ID")
    @Schema(description = "指标ID")
    private Long nIndicatorId;
    /**
    * 指标code
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("指标code")
    @Schema(description = "指标code")
    private String cIndicatorCode;
    /**
    * 指标name
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("指标name")
    @Schema(description = "指标name")
    private String cIndicatorName;
    /**
    * 设备id
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("设备id")
    @Schema(description = "设备id")
    private Long nEquipmentId;
    /**
    * 设备code
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("设备code")
    @Schema(description = "设备code")
    private String cEquipmentCode;
    /**
    * 设备name
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("设备name")
    @Schema(description = "设备name")
    private String cEquipmentName;
    /**
    * 指标值
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("指标值")
    @Schema(description = "指标值")
    private BigDecimal nIndicatorValue;
    /**
    * 状态（正常/预警/违规）
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("状态（正常/预警/违规）")
    @Schema(description = "状态（正常/预警/违规）")
    @Trans(type = TransType.DIC, id = "1949765213621936130")
    private String cStatus;
    /**
    * 时间戳
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("时间戳")
    @Schema(description = "时间戳")
    private Long dtTimestamp;
    /**
    * 创建时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("创建时间")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("最后修改时间")
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;
    /**
    * 逻辑删除标记
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("逻辑删除标记")
    @Schema(description = "逻辑删除标记")
    private Integer nDeleteMark;
    /**
    * 是否有效/启用标记
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否有效/启用标记")
    @Schema(description = "是否有效/启用标记")
    private Integer nEnabledMark;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
