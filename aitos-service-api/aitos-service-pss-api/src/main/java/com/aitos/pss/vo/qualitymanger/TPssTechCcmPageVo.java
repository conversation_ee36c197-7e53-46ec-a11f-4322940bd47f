package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Data
public class TPssTechCcmPageVo {

    
    @ExcelIgnore
    @Schema(description = "主键")
    private String nId;

    @Schema(description = "质量编码id")
    private Long cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;

    @Schema(description = "质量编码名称")
    private String cQualityCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("液相线")
    @Schema(description = "液相线")
    private String nLiquidPhase;

    @Schema(description = "中包目标温度")
    private BigDecimal nMidPagTemp;

    @Schema(description = "搬出温度")
    private BigDecimal nMoveoutTime;

    @Schema(description = "起步温度")
    private BigDecimal nStartTemp;

    @Schema(description = "连浇温度")
    private BigDecimal nCastTemp;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("保护渣型号")
    @Schema(description = "保护渣型号")
    private String cFluxModel;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("钢包铸余")
    @Schema(description = "钢包铸余")
    private String nSldSurplus;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("结晶器水量")
    @Schema(description = "结晶器水量")
    private String nWaterMould;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("比水量")
    @Schema(description = "比水量")
    private String nSpecificVolume;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("中包水口")
    @Schema(description = "中包水口")
    private String cTundishNozzle;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("连铸机号")
    @Schema(description = "连铸机号")
    private String cConNo;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("首端电搅")
    @Schema(description = "首端电搅")
    private String cHeadEleSti;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("末端电搅")
    @Schema(description = "末端电搅")
    private String cTailEleSti;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("振幅")
    @Schema(description = "振幅")
    private String cAmplitude;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("振频")
    @Schema(description = "振频")
    private String cVibrationFrequency;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("液面波动")
    @Schema(description = "液面波动")
    private String cLiquidLevelFluc;
    
    @ContentStyle(dataFormat = 49)
    @ExcelProperty("是否启用")
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;

}
