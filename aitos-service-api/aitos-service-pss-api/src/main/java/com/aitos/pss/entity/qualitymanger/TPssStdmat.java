package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 质量设计性能结果查看
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Data
@TableName("t_pss_stdmat")
@Tag(name = "质量设计性能结果查看对象", description = "质量设计性能结果查看")
public class TPssStdmat implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId
    private Long nId;
    
    @Schema(description = "生产订单号")
    @TableField(value = "order_id")
    private String orderId;
    
    @Schema(description = "质量等级")
    @TableField(value = "c_std_class")
    private String cStdClass;
    
    @Schema(description = "执行标准号")
    @TableField(value = "c_op_std_name")
    private String cOpStdName;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称/描述")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "厚度最小值")
    @TableField(value = "n_thk_min")
    private BigDecimal nThkMin;
    
    @Schema(description = "厚度最大值")
    @TableField(value = "n_thk_max")
    private BigDecimal nThkMax;
    
    @Schema(description = "质检标准名称")
    @TableField(value = "c_verify_item_name")
    private String cVerifyItemName;
    
    @Schema(description = "质检标准编码")
    @TableField(value = "c_verify_item_code")
    private String cVerifyItemCode;

    @Schema(description = "质检标准编码id")
    @TableField(value = "c_verify_item_id")
    private String cVerifyItemId;
    
    @Schema(description = "下限比较符")
    @TableField(value = "c_smp_min_tag")
    private String cSmpMinTag;
    
    @Schema(description = "上限比较符")
    @TableField(value = "c_smp_max_tag")
    private String cSmpMaxTag;
    
    @Schema(description = "下限")
    @TableField(value = "c_smp_min")
    private BigDecimal cSmpMin;
    
    @Schema(description = "下限2")
    @TableField(value = "c_smp_min_2")
    private BigDecimal cSmpMin2;
    
    @Schema(description = "下下限")
    @TableField(value = "c_smp_min_min")
    private BigDecimal cSmpMinMin;
    
    @Schema(description = "下下限2")
    @TableField(value = "c_smp_min_min_2")
    private BigDecimal cSmpMinMin2;
    
    @Schema(description = "上限")
    @TableField(value = "c_smp_max")
    private BigDecimal cSmpMax;
    
    @Schema(description = "上限2")
    @TableField(value = "c_smp_max_2")
    private BigDecimal cSmpMax2;
    
    @Schema(description = "上上限")
    @TableField(value = "c_smp_max_max")
    private BigDecimal cSmpMaxMax;
    
    @Schema(description = "上上限2")
    @TableField(value = "c_smp_max_max_2")
    private BigDecimal cSmpMaxMax2;
    
    @Schema(description = "平均")
    @TableField(value = "c_smp_avg")
    private BigDecimal cSmpAvg;
    
    @Schema(description = "平均2")
    @TableField(value = "c_smp_avg_2")
    private BigDecimal cSmpAvg2;
    
    @Schema(description = "平均下限")
    @TableField(value = "c_smp_avg_min")
    private BigDecimal cSmpAvgMin;
    
    @Schema(description = "平均下限2")
    @TableField(value = "c_smp_avg_min_2")
    private BigDecimal cSmpAvgMin2;
    
    @Schema(description = "平均上限")
    @TableField(value = "c_smp_avg_max")
    private BigDecimal cSmpAvgMax;
    
    @Schema(description = "平均上限2")
    @TableField(value = "c_smp_avg_max_2")
    private BigDecimal cSmpAvgMax2;
    
    @Schema(description = "判定方法")
    @TableField(value = "c_smp_dcs_cd")
    private String cSmpDcsCd;
    
    @Schema(description = "备注")
    @TableField(value = "c_memo")
    private String cMemo;
    
    @Schema(description = "质量编码id")
    @TableField(value = "c_qual_id")
    private String cQualId;

    @Schema(description = "质量编码")
    @TableField(value = "c_qual_code")
    private String cQualCode;
    
    @Schema(description = "质量编码名称")
    @TableField(value = "c_qual_code_name")
    private String cQualCodeName;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "删除标记;默认为0,1为删除")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;


}