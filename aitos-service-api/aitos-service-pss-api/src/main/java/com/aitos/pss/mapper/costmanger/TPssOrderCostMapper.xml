<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aitos.pss.mapper.costmanger.TPssOrderCostMapper">

    <select id="queryPanel" resultType="com.aitos.pss.vo.costmanger.TPssOrderCostVo">
        SELECT
            SUM( n_sum_cost ) AS nSumCost,
            SUM( n_material_cost ) AS nMaterialCost,
            SUM( n_labor_cost ) AS nLaborCost,
            SUM( n_manufact_cost ) AS nManufactCost,
            SUM( n_unit_cost ) AS nUnitCost,
            SUM( n_estimated_profit ) AS nEstimatedProfit
        FROM
        t_pss_order_cost
        <where>

        </where>
    </select>
</mapper>