package com.aitos.pss.vo.inventory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@Data
public class TPssWareHouseLocVo {

    /**
    * 主键
    */
    @Schema(description = "主键")
    private Long nId;
    /**
    * 代码
    */
    @Schema(description = "代码")
    private String cCode;
    /**
    * 设置类型
    */
    @Schema(description = "设置类型")
    private String cType;
    /**
    * 名称
    */
    @Schema(description = "名称")
    private String cName;
    /**
    * 列（数）
    */
    @Schema(description = "列（数）")
    private Integer nCol;
    /**
    * 行（数）
    */
    @Schema(description = "行（数）")
    private Integer nRow;
    /**
    * 上级节点
    */
    @Schema(description = "上级节点")
    private String cParentCode;
    /**
    * 备注
    */
    @Schema(description = "备注")
    private String cRemark;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    private LocalDateTime dtModifyDateTime;



}
