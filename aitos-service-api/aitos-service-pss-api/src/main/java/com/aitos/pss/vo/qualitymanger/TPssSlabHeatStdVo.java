package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-05-21
* @Version 1.0
*/
@Data
public class TPssSlabHeatStdVo {


    @Schema(description = "主键")
    private Long nId;

    @Schema(description = "质量编码id")
    private Long cQualityId;

    @Schema(description = "质量编码")
    private String cQualityCode;

    @Schema(description = "质量编码名称")
    private String cQualityCodeName;

    @Schema(description = "钢种代码")
    private String cStlGrdCd;

    @Schema(description = "钢种名称/描述")
    private String cStlGrdDesc;

    @Schema(description = "坯料厚度")
    private BigDecimal nSlabThkMin;

    @Schema(description = "坯料厚度")
    private BigDecimal nSlabThkMax;

    @Schema(description = "出炉目标温度℃")
    private BigDecimal nFoutAimTemp;
    /**
    * 出炉温度上限℃
    */
    @Schema(description = "出炉温度上限℃")
    private BigDecimal nFoutMaxTemp;
    /**
    * 出炉温度下限℃
    */
    @Schema(description = "出炉温度下限℃")
    private BigDecimal nFoutMinTemp;
    /**
    * 板坯表面/中心目标温度差℃
    */
    @Schema(description = "板坯表面/中心目标温度差℃")
    private BigDecimal nSlabScTempDiff;
    /**
    * 板坯头尾目标温度差℃
    */
    @Schema(description = "板坯头尾目标温度差℃")
    private BigDecimal nSlabHtTempDiff;
    /**
    * 加热一段最低温度
    */
    @Schema(description = "加热一段最低温度")
    private BigDecimal cReserver1;
    /**
    * 加热一段最高温度
    */
    @Schema(description = "加热一段最高温度")
    private BigDecimal cReserver2;
    /**
    * 加热二段最低温度
    */
    @Schema(description = "加热二段最低温度")
    private BigDecimal cReserver3;
    /**
    * 加热二段最高温度
    */
    @Schema(description = "加热二段最高温度")
    private BigDecimal cReserver4;
    /**
    * 加热段3最低温度
    */
    @Schema(description = "加热段3最低温度")
    private BigDecimal cReserver5;
    /**
    * 加热段3最高温度
    */
    @Schema(description = "加热段3最高温度")
    private BigDecimal cReserver6;
    /**
    * 加热段4最低温度
    */
    @Schema(description = "加热段4最低温度")
    private BigDecimal cReserver7;
    /**
    * 加热段4最高温度
    */
    @Schema(description = "加热段4最高温度")
    private BigDecimal cReserver8;
    /**
    * 目标加热时长
    */
    @Schema(description = "目标加热时长")
    private BigDecimal cReserver9;
    /**
    * 备用10
    */
    @Schema(description = "备用10")
    private BigDecimal cReserver10;

    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime dtCreateDateTime;
    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
