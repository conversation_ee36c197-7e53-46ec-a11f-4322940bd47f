package com.aitos.pss.entity.qualitymanger;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
* @title: 炼钢化学成分
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Data
@TableName("t_pss_che_steel")
@Tag(name = "炼钢化学成分对象", description = "炼钢化学成分")
public class TPssCheSteel implements Serializable {

    private static final long serialVersionUID = 1L;

    
    @Schema(description = "顺序号")
    @TableId
    private Long nId;
    
    @Schema(description = "式样类型")
    @TableField(value = "c_sampletype")
    private String cSampletype;
    
    @Schema(description = "炉号")
    @TableField(value = "c_sampleid")
    private String cSampleid;
    
    @Schema(description = "写入时间")
    @TableField(value = "dt_samp_time")
    private LocalDateTime dtSampTime;
    
    @Schema(description = "钢种代码")
    @TableField(value = "c_stl_grd_cd")
    private String cStlGrdCd;
    
    @Schema(description = "钢种名称")
    @TableField(value = "c_stl_grd_desc")
    private String cStlGrdDesc;
    
    @Schema(description = "说明")
    @TableField(value = "c_commentfld")
    private String cCommentfld;
    
    @Schema(description = "取样序号/次数")
    @TableField(value = "n_smp_nums")
    private Integer nSmpNums;
    
    @Schema(description = "试样号")
    @TableField(value = "c_smp_id")
    private String cSmpId;
    
    @Schema(description = "c")
    @TableField(value = "n_c")
    private BigDecimal nC;
    
    @Schema(description = "si")
    @TableField(value = "n_si")
    private BigDecimal nSi;
    
    @Schema(description = "mn")
    @TableField(value = "n_mn")
    private BigDecimal nMn;
    
    @Schema(description = "p")
    @TableField(value = "n_p")
    private BigDecimal nP;
    
    @Schema(description = "s")
    @TableField(value = "n_s")
    private BigDecimal nS;
    
    @Schema(description = "cr")
    @TableField(value = "n_cr")
    private BigDecimal nCr;
    
    @Schema(description = "ni")
    @TableField(value = "n_ni")
    private BigDecimal nNi;
    
    @Schema(description = "cu")
    @TableField(value = "n_cu")
    private BigDecimal nCu;
    
    @Schema(description = "v")
    @TableField(value = "n_v")
    private BigDecimal nV;
    
    @Schema(description = "mo")
    @TableField(value = "n_mo")
    private BigDecimal nMo;
    
    @Schema(description = "ti")
    @TableField(value = "n_ti")
    private BigDecimal nTi;
    
    @Schema(description = "nb")
    @TableField(value = "n_nb")
    private BigDecimal nNb;
    
    @Schema(description = "ceq")
    @TableField(value = "n_ceq")
    private BigDecimal nCeq;
    
    @Schema(description = "als")
    @TableField(value = "n_als")
    private BigDecimal nAls;
    
    @Schema(description = "al")
    @TableField(value = "n_al")
    private BigDecimal nAl;
    
    @Schema(description = "ca")
    @TableField(value = "n_ca")
    private BigDecimal nCa;
    
    @Schema(description = "b")
    @TableField(value = "n_b")
    private BigDecimal nB;
    
    @Schema(description = "pb")
    @TableField(value = "n_pb")
    private BigDecimal nPb;
    
    @Schema(description = "zr")
    @TableField(value = "n_zr")
    private BigDecimal nZr;
    
    @Schema(description = "w")
    @TableField(value = "n_w")
    private BigDecimal nW;
    
    @Schema(description = "asfld")
    @TableField(value = "n_asfld")
    private BigDecimal nAsfld;
    
    @Schema(description = "sn")
    @TableField(value = "n_sn")
    private BigDecimal nSn;
    
    @Schema(description = "co")
    @TableField(value = "n_co")
    private BigDecimal nCo;
    
    @Schema(description = "bi")
    @TableField(value = "n_bi")
    private BigDecimal nBi;
    
    @Schema(description = "zn")
    @TableField(value = "n_zn")
    private BigDecimal nZn;
    
    @Schema(description = "sb")
    @TableField(value = "n_sb")
    private BigDecimal nSb;
    
    @Schema(description = "la")
    @TableField(value = "n_la")
    private BigDecimal nLa;
    
    @Schema(description = "ce")
    @TableField(value = "n_ce")
    private BigDecimal nCe;
    
    @Schema(description = "n")
    @TableField(value = "n_n")
    private BigDecimal nN;
    
    @Schema(description = "bs")
    @TableField(value = "n_bs")
    private BigDecimal nBs;
    
    @Schema(description = "aln")
    @TableField(value = "n_aln")
    private BigDecimal nAln;
    
    @Schema(description = "mg")
    @TableField(value = "n_mg")
    private BigDecimal nMg;
    
    @Schema(description = "ct")
    @TableField(value = "n_ct")
    private BigDecimal nCt;
    
    @Schema(description = "c_g")
    @TableField(value = "n_c_g")
    private Integer nCG;
    
    @Schema(description = "si_g")
    @TableField(value = "n_si_g")
    private Integer nSiG;
    
    @Schema(description = "mn_g")
    @TableField(value = "n_mn_g")
    private Integer nMnG;
    
    @Schema(description = "p_g")
    @TableField(value = "n_p_g")
    private Integer nPG;
    
    @Schema(description = "s_g")
    @TableField(value = "n_s_g")
    private Integer nSG;
    
    @Schema(description = "ceq_g")
    @TableField(value = "n_ceq_g")
    private Integer nCeqG;
    
    @Schema(description = "cr_g")
    @TableField(value = "n_cr_g")
    private Integer nCrG;
    
    @Schema(description = "ni_g")
    @TableField(value = "n_ni_g")
    private Integer nNiG;
    
    @Schema(description = "cu_g")
    @TableField(value = "n_cu_g")
    private Integer nCuG;
    
    @Schema(description = "v_g")
    @TableField(value = "n_v_g")
    private Integer nVG;
    
    @Schema(description = "mo_g")
    @TableField(value = "n_mo_g")
    private Integer nMoG;
    
    @Schema(description = "ti_g")
    @TableField(value = "n_ti_g")
    private Integer nTiG;
    
    @Schema(description = "nb_g")
    @TableField(value = "n_nb_g")
    private Integer nNbG;
    
    @Schema(description = "als_g")
    @TableField(value = "n_als_g")
    private Integer nAlsG;
    
    @Schema(description = "al_g")
    @TableField(value = "n_al_g")
    private Integer nAlG;
    
    @Schema(description = "ca_g")
    @TableField(value = "n_ca_g")
    private Integer nCaG;
    
    @Schema(description = "b_g")
    @TableField(value = "n_b_g")
    private Integer nBG;
    
    @Schema(description = "pb_g")
    @TableField(value = "n_pb_g")
    private Integer nPbG;
    
    @Schema(description = "zr_g")
    @TableField(value = "n_zr_g")
    private Integer nZrG;
    
    @Schema(description = "w_g")
    @TableField(value = "n_w_g")
    private Integer nWG;
    
    @Schema(description = "asfld_g")
    @TableField(value = "n_asfld_g")
    private Integer nAsfldG;
    
    @Schema(description = "sn_g")
    @TableField(value = "n_sn_g")
    private Integer nSnG;
    
    @Schema(description = "co_g")
    @TableField(value = "n_co_g")
    private Integer nCoG;
    
    @Schema(description = "bi_g")
    @TableField(value = "n_bi_g")
    private Integer nBiG;
    
    @Schema(description = "sb_g")
    @TableField(value = "n_sb_g")
    private Integer nSbG;
    
    @Schema(description = "zn_g")
    @TableField(value = "n_zn_g")
    private Integer nZnG;
    
    @Schema(description = "la_g")
    @TableField(value = "n_la_g")
    private Integer nLaG;
    
    @Schema(description = "ce_g")
    @TableField(value = "n_ce_g")
    private Integer nCeG;
    
    @Schema(description = "n_g")
    @TableField(value = "n_n_g")
    private Integer nNG;
    
    @Schema(description = "bs_g")
    @TableField(value = "n_bs_g")
    private Integer nBsG;
    
    @Schema(description = "aln_g")
    @TableField(value = "n_aln_g")
    private Integer nAlnG;
    
    @Schema(description = "mg_g")
    @TableField(value = "n_mg_g")
    private Integer nMgG;
    
    @Schema(description = "ct_g")
    @TableField(value = "n_ct_g")
    private Integer nCtG;
    
    @Schema(description = "质量等级")
    @TableField(value = "c_judge")
    private String cJudge;
    
    @Schema(description = "o")
    @TableField(value = "n_o")
    private BigDecimal nO;

    @Schema(description = "o")
    @TableField(value = "n_o_g")
    private BigDecimal nOG;
    
    @Schema(description = "h")
    @TableField(value = "n_h")
    private BigDecimal nH;

    @Schema(description = "h")
    @TableField(value = "n_h_g")
    private BigDecimal nHG;
    
    @Schema(description = "质量编码id")
    @TableField(value = "c_mat_qul_id")
    private Long cMatQulId;

    @Schema(description = "质量编码")
    @TableField(value = "c_mat_qul_code")
    private String cMatQulCode;

    @Schema(description = "质量编码name")
    @TableField(value = "c_mat_qul_name")
    private String cMatQulName;
    
    @Schema(description = "创建人")
    @TableField(value = "n_create_user_id", fill = FieldFill.INSERT)
    private Long nCreateUserId;
    
    @Schema(description = "创建时间")
    @TableField(value = "dt_create_date_time", fill = FieldFill.INSERT)
    private LocalDateTime dtCreateDateTime;
    
    @Schema(description = "最后修改人")
    @TableField(value = "n_modify_user_id", fill = FieldFill.UPDATE)
    private Long nModifyUserId;
    
    @Schema(description = "最后修改时间")
    @TableField(value = "dt_modify_date_time", fill = FieldFill.UPDATE)
    private LocalDateTime dtModifyDateTime;
    
    @Schema(description = "逻辑删除标记")
    @TableField(value = "n_delete_mark", fill = FieldFill.INSERT)
    @TableLogic
    private Integer nDeleteMark;
    
    @Schema(description = "是否有效/启用标记")
    @TableField(value = "n_enabled_mark", fill = FieldFill.INSERT)
    private Integer nEnabledMark;


}