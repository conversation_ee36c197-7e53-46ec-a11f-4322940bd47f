package com.aitos.pss.vo.qualitymanger;

import com.aitos.common.core.annotation.Trans;
import com.aitos.common.core.enums.TransType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/
@Data
public class TPssStdchemLgPageVo {

    @Schema(description = "主键")
    private Long nId;
    
    @Schema(description = "质量等级")
    @Trans(type = TransType.DIC, id = "1925391913657634818")
    private String cStdClass;

    @Schema(description = "质量编码id")
    private Long cQualityId;
    
    @Schema(description = "质量编码")
    private String cQualityCode;
    
    @Schema(description = "质量编码名称")
    private String cQualityCodeName;

    @Schema(description = "牌号代码")
    private String cStlGrdCd;

    @Schema(description = "牌号")
    private String cStlGrdDesc;
    
    @Schema(description = "下限比较符")
    @Trans(type = TransType.DIC, id = "1925391187640389633")
    private String cCompCodeMin;
    
    @Schema(description = "上限比较符")
    @Trans(type = TransType.DIC, id = "1925391187640389633")
    private String cCompCodeMax;
    
    @Schema(description = "化学成分代码")
    private String cChemCompCd;
    
    @Schema(description = "化学成分最小值")
    private BigDecimal cChemCompMin;
    
    @Schema(description = "化学成分最大值")
    private BigDecimal cChemCompMax;
    
    @Schema(description = "是否启用;默认为0,1为未启用")
    private Integer nEnabledMark;

    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;

    
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

    @Schema(description = "创建人")
    @Trans(type = TransType.USER,transForPropertyName = "nCreateUserName")
    private Long nCreateUserId;

    @Schema(description = "修改人")
    @Trans(type = TransType.USER,transForPropertyName = "nModifyUserName")
    private Long nModifyUserId;

    @Schema(description = "创建人")
    private String nCreateUserName;

    @Schema(description = "修改人")
    private String nModifyUserName;
}
