package com.aitos.pss.dto.prodmonit;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;



/**
* @title: 设备管理
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Data
public class AddTPssEquipmentsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 设备名称
    */
    @Schema(description = "设备名称")
    private String cName;
    /**
    * 设备代码
    */
    @Schema(description = "设备代码")
    private String cCode;
    /**
    * 设备类型
    */
    @Schema(description = "设备类型")
    private String cType;
    /**
    * 位置
    */
    @Schema(description = "位置")
    private String cLocation;
    /**
    * 状态
    */
    @Schema(description = "状态")
    private String cStatus;
    /**
    * 描述
    */
    @Schema(description = "描述")
    private String cDescription;
    /**
    * 制造商
    */
    @Schema(description = "制造商")
    private String cManufacturer;
    /**
    * 型号
    */
    @Schema(description = "型号")
    private String cModel;
    /**
    * 序列号
    */
    @Schema(description = "序列号")
    private String cSerialNumber;
    /**
    * 购买日期
    */
    @Schema(description = "购买日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtPurchaseDate;
    /**
    * 保修期（月）
    */
    @Schema(description = "保修期（月）")
    private Integer nWarrantyPeriod;
    /**
    * 最后维护时间
    */
    @Schema(description = "最后维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtLastMaintainDateTime;
    /**
    * 下次维护时间
    */
    @Schema(description = "下次维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtNextMaintainDateTime;
    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtCreateDateTime;
    /**
    * 最后修改时间
    */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dtModifyDateTime;

}
