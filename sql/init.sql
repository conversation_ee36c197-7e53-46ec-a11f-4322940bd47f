-- t_pss_aggregate_plan DDL
CREATE TABLE `t_pss_aggregate_plan` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                                     `c_aggregate_plan_id` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "综合生产计划编号(AP+合并计划号)",
                                     `c_exg_prod_lot_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否紧急",
                                     `c_product_task_list_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "任务单号",
                                     `c_product_type` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品类型(P0003)",
                                     `c_mat_qul_id` BIGINT(19) NULL Comment "质量编码id",
                                     `c_mat_qul_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                     `c_mat_qul_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                     `c_size_property` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "定尺类型",
                                     `n_prod_dia` DECIMAL(15,4) NULL Comment "产品直径",
                                     `n_prod_thk` DECIMAL(15,4) NULL Comment "产品厚度",
                                     `n_prod_wid` DECIMAL(15,4) NULL Comment "产品宽度",
                                     `n_prod_lenth` DECIMAL(15,4) NULL Comment "产品长度（计划）",
                                     `n_plan_wgt` DECIMAL(15,4) NULL Comment "计划产量",
                                     `c_wgt_unit` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "重量单位",
                                     `c_seat` BIGINT(19) NULL Comment "连铸机id",
                                     `c_seat_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸机code",
                                     `c_seat_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸机name",
                                     `c_del_datetime_from` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货期起始",
                                     `c_del_datetime_end` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货期结束",
                                     `c_customer_cd` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "客户编码",
                                     `c_plan_state` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划状态",
                                     `n_plan_slab_thk` DECIMAL(15,4) NULL Comment "计划坯料厚度",
                                     `n_plan_slab_wid` DECIMAL(15,4) NULL Comment "计划坯料宽度",
                                     `n_plan_slab_len` DECIMAL(15,4) NULL Comment "计划坯料长",
                                     `c_plan_line_id` BIGINT(19) NULL Comment "计划产线id(轧钢)(P0001)",
                                     `c_plan_line_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划产线编码(轧钢)(P0001)",
                                     `c_plan_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划产线名称(轧钢)(P0001)",
                                     `c_plan_steel_line_id` BIGINT(19) NULL Comment "计划产线id（炼钢）默认一炼钢(P0001)",
                                     `c_plan_steel_line_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划产线编码（炼钢）默认一炼钢(P0001)",
                                     `c_plan_steel_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划产线名称（炼钢）默认一炼钢(P0001)",
                                     `c_std_spec` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "标准号",
                                     `c_stl_grd_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                     `c_stl_grd_desc` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述/名称",
                                     `c_inventory_id` BIGINT(19) NULL Comment "存货代码id",
                                     `c_inventory_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货代码",
                                     `c_inventory_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货代码name",
                                     `c_finish_mach_ph_cd` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精整路径",
                                     `c_plan_datetime_to` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划起始日期",
                                     `c_plan_datetime_from` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划终止日期",
                                     `n_plan_slab_count` DECIMAL(15,4) NULL Comment "计划坯料总数",
                                     `n_plan_furance_count` DECIMAL(15,4) NULL Comment "计划炉数",
                                     `n_plan_slab_smw` DECIMAL(15,4) NULL Comment "计划坯料单米重",
                                     `n_plan_yeild` DECIMAL(15,4) NULL Comment "计划成才率",
                                     `n_plan_slab_wgt_count` DECIMAL(15,4) NULL Comment "计划坯料总重量",
                                     `c_sales_plan_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售计划号",
                                     `c_sales_plan_sn` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售计划顺序号",
                                     `n_plan_steel_wgt` DECIMAL(15,4) NULL Comment "计划转炉冶炼量",
                                     `c_old_status` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上一个状态",
                                     `c_operation_note` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "操作记录",
                                     `c_is_add_plan` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否追加计划",
                                     `c_order_explain` VARCHAR(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单备注",
                                     `dt_state_change_time` DATETIME(3) NULL Comment "状态变更时间",
                                     `c_old_operation_note` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上一次的操作记录",
                                     `c_order_cust_type` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单客户类型",
                                     `c_oldcode` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "旧编码",
                                     `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                                     `c_item_cd` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格代码",
                                     `n_mat_item` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成品规格",
                                     `n_thk_bias_max` DECIMAL(15,4) NULL Comment "厚度公差最大值",
                                     `n_thk_bias_min` DECIMAL(15,4) NULL Comment "厚度公差最小值",
                                     `n_wth_bias_max` DECIMAL(15,4) NULL Comment "宽度公差最大值",
                                     `n_wth_bias_min` DECIMAL(15,4) NULL Comment "宽度公差最小值",
                                     `n_prod_wgt_max` DECIMAL(15,4) NULL Comment "成品重量最大值",
                                     `n_prod_wgt_min` DECIMAL(15,4) NULL Comment "成品重量最小值",
                                     `n_prod_len_min` DECIMAL(15,4) NULL Comment "长度最小",
                                     `n_prod_len_max` DECIMAL(15,4) NULL Comment "长度最大",
                                     `n_plan_single_wgt` DECIMAL(15,4) NULL Comment "替代重量",
                                     `n_enabled_mark` INT(10) NULL,
                                     `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                     `dt_create_date_time` DATETIME NULL,
                                     `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                     `dt_modify_date_time` DATETIME NULL,
                                     `n_delete_mark` INT(10) NULL DEFAULT 0 Comment "删除标记;默认为0,1为删除",
                                     PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "综合生产计划表";
-- t_pss_b_mat_ro_out_hmes DDL
CREATE TABLE `t_pss_b_mat_ro_out_hmes` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                        `c_mat_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "坯料号/件次号",
                                        `c_mat_id_mth` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "母坯料号/件次号",
                                        `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                        `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种",
                                        `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码",
                                        `c_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                        `c_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                        `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                        `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                                        `n_mat_wth` DECIMAL(12,3) NULL Comment "宽度",
                                        `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                                        `n_mat_len` DECIMAL(12,3) NULL Comment "长度",
                                        `n_mat_wgt` DECIMAL(12,3) NULL Comment "实际重量",
                                        `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "计算重量",
                                        `c_reout_type` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "退出类型",
                                        `c_reout_rsn` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "退出原因",
                                        `c_mat_to` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "去向",
                                        `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                                        `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班别",
                                        `dt_accept_time` DATETIME NULL Comment "确认接收时间",
                                        `c_buy_order_no` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "采购凭证号",
                                        `c_buy_order_item` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "采购凭证行项目编号",
                                        `c_status` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态0-待接收1-已接收",
                                        `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                        `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                        `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                        `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                        `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                        `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                        PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "轧钢退库记录表";
-- t_pss_b_slab_accept_lg DDL
CREATE TABLE `t_pss_b_slab_accept_lg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                       `c_slab_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "坯号",
                                       `c_heat_id` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                                       `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种",
                                       `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码",
                                       `c_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                       `c_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                       `c_slab_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "坯料类型",
                                       `n_slab_thk` DECIMAL(8,1) NULL Comment "厚度",
                                       `n_slab_wth` DECIMAL(8,1) NULL Comment "宽度",
                                       `n_slab_lth` DECIMAL(8,1) NULL Comment "长度",
                                       `n_slab_wgt` DECIMAL(15,3) NULL Comment "检斤重量",
                                       `c_cert_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                       `c_acpt_rst` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "确认结果 (1接收，0拒绝)",
                                       `c_memo` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                       `c_loc` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                                       `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                       `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                       `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                       `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                       `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                       `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                       PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "炼钢坯料接收记录表";
-- t_pss_batch_sampling DDL
CREATE TABLE `t_pss_batch_sampling` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                                     `c_heat_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉次号",
                                     `c_stl_grd_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                     `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称",
                                     `c_spec` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                     `c_smp_lot` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验批号",
                                     `c_quality_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                     `c_line_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                     `n_thk` DECIMAL(10,3) NULL Comment "物料长度",
                                     `n_samp_num` INT(10) NULL Comment "委托次数",
                                     `c_testcrd_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "委托单号",
                                     `c_smp_heat_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验炉次号",
                                     `c_smp_states` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "委托单状态（A：等待发送、B：等待试验、C：试验完毕、D：性能判定完毕）",
                                     `c_samp_heat_id_list` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验批次包含炉次号",
                                     `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                     `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                     `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                     `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                     `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                     `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                     PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 101 ROW_FORMAT = Dynamic COMMENT = "物料组批取样主表";
-- t_pss_c_mat_balance_zg DDL
CREATE TABLE `t_pss_c_mat_balance_zg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                       `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "件次号",
                                       `c_loc` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                                       `c_balance_type` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "盘库类型",
                                       `c_balance_operation` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "盘库说明",
                                       `n_mat_wgt` DECIMAL(12,3) NULL Comment "盘库重量",
                                       `dt_balance_time` DATETIME NULL Comment "盘库时间",
                                       `c_balance_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "盘库人",
                                       `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                                       `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班别",
                                       `c_mattype` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                       `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                       `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                       `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                       `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                       `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                       `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                       INDEX `idx_c_balance_type`(`c_balance_type` ASC) USING BTREE,
                                       INDEX `idx_c_mat_id`(`c_mat_id` ASC) USING BTREE,
                                       INDEX `idx_dt_balance_time`(`dt_balance_time` ASC) USING BTREE,
                                       PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "轧钢盘库记录表";
-- t_pss_c_transfer_zg DDL
CREATE TABLE `t_pss_c_transfer_zg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                    `c_line_cd` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                    `c_yard_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库代码",
                                    `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "件次号",
                                    `c_prod_lot_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "批次号",
                                    `c_source_area` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "源地",
                                    `c_source_area_lev` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "源地层号",
                                    `c_target_area` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地",
                                    `c_target_area_lev` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地层号",
                                    `c_memo` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                    `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                                    `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班别",
                                    `c_mattype` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                    `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                    `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                    `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                    `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "倒垛记录";
-- t_pss_cast_cnvts DDL
CREATE TABLE `t_pss_cast_cnvts` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                 `c_mat_qul_id` BIGINT(19) NULL Comment "质量编码id",
                                 `c_mat_qul_cd` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                 `c_mat_qul_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                 `c_stl_grd_cd` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                 `c_stl_grd_desc` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述/名称",
                                 `c_pro_line` BIGINT(19) NULL Comment "产线",
                                 `c_pro_line_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线编码",
                                 `c_pro_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线名称",
                                 `n_heatnum_min` DECIMAL(15,4) NULL Comment "炉数下限",
                                 `n_heatnum_max` DECIMAL(15,4) NULL Comment "炉数上限",
                                 `n_enabled_mark` INT(10) NULL,
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME NULL,
                                 `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                 `dt_modify_date_time` DATETIME NULL,
                                 `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1947197508147687427 ROW_FORMAT = Dynamic COMMENT = "浇次炉数编制标准;";
-- t_pss_castp_edit DDL
CREATE TABLE `t_pss_castp_edit` (`n_id` BIGINT(19) NOT NULL Comment "序号",
                                 `n_cast_edt_seq` BIGINT(19) NOT NULL Comment "浇次编制号",
                                 `c_plan_cast_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划浇次号",
                                 `n_slab_thk` DECIMAL(15,4) NULL Comment "浇铸厚度",
                                 `n_slab_wth` DECIMAL(15,4) NULL Comment "浇铸宽度",
                                 `dt_start_dt` DATETIME(3) NULL Comment "计划开始时间",
                                 `dt_end_dt` DATETIME(3) NULL Comment "计划结束时间",
                                 `c_cast_mach_id` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "铸机号",
                                 `n_heat_cnt` BIGINT(19) NULL Comment "炉数",
                                 `n_big_cast_seq` DECIMAL(15,4) NULL Comment "大浇次序号",
                                 `c_status` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                                 `n_enabled_mark` INT(10) NULL,
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME NULL,
                                 `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                 `dt_modify_date_time` DATETIME NULL,
                                 `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "浇次计划编制表";
-- t_pss_cfg_model_cofig DDL
CREATE TABLE `t_pss_cfg_model_cofig` (`n_id` BIGINT(19) NOT NULL Comment "顺序号",
                                      `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "钢种代码",
                                      `c_stl_grd_name` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "钢种名称",
                                      `c_types` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品类型",
                                      `c_model_line` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "模版链接",
                                      `c_bk_up1` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用字段1",
                                      `c_bk_up2` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用字段2",
                                      `c_bk_up3` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用字段3",
                                      `c_bk_up4` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用字段4",
                                      `c_bk_up5` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用字段5",
                                      `c_bk_up6` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用字段6",
                                      `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                      `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                      `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                      `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                      `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                      `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                      PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "模版连接配置表";
-- t_pss_che_steel DDL
CREATE TABLE `t_pss_che_steel` (`n_id` BIGINT(19) NOT NULL Comment "顺序号",
                                `c_sampletype` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "式样类型",
                                `c_sampleid` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                                `dt_samp_time` DATE NULL Comment "写入时间",
                                `c_stl_grd_cd` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称",
                                `c_commentfld` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "说明",
                                `n_smp_nums` INT(10) NULL Comment "取样序号/次数",
                                `c_smp_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "试样号",
                                `n_c` DECIMAL(11,4) NULL Comment "c",
                                `n_si` DECIMAL(11,4) NULL Comment "si",
                                `n_mn` DECIMAL(11,4) NULL Comment "mn",
                                `n_p` DECIMAL(11,4) NULL Comment "p",
                                `n_s` DECIMAL(11,4) NULL Comment "s",
                                `n_cr` DECIMAL(11,4) NULL Comment "cr",
                                `n_ni` DECIMAL(11,4) NULL Comment "ni",
                                `n_cu` DECIMAL(11,4) NULL Comment "cu",
                                `n_v` DECIMAL(11,4) NULL Comment "v",
                                `n_mo` DECIMAL(11,4) NULL Comment "mo",
                                `n_ti` DECIMAL(11,4) NULL Comment "ti",
                                `n_nb` DECIMAL(11,4) NULL Comment "nb",
                                `n_ceq` DECIMAL(11,4) NULL Comment "ceq",
                                `n_als` DECIMAL(11,4) NULL Comment "als",
                                `n_al` DECIMAL(11,4) NULL Comment "al",
                                `n_ca` DECIMAL(11,4) NULL Comment "ca",
                                `n_b` DECIMAL(11,4) NULL Comment "b",
                                `n_pb` DECIMAL(11,4) NULL Comment "pb",
                                `n_zr` DECIMAL(11,4) NULL Comment "zr",
                                `n_w` DECIMAL(11,4) NULL Comment "w",
                                `n_asfld` DECIMAL(11,4) NULL Comment "asfld",
                                `n_sn` DECIMAL(11,4) NULL Comment "sn",
                                `n_co` DECIMAL(11,4) NULL Comment "co",
                                `n_bi` DECIMAL(11,4) NULL Comment "bi",
                                `n_zn` DECIMAL(11,4) NULL Comment "zn",
                                `n_sb` DECIMAL(11,4) NULL Comment "sb",
                                `n_la` DECIMAL(11,4) NULL Comment "la",
                                `n_ce` DECIMAL(11,4) NULL Comment "ce",
                                `n_n` DECIMAL(11,4) NULL Comment "n",
                                `n_bs` DECIMAL(11,4) NULL Comment "bs",
                                `n_aln` DECIMAL(11,4) NULL Comment "aln",
                                `n_mg` DECIMAL(11,4) NULL Comment "mg",
                                `n_ct` DECIMAL(11,4) NULL Comment "ct",
                                `n_c_g` INT(10) NULL Comment "c_g",
                                `n_si_g` INT(10) NULL Comment "si_g",
                                `n_mn_g` INT(10) NULL Comment "mn_g",
                                `n_p_g` INT(10) NULL Comment "p_g",
                                `n_s_g` INT(10) NULL Comment "s_g",
                                `n_ceq_g` INT(10) NULL Comment "ceq_g",
                                `n_cr_g` INT(10) NULL Comment "cr_g",
                                `n_ni_g` INT(10) NULL Comment "ni_g",
                                `n_cu_g` INT(10) NULL Comment "cu_g",
                                `n_v_g` INT(10) NULL Comment "v_g",
                                `n_mo_g` INT(10) NULL Comment "mo_g",
                                `n_ti_g` INT(10) NULL Comment "ti_g",
                                `n_nb_g` INT(10) NULL Comment "nb_g",
                                `n_als_g` INT(10) NULL Comment "als_g",
                                `n_al_g` INT(10) NULL Comment "al_g",
                                `n_ca_g` INT(10) NULL Comment "ca_g",
                                `n_b_g` INT(10) NULL Comment "b_g",
                                `n_pb_g` INT(10) NULL Comment "pb_g",
                                `n_zr_g` INT(10) NULL Comment "zr_g",
                                `n_w_g` INT(10) NULL Comment "w_g",
                                `n_asfld_g` INT(10) NULL Comment "asfld_g",
                                `n_sn_g` INT(10) NULL Comment "sn_g",
                                `n_co_g` INT(10) NULL Comment "co_g",
                                `n_bi_g` INT(10) NULL Comment "bi_g",
                                `n_sb_g` INT(10) NULL Comment "sb_g",
                                `n_zn_g` INT(10) NULL Comment "zn_g",
                                `n_la_g` INT(10) NULL Comment "la_g",
                                `n_ce_g` INT(10) NULL Comment "ce_g",
                                `n_n_g` INT(10) NULL Comment "n_g",
                                `n_bs_g` INT(10) NULL Comment "bs_g",
                                `n_aln_g` INT(10) NULL Comment "aln_g",
                                `n_mg_g` INT(10) NULL Comment "mg_g",
                                `n_ct_g` INT(10) NULL Comment "ct_g",
                                `c_judge` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量等级",
                                `n_o` DECIMAL(11,4) NULL Comment "o",
                                `n_o_g` DECIMAL(11,4) NULL Comment "o",
                                `n_h_g` DECIMAL(11,4) NULL Comment "o",
                                `n_h` DECIMAL(11,4) NULL Comment "h",
                                `c_mat_qul_id` BIGINT(19) NULL Comment "质量编码id",
                                `c_mat_qul_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码code",
                                `c_mat_qul_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码name",
                                `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                `n_enabled_mark` INT(10) NULL,
                                PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "炼钢化学成分表";
-- t_pss_cheat_plan DDL
CREATE TABLE `t_pss_cheat_plan` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                                 `n_heat_edt_seq` DECIMAL(15,4) NOT NULL Comment "炉次编制号",
                                 `c_plan_heat_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划炉次号",
                                 `c_stl_grd_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                 `c_stl_grd_desc` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述/名称",
                                 `c_mat_qul_id` BIGINT(19) NULL Comment "质量编码id",
                                 `c_mat_qul_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                 `c_mat_qul_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                 `c_pl_route` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炼钢工艺流程",
                                 `n_ccm_thk` DECIMAL(15,4) NULL Comment "浇铸厚度",
                                 `n_ccm_wth` DECIMAL(15,4) NULL Comment "浇铸宽度",
                                 `n_ccm_tme` BIGINT(19) NULL Comment "浇铸时长",
                                 `n_ord_slab_cnt` BIGINT(19) NULL Comment "订单板坯张数",
                                 `n_woo_slab_cnt` BIGINT(19) NULL Comment "余材板坯张数",
                                 `c_woo_heat_fl` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "余材炉次代码",
                                 `n_slab_cnt` BIGINT(19) NULL Comment "板坯块数",
                                 `n_pre_heat_wgt` DECIMAL(15,4) NULL Comment "计划出钢量",
                                 `c_sms_duedatetime` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炼钢作业期限",
                                 `n_cast_edt_seq` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "浇次编制号",
                                 `c_plan_cast_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划浇次号",
                                 `n_cast_heat_seq` BIGINT(19) NULL Comment "浇次内顺序号",
                                 `n_cast_heat_cnt` BIGINT(19) NULL Comment "浇次炉数",
                                 `c_ld_wkst_id` BIGINT(19) NULL Comment "转炉炉座号id",
                                 `c_ld_wkst` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "转炉炉座号",
                                 `c_ld_wkst_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "转炉炉座号名称",
                                 `dt_pre_ld_time` DATETIME NULL Comment "下达时间",
                                 `dt_pre_ld_str_tme` DATETIME NULL Comment "计划出钢开始时间",
                                 `dt_pre_ld_end_tme` DATETIME NULL Comment "计划出钢结束时间",
                                 `c_fir_lf_wkst_id` BIGINT(19) NULL Comment "计划第一次LF炉座号id",
                                 `c_fir_lf_wkst` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第一次LF炉座号",
                                 `c_fir_lf_wkst_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第一次LF炉座号名称",
                                 `dt_fir_lf_sttime` DATETIME NULL Comment "计划第一次 LF 开始时间",
                                 `dt_fir_lf_endtime` DATETIME NULL Comment "计划第一次 LF 结束时间",
                                 `c_sec_lf_wkst_id` BIGINT(19) NULL Comment "计划第二次LF炉座号id",
                                 `c_sec_lf_wkst` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第二次LF炉座号",
                                 `c_sec_lf_wkst_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第二次LF炉座号名称",
                                 `dt_sec_lf_sttime` DATETIME NULL Comment "计划第二次 LF 开始时间",
                                 `dt_sec_lf_endtime` DATETIME NULL Comment "计划第二次 LF 结束时间",
                                 `c_fir_vd_wkst_id` BIGINT(19) NULL Comment "计划第一次RH炉座号id",
                                 `c_fir_vd_wkst` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第一次RH炉座号",
                                 `c_fir_vd_wkst_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第一次RH炉座号名称",
                                 `dt_fir_vd_sttime` DATETIME NULL Comment "计划第一次RH开始时间",
                                 `dt_fir_vd_endtime` DATETIME NULL Comment "计划第一次RH结束时间",
                                 `c_sec_vd_wkst_id` BIGINT(19) NULL Comment "计划第二次RH炉座号id",
                                 `c_sec_vd_wkst` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第二次RH炉座号",
                                 `c_sec_vd_wkst_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第二次RH炉座号名称",
                                 `dt_sec_vd_sttime` DATETIME NULL Comment "计划第二次RH 开始时间",
                                 `dt_sec_vd_endtime` DATETIME NULL Comment "计划第二次RH结束时间",
                                 `dt_plan_ladle_open` DATETIME(3) NULL Comment "大包开浇时间",
                                 `c_ccm_wkst` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸机号",
                                 `dt_pre_ccm_str_tme` DATETIME(3) NULL Comment "计划浇铸开始时间",
                                 `dt_pre_ccm_end_tme` DATETIME(3) NULL Comment "计划浇铸结束时间",
                                 `c_mstlgrd_fl` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "混炉标志",
                                 `c_status` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                                 `c_heat_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "实绩炉次号",
                                 `c_act_stl_grd_cd` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "实绩钢种",
                                 `c_act_stl_grd_desc` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "实绩钢种描述/名称",
                                 `c_act_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "实绩材质",
                                 `c_cast_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸机号",
                                 `c_huntie_wks` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "读取标志 N-新计划",
                                 `c_yuchuli_wks` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "米重",
                                 `c_curr_station_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "当前工位",
                                 `c_curr_event_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "当前事件（P0210）",
                                 `c_prev_event_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "前一事件（P0210）",
                                 `n_slab_len` DECIMAL(15,4) NULL Comment "坯料长度",
                                 `c_task_list_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "任务单号",
                                 `n_if_change_time` BIGINT(19) NULL Comment "转炉时间是否改变",
                                 `n_if_change_time_lf` BIGINT(19) NULL Comment "精炼时间是否改变",
                                 `n_if_change_time_vod` BIGINT(19) NULL Comment "VOD时间是否改变",
                                 `n_if_change_time_ccm` BIGINT(19) NULL Comment "连铸时间是否改变",
                                 `n_prod_thk` DECIMAL(15,4) NULL Comment "产品直径",
                                 `n_prod_len` DECIMAL(15,4) NULL Comment "产品长度",
                                 `c_ld_status` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电炉状态",
                                 `c_lf_status` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精炼状态",
                                 `c_vd_status` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "VD状态",
                                 `c_ccm_status` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸状态",
                                 `c_ld_time` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电炉时间",
                                 `c_lf_time` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精炼时间",
                                 `c_vd_time` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "VD时间",
                                 `c_ccm_time` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸时间",
                                 `c_ccm_str_time` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸开始时间",
                                 `c_vd_str_time` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "VD开始时间",
                                 `c_lf_str_time` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精炼开始时间",
                                 `c_ld_str_time` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电炉开始时间",
                                 `n_deal_flag` DECIMAL(15,4) NULL Comment "炉次制造命令处理区分",
                                 `c_pono` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "制造命令号",
                                 `c_cast_lot_div_no` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "CAST_LOT分割号",
                                 `c_cc_type` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸机类型 PM44",
                                 `c_restrand_flg` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "甘特图显示标志",
                                 `c_smelt_mode` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "冶炼模式",
                                 `c_dest` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材料去向 PM16",
                                 `c_plan_datetime` DATETIME NULL Comment "计划日期",
                                 `c_customer_cd` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "客户编码",
                                 `c_customer_name` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "客户名称",
                                 `c_ccm_len` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "浇铸长度",
                                 `c_return_heat_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢水回炉目标炉号(改为支数确认标记)",
                                 `c_return_direction` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢水回炉去向(改为代表样确认标记)",
                                 `c_dest2` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材料去向2",
                                 `c_ccm_wgt` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "定重",
                                 `c_ld_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢包号",
                                 `c_len_is_change` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否变定尺",
                                 `c_stl_grd_change` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否变钢种标记",
                                 `c_mon_pro_seq` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "月生产的第几炉",
                                 `n_ld_tme` BIGINT(19) NULL Comment "转炉作业时长",
                                 `c_is_first_plan` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否浇次首炉",
                                 `c_prod_shift` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产班次",
                                 `c_prod_group` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产班组",
                                 `c_ld_stdtime` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "转炉标准时长",
                                 `st_before_ldendtime` DATETIME(3) NULL Comment "前一炉转炉结束时间",
                                 `c_ccm_stdtime` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸标准时长",
                                 `dt_before_ccmendtime` DATETIME(3) NULL Comment "前一炉连铸结束时间",
                                 `n_enabled_mark` INT(10) NULL,
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME NULL,
                                 `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                 `dt_modify_date_time` DATETIME NULL,
                                 `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "炉次指示表（炉次计划表）";
-- t_pss_chp_edit DDL
CREATE TABLE `t_pss_chp_edit` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                               `n_heat_edt_seq` DECIMAL(15,4) NOT NULL Comment "炉次编制号",
                               `c_plan_heat_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划炉次号",
                               `c_stl_grd_cd` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                               `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                               `c_pl_route` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炼钢工艺流程",
                               `n_ccm_thk` DECIMAL(15,4) NULL Comment "浇铸厚度",
                               `n_ccm_wth` DECIMAL(15,4) NULL Comment "浇铸宽度",
                               `n_ccm_tme` BIGINT(19) NULL Comment "浇铸时长",
                               `n_ord_slab_cnt` BIGINT(19) NULL Comment "订单板坯张数",
                               `n_woo_slab_cnt` BIGINT(19) NULL Comment "余材板坯张数",
                               `c_woo_heat_fl` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "余材炉次代码",
                               `n_slab_cnt` BIGINT(19) NULL Comment "板坯块数",
                               `n_pre_heat_wgt` DECIMAL(15,4) NULL Comment "计划出钢量",
                               `c_sms_duedatetime` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炼钢作业期限",
                               `n_cast_edt_seq` DECIMAL(15,4) NULL Comment "浇次编制号",
                               `c_plan_cast_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划浇次号",
                               `n_cast_heat_seq` BIGINT(19) NULL Comment "浇次内顺序号",
                               `n_cast_heat_cnt` BIGINT(19) NULL Comment "浇次炉数",
                               `c_ld_wkst` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "转炉炉座号",
                               `dt_pre_ld_time` DATETIME(3) NULL Comment "计划冶炼开始时间",
                               `dt_pre_ld_str_tme` DATETIME(3) NULL Comment "计划出钢开始时间",
                               `dt_pre_ld_end_tme` DATETIME(3) NULL Comment "计划出钢结束时间",
                               `c_fir_lf_wkst` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第一次LF炉座号",
                               `dt_fir_lf_sttime` DATETIME(3) NULL Comment "计划第一次 LF 开始时间",
                               `dt_fir_lf_endtime` DATETIME(3) NULL Comment "计划第一次 LF 结束时间",
                               `c_sec_lf_wkst` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第二次LF炉座号",
                               `dt_sec_lf_sttime` DATETIME(3) NULL Comment "计划第二次 LF 开始时间",
                               `dt_sec_lf_endtime` DATETIME(3) NULL Comment "计划第二次 LF 结束时间",
                               `c_fir_vd_wkst` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第一次VOD炉座号",
                               `dt_fir_vd_sttime` DATETIME(3) NULL Comment "计划第一次VD开始时间",
                               `dt_fir_vd_endtime` DATETIME(3) NULL Comment "计划第一次VD结束时间",
                               `c_sec_vd_wkst` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划第二次VD炉座号",
                               `dt_sec_vd_sttime` DATETIME(3) NULL Comment "计划第二次VD 开始时间",
                               `dt_sec_vd_endtime` DATETIME(3) NULL Comment "计划第二次VD结束时间",
                               `dt_plan_ladle_open` DATETIME(3) NULL Comment "大包开浇时间",
                               `c_ccm_wkst` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸机号",
                               `dt_pre_ccm_str_tme` DATETIME(3) NULL Comment "计划浇铸开始时间",
                               `dt_pre_ccm_end_tme` DATETIME(3) NULL Comment "计划浇铸结束时间",
                               `c_mstlgrd_fl` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "混炉标志",
                               `c_upd_pgmid` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "变更程序ID",
                               `c_status` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                               `c_heat_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                               `c_act_stl_grd_cd` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "实际钢种代码",
                               `c_act_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "实际质量编码",
                               `c_cast_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸机号",
                               `c_huntie_wks` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "混铁炉座次",
                               `c_yuchuli_wks` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "预处理座次",
                               `c_curr_station_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "当前工位",
                               `c_curr_event_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "当前事件",
                               `c_prev_event_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上一事件",
                               `c_slab_len` DECIMAL(15,4) NULL Comment "坯料长度",
                               `task_list_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                               `n_enabled_mark` INT(10) NULL,
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME NULL,
                               `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                               `dt_modify_date_time` DATETIME NULL,
                               `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "炉次计划编制中间表";
-- t_pss_common_code DDL
CREATE TABLE `t_pss_common_code` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                  `c_mana_no` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代码管理号",
                                  `c_code` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代码",
                                  `c_short_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代码简称",
                                  `c_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代码名称",
                                  `c_short_eng` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代码英文简称",
                                  `c_full_eng` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代码英文名称",
                                  `n_enabled_mark` INT(10) NULL,
                                  `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                  `dt_create_date_time` DATETIME NULL,
                                  `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                  `dt_modify_date_time` DATETIME NULL,
                                  `n_delete_mark` INT(10) NULL DEFAULT 0 Comment "删除标记;默认为0,1为删除",
                                  PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1945036177835487235 ROW_FORMAT = Dynamic COMMENT = "公共编码表";
-- t_pss_cut_plan DDL
CREATE TABLE `t_pss_cut_plan` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                               `c_plan_slab_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "计划钢坯号",
                               `c_task_list_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                               `c_stl_grd_cd` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                               `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                               `n_slab_thk` DECIMAL(15,4) NULL Comment "钢坯厚度",
                               `n_slab_wth` DECIMAL(15,4) NULL Comment "钢坯宽度",
                               `n_slab_len` DECIMAL(15,4) NULL Comment "钢坯长度",
                               `c_ht_heat` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否可以头尾炉",
                               `c_ht_slab` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否可以头尾坯",
                               `c_hot_flag` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否热装热送",
                               `c_test_flag` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否检验",
                               `c_sent_place` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢坯去向",
                               `c_dingchi_type` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "定尺类型",
                               `c_slab_type` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "坯料类型",
                               `c_use_std` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准",
                               `c_cast_id` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸机号",
                               `c_lljg` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否来料加工材",
                               `c_mat_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                               `c_conta_id` VARCHAR(29) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "合同号",
                               `c_conta_line_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "合同行号",
                               `n_fulw_rad` DECIMAL(15,4) NULL Comment "溢短装比",
                               `c_workout_tim` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "要求完工日期",
                               `c_spc_need` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "特殊要求",
                               `c_emp_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "发送人",
                               `dt_send_dt` DATETIME(3) NULL Comment "发送时间",
                               `dt_check_dt` DATETIME(3) NULL Comment "接受时间",
                               `c_status` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                               `c_plan_heat_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划炉次号",
                               `n_heat_seq` DECIMAL(15,4) NULL Comment "炉次内序号",
                               `n_plan_wgt` DECIMAL(15,4) NULL Comment "计划重量",
                               `c_work_shop` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                               `c_rem_slab_flg` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否余材",
                               `c_first_cut_group` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "一切分组编号",
                               `c_act_slab_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "实际版批号",
                               `c_first_cut_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "一切标志",
                               `n_first_cut_seq` DECIMAL(15,4) NULL Comment "一切索引",
                               `n_prod_wth` DECIMAL(15,4) NULL Comment "成品宽度",
                               `n_prod_thk` DECIMAL(15,4) NULL Comment "成品厚度",
                               `n_prod_len` DECIMAL(15,4) NULL Comment "成品长度",
                               `n_left_cal_wgt` DECIMAL(15,4) NULL Comment "钢水重量",
                               `c_heat_no` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉次号",
                               `n_first_cut_len` DECIMAL(15,4) NULL Comment "一切长度",
                               `n_first_cut_wgt` DECIMAL(15,4) NULL Comment "一切重量",
                               `c_first_cut_emp` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "一切操作员",
                               `dt_first_cut_time` DATETIME(3) NULL Comment "一切时间",
                               `n_is_first_add` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否一切添加标记",
                               `c_first_hot_len` DECIMAL(15,4) NULL Comment "一切热坯长度",
                               `c_has_sure_plan` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否确认",
                               `c_cast_edt_seq` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划浇次号",
                               `c_heat_id_after` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号后缀",
                               `c_treat_no` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理次数",
                               `c_slab_qual_level` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢坯成分等级",
                               `c_load_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "装车单号",
                               `c_plan_type` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划类别0：浇次内；1：浇次外；2：计划外",
                               `n_liu_no` BIGINT(19) NULL Comment "切割流号",
                               `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME NULL,
                               `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                               `dt_modify_date_time` DATETIME NULL,
                               `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "切割计划表";
-- t_pss_d_mat_match_zg DDL
CREATE TABLE `t_pss_d_mat_match_zg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                     `c_match_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "派车单号",
                                     `c_contract_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售合同号",
                                     `c_contract_sn` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售合同行号",
                                     `c_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                     `c_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                     `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种",
                                     `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码",
                                     `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                     `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                                     `n_mat_wth` DECIMAL(12,3) NULL Comment "宽度",
                                     `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                                     `n_mat_len` DECIMAL(12,3) NULL Comment "长度",
                                     `c_sale_type` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "按量按件发货",
                                     `c_is_measure` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否计量0不计量；1计量",
                                     `n_sale_wgt` DECIMAL(15,3) NULL Comment "计划发货重量",
                                     `n_sale_nums` INT(10) NULL Comment "计划发货块数",
                                     `n_now_wgt1` DECIMAL(15,3) NULL Comment "当前装车量",
                                     `n_now_nums` INT(10) NULL Comment "当前装车块数",
                                     `c_gross_bill` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "皮重单号",
                                     `c_tare_bill` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "毛重单号",
                                     `n_now_act_wgt` DECIMAL(15,3) NULL Comment "检斤重量",
                                     `c_status` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                                     `c_confirm_seq` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "装车确认序号",
                                     `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                     `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                     `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                     `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                     `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                     `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                     PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "轧钢装车单表";
-- t_pss_d_mat_yardout_zg DDL
CREATE TABLE `t_pss_d_mat_yardout_zg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                       `c_yardout_id` VARCHAR(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "出库单号（装车单号）",
                                       `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "件次号",
                                       `n_cnt` INT(10) NULL Comment "数量",
                                       `c_if_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库状态",
                                       `c_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                       `c_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                       `c_line_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                       `c_yard_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库代码",
                                       `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                       `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码",
                                       `c_ord_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单材/余材",
                                       `c_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "批次号",
                                       `c_mat_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原料批次号",
                                       `c_task_list_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                       `c_order_no` VARCHAR(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号（代表订单号）",
                                       `c_sale_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单号",
                                       `c_sale_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单行号",
                                       `c_order_id_final` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下游订单号",
                                       `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                       `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                                       `n_mat_wth` DECIMAL(12,3) NULL Comment "宽度",
                                       `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                                       `n_mat_len` DECIMAL(12,3) NULL Comment "长度",
                                       `n_mat_cnt` INT(10) NULL Comment "块数",
                                       `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "理论重量",
                                       `n_mat_wgt` DECIMAL(12,3) NULL Comment "入库检斤重量",
                                       `n_mat_act_wgt` DECIMAL(12,3) NULL Comment "出库检斤重量",
                                       `c_loc_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                                       `c_trans_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "运输方式",
                                       `c_trans_no` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "车号",
                                       `c_jilian_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量号",
                                       `c_yardout_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库类型",
                                       `c_out_plan_no` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库计划号",
                                       `c_out_plan_rno` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库计划行号",
                                       `c_yardout_rsn` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库原因",
                                       `c_aim_line_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地产线",
                                       `c_to_yard` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地仓库代码",
                                       `c_out_emp` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库员",
                                       `dt_out_time` DATETIME NULL Comment "出库时间",
                                       `c_shift` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库班次",
                                       `c_crew` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库班别",
                                       `c_data_source` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "数据来源",
                                       `c_memo` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                       `c_pgmid` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "最后修改程序",
                                       `c_upload_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否上传",
                                       `n_msg_no` BIGINT(19) NULL Comment "上传消息号",
                                       `c_ifhot` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否热装",
                                       `c_prod_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "终判等级",
                                       `c_mattype` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                       `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                       `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                       `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                       `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                       `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                       `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                       PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "轧钢出库记录表";
-- t_pss_dispatch DDL
CREATE TABLE `t_pss_dispatch` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                               `c_dispatch_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "调度单号",
                               `c_line_id` BIGINT(19) NULL Comment "产线id",
                               `c_line_no` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线编码",
                               `c_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线名称",
                               `c_stl_grd_cd` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                               `c_stl_grd_desc` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                               `n_spec` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品规格",
                               `n_tal_wgt` DECIMAL(15,4) NULL Comment "总重量",
                               `n_tal_cnt` DECIMAL(15,4) NULL Comment "总支数",
                               `c_release_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下达人",
                               `c_empty_fl` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "调度单中没有轧制计划后设置1",
                               `c_post_datetime` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货日期",
                               `n_left_cnt` DECIMAL(15,4) NULL Comment "剩余支数",
                               `c_plan_finishing_path` VARCHAR(34) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划精整工艺路径（精整工序编号+分隔符-逗号+精整工序编号）",
                               `c_product_plan_no` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综合生产计划号",
                               `c_sale_plan_no` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售计划号",
                               `c_prod_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品类型",
                               `c_size_property` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "定尺类型",
                               `dt_release_time` DATETIME(3) NULL Comment "下达时间",
                               `c_mat_id` BIGINT(19) NULL Comment "存货id",
                               `c_mat_code` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货编码",
                               `c_mat_name` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货名称",
                               `n_prod_len` DECIMAL(15,4) NULL Comment "产品长度",
                               `c_mat_qul_id` BIGINT(19) NULL Comment "产品材质id-物料",
                               `c_mat_qul_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品材质code-物料",
                               `c_mat_qul_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品材质name-物料",
                               `n_dispatchseq` DECIMAL(15,4) NULL Comment "顺序",
                               `c_status` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "1调度下达，0未下达,2 转去装炉,3已经生产,8生产结束,9撤销(P0019)",
                               `c_send_emp` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划下达人",
                               `dt_send_time` DATETIME(3) NULL Comment "计划下达时间",
                               `c_std_spec` VARCHAR(70) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准",
                               `c_std_spec_name` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准名称",
                               `n_product_plan_sn` DECIMAL(15,4) NULL Comment "综合生产计划序号",
                               `c_org_dispatchid` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原始调度单号",
                               `c_dis_source` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "0原始调度单，1拆分后调度单，2合并后调度单，3合并后消失调度单",
                               `c_dis_product_plan_no` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综合生产计划号(任务单调整用)",
                               `n_sale_plan_sn` DECIMAL(15,4) NULL Comment "综合销售计划序号",
                               `c_fpoststateid` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货状态",
                               `c_memo` VARCHAR(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                               `c_dissend_emp` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "调度下达人（下达到物料匹配）",
                               `dt_dissend_time` DATETIME(3) NULL Comment "调度下达时间",
                               `c_pre_status` INT(10) NULL Comment "是否启用订单生产",
                               `c_is_tempplan` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否为临时计划（1：临时计划）",
                               `c_mergememo` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售操作日志",
                               `c_dispatchmemo` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "调度备注",
                               `dt_updatetime` DATETIME NULL Comment "调整顺序时间",
                               `c_updatetime` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "调整顺序人",
                               `c_clearcont` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精整内容",
                               `dt_chearfis_time` DATETIME(3) NULL Comment "精整交货时间",
                               `c_clearedtemp` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精整内容编制人",
                               `dt_clearedt_time` DATETIME(3) NULL Comment "精整内容编制时间",
                               `c_finemp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "结束人",
                               `c_exg_prod_lot_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否紧急订单（0：正常订单，1：紧急订单）",
                               `c_customer_cd` VARCHAR(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "客户名称",
                               `dt_fin_time` DATETIME(3) NULL Comment "结束时间",
                               `c_cus_level` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "客户重要度",
                               `c_s_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否下发二级",
                               `c_oldcode` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "旧编码",
                               `c_kcgp` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢坯库存对应重量",
                               `n_prod_wth` DECIMAL(15,4) NULL Comment "产品宽度",
                               `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                               `c_item_cd` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格代码",
                               `c_mat_item` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成品规格",
                               `n_thk_bias_max` DECIMAL(15,4) NULL Comment "厚度公差最大值",
                               `n_thk_bias_min` DECIMAL(15,4) NULL Comment "厚度公差最小值",
                               `n_wth_bias_max` DECIMAL(15,4) NULL Comment "宽度公差最大值",
                               `n_wth_bias_min` DECIMAL(15,4) NULL Comment "宽度公差最小值",
                               `n_prod_wgt_max` DECIMAL(15,4) NULL Comment "成品重量最大值",
                               `n_prod_wgt_min` DECIMAL(15,4) NULL Comment "成品重量最小值",
                               `n_prod_len_min` DECIMAL(15,4) NULL Comment "长度最小",
                               `n_prod_len_max` DECIMAL(15,4) NULL Comment "长度最大",
                               `n_plan_single_wgt` DECIMAL(15,4) NULL Comment "计划单支重",
                               `n_dofinal_wgt` DECIMAL(15,4) NULL Comment "挂单重量",
                               `c_slab_item` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "坯料规格",
                               `n_enabled_mark` INT(10) NULL,
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME NULL,
                               `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                               `dt_modify_date_time` DATETIME NULL,
                               `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "调度单表";
-- t_pss_fcs_cd DDL
CREATE TABLE `t_pss_fcs_cd` (`n_id` BIGINT(19) NOT NULL Comment "顺序号",
                             `c_mat_type_b_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目大类编码",
                             `c_mat_type_b_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目大类名称",
                             `c_cost_item_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "项目小类编码",
                             `c_cost_item_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目小类名称",
                             `c_mat_unit_id` BIGINT(19) NULL Comment "计量单位",
                             `c_mat_unit_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量单位编码",
                             `c_mat_unit_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量单位名称",
                             `c_pro_line` BIGINT(19) NULL Comment "产线id",
                             `c_pro_line_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                             `c_pro_line_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线名称",
                             `c_cost_seq` INT(10) NULL Comment "显示顺序",
                             `c_memo` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                             `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                             `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                             `dt_create_date_time` DATETIME NULL,
                             `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                             `dt_modify_date_time` DATETIME NULL,
                             PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "成本项目定义";
-- t_pss_fcs_mat_cd DDL
CREATE TABLE `t_pss_fcs_mat_cd` (`n_id` BIGINT(19) NOT NULL Comment "顺序号",
                                 `c_cost_item_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "成本小类编码",
                                 `c_cost_item_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本小类名称",
                                 `c_mat_class_id` BIGINT(19) NULL Comment "成本项目id",
                                 `c_mat_class_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本项目编码",
                                 `c_mat_class_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本项目名称",
                                 `c_memo` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                 `c_unit` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "单位",
                                 `c_pro_line` BIGINT(19) NULL Comment "产线id",
                                 `c_pro_line_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线编码",
                                 `c_pro_line_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线名称",
                                 `n_cost_seq` DECIMAL NULL Comment "显示顺序",
                                 `c_proc_id` BIGINT(19) NULL Comment "工序id",
                                 `c_proc_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序编码",
                                 `c_proc_name` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序名称",
                                 `c_mat_id` BIGINT(19) NULL Comment "物料id",
                                 `c_mat_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                 `c_mat_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                 `c_seat` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否区分座次",
                                 `c_spare1` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用字段1",
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                 `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                 `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                 `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                 `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "成本项物料分类";
-- t_pss_fcs_mat_code DDL
CREATE TABLE `t_pss_fcs_mat_code` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                   `c_mat_id` BIGINT(19) NULL Comment "物料id",
                                   `c_mat_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                   `c_mat_unit` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "单位",
                                   `c_aply_std` INT(10) NULL Comment "是否使用",
                                   `c_mat_class_code` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料科目编码",
                                   `c_mat_class_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料科目名称",
                                   `c_mat_group_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料组编码",
                                   `c_mat_group_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料组名称",
                                   `c_mat_type_b_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目大类代码",
                                   `c_mat_type_b_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目大类名称",
                                   `c_mat_type_m_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目中类代码",
                                   `c_mat_type_m_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目中类名称",
                                   `c_mat_type_s_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目小类代码",
                                   `c_mat_type_s_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目小类名称",
                                   `c_stl_grd_id` BIGINT(19) NULL Comment "钢种代码id",
                                   `c_stl_grd_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                   `c_stl_grd_desc` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述/名称",
                                   `c_mat_item1` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格1",
                                   `c_mat_item2` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格2",
                                   `c_mat_item3` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格3",
                                   `c_mat_lth_group_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "长度组编码",
                                   `c_mat_lth_group_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "长度组名称",
                                   `c_area_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "使用区域",
                                   `c_memo` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                   `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                   `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                   `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                   `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                   `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                   `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                   `c_mat_code` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                   PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1945417768608911362 ROW_FORMAT = Dynamic COMMENT = "成本物料配置";
-- t_pss_fcs_mat_price DDL
CREATE TABLE `t_pss_fcs_mat_price` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "顺序号",
                                    `c_cost_item_id` BIGINT(19) NULL Comment "成本项目id",
                                    `c_cost_item_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本项目编码",
                                    `c_cost_item_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本项目名称",
                                    `c_mat_id` BIGINT(19) NULL Comment "物料id",
                                    `c_mat_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料代码",
                                    `c_mat_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                    `c_unit_id` BIGINT(19) NULL Comment "单位",
                                    `c_unit_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "单位编码",
                                    `c_unit_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "单位名称",
                                    `c_plan_price` DECIMAL(15,4) NULL Comment "计划价格",
                                    `c_act_price` DECIMAL(15,4) NULL Comment "实际价格",
                                    `c_recovery` DECIMAL(5,2) NULL Comment "回收率",
                                    `c_state` INT(10) NULL Comment "状态",
                                    `dt_starttime` DATE NULL Comment "开始启用时间",
                                    `dt_endtime` DATE NULL Comment "结束时间",
                                    `c_memo` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                    `c_pro_line` BIGINT(19) NULL Comment "产线id",
                                    `c_pro_line_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                    `c_pro_line_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线name",
                                    `c_proc_id` BIGINT(19) NULL Comment "工序id",
                                    `c_proc_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序cd",
                                    `c_proc_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序name",
                                    `c_mat_type_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型代码",
                                    `c_mat_type_name` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型名称",
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                    `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                    `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                    `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                    `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1945645746852974595 ROW_FORMAT = Dynamic COMMENT = "物料价格维护表";
-- t_pss_fcs_rawm_cost DDL
CREATE TABLE `t_pss_fcs_rawm_cost` (`n_id` BIGINT(19) NOT NULL,
                                    `c_heat_id` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                                    `c_stl_grd_cd` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种cd",
                                    `c_stl_grd_name` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种name",
                                    `c_stl_grd_desc` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述/名称",
                                    `c_pro_line_id` BIGINT(19) NULL Comment "产线id",
                                    `c_pro_line_cd` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线cd",
                                    `c_pro_line_name` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线name",
                                    `c_proc_id` BIGINT(19) NULL Comment "工序id",
                                    `c_proc_cd` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序cd",
                                    `c_proc_name` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序name",
                                    `c_cost_item_code` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本项目编码",
                                    `c_cost_item_name` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本项目名称",
                                    `c_mat_id` BIGINT(19) NULL Comment "物料id",
                                    `c_mat_code` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料代码",
                                    `c_mat_name` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                    `c_plan_price` DECIMAL(15,4) NULL Comment "计划价格",
                                    `c_act_price` DECIMAL(15,4) NULL Comment "实际价格",
                                    `c_act_mat_qty` DECIMAL(15,4) NULL Comment "实际成本量",
                                    `c_mat_qty` DECIMAL(15,4) NULL Comment "消耗量",
                                    `dt_use_date` DATE NULL Comment "开始使用日期",
                                    `c_crew` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班组",
                                    `c_shift` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                                    `dt_use_date_end` DATE NULL Comment "结束使用时间",
                                    `n_cost_seq` INT(10) NULL Comment "显示顺序",
                                    `c_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "核算单位",
                                    `c_quota` DECIMAL(10,2) NULL Comment "定额",
                                    `c_ratio` DECIMAL(10,4) NULL Comment "公摊系数",
                                    `c_seat` BIGINT(19) NULL Comment "座次id",
                                    `c_seat_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "座次code",
                                    `c_seat_name` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "座次name",
                                    `c_cost_date` DATETIME(3) NULL Comment "成本日期",
                                    `c_state` INT(10) NULL Comment "状态",
                                    `c_pro_emp` BIGINT(19) NULL Comment "审核人",
                                    `dt_pro_time` DATETIME(3) NULL Comment "审核时间",
                                    `c_month_flag` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上传标志位",
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                    `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                    `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                    `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                    `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                    `c_unit_id` BIGINT(19) NULL Comment "单位",
                                    `c_unit_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "单位编码",
                                    `c_unit_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "单位名称",
                                    `c_pro_line` BIGINT(19) NULL Comment "产线id",
                                    `dt_user_date` DATETIME(3) NULL Comment "开始使用日期",
                                    `dt_user_date_end` DATETIME(3) NULL Comment "结束使用时间",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "工序成本统计";
-- t_pss_final_des_slab DDL
CREATE TABLE `t_pss_final_des_slab` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                                     `c_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "设计钢坯id",
                                     `c_slab_mat_qul_cd` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表钢坯质量编码",
                                     `c_plate_mat_qul_cd` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表钢板质量编码",
                                     `n_thk` DECIMAL(15,4) NULL Comment "钢坯厚",
                                     `n_wth` DECIMAL(15,4) NULL Comment "钢坯宽",
                                     `n_lth` DECIMAL(15,4) NULL Comment "钢坯长",
                                     `n_cal_wgt` DECIMAL(15,4) NULL Comment "钢坯计算重量",
                                     `c_stl_grd_cd` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                     `n_deg_ratio` DECIMAL(15,4) NULL Comment "设计成材率",
                                     `n_asroll_lth` DECIMAL(15,4) NULL Comment "轧件长度",
                                     `n_asroll_thk` DECIMAL(15,4) NULL Comment "轧件厚度",
                                     `n_asroll_wth` DECIMAL(15,4) NULL Comment "轧件宽度",
                                     `c_fpoststateid` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货状态",
                                     `c_ust_lev` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤等级",
                                     `c_ust_std` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤标准",
                                     `n_cut_head` DECIMAL(15,4) NULL Comment "切头长",
                                     `n_cut_tail` DECIMAL(15,4) NULL Comment "切尾长",
                                     `n_cut_lth_lose` DECIMAL(15,4) NULL Comment "长度切损",
                                     `n_cut_lth_rem` DECIMAL(15,4) NULL Comment "长度余量",
                                     `n_cut_trim_lose` DECIMAL(15,4) NULL Comment "宽度切损",
                                     `n_cut_wth_rem` DECIMAL(15,4) NULL Comment "宽度余量",
                                     `n_smp_lth` DECIMAL(15,4) NULL Comment "试样长度",
                                     `c_std_spec` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表执行标准",
                                     `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                                     `c_prod_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品名称",
                                     `c_task_list_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "任务单号",
                                     `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                     `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                     `dt_create_date_time` DATETIME NULL,
                                     `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                     `dt_modify_date_time` DATETIME NULL,
                                     `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                     PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "坯料设计表";
-- t_pss_final_des_slab_t DDL
CREATE TABLE `t_pss_final_des_slab_t` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                                       `c_slab_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "设计钢坯id",
                                       `c_slab_mat_qul_cd` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表钢坯质量编码",
                                       `c_plate_mat_qul_cd` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表钢板质量编码",
                                       `n_thk` DECIMAL(15,4) NULL Comment "钢坯厚",
                                       `n_wth` DECIMAL(15,4) NULL Comment "钢坯宽",
                                       `n_lth` DECIMAL(15,4) NULL Comment "钢坯长",
                                       `n_cal_wgt` DECIMAL(15,4) NULL Comment "钢坯计算重量",
                                       `c_stl_grd_cd` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                       `n_deg_ratio` DECIMAL(15,4) NULL Comment "设计成材率",
                                       `n_asroll_lth` DECIMAL(15,4) NULL Comment "轧件长度",
                                       `n_asroll_thk` DECIMAL(15,4) NULL Comment "轧件厚度",
                                       `n_asroll_wth` DECIMAL(15,4) NULL Comment "轧件宽度",
                                       `c_fpoststateid` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货状态",
                                       `c_ust_lev` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤等级",
                                       `c_ust_std` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤标准",
                                       `n_cut_head` DECIMAL(15,4) NULL Comment "切头长",
                                       `n_cut_tail` DECIMAL(15,4) NULL Comment "切尾长",
                                       `n_cut_lth_lose` DECIMAL(15,4) NULL Comment "长度切损",
                                       `n_cut_lth_rem` DECIMAL(15,4) NULL Comment "长度余量",
                                       `n_cut_trim_lose` DECIMAL(15,4) NULL Comment "宽度切损",
                                       `n_cut_wth_rem` DECIMAL(15,4) NULL Comment "宽度余量",
                                       `n_smp_lth` DECIMAL(15,4) NULL Comment "试样长度",
                                       `c_std_spec` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表执行标准",
                                       `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                                       `c_prod_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品名称",
                                       `c_ctask_list_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                       `c_sch_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "轧制计划号",
                                       `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                       `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                       `dt_create_date_time` DATETIME NULL,
                                       `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                       `dt_modify_date_time` DATETIME NULL,
                                       `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                       PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "坯料设计临时表";
-- t_pss_final_design_slab_temp DDL
CREATE TABLE `t_pss_final_design_slab_temp` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                                             `c_slab_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "设计钢坯id",
                                             `c_slab_mat_qul_id` BIGINT(19) NULL Comment "代表钢坯材质代码",
                                             `c_slab_mat_qul_cd` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表钢坯材质代码",
                                             `c_plate_mat_qul_cd` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表钢板材质代码",
                                             `n_thk` DECIMAL(7,3) NULL Comment "钢坯厚",
                                             `n_wth` DECIMAL(7,3) NULL Comment "钢坯宽",
                                             `n_lth` DECIMAL(8,1) NULL Comment "钢坯长",
                                             `n_cal_wgt` DECIMAL(15,3) NULL Comment "钢坯计算重量",
                                             `c_stl_grd_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                             `c_stl_grd_desc` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                             `n_deg_ratio` DECIMAL(8,3) NULL Comment "设计成材率",
                                             `n_asroll_lth` DECIMAL(8,1) NULL Comment "轧件长度",
                                             `n_asroll_thk` DECIMAL(7,3) NULL Comment "轧件厚度",
                                             `n_asroll_wth` DECIMAL(7,3) NULL Comment "轧件宽度",
                                             `c_fpoststateid` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货状态",
                                             `c_ust_lev` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤等级",
                                             `c_ust_std` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤标准",
                                             `n_cut_head` DECIMAL(8,1) NULL Comment "切头长",
                                             `n_cut_tail` DECIMAL(8,1) NULL Comment "切尾长",
                                             `n_cut_lth_lose` DECIMAL(8,1) NULL Comment "长度切损",
                                             `n_cut_lth_rem` DECIMAL(8,1) NULL Comment "长度余量",
                                             `n_cut_trim_lose` DECIMAL(6,2) NULL Comment "宽度切损",
                                             `n_cut_wth_rem` DECIMAL(6,2) NULL Comment "宽度余量",
                                             `n_smp_lth` DECIMAL(8,1) NULL Comment "试样长度",
                                             `c_std_spec` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表执行标准",
                                             `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                                             `c_prod_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品名称",
                                             `c_ctask_list_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                             `c_sch_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "轧制计划号",
                                             `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                             `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                             `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                             `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                             `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                             `n_enabled_mark` INT(10) NULL,
                                             PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "坯料设计临时表";
-- t_pss_final_slab DDL
CREATE TABLE `t_pss_final_slab` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                                 `c_final_slab_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "最终钢坯ID",
                                 `c_final_slab_name` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "最终钢坯NAME",
                                 `n_target_slab_id` DECIMAL(15,4) NULL Comment "目标钢坯ID",
                                 `n_prev_final_slab_id_in_target` DECIMAL(15,4) NULL Comment "目标钢坯内前一最终钢坯ID",
                                 `c_roll_sch_id` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "轧制顺序计划id",
                                 `n_prev_final_slab_id_in_sch` DECIMAL(15,4) NULL Comment "轧制顺序计划内前一最终钢坯id",
                                 `c_slab_mat_qul_id` BIGINT(19) NULL Comment "代表钢坯质量编码",
                                 `c_slab_mat_qul_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表钢坯质量编码",
                                 `c_slab_mat_qul_name` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表钢坯质量编码",
                                 `n_thk` DECIMAL(15,4) NULL Comment "钢坯厚",
                                 `n_wth` DECIMAL(15,4) NULL Comment "钢坯宽",
                                 `n_lth` DECIMAL(15,4) NULL Comment "钢坯长",
                                 `n_cal_wgt` DECIMAL(15,4) NULL Comment "钢坯计算重量",
                                 `c_stl_grd_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种",
                                 `c_stl_grd_desc` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述",
                                 `n_deg_ratio` DECIMAL(15,4) NULL Comment "设计成材率",
                                 `n_asroll_lth` DECIMAL(15,4) NULL Comment "产品长度 （方钢/开坯）",
                                 `n_asroll_thk` DECIMAL(15,4) NULL Comment "产品厚度（方钢/开坯）",
                                 `n_asroll_wth` DECIMAL(15,4) NULL Comment "产品宽度（方钢/开坯）",
                                 `c_fpost_state` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货状态",
                                 `c_ust_lev` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤等级",
                                 `c_ust_std` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤标准",
                                 `n_cut_head` DECIMAL(15,4) NULL Comment "切头长",
                                 `n_cut_tail` DECIMAL(15,4) NULL Comment "切尾长",
                                 `n_cut_lth_lose` DECIMAL(15,4) NULL Comment "长度切损",
                                 `n_cut_lth_rem` DECIMAL(15,4) NULL Comment "长度余量",
                                 `n_cut_trim_lose` DECIMAL(15,4) NULL Comment "宽度切损",
                                 `n_cut_wth_rem` DECIMAL(15,4) NULL Comment "宽度余量",
                                 `n_smp_lth` DECIMAL(15,4) NULL Comment "试样长度",
                                 `c_source` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "来源",
                                 `c_status` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                                 `n_seq_in_target` DECIMAL(15,4) NULL Comment "目标钢坯内顺序",
                                 `n_seq_in_sch` DECIMAL(15,4) NULL Comment "轧制顺序计划内顺序",
                                 `c_std_spec` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "代表执行标准",
                                 `dt_released_moment` DATETIME(3) NULL Comment "下发作业时间",
                                 `n_target_bak` DECIMAL(15,4) NULL Comment "程序跟踪用",
                                 `dt_apply_moment_bak` DATETIME(3) NULL Comment "提料上传时间",
                                 `dt_ins_time` DATETIME(3) NULL Comment "创建日期",
                                 `c_memo_bak` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                 `c_msg_status` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "消息状态",
                                 `c_error_text` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "错误描述",
                                 `c_msg_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "消息类别",
                                 `c_msg_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上传者",
                                 `c_msg_id` DECIMAL(15,4) NULL Comment "消息号",
                                 `c_applied_fl` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否上传",
                                 `c_hcr_fl` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "热送标记",
                                 `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "物料类型",
                                 `n_apply_list_id` DECIMAL(15,4) NULL Comment "提料单号",
                                 `c_dispatch_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "调度令号",
                                 `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                                 `n_line_spec` DECIMAL(15,4) NULL Comment "直径",
                                 `c_pre_status` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "前状态",
                                 `dt_attach_time` DATETIME(3) NULL Comment "挂单时间",
                                 `c_attach_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "挂单人",
                                 `c_prod_type` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品类型P0001",
                                 `c_plan_finishing_path` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精整路径",
                                 `c_production_line` BIGINT(19) NULL Comment "产线",
                                 `c_production_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线code",
                                 `c_production_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线name",
                                 `c_size_property` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "定尺代码",
                                 `c_prod_mat_id` BIGINT(19) NULL Comment "存货编码",
                                 `c_prod_mat_code` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货编码",
                                 `c_prod_mat_name` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货名称",
                                 `c_org_dipatch_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "调度单合并用原调度单号",
                                 `n_prod_wid` DECIMAL(15,4) NULL Comment "产品宽",
                                 `c_slab_spec` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "开坯产品规格",
                                 `n_ag_plan_seq` DECIMAL(15,4) NULL Comment "综合生产计划主键",
                                 `n_enabled_mark` INT(10) NULL,
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME NULL,
                                 `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                 `dt_modify_date_time` DATETIME NULL,
                                 `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "最终计划钢坯";
-- t_pss_hsec_smpstd DDL
CREATE TABLE `t_pss_hsec_smpstd` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                                  `c_quality_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                  `c_quality_code_name` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                  `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                  `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称",
                                  `c_smp_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "取样类型",
                                  `c_line_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                  `n_thk` DECIMAL(10,3) NULL Comment "产品长度",
                                  `c_spec` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品规格",
                                  `n_smp_num` INT(10) NULL Comment "取样次数",
                                  `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                  `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                  `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                  `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                  `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                  `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                  PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1935158116642062339 ROW_FORMAT = Dynamic COMMENT = "组批取样规则";
-- t_pss_mat_balance_lg DDL
CREATE TABLE `t_pss_mat_balance_lg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                     `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "件次号",
                                     `c_loc` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                                     `c_balance_type` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "盘库类型",
                                     `c_balance_operation` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "盘库说明",
                                     `n_mat_wgt` DECIMAL(12,3) NULL Comment "盘库重量",
                                     `dt_balance_time` DATETIME NULL Comment "盘库时间",
                                     `c_balance_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "盘库人",
                                     `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                                     `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班别",
                                     `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                     `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                     `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                     `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                     `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                     `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                     PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "炼钢盘库记录表";
-- t_pss_mat_info DDL
CREATE TABLE `t_pss_mat_info` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                               `c_pro_line` BIGINT(19) NULL Comment "产线id",
                               `c_bof_id` BIGINT(19) NULL Comment "电路座次id",
                               `c_proc_id` BIGINT(19) NULL Comment "工序id",
                               `c_mac_code` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "座次号",
                               `c_mate_id` BIGINT(19) NULL Comment "物料id",
                               `c_material_code` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                               `c_material_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                               `c_TOT_VAL` DECIMAL(15,2) NULL Comment "投料累计/产出累计",
                               `c_msg_status` INT(10) NULL Comment "信息状态",
                               `c_material_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                               `c_crew` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班组",
                               `c_shift` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                               `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                               `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                               `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                               `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                               `c_pro_line_code` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线code",
                               `c_pro_line_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线名称",
                               `c_proc_cd` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序编码",
                               `c_proc_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序名称",
                               `c_bof_no` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电炉座次编码",
                               `c_bof_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电炉座次名称",
                               `c_material_type_id` BIGINT(19) NULL Comment "物料类型id",
                               `c_heat_id` BIGINT(19) NULL Comment "炉次号",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 11 ROW_FORMAT = Dynamic COMMENT = "投入产出物料表";
-- t_pss_mat_locmap_lg DDL
CREATE TABLE `t_pss_mat_locmap_lg` (`n_id` BIGINT(19) NOT NULL Comment "顺序号",
                                    `c_loc` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                                    `c_loc_lvl` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "层号/入库顺序号",
                                    `c_line_id` BIGINT(19) NULL Comment "产线id",
                                    `c_line_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线code",
                                    `c_line_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线name",
                                    `c_yard_id` BIGINT(19) NULL Comment "仓库id",
                                    `c_yard_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库代码",
                                    `c_yard_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库name",
                                    `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "件次号",
                                    `n_mat_cnt` INT(10) NULL Comment "数量",
                                    `c_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                                    `c_mat_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "轧制批号",
                                    `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                    `c_mate_id` BIGINT(19) NULL Comment "物料id",
                                    `c_mate_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                    `c_mate_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                    `c_stl_grd_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_stl_grd_desc` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述",
                                    `c_mat_qul_id` BIGINT(19) NULL Comment "质量编码id",
                                    `c_mat_qul_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                    `c_mat_qul_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码name",
                                    `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                    `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                                    `n_mat_wth` DECIMAL(12,3) NULL Comment "宽度",
                                    `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                                    `n_mat_len` DECIMAL(12,3) NULL Comment "长度",
                                    `n_mat_wgt` DECIMAL(12,3) NULL Comment "实际重量",
                                    `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "计算重量",
                                    `c_ord_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单材/余材",
                                    `c_task_list_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                    `c_order_no` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                                    `c_sale_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单号",
                                    `c_sale_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单行号",
                                    `c_order_id_final` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下游订单号",
                                    `c_status` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                                    `c_pre_out` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "去向（产线号）",
                                    `dt_prod_time` DATE NULL Comment "生产时间",
                                    `c_prod_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "终判等级",
                                    `c_checker_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "盘库标识",
                                    `c_car_no` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "装车车号",
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                    `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                    `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                    `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                    `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "炼钢库存表";
-- t_pss_mat_locmap_zg DDL
CREATE TABLE `t_pss_mat_locmap_zg` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                                    `c_loc` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                                    `c_loc_lvl` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "层号/入库顺序号",
                                    `c_line_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                    `c_yard_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库代码",
                                    `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "件次号",
                                    `c_mat_cnt` DECIMAL NULL Comment "数量",
                                    `c_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                                    `c_mat_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "轧制批号",
                                    `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                    `c_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                    `c_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                    `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码",
                                    `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                    `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                                    `n_mat_wth` DECIMAL(12,3) NULL Comment "宽度",
                                    `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                                    `n_mat_len` DECIMAL(12,3) NULL Comment "长度",
                                    `n_mat_wgt` DECIMAL(12,3) NULL Comment "实际重量",
                                    `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "计算重量",
                                    `c_ord_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单材/余材",
                                    `c_task_list_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                    `c_order_no` VARCHAR(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                                    `c_sale_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单号",
                                    `c_sale_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单行号",
                                    `c_order_id_final` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下游订单号",
                                    `c_status` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                                    `c_pre_out` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "去向（产线号）",
                                    `dt_prod_time` DATE NULL Comment "生产时间",
                                    `c_prod_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "终判等级",
                                    `c_checker_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "盘库标识",
                                    `c_car_no` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "装车车号",
                                    `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME NULL,
                                    `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                    `dt_modify_date_time` DATETIME NULL,
                                    `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "轧钢库存表";
-- t_pss_mat_math_rule DDL
CREATE TABLE `t_pss_mat_math_rule` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                                    `c_line_cd` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                    `c_wp_coce` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序号",
                                    `c_mate_id` BIGINT(19) NULL Comment "物料id",
                                    `c_material_code` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料代码",
                                    `c_material_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                    `c_material_type_id` BIGINT(19) NULL Comment "物料类型id",
                                    `c_material_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                    `c_data_type` INT(10) NULL Comment "数据类型（0投入，1产出）",
                                    `c_data_source` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "数据来源",
                                    `c_data_rule` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "数据规则",
                                    `c_math_rule` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计算规则",
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                    `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                    `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                    `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                    `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                    `c_proc_id` BIGINT(19) NULL Comment "工序id",
                                    `c_proc_cd` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序cd",
                                    `c_proc_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序name",
                                    `c_bof_id` BIGINT(19) NULL Comment "电炉座次id",
                                    `c_bof_no` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电炉座次编码",
                                    `c_bof_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电炉座次名称",
                                    `c_pro_line` BIGINT(19) NULL Comment "产线",
                                    `c_pro_line_code` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线编码",
                                    `c_pro_line_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线名字",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1945767172669284355 ROW_FORMAT = Dynamic COMMENT = "投入产出物料计算规则表";
-- t_pss_mat_outbound_record DDL
CREATE TABLE `t_pss_mat_outbound_record` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键ID",
                                          `c_furnace_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "炉号",
                                          `n_material_id` BIGINT(19) NOT NULL Comment "物料ID",
                                          `n_theoretical_amount` DECIMAL(10,2) NOT NULL Comment "理论用量",
                                          `n_adjust_factor` DECIMAL(10,2) NOT NULL Comment "调整系数",
                                          `n_actual_amount` DECIMAL(10,2) NOT NULL Comment "实际用量",
                                          `n_before_stock` DECIMAL(10,2) NOT NULL Comment "出库前库存",
                                          `n_after_stock` DECIMAL(10,2) NOT NULL Comment "出库后库存",
                                          `dt_outbound_time` DATETIME NOT NULL Comment "出库时间",
                                          `c_status` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "状态(pending:待确认,completed:已完成,adjusted:已调整)",
                                          `c_operation_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "操作类型(auto:自动,manual:人工)",
                                          `c_remark` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                          `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                          `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                          `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                          `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                          `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                          `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                          PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "原辅料出库记录表";
-- t_pss_mat_res DDL
CREATE TABLE `t_pss_mat_res` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                              `c_plan_mat_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划件次号",
                              `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "件次号/坯料号",
                              `n_cnt` INT(10) NULL Comment "数量",
                              `c_pre_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原件次号/原坯料号",
                              `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                              `c_heat_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                              `c_roll_sch_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "轧制批次号",
                              `c_mat_id_mth` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "母板号",
                              `c_status` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料状态",
                              `c_pre_status` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "前物料状态",
                              `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                              `c_stl_grd_desc` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述/名称",
                              `c_quality_id` BIGINT(19) NULL Comment "质量编码id",
                              `c_quality_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                              `c_quality_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                              `c_mat_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                              `c_mat_name` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                              `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                              `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                              `n_mat_wid` DECIMAL(12,3) NULL Comment "宽度",
                              `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                              `n_mat_dia_o` DECIMAL(12,3) NULL Comment "钢卷外径",
                              `n_mat_lth` DECIMAL(12,3) NULL Comment "长度",
                              `n_mat_wgt` DECIMAL(12,3) NULL Comment "实际重量",
                              `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "计算重量",
                              `n_mat_act_wgt` DECIMAL(12,3) NULL Comment "检斤重量",
                              `c_std_spec` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准号",
                              `c_smp_lot` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验批号",
                              `dt_judge_time` DATETIME(3) NULL Comment "综合判定时间 Q",
                              `c_prod_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综判等级",
                              `c_prel_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "初判等级",
                              `c_chem_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成分等级",
                              `c_size_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "尺寸等级",
                              `c_surf_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "外观等级（表面等级）",
                              `c_mtal_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "性能等级",
                              `c_w_pro` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤结果",
                              `dt_w_date` DATETIME(3) NULL Comment "探伤日期",
                              `c_upd_not` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "不合格原因",
                              `c_upd_rsn` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理方式",
                              `c_bd_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否改配合同，否：Null 是：1",
                              `c_cert_id` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量证明书号",
                              `dt_pch_time` DATETIME(3) NULL Comment "购入时间",
                              `dt_prod_time` DATETIME(3) NULL Comment "生产时间",
                              `c_prod_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产班次",
                              `c_prod_group` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产班别",
                              `c_occr_cd` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "信息来源",
                              `c_factory` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产地",
                              `c_supplier` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "供货商",
                              `c_sale_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单号",
                              `c_sale_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单行号",
                              `c_task_list_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                              `c_buy_order_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "采购订单号",
                              `c_order_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "采购订单行号",
                              `c_ord_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单材/余材标志",
                              `c_order_no` VARCHAR(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产订单号",
                              `c_multi_ord_tag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否多订单",
                              `c_woo_rsn` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "余材原因",
                              `c_hcr_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "板坯热送标志",
                              `c_smp_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否试样板",
                              `c_line_id` BIGINT(19) NULL Comment "当前产线id",
                              `c_line_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "当前产线编码",
                              `c_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "当前产线名称",
                              `c_nc_yard_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产销仓库代码",
                              `c_loc` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                              `n_loc_lvl` INT(10) NULL Comment "层号",
                              `c_pre_out` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划去向",
                              `c_memo` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                              `c_pgm_id` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "最后修改程序",
                              `c_month_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否下发",
                              `c_month_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上传人员",
                              `dt_month_time` DATETIME(3) NULL Comment "上传时间",
                              `c_reweight_fl` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "复秤标记",
                              `n_reweight` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "复秤重量",
                              `c_re_wgt_flag` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "复秤类型",
                              `c_two_rewgt_fl` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "二次复秤标记",
                              `dt_two_wgt_time` DATETIME(3) NULL Comment "二次复秤日期",
                              `c_two_wgt_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "二次复秤人员",
                              `n_two_wgt` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "二次复秤重量",
                              `c_cha_empid` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "改判人",
                              `dt_cha_time` DATETIME NULL Comment "改判时间",
                              `c_cha_memo` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "改判备注",
                              `c_pre_mat_qul_id` BIGINT(19) NULL Comment "原质量编码id",
                              `c_pre_mat_qul_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原质量编码",
                              `c_pre_mat_qul_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原质量编码名称",
                              `c_pre_stl_grd_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原钢种",
                              `c_pre_stl_grd_desc` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原钢种描述/名称",
                              `n_pre_mat_lth` DECIMAL(12,3) NULL Comment "原长度",
                              `n_pre_mat_wgt_cal` DECIMAL(12,3) NULL Comment "原计算重量",
                              `c_pre_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原物料编码",
                              `c_pre_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原物料名称",
                              `c_is_lock` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否锁定",
                              `c_w_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "废品标记",
                              `n_ping_num` INT(10) NULL Comment "平整分卷数",
                              `c_ping_status` INT(10) NULL Comment "是否挂单",
                              `c_offline_time` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原料卷下线时长(前台定时查,上料/下发二级时写入数据库)时分",
                              `c_dispatch_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "调度单号",
                              `c_dispatch_seq` INT(10) NULL Comment "挂单顺序",
                              `c_next_line` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "平整去向",
                              `c_ping_plan` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "平整计划号",
                              `c_pign_seq` INT(10) NULL Comment "(材料顺序)",
                              `dt_delivery_time` DATETIME(3) NULL Comment "成品交货日期(平整排计划用)",
                              `n_enabled_mark` INT(10) NULL,
                              `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                              `dt_create_date_time` DATETIME NULL,
                              `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                              `dt_modify_date_time` DATETIME NULL,
                              `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                              `c_mate_id` BIGINT(19) NULL Comment "物料id",
                              PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "物料主表（坯料表）/轧钢坯料表";
-- t_pss_mat_ro_back_lg DDL
CREATE TABLE `t_pss_mat_ro_back_lg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                     `c_mat_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "坯料号/件次号",
                                     `c_mat_id_mth` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "母坯料号/件次号",
                                     `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                     `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种",
                                     `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码",
                                     `c_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                     `c_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                     `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                     `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                                     `n_mat_wth` DECIMAL(12,3) NULL Comment "宽度",
                                     `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                                     `n_mat_len` DECIMAL(12,3) NULL Comment "长度",
                                     `n_mat_wgt` DECIMAL(12,3) NULL Comment "实际重量",
                                     `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "计算重量",
                                     `c_reback_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "退入类型",
                                     `c_reback_rsn` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "退入原因",
                                     `c_mat_from` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "源地",
                                     `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                                     `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班别",
                                     `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                     `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                     `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                     `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                     `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                     `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                     PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "退入记录表";
-- t_pss_mat_ro_out_lg DDL
CREATE TABLE `t_pss_mat_ro_out_lg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                    `c_mat_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "坯料号/件次号",
                                    `c_mat_id_mth` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "母坯料号/件次号",
                                    `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                    `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种",
                                    `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码",
                                    `c_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                    `c_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                    `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                    `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                                    `n_mat_wth` DECIMAL(12,3) NULL Comment "宽度",
                                    `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                                    `n_mat_len` DECIMAL(12,3) NULL Comment "长度",
                                    `n_mat_wgt` DECIMAL(12,3) NULL Comment "实际重量",
                                    `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "计算重量",
                                    `c_reout_type` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "退出类型",
                                    `c_reout_rsn` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "退出原因",
                                    `c_mat_to` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "去向",
                                    `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                                    `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班别",
                                    `c_accept_time` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "确认接收时间",
                                    `c_buy_order_no` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "采购凭证号",
                                    `c_buy_order_item` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "采购凭证行项目编号",
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                    `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                    `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                    `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                    `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "退库记录表";
-- t_pss_mat_stock_limit DDL
CREATE TABLE `t_pss_mat_stock_limit` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键ID",
                                      `c_material_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "物料编码",
                                      `c_material_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "物料名称",
                                      `c_mate_id` BIGINT(19) NULL,
                                      `c_material_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "物料类型(raw:原辅料,alloy:合金,scrap:废钢)",
                                      `n_current_stock` DECIMAL(10,2) NOT NULL Comment "当前库存",
                                      `n_min_stock` DECIMAL(10,2) NOT NULL Comment "库存下限",
                                      `n_max_stock` DECIMAL(10,2) NOT NULL Comment "库存上限",
                                      `c_unit` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "单位",
                                      `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                      `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                      `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                      `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                      `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                      `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                      `c_material_type_id` BIGINT(19) NULL,
                                      PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1945415393261346818 ROW_FORMAT = Dynamic COMMENT = "原辅料库存上下限维护表";
-- t_pss_mat_yardin_lg DDL
CREATE TABLE `t_pss_mat_yardin_lg` (`n_id` BIGINT(19) NOT NULL Comment "顺序号",
                                    `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "件次号/坯料号",
                                    `c_yardin_id` VARCHAR(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "入库单号",
                                    `n_cnt` INT(10) NULL Comment "数量",
                                    `c_if_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库状态",
                                    `c_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                    `c_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                    `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                    `c_line_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                    `c_yard_cd` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库代码",
                                    `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                    `c_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "批次号",
                                    `c_mat_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原料批次号",
                                    `c_task_list_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                    `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号（代表订单号）",
                                    `c_sale_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单号",
                                    `c_sale_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单行号",
                                    `c_order_id_final` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下游订单号",
                                    `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                    `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                                    `n_mat_wth` DECIMAL(12,3) NULL Comment "宽度",
                                    `n_mat_dia` DECIMAL(12,3) NULL Comment "内径",
                                    `n_mat_len` DECIMAL(12,3) NULL Comment "长度",
                                    `n_mat_cnt` INT(10) NULL Comment "件数",
                                    `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "理论重量/发货重量",
                                    `n_mat_wgt` DECIMAL(12,3) NULL Comment "入库检斤重量",
                                    `c_source` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产地产线代码",
                                    `c_from_yard` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "源地仓库代码",
                                    `c_to_yard` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地仓库代码",
                                    `c_loc` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                                    `c_trans_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "运输方式",
                                    `c_trans_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "车号",
                                    `c_jilian_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量单号",
                                    `c_yardin_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库类型",
                                    `c_yardin_rsn` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库原因",
                                    `c_memo` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                    `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库班次",
                                    `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库班别",
                                    `c_month` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "结存月份",
                                    `c_data_source` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "数据来源",
                                    `c_pgm_id` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "最后修改程序",
                                    `c_upload_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否上传",
                                    `c_msg_no` BIGINT(19) NULL Comment "上传消息号",
                                    `c_prod_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "终判等级",
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                    `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                    `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                    `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                    `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "炼钢入库记录表";
-- t_pss_mat_yardin_zg DDL
CREATE TABLE `t_pss_mat_yardin_zg` (`n_id` BIGINT(19) NOT NULL Comment "顺序号",
                                    `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "件次号/坯料号",
                                    `c_yardin_id` VARCHAR(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "入库单号",
                                    `n_cnt` INT(10) NULL Comment "数量",
                                    `c_if_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库状态",
                                    `c_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                    `c_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                    `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                                    `c_line_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                    `c_yard_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库代码",
                                    `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                    `c_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "批次号",
                                    `c_mat_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原料批次号",
                                    `c_task_list_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                    `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号（代表订单号）",
                                    `c_sale_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单号",
                                    `c_sale_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单行号",
                                    `c_order_id_final` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下游订单号",
                                    `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                    `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                                    `n_mat_wth` DECIMAL(12,3) NULL Comment "宽度",
                                    `n_mat_dia` DECIMAL(12,3) NULL Comment "内径",
                                    `n_mat_len` DECIMAL(12,3) NULL Comment "长度",
                                    `n_mat_cnt` INT(10) NULL Comment "件数",
                                    `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "理论重量/发货重量",
                                    `n_mat_wgt` DECIMAL(12,3) NULL Comment "入库检斤重量",
                                    `c_source` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产地产线代码",
                                    `c_from_yard` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "源地仓库代码",
                                    `c_to_yard` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地仓库代码",
                                    `c_loc` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                                    `c_trans_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "运输方式",
                                    `c_trans_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "车号",
                                    `c_jilian_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量单号",
                                    `c_yardin_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库类型",
                                    `c_yardin_rsn` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库原因",
                                    `c_memo` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                    `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库班次",
                                    `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库班别",
                                    `c_month` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "结存月份",
                                    `c_data_source` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "数据来源",
                                    `c_pgm_id` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "最后修改程序",
                                    `c_upload_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否上传",
                                    `c_msg_no` BIGINT(19) NULL Comment "上传消息号",
                                    `c_prod_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "终判等级",
                                    `c_heat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                    `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                    `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                    `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                    `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "轧钢入库记录表";
-- t_pss_mat_yardout_lg DDL
CREATE TABLE `t_pss_mat_yardout_lg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                     `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                     `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                     `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                     `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                     `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                     `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                     `c_yardout_id` VARCHAR(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "出库单号",
                                     `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "件次号",
                                     `n_cnt` INT(10) NULL Comment "数量",
                                     `c_if_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库状态",
                                     `c_mate_id` BIGINT(19) NULL Comment "物料id",
                                     `c_mate_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料code",
                                     `c_mate_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料name",
                                     `c_line_id` BIGINT(19) NULL Comment "产线id",
                                     `c_line_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线code",
                                     `c_line_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线name",
                                     `c_yard_id` BIGINT(19) NULL Comment "仓库id",
                                     `c_yard_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库代码",
                                     `c_yard_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库name",
                                     `c_stl_grd_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                     `c_stl_grd_desc` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述",
                                     `c_mat_qul_id` BIGINT(19) NULL Comment "材质代码id",
                                     `c_mat_qul_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码code",
                                     `c_mat_qul_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码name",
                                     `c_ord_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单材/余材",
                                     `c_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "批次号",
                                     `c_mat_lot_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原料批次号",
                                     `c_task_list_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                     `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号（代表订单号）",
                                     `c_sale_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单号",
                                     `c_sale_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单行号",
                                     `c_order_id_final` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下游订单号",
                                     `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                     `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                                     `n_mat_wth` DECIMAL(12,3) NULL Comment "宽度",
                                     `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                                     `n_mat_len` DECIMAL(12,3) NULL Comment "长度",
                                     `n_mat_cnt` INT(10) NULL Comment "块数",
                                     `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "理论重量",
                                     `n_mat_wgt` DECIMAL(12,3) NULL Comment "入库检斤重量",
                                     `n_mat_act_wgt` DECIMAL(12,3) NULL Comment "出库检斤重量",
                                     `c_loc_id` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                                     `c_trans_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "运输方式",
                                     `c_trans_no` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "车号",
                                     `c_jilian_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量号",
                                     `c_yardout_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库类型",
                                     `c_out_plan_no` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库计划号(派车单号)",
                                     `c_out_plan_rno` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库计划行号(派车单销售行号)",
                                     `c_yardout_rsn` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库原因",
                                     `c_aim_line_id` BIGINT(19) NULL Comment "目的地产线id",
                                     `c_aim_line_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地产线code",
                                     `c_aim_line_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地产线name",
                                     `c_to_yard_id` BIGINT(19) NULL Comment "目的地仓库代码id",
                                     `c_to_yard_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地仓库代码code",
                                     `c_to_yard_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地仓库代码name",
                                     `c_out_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库员",
                                     `dt_out_time` DATETIME NULL Comment "出库时间",
                                     `c_shift` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库班次",
                                     `c_crew` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出库班别",
                                     `c_data_source` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "数据来源",
                                     `c_memo` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                     `c_pgmid` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "最后修改程序",
                                     `c_upload_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否上传",
                                     `n_msg_no` BIGINT(19) NULL Comment "上传消息号",
                                     `c_ifhot` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否热装",
                                     `c_prod_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "终判等级",
                                     `c_ins_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "录入人",
                                     `dt_ins_time` DATETIME NULL Comment "录入时间",
                                     `c_mod_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "修改人(接收人)",
                                     `dt_mod_time` DATETIME NULL Comment "修改时间（接收时间）",
                                     `c_del_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "删除标记",
                                     PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "炼钢出库记录表";
-- t_pss_material_inspection DDL
CREATE TABLE `t_pss_material_inspection` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键ID",
                                          `n_receipt_id` BIGINT(19) NOT NULL Comment "到货记录ID",
                                          `c_item` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "检验项目(chemical:化学成分,physical:物理性能,appearance:外观质量,size:尺寸规格,weight:重量偏差)",
                                          `c_standard` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "检验标准",
                                          `c_result` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "检验结果",
                                          `n_is_qualified` INT(10) NOT NULL Comment "是否合格(0:不合格,1:合格)",
                                          `c_inspector` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "检验人",
                                          `dt_inspection_time` DATETIME NOT NULL Comment "检验时间",
                                          `c_remark` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                          `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                          `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                          `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                          `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                          `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                          `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                          `n_association_id` BIGINT(19) NULL,
                                          PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1941009270023655427 ROW_FORMAT = Dynamic COMMENT = "原辅料工序检验项目表";
-- t_pss_material_receipt DDL
CREATE TABLE `t_pss_material_receipt` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键ID",
                                       `c_receipt_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "到货单号",
                                       `c_storage_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "入库单号",
                                       `c_batch_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "批次号",
                                       `c_measure_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "计量单号",
                                       `c_purchase_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "采购订单号",
                                       `c_purchase_order_line` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "采购订单行号",
                                       `c_material_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "物料编码",
                                       `c_material_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "物料名称",
                                       `c_material_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "物料类型(raw:原辅料,alloy:合金,scrap:废钢)",
                                       `n_quantity` DECIMAL(10,2) NOT NULL Comment "到货数量",
                                       `n_measure_weight` DECIMAL(10,2) NULL Comment "入库检斤重量",
                                       `n_confirm_weight` DECIMAL(10,2) NULL Comment "入库确认重量",
                                       `c_unit` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "单位",
                                       `dt_receipt_date` DATE NOT NULL Comment "到货日期",
                                       `c_supplier` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "供应商",
                                       `c_storage_location` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "目的库位",
                                       `c_storage_location_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "目的库位名",
                                       `c_status` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "状态(pending:待检验,testing:检验中,completed:已完成,rejected:不合格)",
                                       `c_remark` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                       `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                       `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                       `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                       `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                       `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                       `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                       PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1942395204807979011 ROW_FORMAT = Dynamic COMMENT = "原辅料到货记录表";
-- t_pss_order_combin DDL
CREATE TABLE `t_pss_order_combin` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                                   `c_sales_plan_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "合并计划号（产品类型+年月+四位流水）",
                                   `c_plan_sn` DECIMAL(15,4) NULL Comment "计划内顺序号",
                                   `c_exg_prod_lot_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否紧急生产订单（0：否 1：是）",
                                   `c_customer_cd` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "客户代码",
                                   `c_pro_line` BIGINT(19) NULL Comment "产线id",
                                   `c_pro_line_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线code",
                                   `c_pro_line_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线name",
                                   `c_product_type` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品类型代码(P0003)",
                                   `c_qual_id` BIGINT(19) NULL Comment "质量编码id",
                                   `c_qual_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                   `c_qual_code_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码name",
                                   `c_std_spec` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "标准号",
                                   `c_stl_grd_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                   `c_stl_grd_desc` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                   `c_inventory_id` BIGINT(19) NULL Comment "存货代码id",
                                   `c_inventory_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货代码",
                                   `c_inventory_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货代码name",
                                   `c_size_property` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "定尺类型代码（P0013)",
                                   `n_prod_dia` DECIMAL(15,4) NULL Comment "直径",
                                   `n_prod_thk` DECIMAL(15,4) NULL Comment "厚度(方钢)",
                                   `n_prod_wid` DECIMAL(15,4) NULL Comment "宽度（方钢）",
                                   `n_prod_lenth` DECIMAL(15,4) NULL Comment "目标长度",
                                   `n_order_wgt` DECIMAL(15,4) NULL Comment "订货重量",
                                   `c_wgt_unit` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "重量单位代码（P0023）",
                                   `c_del_datetime_from` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货起始日",
                                   `c_del_datetime_to` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货终止日",
                                   `c_combin_state` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划状态",
                                   `c_aggregate_plan_id` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综合生产计划号",
                                   `n_aggregate_plan_sn` DECIMAL(15,4) NULL Comment "综合生产计划序号",
                                   `c_product_task_list_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                   `c_plan_datetime_from` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划有效期启",
                                   `c_plan_datetime_to` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划有效期止",
                                   `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号（备用）",
                                   `c_order_explain` VARCHAR(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                   `c_operation_note` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "操作记录",
                                   `c_is_add_plan` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否追加计划",
                                   `c_old_state` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上一个状态",
                                   `dt_state_change_time` DATETIME(3) NULL Comment "状态变更时间",
                                   `c_dept_no` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "部门编号",
                                   `c_old_operation_note` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上一次的操作记录",
                                   `c_order_cust_type` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单客户类型",
                                   `c_oldcode` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "旧编码",
                                   `n_enabled_mark` INT(10) NULL,
                                   `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                   `dt_create_date_time` DATETIME NULL,
                                   `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                   `dt_modify_date_time` DATETIME NULL,
                                   `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                   PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "合并订单表(销售计划)";
-- t_pss_order_cost DDL
CREATE TABLE `t_pss_order_cost` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                                 `c_line_cd` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                 `c_order_no` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产订单号",
                                 `c_pro_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品名称",
                                 `n_sum_cost` DECIMAL(20,2) NULL Comment "总成本",
                                 `n_labor_cost` DECIMAL(20,2) NULL Comment "人工成本",
                                 `n_material_cost` DECIMAL(20,2) NULL Comment "材料成本",
                                 `n_manufact_cost` DECIMAL(20,2) NULL Comment "制造成本",
                                 `n_unit_cost` DECIMAL(20,2) NULL Comment "单位成本",
                                 `n_estimated_profit` DECIMAL(20,2) NULL Comment "预计利润",
                                 `n_order_wgt` DECIMAL(20,2) NULL Comment "订单产量",
                                 `c_plan_cycle` DECIMAL(20,2) NULL Comment "计划周期",
                                 `c_cost_account_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本科目名称",
                                 `c_cost_account_code` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本科目代码",
                                 `n_planned_amount` DECIMAL(20,2) NULL Comment "计划金额",
                                 `n_actual_amount` DECIMAL(20,2) NULL Comment "实际金额",
                                 `n_difference_amount` DECIMAL(20,2) NULL Comment "差异金额",
                                 `n_difference_rate` DECIMAL(10,4) NULL Comment "差异率",
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                 `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                 `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                 `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                 `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 31 ROW_FORMAT = Dynamic COMMENT = "生产订单成本分析";
-- t_pss_order_info DDL
CREATE TABLE `t_pss_order_info` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                                 `c_order_no` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "订单号（订单类型+年月日+3位顺序号）",
                                 `c_order_type` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单类型（P0004）",
                                 `c_customer_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "客户编码",
                                 `c_product_type` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品类型（Y0006）",
                                 `c_product_line_id` BIGINT(19) NULL Comment "产线id（P0001）",
                                 `c_product_line_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线代码（P0001）",
                                 `c_product_line_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线名称（P0001）",
                                 `c_contract_no` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售合同号",
                                 `c_qual_id` BIGINT(19) NULL Comment "质量编码id",
                                 `c_qual_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                 `c_qual_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码明",
                                 `c_stdcode` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "标准编号",
                                 `c_stl_grd_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种编码",
                                 `c_stl_grd_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                 `c_inventory_id` BIGINT(19) NULL Comment "存货代码id",
                                 `c_inventory_cd` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货代码cd",
                                 `c_inventory_name` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货代码name",
                                 `c_size_property` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "定尺类型（P0013）",
                                 `n_prod_dia` DECIMAL(15,4) NULL Comment "直径（直条等）",
                                 `n_prod_thk` DECIMAL(15,4) NULL Comment "厚度（方钢等）",
                                 `n_prod_wid` DECIMAL(15,4) NULL Comment "宽度（方钢等）",
                                 `n_prod_lenth` DECIMAL(15,4) NULL Comment "长度目标（卷和高线为9999999.999）",
                                 `n_prod_len_min` DECIMAL(15,4) NULL Comment "长度最小",
                                 `n_prod_len_max` DECIMAL(15,4) NULL Comment "长度最大",
                                 `c_wgt_fl` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "称重方式（P0016）",
                                 `n_order_wgt` DECIMAL(15,4) NULL Comment "订单重量(成品替代后订单剩余重量)",
                                 `c_wgt_unit` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "重量单位（P0023）",
                                 `n_ext_rate` DECIMAL(15,4) NULL Comment "允许超交比率(%)",
                                 `n_rem_rate` DECIMAL(15,4) NULL Comment "允许欠交比率(%)",
                                 `c_fpoststate_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货方式代码(P0008)",
                                 `c_del_datetime` DATETIME NULL Comment "目标交货日期",
                                 `n_del_interval` DECIMAL(15,4) NULL Comment "交货周期",
                                 `c_del_interval_unit` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货周期单位（P0026)",
                                 `c_pak_way_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "包装方式代码(P0030)",
                                 `c_cust_spec_print` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "客户特殊包装要求",
                                 `c_end_datetime` DATETIME NULL Comment "订单结束日期",
                                 `c_order_state` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单状态代码（P0020）",
                                 `c_is_combined` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否被合并（Y/N）",
                                 `c_sales_plan_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售计划号",
                                 `c_sales_plan_sn` DECIMAL(15,4) NULL Comment "销售计划序号",
                                 `c_aggregate_plan_id` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综合生产计划号",
                                 `n_aggregate_plan_sn` DECIMAL(15,4) NULL Comment "综合生产计划序号",
                                 `c_product_task_list_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单编号",
                                 `c_sales_dept_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售部门",
                                 `c_exg_prod_lot_fl` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否紧急订单（0/1    0-普通订单 ，1-紧急订单）",
                                 `c_order_explain` VARCHAR(900) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单备注",
                                 `n_order_page_no` DECIMAL(15,4) NULL Comment "订单分组页号",
                                 `c_approval_emp` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "核准人",
                                 `dt_approval_datetime` DATETIME NULL Comment "核准时间",
                                 `c_operation_note` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单操作说明",
                                 `c_old_state` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上一个状态",
                                 `dt_old_input_datetime` DATETIME NULL Comment "从回收站释放前的订单录入时间",
                                 `dt_state_change_time` DATETIME NULL Comment "状态变更时间",
                                 `n_reduce_weight` DECIMAL(15,4) NULL Comment "计划扣重",
                                 `c_reduce_emp_id` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划扣重录入人员",
                                 `dt_reduce_input_time` DATETIME NULL Comment "计划扣重录入时间",
                                 `c_reduce_review_status` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划扣重审核状态（X—正常订单 W—等待审核 P—审核通过 C—审核否决）",
                                 `c_reduce_review_emp` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划扣重审核人员",
                                 `dt_reduce_review_time` DATETIME NULL Comment "计划扣重审核时间",
                                 `c_reduce_operat_log` VARCHAR(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划扣重操作日志",
                                 `c_qual_des_log` VARCHAR(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量设计信息日志",
                                 `c_old_operation_note` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上一次的操作记录",
                                 `c_order_cust_type` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单客户类型",
                                 `c_qrcode` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "二维码类型",
                                 `c_ins_source` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "数据来源 （电商接口DS）暂时作为原物料编码使用，切换完成后仍作为原字段",
                                 `c_smp_fl` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否保性能",
                                 `c_ship_way` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "运输方式",
                                 `n_plan_order_wgt` DECIMAL(15,4) NULL Comment "订单计划重量",
                                 `n_sch_order_wgt` DECIMAL(15,4) NULL Comment "已排产数量",
                                 `n_order_cnt` DECIMAL(15,4) NULL Comment "订单件数",
                                 `n_prod_wgt` DECIMAL(15,4) NULL Comment "成品单重",
                                 `n_thk_bias_max` DECIMAL(15,4) NULL Comment "厚度公差最大值",
                                 `n_thk_bias_min` DECIMAL(15,4) NULL Comment "厚度公差最小值",
                                 `n_wth_bias_max` DECIMAL(15,4) NULL Comment "宽度公差最大值",
                                 `n_wth_bias_min` DECIMAL(15,4) NULL Comment "宽度公差最小值",
                                 `n_checker_flag` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否滑纹板",
                                 `n_checker_height` BIGINT(19) NULL Comment "滑纹板高度",
                                 `n_prod_wgt_max` DECIMAL(15,4) NULL Comment "成品重量最大值",
                                 `n_prod_wgt_min` DECIMAL(15,4) NULL Comment "成品重量最小值",
                                 `c_morder_id` VARCHAR(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上级订单号",
                                 `c_sale_contract_no` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售合同号",
                                 `c_item_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格代码",
                                 `c_mat_item` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成品规格",
                                 `n_enabled_mark` INT(10) NULL,
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME NULL,
                                 `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                 `dt_modify_date_time` DATETIME NULL,
                                 `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "订单表";
-- t_pss_p_face DDL
CREATE TABLE `t_pss_p_face` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                             `c_plate_id` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "线卷号/坯料号/件次号",
                             `n_chk_seq` INT(10) NULL Comment "检查次数",
                             `c_chk_pos` BIGINT(19) NULL Comment "检查工序",
                             `c_chk_pos_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检查工序cd",
                             `c_chk_pos_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检查工序name",
                             `c_stl_grd_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                             `c_stl_grd_desc` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述/名称",
                             `c_plate_face` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "线卷面",
                             `c_defect_seq` BIGINT(19) NULL Comment "缺陷序号",
                             `c_defect_type` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "缺陷代码",
                             `c_defect_pos` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "缺陷位置",
                             `c_defect_desc` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "缺陷描述",
                             `c_disp_pro` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理建议",
                             `c_disp_manner` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理方式",
                             `c_disp_result` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理结果",
                             `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                             `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班别",
                             `c_grind_operater` VARCHAR(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "修磨人",
                             `c_grind_shift` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "修磨班次",
                             `c_grind_crew` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "修磨班别",
                             `c_grind_pos` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "修磨工序",
                             `dt_grind_time` DATE NULL Comment "修磨时间",
                             `c_face_rlt` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "表面等级",
                             `c_remarks` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                             `c_defect_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "缺陷名称",
                             `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                             `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                             `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                             `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                             `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                             `n_enabled_mark` INT(10) NULL,
                             PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1944663324338388994 ROW_FORMAT = Dynamic COMMENT = "钢材表面实绩";
-- t_pss_p_judge DDL
CREATE TABLE `t_pss_p_judge` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                              `c_plate_id` VARCHAR(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "件次号",
                              `c_body_result` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "外形等级",
                              `c_size_result` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "尺寸等级",
                              `c_jun_check_lvl` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "初判等级",
                              `c_face_result` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "表面等级",
                              `dt_jun_check_time` DATETIME NULL Comment "初判时间",
                              `c_jun_check_operator` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "初判人员",
                              `c_mod_jun_check_lvl` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "初判更正等级",
                              `c_mtal_grd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "性能等级",
                              `c_ust_grd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤等级",
                              `c_prod_grd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综判等级",
                              `c_end_judge_id` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综判人员",
                              `c_prod_grd_reason` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综判不合原因",
                              `dt_prod_grd_time` DATETIME NULL Comment "综判时间",
                              `c_end_judge_mod_id` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综判修改人员",
                              `dt_end_judge_mod_time` DATETIME NULL Comment "综判修改时间",
                              `c_memo` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                              `c_rescue_fl` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否性能挽救",
                              `c_old_face_result` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原表面等级",
                              `c_old_body_result` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原外形等级",
                              `c_old_size_result` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原尺寸等级",
                              `n_old_lth` DECIMAL(15,1) NULL Comment "原长度",
                              `n_old_wth` DECIMAL(8,3) NULL Comment "原宽度",
                              `n_old_thk` DECIMAL(8,3) NULL Comment "原厚度",
                              `c_w_pro` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "废品标记",
                              `c_org_prod_grd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原综判等级",
                              `c_org_mtal_grd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原性能等级",
                              `c_prod_grd_cha` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "改判等级",
                              `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                              `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                              `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                              `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                              `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                              `n_enabled_mark` INT(10) NULL,
                              PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1930450637261832195 ROW_FORMAT = Dynamic COMMENT = "钢材判定表/轧钢坯料综合判定表";
-- t_pss_p_size DDL
CREATE TABLE `t_pss_p_size` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                             `c_plate_id` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "线卷号/坯料号/件次号",
                             `n_chk_seq` INT(10) NULL Comment "检查次数",
                             `c_chk_pos` BIGINT(19) NULL Comment "检查工序id",
                             `c_chk_pos_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检查工序code",
                             `c_chk_pos_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检查工序name",
                             `c_stl_grd_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                             `c_stl_grd_desc` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述/名称",
                             `c_plate_face` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "线卷面",
                             `n_defect_seq` BIGINT(19) NULL Comment "缺陷序号",
                             `c_defect_type` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "缺陷代码",
                             `c_defect_pos` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "缺陷位置",
                             `c_defect_desc` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "缺陷描述",
                             `c_disp_pro` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理建议",
                             `c_disp_manner` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理方式",
                             `c_disp_result` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理结果",
                             `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                             `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班别",
                             `c_grind_operater` VARCHAR(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理人",
                             `c_grind_shift` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理班次",
                             `c_grind_crew` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理班别",
                             `c_size_rlt` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "尺寸等级",
                             `c_remarks` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                             `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                             `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                             `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                             `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                             `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                             `n_enabled_mark` INT(10) NULL,
                             PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1944666352613294082 ROW_FORMAT = Dynamic COMMENT = "钢材尺寸实绩";
-- t_pss_pos_move_time DDL
CREATE TABLE `t_pss_pos_move_time` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                    `c_start_process` BIGINT(19) NULL Comment "开始工序/工位",
                                    `c_start_process_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "开始工序/工位编码",
                                    `c_start_process_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "开始工序/工位名称",
                                    `c_end_process` BIGINT(19) NULL Comment "结束工序/工位",
                                    `c_end_process_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "结束工序/工位编码",
                                    `c_end_process_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "结束工序/工位名称",
                                    `c_pro_line` BIGINT(19) NULL Comment "产线",
                                    `c_pro_line_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线编码",
                                    `c_pro_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线名称",
                                    `n_move_time` DECIMAL(15,4) NULL Comment "移动时间-分钟",
                                    `n_enabled_mark` INT(10) NULL,
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME NULL,
                                    `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                    `dt_modify_date_time` DATETIME NULL,
                                    `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1947196796844060675 ROW_FORMAT = Dynamic COMMENT = "钢包移动时间标准;";
-- t_pss_pos_time DDL
CREATE TABLE `t_pss_pos_time` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                               `c_stl_grd_cd` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                               `c_process` BIGINT(19) NULL Comment "工序/工位",
                               `c_process_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序编码",
                               `c_process_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序名称",
                               `c_pro_line` BIGINT(19) NULL Comment "产线",
                               `c_pro_line_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线编码",
                               `c_pro_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线名称",
                               `c_stl_grd_desc` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称",
                               `n_work_time` DECIMAL(15,4) NULL Comment "冶炼时间-分钟",
                               `n_enabled_mark` INT(10) NULL,
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME NULL,
                               `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                               `dt_modify_date_time` DATETIME NULL,
                               `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1947196970152701954 ROW_FORMAT = Dynamic COMMENT = "钢包工位时间标准;";
-- t_pss_prod_res DDL
CREATE TABLE `t_pss_prod_res` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                               `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "件次号()",
                               `n_cnt` INT(10) NULL Comment "数量",
                               `c_pre_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原料号(找本表)",
                               `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                               `c_heat_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                               `c_roll_sch_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "轧制批次号",
                               `c_mat_id_mth` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "母板号",
                               `c_status` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料状态",
                               `c_pre_status` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "前物料状态",
                               `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                               `c_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码",
                               `c_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                               `c_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                               `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                               `n_mat_thk` DECIMAL(12,3) NULL Comment "订单/公称厚度",
                               `n_mat_wid` DECIMAL(12,3) NULL Comment "订单/公称宽度",
                               `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                               `n_mat_dia_o` DECIMAL(12,3) NULL Comment "钢卷外径",
                               `n_mat_lth` DECIMAL(12,3) NULL Comment "长度",
                               `n_mat_wgt` DECIMAL(12,3) NULL Comment "入库检斤重量",
                               `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "计算重量",
                               `n_mat_act_wgt` DECIMAL(12,3) NULL Comment "出库检斤重量",
                               `c_std_spec` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准号",
                               `c_smp_lot` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验批号()",
                               `dt_judge_time` DATETIME NULL Comment "综合判定时间 Q",
                               `c_prod_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "终判等级()",
                               `c_prel_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "初判等级",
                               `c_chem_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成分等级",
                               `c_size_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "尺寸等级",
                               `c_surf_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "外观等级",
                               `c_mtal_grd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "性能等级",
                               `c_w_pro` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤结果",
                               `dt_w_date` DATETIME NULL Comment "探伤日期",
                               `c_upd_not` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "废品标记",
                               `c_upd_rsn` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "不合格原因",
                               `c_bd_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否改配合同，否：Null 是：1",
                               `c_cert_id` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量证明书号",
                               `dt_pch_time` DATETIME NULL Comment "购入时间",
                               `dt_prod_time` DATETIME NULL Comment "生产时间",
                               `c_prod_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产班次",
                               `c_prod_group` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产班别",
                               `c_occr_cd` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "信息来源",
                               `c_factory` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产地",
                               `c_supplier` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "供货商",
                               `c_sale_no` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单号",
                               `c_sale_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单行号",
                               `c_task_list_id` VARCHAR(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                               `c_order_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "采购订单号",
                               `c_order_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "采购订单行号",
                               `c_ord_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单材/余材标志",
                               `c_order_id` VARCHAR(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产订单号=ORDER_ID",
                               `c_multi_ord_tag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否多订单",
                               `c_woo_rsn` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "余材原因",
                               `c_hcr_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "板坯热送标志",
                               `c_smp_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否试样板",
                               `c_line_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "当前产线",
                               `c_nc_yard_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产销仓库代码",
                               `c_loc` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                               `n_loc_lvl` INT(10) NULL Comment "层号",
                               `c_pre_out` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划去向",
                               `c_memo` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                               `c_mod_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "修改人",
                               `dt_mod_time` DATETIME NULL Comment "修改时间",
                               `c_pgm_id` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "最后修改程序",
                               `c_month_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上传标志",
                               `c_month_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上传人员",
                               `dt_month_time` DATETIME NULL Comment "上传时间",
                               `c_reweight_fl` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "复秤标记",
                               `c_reweight` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "复秤重量",
                               `c_re_wgt_flag` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "复秤类型",
                               `c_two_rewgt_fl` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "二次复秤标记",
                               `dt_two_wgt_time` DATETIME NULL Comment "二次复秤日期",
                               `c_two_wgt_emp` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "二次复秤人员",
                               `c_two_wgt` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "二次复秤重量",
                               `c_del_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "删除标记",
                               `c_cha_empid` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "改判人",
                               `dt_cha_time` DATETIME NULL Comment "改判时间",
                               `c_cha_memo` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "改判备注",
                               `c_pre_mat_qul_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原材质代码",
                               `c_pre_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原钢种",
                               `n_pre_mat_lth` DECIMAL(12,3) NULL Comment "原长度",
                               `n_pre_mat_wgt_cal` DECIMAL(12,3) NULL Comment "原计算重量",
                               `c_pre_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原物料编码",
                               `c_pre_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原物料名称",
                               `c_is_lock` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否锁定",
                               `n_mat_act_thk` DECIMAL(12,3) NULL Comment "实际厚度",
                               `n_mat_act_wid` DECIMAL(12,3) NULL Comment "实际宽度",
                               `n_mat_act_lth` DECIMAL(12,3) NULL Comment "实际长度",
                               `n_pre_mat_thk` DECIMAL(12,3) NULL Comment "原物料厚度",
                               `n_pre_mat_wid` DECIMAL(12,3) NULL Comment "原物料宽度",
                               `n_pre_mat_act_thk` DECIMAL(12,3) NULL Comment "原物料实际厚度",
                               `n_pre_mat_act_wid` DECIMAL(12,3) NULL Comment "原物料实际宽度",
                               `n_pre_mat_act_lth` DECIMAL(12,3) NULL Comment "原物料实际长度",
                               `n_package_wgt` DECIMAL(10,3) NULL Comment "包装物重量",
                               `c_mat_id_show` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "平整成品展示卷号",
                               `c_is_print` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否打印标牌",
                               `c_item_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格代码",
                               `c_car_no` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "装车车号",
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                               `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                               `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                               `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                               `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "热轧钢卷主表";
-- t_pss_prod_slab_std DDL
CREATE TABLE `t_pss_prod_slab_std` (`n_id` BIGINT(19) NOT NULL Comment "序列号",
                                    `c_stl_grd_cd` VARCHAR(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_stl_grd_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                    `n_prod_wth_max` DECIMAL(15,4) NULL Comment "产品宽上限",
                                    `n_prod_wth_min` DECIMAL(15,4) NULL Comment "产品宽下限",
                                    `n_prod_thk_max` DECIMAL(15,4) NULL Comment "产品厚上限",
                                    `n_prod_thk_min` DECIMAL(15,4) NULL Comment "产品厚下限",
                                    `n_prod_std_max` DECIMAL(15,4) NULL Comment "产品规格上限",
                                    `n_prod_std_min` DECIMAL(15,4) NULL Comment "产品规格下限",
                                    `n_prod_len_max` DECIMAL(15,4) NULL Comment "产品长度上限",
                                    `n_prod_len_min` DECIMAL(15,4) NULL Comment "产品长度下限",
                                    `n_slab_wth` DECIMAL(15,4) NULL Comment "坯料宽",
                                    `n_slab_thk` DECIMAL(15,4) NULL Comment "坯料厚",
                                    `n_slab_len` DECIMAL(15,4) NULL Comment "坯料长度",
                                    `c_product_code` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品类型(P0003)",
                                    `c_line_cd` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                    `n_slab_wgt` DECIMAL(15,4) NULL Comment "坯料重量",
                                    `c_bc_len` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "倍尺长",
                                    `n_cut_count` DECIMAL(15,4) NULL Comment "切割支数",
                                    `n_bund_count` DECIMAL(15,4) NULL Comment "打捆支数",
                                    `n_bund_wgt` DECIMAL(15,4) NULL Comment "打捆重量",
                                    `c_gc` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "公差",
                                    `c_tyd` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "椭圆度",
                                    `n_ysb` DECIMAL(15,4) NULL Comment "压缩比",
                                    `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME NULL,
                                    `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                    `dt_modify_date_time` DATETIME NULL,
                                    `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "产品对应坯料规格表";
-- t_pss_pslab_chos DDL
CREATE TABLE `t_pss_pslab_chos` (`n_id` BIGINT(19) NOT NULL Comment "顺序号",
                                 `c_plan_slab_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "计划坯料号/件次号",
                                 `c_task_list_id` VARCHAR(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                 `c_stl_grd_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                 `c_stl_grd_desc` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述",
                                 `c_mat_qul_id` BIGINT(19) NULL Comment "质量编码",
                                 `c_mat_qul_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                 `c_mat_qul_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                 `n_slab_thk` DECIMAL(15,4) NULL Comment "板坯厚度",
                                 `n_slab_wth` DECIMAL(15,4) NULL Comment "板坯宽度",
                                 `n_slab_len` DECIMAL(15,4) NULL Comment "板坯长度",
                                 `c_ht_heat` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否可以头尾炉",
                                 `c_ht_slab` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否可以头尾坯",
                                 `c_hot_flag` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否热装热送",
                                 `c_test_flag` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否检验",
                                 `c_sent_place` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "板坯去向",
                                 `c_dingchi_type` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "定尺类型",
                                 `c_slab_type` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "坯料类型",
                                 `c_use_std` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准",
                                 `c_cast_id` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸机号",
                                 `c_lljg` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否来料加工材",
                                 `c_mat_id` BIGINT(19) NULL Comment "物料id",
                                 `c_mat_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                                 `c_mat_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料name",
                                 `c_conta_id` VARCHAR(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "合同号",
                                 `c_fulw_rad` DECIMAL(15,4) NULL Comment "溢短装比",
                                 `c_workout_tim` DATETIME(3) NULL Comment "要求完工日期",
                                 `c_spc_need` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "特殊要求",
                                 `c_send_empid` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "发送人",
                                 `c_send_dt` DATETIME(3) NULL Comment "发送时间",
                                 `c_check_in_dt` DATETIME(3) NULL Comment "接受时间",
                                 `c_chos_emp` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "提取人",
                                 `c_chos_dt` DATETIME(3) NULL Comment "提取时间",
                                 `c_slab_wgt` DECIMAL(15,4) NULL Comment "板坯重量",
                                 `c_has_edit` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "已经编制(1:全部编制,2:部分编制)",
                                 `c_edit_wgt` DECIMAL(15,4) NULL Comment "已经编制重量",
                                 `c_rem_slab_flg` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "余材标志",
                                 `c_rem_crtway` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "余材产生方式(1:已经加入计划板坯表；2:未加入计划板坯表)",
                                 `c_plan_heat_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划炉次号",
                                 `c_heat_seq` DECIMAL(15,4) NULL Comment "炉次内序号",
                                 `n_enabled_mark` INT(10) NULL,
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME NULL,
                                 `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                 `dt_modify_date_time` DATETIME NULL,
                                 `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "计划坯料提取表";
-- t_pss_qual_std_lib_main DDL
CREATE TABLE `t_pss_qual_std_lib_main` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                        `c_stdcode` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检标准编码",
                                        `c_stdname` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检标准名称",
                                        `c_itemname` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检项目名称",
                                        `c_pro_line` BIGINT(19) NULL Comment "产线id",
                                        `c_pro_line_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线code",
                                        `c_pro_line_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线name",
                                        `c_material` BIGINT(19) NULL Comment "物料id",
                                        `c_material_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料code",
                                        `c_material_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料name",
                                        `c_stdtype` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检标准类别",
                                        `c_stdtype_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检标准类别name",
                                        `c_execute` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "标准类型",
                                        `c_project_attribute_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准",
                                        `n_issued` INT(10) NULL Comment "是否下发;0未下发，1下发",
                                        `c_value_type` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "判定等级",
                                        `n_enabled_mark` INT(10) NULL,
                                        `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                        `dt_create_date_time` DATETIME NULL,
                                        `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                        `dt_modify_date_time` DATETIME NULL,
                                        `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                        PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1947138303327973379 ROW_FORMAT = Dynamic COMMENT = "质检标准库管理主表;";
-- t_pss_qual_std_lib_sub DDL
CREATE TABLE `t_pss_qual_std_lib_sub` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                       `c_Stdcode` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检标准编码",
                                       `c_stdname` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检标准名称",
                                       `c_quality_standard_lib_item_id` BIGINT(19) NULL Comment "质检项目id",
                                       `c_quality_standard_lib_item_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检项目编码",
                                       `c_quality_standard_lib_item_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检项目名称",
                                       `c_measures_unit` BIGINT(19) NULL Comment "计量单位id",
                                       `c_measures_unit_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量单位code",
                                       `c_measures_unit_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量单位name",
                                       `c_value_type` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "值类型",
                                       `n_testcount` INT(10) NULL Comment "试验次数",
                                       `c_project_attribute_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目属性",
                                       `n_precision` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "数据精度",
                                       `c_lowersymbol` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下限比较符",
                                       `n_lowervalue` DECIMAL(15,4) NULL Comment "下限值",
                                       `c_uppersymbol` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上限比较符",
                                       `n_uppervalue` DECIMAL(15,4) NULL Comment "上限值",
                                       `n_remark` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                       `n_enabled_mark` INT(10) NULL,
                                       `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                       `dt_create_date_time` DATETIME NULL,
                                       `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                       `dt_modify_date_time` DATETIME NULL,
                                       `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                       PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1947186213742653443 ROW_FORMAT = Dynamic COMMENT = "质检标准库管理子表;";
-- t_pss_quality_code DDL
CREATE TABLE `t_pss_quality_code` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                   `c_qual_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量代码;质量编码",
                                   `c_stl_grd_cd` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                   `c_stl_grd_desc` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述",
                                   `c_qual_code_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "名称",
                                   `c_qual_code_abb` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "简称",
                                   `c_describe` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "描述;",
                                   `c_std_name_chi` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "标准名称（中）",
                                   `c_std_name_eng` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "标准名称（英）",
                                   `c_op_std_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准",
                                   `c_material_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成品物料名称",
                                   `c_material_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成品物料编码",
                                   `c_mate_id` BIGINT(19) NULL Comment "成品物料Id",
                                   `c_status` INT(10) NULL Comment "状态（新建/已转换）",
                                   `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                   `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                   `dt_create_date_time` DATETIME NULL,
                                   `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                   `dt_modify_date_time` DATETIME NULL,
                                   `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                   PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1947188005197000706 ROW_FORMAT = Dynamic COMMENT = "质量编码主表;";
-- t_pss_quality_code_lib DDL
CREATE TABLE `t_pss_quality_code_lib` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                       `c_qual_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                       `c_qual_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                       `c_qual_std_lib_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检标准编码",
                                       `c_qual_std_lib_item_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检项目编码",
                                       `c_qual_std_lib_item_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检项目名称",
                                       `c_stl_grd_cd` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                       `c_stl_grd_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                       `c_measures_unit` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量单位",
                                       `c_value_type` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "值类型",
                                       `n_test_count` INT(10) NULL Comment "试验次数",
                                       `c_project_attribute_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目属性",
                                       `n_precision` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "数据精度",
                                       `c_lower_symbol` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下限比较符",
                                       `n_lower_value` DECIMAL(15,4) NULL Comment "下限值",
                                       `c_upper_symbol` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上限比较符",
                                       `n_upper_value` DECIMAL(15,4) NULL Comment "上限值",
                                       `n_remark` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                       `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                       `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                       `dt_create_date_time` DATETIME NULL,
                                       `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                       `dt_modify_date_time` DATETIME NULL,
                                       `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                       PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1930499235622084611 ROW_FORMAT = Dynamic COMMENT = "质量编码库表;";
-- t_pss_quality_code_sub DDL
CREATE TABLE `t_pss_quality_code_sub` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                       `c_qual_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质量编码",
                                       `c_qual_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                       `n_fstditem_id` BIGINT(19) NULL Comment "质检标准Id",
                                       `n_fstditem_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质检标准名称",
                                       `n_fsimplename` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "简称",
                                       `n_fstditem` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质检标准编码",
                                       `n_fdescription` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "描述",
                                       `n_fjudgetype` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "标准类型",
                                       `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                       `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                       `dt_create_date_time` DATETIME NULL,
                                       `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                       `dt_modify_date_time` DATETIME NULL,
                                       `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                       PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1943189547649736706 ROW_FORMAT = Dynamic COMMENT = "质量编码子表";
-- t_pss_rate_std DDL
CREATE TABLE `t_pss_rate_std` (`n_id` BIGINT(19) NOT NULL Comment "序号",
                               `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "钢种代码",
                               `c_stl_grd_desc` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "钢种描述/名称",
                               `n_plate_slab_rate` DECIMAL(15,4) NULL Comment "成材率",
                               `n_slab_stl_rate` DECIMAL(15,4) NULL Comment "收得率",
                               `n_density` DECIMAL(15,4) NOT NULL Comment "密度(t/m)",
                               `n_enabled_mark` INT(10) NULL,
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME NULL,
                               `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                               `dt_modify_date_time` DATETIME NULL,
                               `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "钢种收得率/成材率/密度";
-- t_pss_report_print DDL
CREATE TABLE `t_pss_report_print` (`n_id` BIGINT(19) NOT NULL Comment "顺序号",
                                   `c_car_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "车号",
                                   `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种",
                                   `c_stl_grd_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称",
                                   `c_item` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                   `n_cnt` INT(10) NULL Comment "件数",
                                   `n_wgt` DECIMAL(30,5) NULL Comment "重量",
                                   `c_status` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                                   `c_check_item01` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "C",
                                   `c_check_item02` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Si",
                                   `c_check_item03` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Mn",
                                   `c_check_item04` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "P",
                                   `c_check_item05` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "S",
                                   `c_check_item06` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Cr",
                                   `c_check_item07` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Ni",
                                   `c_check_item08` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Cu",
                                   `c_check_item09` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Mo",
                                   `c_check_item10` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Ti",
                                   `c_check_item11` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "V",
                                   `c_check_item12` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Al",
                                   `c_check_item13` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Nb",
                                   `c_check_item14` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Ca",
                                   `c_check_item15` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "B",
                                   `c_check_item16` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "O",
                                   `c_check_item17` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "N",
                                   `c_check_item18` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "H",
                                   `c_check_item19` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "中心缩孔",
                                   `c_check_item20` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "中心疏松",
                                   `c_check_item21` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "中心偏析",
                                   `c_check_item22` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "锭行偏析",
                                   `c_check_item23` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "中心裂纹",
                                   `c_check_item24` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "中间裂纹",
                                   `c_check_item26` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "皮下裂纹",
                                   `c_check_item27` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "皮下气泡",
                                   `c_pro_emp` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "审核人",
                                   `dt_pro_time` DATE NULL Comment "审核时间",
                                   `c_report_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质保书编号",
                                   `c_heat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                                   `c_order_user` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "收货单位",
                                   `c_mat_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                                   `c_std_spec` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准",
                                   `c_mat_qul_cd` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质代码",
                                   `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                   `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                   `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                   `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                   `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                   `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                   PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "质保书";
-- t_pss_roll_sch DDL
CREATE TABLE `t_pss_roll_sch` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                               `c_sch_id` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "轧制顺序计划id",
                               `c_prev_sch_id` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "前顺序计划id",
                               `n_seq_in_mill` DECIMAL(15,4) NULL Comment "同轧制队列中顺序",
                               `c_ref_completed_fl` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否完结(参考值)",
                               `c_memo` VARCHAR(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                               `c_dispatch_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "调度单号",
                               `c_line_no` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                               `c_heat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                               `c_make_fl` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "自动手动标志 0手动 1自动",
                               `c_prod_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品类型",
                               `n_spec` DECIMAL(15,4) NULL Comment "直径",
                               `n_prod_len` DECIMAL(15,4) NULL Comment "产品长度",
                               `c_plan_finishing_path` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精整路径",
                               `c_size_property` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "定尺代码",
                               `c_matcode` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货编码 （ERP）",
                               `c_matname` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "存货名称 （ERP）",
                               `c_stl_grd_cd` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品钢种",
                               `c_mat_qul_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品材质",
                               `n_prod_wth` DECIMAL(15,4) NULL Comment "产品宽",
                               `c_slab_spec` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢坯规格",
                               `c_shift` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                               `c_crew` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班别",
                               `c_heat_head` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "V 真空,X高线,D大棒,B棒卷",
                               `c_stl_grd_out` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "优质钢判断",
                               `c_memomemo` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精整备注",
                               `c_oldcode` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "旧存货编码",
                               `c_order_no` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                               `c_item_cd` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成品代码",
                               `c_mat_item` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成品规格",
                               `n_dofinal_wgt` DECIMAL(15,4) NULL Comment "挂单重量",
                               `c_status` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                               `n_dofinal_count` DECIMAL(15,4) NULL Comment "挂单数量",
                               `c_std_spec` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准",
                               `n_enabled_mark` INT(10) NULL,
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME NULL,
                               `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                               `dt_modify_date_time` DATETIME NULL,
                               `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "轧制顺序计划";
-- t_pss_roll_std DDL
CREATE TABLE `t_pss_roll_std` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                               `c_quality_id` BIGINT(19) NOT NULL Comment "质量编码id",
                               `c_quality_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质量编码",
                               `c_quality_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                               `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                               `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                               `n_prod_thk_min` DECIMAL(15,4) NOT NULL Comment "产品厚度",
                               `n_prod_thk_max` DECIMAL(15,4) NOT NULL Comment "产品厚度",
                               `n_control_roll` BIGINT(19) NULL Comment "控制轧制",
                               `n_start_temp` DECIMAL(15,4) NULL Comment "开轧温度℃",
                               `n_first_roll_temp` DECIMAL(15,4) NULL Comment "粗轧轧制温度℃",
                               `n_sec_roll_temp` DECIMAL(15,4) NULL Comment "精轧轧制温度℃",
                               `n_final_temp` DECIMAL(15,4) NULL Comment "终轧温度℃",
                               `c_reserver1` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用1",
                               `c_reserver2` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用2",
                               `c_reserver3` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用3",
                               `c_reserver4` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用4",
                               `c_reserver5` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用5",
                               `c_reserver6` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用6",
                               `c_reserver7` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用7",
                               `c_reserver8` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用8",
                               `c_reserver9` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用9",
                               `c_reserver10` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                               `n_enabled_mark` INT(10) NULL,
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME NULL,
                               `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                               `dt_modify_date_time` DATETIME NULL,
                               `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "轧制工艺参数";
-- t_pss_roll_std_info DDL
CREATE TABLE `t_pss_roll_std_info` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                                    `c_order_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "订单号",
                                    `c_qual_id` BIGINT(19) NULL Comment "质量编码id",
                                    `c_qual_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                    `c_qual_code_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码简称",
                                    `c_stl_grd_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_stl_grd_desc` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                    `n_control_roll` BIGINT(19) NULL Comment "控制轧制",
                                    `n_start_temp` DECIMAL(15,4) NULL Comment "开轧温度℃",
                                    `n_first_roll_temp` DECIMAL(15,4) NULL Comment "粗轧轧制温度℃",
                                    `n_sec_roll_temp` DECIMAL(15,4) NULL Comment "精轧轧制温度℃",
                                    `n_final_temp` DECIMAL(15,4) NULL Comment "终轧温度℃",
                                    `c_reserver1` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用1",
                                    `c_reserver2` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用2",
                                    `c_reserver3` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用3",
                                    `c_reserver4` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用4",
                                    `c_reserver5` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用5",
                                    `c_reserver6` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用6",
                                    `c_reserver7` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用7",
                                    `c_reserver8` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用8",
                                    `c_reserver9` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用9",
                                    `c_reserver10` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                                    `n_enabled_mark` INT(10) NULL,
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME NULL,
                                    `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                    `dt_modify_date_time` DATETIME NULL,
                                    `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "轧制工艺参数质量设计";
-- t_pss_slab_face DDL
CREATE TABLE `t_pss_slab_face` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                                `c_slab_id` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "坯料号/件次号",
                                `c_defect_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "缺陷代码",
                                `c_stl_grd_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种",
                                `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称",
                                `c_face_rlt` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "表面等级",
                                `c_old_face_rlt` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原表面等级",
                                `c_remark` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量描述",
                                `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                `n_enabled_mark` INT(10) NULL,
                                PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1944637394937114626 ROW_FORMAT = Dynamic COMMENT = "坯料表面管理";
-- t_pss_slab_heat_std DDL
CREATE TABLE `t_pss_slab_heat_std` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                                    `c_quality_id` BIGINT(19) NOT NULL Comment "质量编码id",
                                    `c_quality_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质量编码",
                                    `c_quality_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                    `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                    `n_slab_thk_min` DECIMAL(15,4) NOT NULL Comment "坯料厚度",
                                    `n_slab_thk_max` DECIMAL(15,4) NOT NULL Comment "坯料厚度",
                                    `n_fout_aim_temp` DECIMAL(15,4) NULL Comment "出炉目标温度℃",
                                    `n_fout_max_temp` DECIMAL(15,4) NULL Comment "出炉温度上限℃",
                                    `n_fout_min_temp` DECIMAL(15,4) NULL Comment "出炉温度下限℃",
                                    `n_slab_sc_temp_diff` DECIMAL(15,4) NULL Comment "板坯表面/中心目标温度差℃",
                                    `n_slab_ht_temp_diff` DECIMAL(15,4) NULL Comment "板坯头尾目标温度差℃",
                                    `c_reserver1` DECIMAL(15,4) NULL Comment "加热一段最低温度",
                                    `c_reserver2` DECIMAL(15,4) NULL Comment "加热一段最高温度",
                                    `c_reserver3` DECIMAL(15,4) NULL Comment "加热二段最低温度",
                                    `c_reserver4` DECIMAL(15,4) NULL Comment "加热二段最高温度",
                                    `c_reserver5` DECIMAL(15,4) NULL Comment "加热段3最低温度",
                                    `c_reserver6` DECIMAL(15,4) NULL Comment "加热段3最高温度",
                                    `c_reserver7` DECIMAL(15,4) NULL Comment "加热段4最低温度",
                                    `c_reserver8` DECIMAL(15,4) NULL Comment "加热段4最高温度",
                                    `c_reserver9` DECIMAL(15,4) NULL Comment "目标加热时长",
                                    `c_reserver10` DECIMAL(15,4) NULL Comment "备用10",
                                    `n_enabled_mark` INT(10) NULL,
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME NULL,
                                    `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                    `dt_modify_date_time` DATETIME NULL,
                                    `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "坯料加热工艺参数";
-- t_pss_slab_heat_std_info DDL
CREATE TABLE `t_pss_slab_heat_std_info` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                                         `c_order_no` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                                         `c_qual_id` BIGINT(19) NOT NULL Comment "质量编码id",
                                         `c_qual_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质量编码",
                                         `c_qual_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                         `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                         `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                         `n_slab_thk_min` DECIMAL(15,4) NOT NULL Comment "坯料厚度",
                                         `n_slab_thk_max` DECIMAL(15,4) NOT NULL Comment "坯料厚度",
                                         `n_fout_aim_temp` DECIMAL(15,4) NULL Comment "出炉目标温度℃",
                                         `n_fout_max_temp` DECIMAL(15,4) NULL Comment "出炉温度上限℃",
                                         `n_fout_min_temp` DECIMAL(15,4) NULL Comment "出炉温度下限℃",
                                         `n_slab_sc_temp_diff` DECIMAL(15,4) NULL Comment "板坯表面/中心目标温度差℃",
                                         `n_slab_ht_temp_diff` DECIMAL(15,4) NULL Comment "板坯头尾目标温度差℃",
                                         `c_reserver1` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "加热一段最低温度",
                                         `c_reserver2` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "加热一段最高温度",
                                         `c_reserver3` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "加热二段最低温度",
                                         `c_reserver4` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "加热二段最高温度",
                                         `c_reserver5` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "加热段3最低温度",
                                         `c_reserver6` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "加热段3最高温度",
                                         `c_reserver7` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "加热段4最低温度",
                                         `c_reserver8` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "加热段4最高温度",
                                         `c_reserver9` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目标加热时长",
                                         `c_reserver10` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                                         `n_enabled_mark` INT(10) NULL,
                                         `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                         `dt_create_date_time` DATETIME NULL,
                                         `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                         `dt_modify_date_time` DATETIME NULL,
                                         `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                         PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "坯料加热工艺参数质量设计结果";
-- t_pss_slab_judge DDL
CREATE TABLE `t_pss_slab_judge` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                                 `c_slab_id` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "坯料号",
                                 `c_old_mat_id` BIGINT(19) NULL Comment "原质量编码id",
                                 `c_old_mat_code` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原质量编码",
                                 `c_old_mat_name` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原质量编码name",
                                 `c_old_stl_grd_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原钢种代码",
                                 `c_old_stl_grd_desc` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原钢种代码描述",
                                 `c_new_mat_id` BIGINT(19) NULL Comment "现质量编码id",
                                 `c_new_mat_code` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "现质量编码",
                                 `c_new_mat_name` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "现质量编码name",
                                 `c_new_stl_grd_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "现钢种代码",
                                 `c_new_stl_grd_desc` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "现钢种代码描述",
                                 `c_chem_rlt` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成分等级",
                                 `c_body_rlt` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "外形等级（表面等级）",
                                 `c_size_rlt` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "尺寸等级",
                                 `c_judge_rlt` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综判等级",
                                 `c_heat_id` VARCHAR(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                                 `c_require_no` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "申请编号",
                                 `c_judge_status` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综判状态",
                                 `c_face_rlt` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "表面等级",
                                 `c_old_face_rlt` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原表面等级",
                                 `c_del_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' Comment "删除标记",
                                 `c_chem_remark` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成分描述",
                                 `c_face_remark` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "表面描述",
                                 `c_size_remark` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "尺寸描述",
                                 `c_property_remark` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "性能描述",
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                 `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                 `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                 `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                 `n_enabled_mark` INT(10) NULL,
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1944637394698039299 ROW_FORMAT = Dynamic COMMENT = "炼钢坯料综合判定表";
-- t_pss_slab_res DDL
CREATE TABLE `t_pss_slab_res` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                               `c_plan_mat_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划件次号",
                               `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "件次号/坯料号",
                               `n_cnt` INT(10) NULL Comment "数量",
                               `c_pre_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原料号",
                               `c_mate_id` BIGINT(19) NULL Comment "物料id",
                               `c_mat_type` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料类型",
                               `c_heat_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                               `c_roll_sch_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "轧制批次号",
                               `c_mat_id_mth` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "母板号",
                               `c_status` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料状态",
                               `c_pre_status` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "前物料状态",
                               `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/名称",
                               `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                               `c_quality_id` BIGINT(19) NULL Comment "质量编码id",
                               `c_quality_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                               `c_quality_code_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                               `c_mat_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料编码",
                               `c_mat_name` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "物料名称",
                               `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                               `n_mat_thk` DECIMAL(12,3) NULL Comment "厚度",
                               `n_mat_wid` DECIMAL(12,3) NULL Comment "宽度",
                               `n_mat_dia` DECIMAL(12,3) NULL Comment "直径",
                               `n_mat_lth` DECIMAL(12,3) NULL Comment "长度",
                               `n_mat_wgt` DECIMAL(12,3) NULL Comment "实际重量",
                               `n_mat_wgt_cal` DECIMAL(12,3) NULL Comment "计算重量",
                               `n_mat_act_wgt` DECIMAL(12,3) NULL Comment "检斤重量",
                               `c_std_spec` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准号",
                               `dt_judge_time` DATETIME NULL Comment "综合判定时间 Q",
                               `c_prod_grd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "综判等级",
                               `c_prel_grd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "初判等级",
                               `c_chem_grd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成分等级",
                               `c_size_grd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "尺寸等级",
                               `c_surf_grd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "外观等级（表面等级）",
                               `c_mtal_grd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "性能等级",
                               `c_w_pro` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "探伤结果",
                               `dt_w_date` DATETIME NULL Comment "探伤日期",
                               `c_upd_not` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "不合格原因",
                               `c_upd_rsn` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "处理方式",
                               `c_bd_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否改配合同，否：Null 是：1",
                               `c_cert_id` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量证明书号",
                               `dt_pch_time` DATETIME NULL Comment "购入时间",
                               `dt_prod_time` DATETIME NULL Comment "生产时间",
                               `c_prod_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产班次",
                               `c_prod_group` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产班别",
                               `c_occr_cd` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "信息来源",
                               `c_factory` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产地",
                               `c_supplier` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "供货商",
                               `c_sale_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单号",
                               `c_sale_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "销售订单行号",
                               `c_task_list_id` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                               `c_buy_order_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "采购订单号",
                               `c_order_sn` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "采购订单行号",
                               `c_ord_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单材/余材标志",
                               `c_order_no` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产订单号",
                               `c_multi_ord_tag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否多订单",
                               `c_woo_rsn` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "余材原因",
                               `c_hcr_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "板坯热送标志",
                               `c_smp_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否试样板",
                               `c_line_id` BIGINT(19) NULL Comment "当前产线id",
                               `c_line_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "当前产线",
                               `c_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "当前产线name",
                               `c_nc_yard_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产销仓库代码",
                               `c_loc` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "库位号",
                               `n_loc_lvl` INT(10) NULL Comment "层号",
                               `c_pre_out` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划去向",
                               `c_memo` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                               `c_pgm_id` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "最后修改程序",
                               `c_month_flag` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上传标志",
                               `c_month_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上传人员",
                               `dt_month_time` DATETIME NULL Comment "上传时间",
                               `c_reweight_fl` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "复秤标记",
                               `n_reweight` DECIMAL NULL Comment "复秤重量",
                               `c_re_wgt_flag` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "复秤类型",
                               `c_two_rewgt_fl` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "二次复秤标记",
                               `dt_two_wgt_time` DATE NULL Comment "二次复秤日期",
                               `c_two_wgt_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "二次复秤人员",
                               `n_two_wgt` DECIMAL NULL Comment "二次复秤重量",
                               `c_cha_empid` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "改判人",
                               `dt_cha_time` DATETIME NULL Comment "改判时间",
                               `c_cha_memo` VARCHAR(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "改判备注",
                               `c_pre_mat_qul_id` BIGINT(19) NULL Comment "原质量编码id",
                               `c_pre_mat_qul_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原质量编码",
                               `c_pre_mat_qul_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原质量编码name",
                               `c_pre_stl_grd_cd` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原钢种",
                               `c_pre_stl_grd_desc` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原钢种描述",
                               `n_pre_mat_lth` DECIMAL(12,3) NULL Comment "原长度",
                               `n_pre_mat_wgt_cal` DECIMAL(12,3) NULL Comment "原计算重量",
                               `c_pre_matcode` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原物料编码",
                               `c_pre_matname` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原物料名称",
                               `c_is_lock` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否锁定",
                               `c_car_no` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "装车车号",
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                               `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                               `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                               `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                               `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 101 ROW_FORMAT = Dynamic COMMENT = "炼钢物料主表";
-- t_pss_slab_size DDL
CREATE TABLE `t_pss_slab_size` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                                `c_slab_id` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "坯料号/件次号",
                                `c_defect_code` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "缺陷代码",
                                `c_stl_grd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称",
                                `n_slab_thk` DECIMAL(8,3) NULL Comment "板坯厚度",
                                `n_slab_wid` DECIMAL(8,3) NULL Comment "板坯宽度",
                                `n_slab_len` DECIMAL(8,3) NULL Comment "板坯长度",
                                `n_slab_mea_thk` DECIMAL(8,3) NULL Comment "板坯测量厚度",
                                `n_slab_mea_wid` DECIMAL(8,3) NULL Comment "板坯测量宽度",
                                `n_slab_mea_len` DECIMAL(8,3) NULL Comment "板坯测量长度",
                                `c_size_rlt` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "尺寸等级",
                                `c_remark` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量描述",
                                `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                `n_enabled_mark` INT(10) NULL,
                                PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1944637437685460995 ROW_FORMAT = Dynamic COMMENT = "炼钢坯料尺寸判定表";
-- t_pss_std_chem_nk_pro DDL
CREATE TABLE `t_pss_std_chem_nk_pro` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                      `c_mac_id` BIGINT(19) NULL Comment "工序id",
                                      `c_mac_code` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序编码",
                                      `c_mac_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序名称",
                                      `c_op_std_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准号",
                                      `c_stl_grd_cd` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                      `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                      `c_quality_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                      `c_quality_id` BIGINT(19) NULL Comment "质量编码id",
                                      `c_quality_code` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码;质量编码",
                                      `c_quality_code_abbreviation` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码简称",
                                      `c_comp_code_min` DECIMAL NULL Comment "下限比较符",
                                      `c_comp_code_max` DECIMAL NULL Comment "上限比较符",
                                      `c_chem_comp_cd` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "化学成分代码",
                                      `c_chem_comp` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "化学成分",
                                      `c_chem_comp_min` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "化学成分最小值",
                                      `c_chem_comp_max` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "化学成分最大值",
                                      `n_enabled_mark` INT(10) NULL,
                                      `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                      `dt_create_date_time` DATETIME NULL,
                                      `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                      `dt_modify_date_time` DATETIME NULL,
                                      `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                      PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1945038527652335618 ROW_FORMAT = Dynamic COMMENT = "炼钢工序成分标准表";
-- t_pss_stdchem DDL
CREATE TABLE `t_pss_stdchem` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                              `order_id` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产订单号",
                              `c_std_class` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量等级",
                              `c_qual_id` BIGINT(19) NULL Comment "质量编码id",
                              `c_qual_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                              `c_qual_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                              `c_stl_grd_cd` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "牌号代码",
                              `c_stl_grd_desc` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                              `c_comp_code_min` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下限比较符",
                              `c_comp_code_max` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上限比较符",
                              `c_chem_comp_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "化学成分代码",
                              `c_chem_comp_min` DECIMAL(15,4) NULL Comment "化学成分最小值",
                              `c_chem_comp_max` DECIMAL(15,4) NULL Comment "化学成分最大值",
                              `n_enabled_mark` INT(10) NULL,
                              `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                              `dt_create_date_time` DATETIME NULL,
                              `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                              `dt_modify_date_time` DATETIME NULL,
                              `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                              PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "质量设计化学成分结果表";
-- t_pss_stdchem_lg DDL
CREATE TABLE `t_pss_stdchem_lg` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                                 `c_std_class` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量等级",
                                 `c_quality_id` BIGINT(19) NULL Comment "质量编码id",
                                 `c_quality_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                 `c_quality_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                 `c_stl_grd_cd` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                 `c_stl_grd_desc` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                 `c_comp_code_min` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下限比较符",
                                 `c_comp_code_max` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上限比较符",
                                 `c_chem_comp_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "化学成分代码",
                                 `c_chem_comp_min` DECIMAL(15,4) NULL Comment "化学成分最小值",
                                 `c_chem_comp_max` DECIMAL(15,4) NULL Comment "化学成分最大值",
                                 `n_enabled_mark` INT(10) NULL,
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME NULL,
                                 `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                 `dt_modify_date_time` DATETIME NULL,
                                 `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "炼钢成分标准表";
-- t_pss_stdmat DDL
CREATE TABLE `t_pss_stdmat` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                             `order_id` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产订单号",
                             `c_std_class` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量等级",
                             `c_op_std_name` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "执行标准号",
                             `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                             `c_stl_grd_desc` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                             `n_thk_min` DECIMAL(15,4) NOT NULL Comment "厚度最小值",
                             `n_thk_max` DECIMAL(15,4) NOT NULL Comment "厚度最大值",
                             `c_verify_item_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检标准名称",
                             `c_verify_item_code` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质检标准编码",
                             `c_verify_item_id` BIGINT(19) NOT NULL Comment "质检标准编码id",
                             `c_smp_min_tag` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下限比较符",
                             `c_smp_max_tag` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上限比较符",
                             `c_smp_min` DECIMAL(19,4) NULL Comment "下限",
                             `c_smp_min_2` DECIMAL(19,4) NULL Comment "下限2",
                             `c_smp_min_min` DECIMAL(19,4) NULL Comment "下下限",
                             `c_smp_min_min_2` DECIMAL(19,4) NULL Comment "下下限2",
                             `c_smp_max` DECIMAL(19,4) NULL Comment "上限",
                             `c_smp_max_2` DECIMAL(19,4) NULL Comment "上限2",
                             `c_smp_max_max` DECIMAL(19,4) NULL Comment "上上限",
                             `c_smp_max_max_2` DECIMAL(19,4) NULL Comment "上上限2",
                             `c_smp_avg` DECIMAL(19,4) NULL Comment "平均",
                             `c_smp_avg_2` DECIMAL(19,4) NULL Comment "平均2",
                             `c_smp_avg_min` DECIMAL(19,4) NULL Comment "平均下限",
                             `c_smp_avg_min_2` DECIMAL(19,4) NULL Comment "平均下限2",
                             `c_smp_avg_max` DECIMAL(19,4) NULL Comment "平均上限",
                             `c_smp_avg_max_2` DECIMAL(19,4) NULL Comment "平均上限2",
                             `c_smp_dcs_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "判定方法",
                             `c_memo` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                             `c_qual_id` BIGINT(19) NULL Comment "质量编码id",
                             `c_qual_code` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                             `c_qual_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                             `n_enabled_mark` INT(10) NULL,
                             `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                             `dt_create_date_time` DATETIME NULL,
                             `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                             `dt_modify_date_time` DATETIME NULL,
                             `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                             PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1945039243284484099 ROW_FORMAT = Dynamic COMMENT = "质量设计性能结果";
-- t_pss_stdmat_zg DDL
CREATE TABLE `t_pss_stdmat_zg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                `c_std_class` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量等级",
                                `c_op_std_name` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准号",
                                `c_stl_grd_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                `c_stl_grd_desc` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                `n_thk_min` DECIMAL(15,4) NULL Comment "厚度最小值",
                                `n_thk_max` DECIMAL(15,4) NULL Comment "厚度最大值",
                                `c_verify_item_id` BIGINT(19) NULL Comment "质检标准id",
                                `c_verify_item_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检标准名称/试验项目",
                                `c_verify_item_code` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质检标准编码/实验项目编码",
                                `c_smp_min_tag` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下限比较符",
                                `c_smp_max_tag` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上限比较符",
                                `c_smp_min` DECIMAL(19,4) NULL Comment "下限",
                                `c_smp_min2` DECIMAL(19,4) NULL Comment "下限2",
                                `c_smp_min_min` DECIMAL(19,4) NULL Comment "下下限",
                                `c_smp_min_min2` DECIMAL(19,4) NULL Comment "下下限2",
                                `c_smp_max` DECIMAL(19,4) NULL Comment "上限",
                                `c_smp_max2` DECIMAL(19,4) NULL Comment "上限2",
                                `c_smp_max_max` DECIMAL(19,4) NULL Comment "上上限",
                                `c_smp_max_max2` DECIMAL(19,4) NULL Comment "上上限2",
                                `c_smp_avg` DECIMAL(19,4) NULL Comment "平均",
                                `c_smp_avg2` DECIMAL(19,4) NULL Comment "平均2",
                                `c_smp_avg_min` DECIMAL(19,4) NULL Comment "平均下限",
                                `c_smp_avg_min2` DECIMAL(19,4) NULL Comment "平均下限2",
                                `c_smp_avg_max` DECIMAL(19,4) NULL Comment "平均上限",
                                `c_smp_avg_max2` DECIMAL(19,4) NULL Comment "平均上限2",
                                `c_smp_dcs_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "判定方法",
                                `c_memo` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                `c_quality_id` BIGINT(19) NULL Comment "质量编码id",
                                `c_quality_code` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                `c_quality_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                `n_enabled_mark` INT(10) NULL,
                                `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                `dt_create_date_time` DATETIME NULL,
                                `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                `dt_modify_date_time` DATETIME NULL,
                                `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1945039243284484099 ROW_FORMAT = Dynamic COMMENT = "国标性能标准";
-- t_pss_steel_class DDL
CREATE TABLE `t_pss_steel_class` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                  `c_stl_grd_class_cd` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种类别代码",
                                  `c_stl_grd_class_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种类别名称",
                                  `c_memo` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                  `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                  `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                  `dt_create_date_time` DATETIME NULL,
                                  `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                  `dt_modify_date_time` DATETIME NULL,
                                  `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                  PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "钢种类别管理;";
-- t_pss_steel_cost DDL
CREATE TABLE `t_pss_steel_cost` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                                 `c_line_cd` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                 `c_stl_grd_cd` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                 `c_cost_name` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本项目",
                                 `n_sum_cost` DECIMAL(15,4) NULL Comment "总成本",
                                 `n_estimated_profit` DECIMAL(15,4) NULL Comment "预计利润",
                                 `c_cost_account_name` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本科目名称",
                                 `c_cost_account_code` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "成本科目代码",
                                 `n_planned_amount` DECIMAL(15,4) NULL Comment "计划金额",
                                 `n_actual_amount` DECIMAL(15,4) NULL Comment "实际金额",
                                 `n_difference_amount` DECIMAL(15,4) NULL Comment "差异金额",
                                 `n_difference_rate` DECIMAL(8,6) NULL Comment "差异率",
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                 `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                 `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                 `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                 `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "钢种成本定额消耗分析";
-- t_pss_steel_manger DDL
CREATE TABLE `t_pss_steel_manger` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                   `c_stl_grd_cd` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                   `c_stl_grd_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                   `c_stl_grd_sort_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种简称",
                                   `c_stl_grd_class_cd` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种分类代码",
                                   `c_stl_grd_eng_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种英文名称",
                                   `c_stl_grd_eng_sort_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种英文简称",
                                   `c_memo` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                   `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                   `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                   `dt_create_date_time` DATETIME NULL,
                                   `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                   `dt_modify_date_time` DATETIME NULL,
                                   `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                   PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "钢种管理;";
-- t_pss_steel_return_record DDL
CREATE TABLE `t_pss_steel_return_record` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                          `c_delivery_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "提货单号",
                                          `c_stock_no` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "库号",
                                          `c_batch_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "批次号",
                                          `c_material_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "材料号/卷号",
                                          `c_steel_grade` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "钢种名称",
                                          `c_spec` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "规格",
                                          `n_weight` DECIMAL(12,3) NULL Comment "单件重量(kg)",
                                          `n_return_qty` INT(10) NULL Comment "退货数量(件/卷)",
                                          `n_return_weight` DECIMAL(12,3) NULL Comment "退货总重量(kg)",
                                          `dt_return_time` DATETIME NULL Comment "退货入库时间",
                                          `c_return_user` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "退货操作人",
                                          `c_return_reason` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "退货原因",
                                          `c_out_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原出库单号",
                                          `c_status` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态(已退库/已冲销等)",
                                          `c_remark` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                          `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                          `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                          `dt_create_date_time` DATETIME NULL,
                                          `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                          `dt_modify_date_time` DATETIME NULL,
                                          PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "退货记录表";
-- t_pss_steel_sale_return DDL
CREATE TABLE `t_pss_steel_sale_return` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                        `delivery_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "提货单号",
                                        `stock_no` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "库号",
                                        `batch_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "批次号",
                                        `material_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "材料号/卷号",
                                        `steel_grade` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "钢种名称",
                                        `spec` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "规格",
                                        `weight` DECIMAL(12,3) NULL Comment "单件重量(kg)",
                                        `return_qty` INT(10) NULL Comment "退货数量(件/卷)",
                                        `return_weight` DECIMAL(12,3) NULL Comment "退货总重量(kg)",
                                        `return_time` DATETIME NULL Comment "退货申请时间",
                                        `return_user` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "退货申请人",
                                        `return_reason` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "退货原因",
                                        `out_no` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "原出库单号",
                                        `status` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态(待退库/已退库/已驳回等)",
                                        `remark` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                        `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                        `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                        `dt_create_date_time` DATETIME NULL,
                                        `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                        `dt_modify_date_time` DATETIME NULL,
                                        PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "销售退货数据表";
-- t_pss_stl_cpct DDL
CREATE TABLE `t_pss_stl_cpct` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                               `c_stl_grd_cd` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                               `c_stl_grd_desc` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述/名称",
                               `c_bof_id` BIGINT(19) NULL Comment "电炉座次id",
                               `c_bof_no` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电炉座次编码",
                               `c_bof_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电炉座次名称",
                               `n_heat_min` INT(10) NULL Comment "钢水下限",
                               `n_heat_max1` INT(10) NULL Comment "钢水上限",
                               `n_heat_racv_radio` DECIMAL(15,4) NULL Comment "收得率",
                               `n_enabled_mark` INT(10) NULL,
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME NULL,
                               `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                               `dt_modify_date_time` DATETIME NULL,
                               `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1947227629210501122 ROW_FORMAT = Dynamic COMMENT = "炉熔量标准;";
-- t_pss_stl_mingle DDL
CREATE TABLE `t_pss_stl_mingle` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                 `c_stl_grd_cd_a` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "首浇钢种",
                                 `c_stl_grd_desc_a` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "首浇钢种描述/名称",
                                 `c_stl_grd_cd_b` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "次浇钢种",
                                 `c_stl_grd_desc_b` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "次浇钢种描述/名称",
                                 `c_pro_line` BIGINT(19) NULL Comment "产线",
                                 `c_pro_line_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线编码",
                                 `c_pro_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线名称",
                                 `n_cast_num` INT(10) NULL Comment "浇次内顺序",
                                 `n_enabled_mark` INT(10) NULL,
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME NULL,
                                 `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                 `dt_modify_date_time` DATETIME NULL,
                                 `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1945037786170691586 ROW_FORMAT = Dynamic COMMENT = "混浇钢种标准;";
-- t_pss_task_list DDL
CREATE TABLE `t_pss_task_list` (`n_id` BIGINT(19) NOT NULL Comment "顺序号",
                                `c_task_list_id` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产任务单号",
                                `c_prod_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产品类别",
                                `c_crt_dept` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "编制部门",
                                `dt_crt_time` DATETIME(3) NULL Comment "编制时间",
                                `c_crt_emp` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "编制人",
                                `c_prc_dept` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产部门",
                                `c_stl_grd_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                `c_mat_qul_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                `n_slab_thk` DECIMAL(15,4) NULL Comment "板坯厚度",
                                `n_slab_wth` DECIMAL(15,4) NULL Comment "板坯宽度",
                                `n_slab_len` DECIMAL(15,4) NULL Comment "板坯长度",
                                `n_slab_cnt` BIGINT(19) NULL Comment "块数",
                                `n_slab_wgt` DECIMAL(15,4) NULL Comment "总重量",
                                `c_emp_id` VARCHAR(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "发送人",
                                `dt_send_dt` DATETIME(3) NULL Comment "发送时间",
                                `dt_check_dt` DATETIME(3) NULL Comment "接受时间",
                                `c_status` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                                `n_fulw_rad` DECIMAL(15,4) NULL Comment "溢短装比",
                                `c_sent_place` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "板坯去向",
                                `c_qul_dsg_err` VARCHAR(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量设计错误信息",
                                `c_position_demand` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "XT标识",
                                `c_order_no` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "订单号",
                                `c_smp_fl` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否保性能",
                                `n_back_wgt` DECIMAL(15,4) NULL Comment "回收重量",
                                `n_enabled_mark` INT(10) NULL,
                                `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                `dt_create_date_time` DATETIME NULL,
                                `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                `dt_modify_date_time` DATETIME NULL,
                                `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "生产任务单表";
-- t_pss_tech_ccm DDL
CREATE TABLE `t_pss_tech_ccm` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                               `c_quality_id` BIGINT(19) NOT NULL Comment "质量编码id",
                               `c_quality_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质量编码",
                               `c_quality_code_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                               `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                               `c_stl_grd_desc` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                               `n_liquid_phase` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "液相线",
                               `n_mid_pag_temp` DECIMAL(19,4) NULL Comment "中包目标温度",
                               `n_moveout_time` DECIMAL(19,4) NULL Comment "搬出温度",
                               `n_start_temp` DECIMAL(19,4) NULL Comment "起步温度",
                               `n_cast_temp` DECIMAL(19,4) NULL Comment "连浇温度",
                               `c_flux_model` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "保护渣型号",
                               `n_sld_surplus` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢包铸余",
                               `n_water_mould` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "结晶器水量",
                               `n_specific_volume` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "比水量",
                               `c_tundish_nozzle` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "中包水口",
                               `c_con_no` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "连铸机号",
                               `c_head_ele_sti` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "首端电搅",
                               `c_tail_ele_sti` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "末端电搅",
                               `c_amplitude` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "振幅",
                               `c_vibration_frequency` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "振频",
                               `c_liquid_level_fluc` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "液面波动",
                               `c_backup1` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用1",
                               `c_backup2` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用2",
                               `c_backup3` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用3",
                               `c_backup4` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用4",
                               `c_backup5` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用5",
                               `c_backup6` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用6",
                               `c_backup7` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用7",
                               `c_backup8` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用8",
                               `c_backup9` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用9",
                               `c_backup10` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                               `n_enabled_mark` INT(10) NULL,
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME NULL,
                               `n_modify_user_id` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "修改人",
                               `dt_modify_date_time` DATETIME NULL,
                               `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "连铸工艺参数表";
-- t_pss_tech_ccm_info DDL
CREATE TABLE `t_pss_tech_ccm_info` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                                    `order_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "订单号",
                                    `c_qual_id` BIGINT(19) NOT NULL Comment "质量编码id",
                                    `c_qual_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质量编码",
                                    `c_qual_code_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                    `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                    `n_liquid_phase` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "液相线",
                                    `n_mid_pag_temp` DECIMAL(19,4) NULL Comment "中包目标温度",
                                    `n_moveout_time` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "搬出温度",
                                    `n_start_temp` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "起步温度",
                                    `n_cast_temp` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连浇温度",
                                    `c_flux_model` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "保护渣型号",
                                    `n_sld_surplus` VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢包铸余",
                                    `n_water_mould` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "结晶器水量",
                                    `n_specific_volume` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "比水量",
                                    `c_tundish_nozzle` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "中包水口",
                                    `c_con_no` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "连铸机号",
                                    `c_head_ele_sti` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "首端电搅",
                                    `c_tail_ele_sti` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "末端电搅",
                                    `c_amplitude` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "振幅",
                                    `c_vibration_frequency` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "振频",
                                    `c_liquid_level_fluc` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "液面波动",
                                    `c_backup1` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用1",
                                    `c_backup2` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用2",
                                    `c_backup3` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用3",
                                    `c_backup4` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用4",
                                    `c_backup5` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用5",
                                    `c_backup6` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用6",
                                    `c_backup7` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用7",
                                    `c_backup8` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用8",
                                    `c_backup9` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用9",
                                    `c_backup10` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                                    `n_enabled_mark` INT(10) NULL,
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME NULL,
                                    `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                    `dt_modify_date_time` DATETIME NULL,
                                    `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "连铸工艺参数质量设计结果";
-- t_pss_tech_eaf DDL
CREATE TABLE `t_pss_tech_eaf` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                               `c_quality_id` BIGINT(19) NOT NULL Comment "质量编码id",
                               `c_quality_code` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质量编码",
                               `c_quality_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                               `c_stl_grd_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                               `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                               `c_target_s` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目标S",
                               `c_slag_std` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "扒渣基准",
                               `c_iron` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "铁水",
                               `c_steel_scrap` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "废钢",
                               `c_premel_slag` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "预熔渣",
                               `c_lime` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "白灰",
                               `c_deoxidize_cdoe` DECIMAL(19,4) NULL Comment "碱度",
                               `c_deoxidize_name` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出钢[C]",
                               `c_deoxidize_sum` DECIMAL(19,4) NULL Comment "出钢温度",
                               `c_hang_pot_temp` DECIMAL(19,4) NULL Comment "挂罐温度",
                               `c_arrive_temp` DECIMAL(19,4) NULL Comment "到站温度",
                               `c_slag_tap_yn` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否挡渣出钢",
                               `c_ar_flux` DECIMAL(19,4) NULL Comment "钢包氩气流量",
                               `c_fluorite` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "萤石",
                               `c_slag_lotion` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "渣洗剂",
                               `c_silica_fume` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "硅灰石",
                               `c_calcium_carbide` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电石",
                               `c_ca_ball` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钙渣球",
                               `c_sio2` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "石英砂",
                               `c_backup1` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用1",
                               `c_backup2` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用2",
                               `c_backup3` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用3",
                               `c_backup4` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用4",
                               `c_backup5` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用5",
                               `c_backup6` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用6",
                               `c_backup7` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用7",
                               `c_backup8` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用8",
                               `c_backup9` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用9",
                               `c_backup10` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                               `n_enabled_mark` INT(10) NULL,
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME NULL,
                               `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                               `dt_modify_date_time` DATETIME NULL,
                               `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "电炉工艺参数";
-- t_pss_tech_eaf_info DDL
CREATE TABLE `t_pss_tech_eaf_info` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                                    `order_id` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "生产订单号",
                                    `c_qual_id` BIGINT(19) NULL Comment "质量编码id",
                                    `c_qual_code` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                                    `c_qual_code_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                    `c_stl_grd_cd` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_stl_grd_desc` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                    `c_target_s` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目标S",
                                    `c_slag_std` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "扒渣基准",
                                    `c_iron` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "铁水",
                                    `c_steel_scrap` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "废钢",
                                    `c_premel_slag` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "预熔渣",
                                    `c_lime` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "白灰",
                                    `c_deoxidize_cdoe` DECIMAL(19,4) NULL Comment "碱度",
                                    `c_deoxidize_name` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "出钢[C]",
                                    `c_deoxidize_sum` DECIMAL(19,4) NULL Comment "出钢温度",
                                    `c_hang_pot_temp` DECIMAL(19,4) NULL Comment "挂罐温度",
                                    `c_arrive_temp` DECIMAL(19,4) NULL Comment "到站温度",
                                    `c_slag_tap_yn` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否挡渣出钢",
                                    `c_ar_flux` DECIMAL(19,4) NULL Comment "钢包氩气流量",
                                    `c_fluorite` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "萤石",
                                    `c_slag_lotion` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "渣洗剂",
                                    `c_silica_fume` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "硅灰石",
                                    `c_calcium_carbide` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电石",
                                    `c_ca_ball` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钙渣球",
                                    `c_sio2` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "石英砂",
                                    `c_backup1` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用1",
                                    `c_backup2` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用2",
                                    `c_backup3` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用3",
                                    `c_backup4` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用4",
                                    `c_backup5` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用5",
                                    `c_backup6` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用6",
                                    `c_backup7` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用7",
                                    `c_backup8` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用8",
                                    `c_backup9` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用9",
                                    `c_backup10` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                                    `n_enabled_mark` INT(10) NULL,
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME NULL,
                                    `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                    `dt_modify_date_time` DATETIME NULL,
                                    `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "电炉/转炉工艺参数质量设计结果";
-- t_pss_tech_lf DDL
CREATE TABLE `t_pss_tech_lf` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                              `c_quality_id` BIGINT(19) NULL Comment "质量编码id",
                              `c_quality_code` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                              `c_quality_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                              `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                              `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                              `c_alcalinity` DECIMAL(19,4) NULL Comment "碱度",
                              `c_casi` DECIMAL(19,4) NULL Comment "CaSi喂丝量",
                              `c_caal` DECIMAL(19,4) NULL Comment "CaAl喂丝量",
                              `c_ca` DECIMAL(19,4) NULL Comment "Ca喂丝量",
                              `c_argon_blow` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "净吹氩时间",
                              `c_lf_moveout_temp` DECIMAL(19,4) NULL Comment "LF搬出温度",
                              `c_vod_press_vac` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "VOD保压真空度",
                              `c_vod_keep_time` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "VOD保持时间",
                              `c_vod_moveout_time` DECIMAL(19,4) NULL Comment "VOD搬出温度",
                              `c_rh_press_vac` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "RH保压真空度",
                              `c_rh_keep_time` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "RH保持时间",
                              `c_rh_moveout_time` DECIMAL(19,4) NULL Comment "RH搬出温度",
                              `c_lf_lianjiao` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "LF炼焦",
                              `c_vod_lianjiao` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "VOD炼焦",
                              `c_rh_lianjiao` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "RH炼焦",
                              `c_lime` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "石灰",
                              `c_fluorite` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "萤石",
                              `c_silica_fume` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "硅灰石",
                              `c_refining_slag` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精炼渣",
                              `c_slag_lotion` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "渣洗剂",
                              `c_quartz_sand` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "石英砂",
                              `c_calcium_carbide` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电石",
                              `c_lf_morecast_temp` DECIMAL(19,4) NULL Comment "LF连浇搬出温度",
                              `c_rh_moveout_cast_time` DECIMAL(19,4) NULL Comment "RH连浇搬出温度",
                              `c_vacuum_circulation` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "真空环流",
                              `c_w_slag_h_time` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "白渣保持时间",
                              `c_lf_time_bz` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精炼标准时间",
                              `c_tfemno` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "TFE+MNO",
                              `c_ca_ball` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钙渣球",
                              `c_alcalinity_min` DECIMAL(19,4) NULL Comment "碱度最小",
                              `c_alcalinity_max` DECIMAL(19,4) NULL Comment "碱度最大",
                              `c_backup1` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用1",
                              `c_backup2` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用2",
                              `c_backup3` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用3",
                              `c_backup4` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用4",
                              `c_backup5` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用5",
                              `c_backup6` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用6",
                              `c_backup7` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用7",
                              `c_backup8` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用8",
                              `c_backup9` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用9",
                              `c_backup10` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                              `n_enabled_mark` INT(10) NULL,
                              `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                              `dt_create_date_time` DATETIME NULL,
                              `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                              `dt_modify_date_time` DATETIME NULL,
                              `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                              PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "精炼工艺参数表";
-- t_pss_tech_lf_info DDL
CREATE TABLE `t_pss_tech_lf_info` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                                   `order_id` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "生产任务单号",
                                   `c_qual_id` BIGINT(19) NOT NULL Comment "质量编码id",
                                   `c_qual_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质量编码",
                                   `c_qual_code_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                   `c_stl_grd_cd` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                   `c_stl_grd_desc` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                   `c_alcalinity` DECIMAL(19,4) NULL Comment "碱度",
                                   `c_casi` DECIMAL(19,4) NULL Comment "CaSi喂丝量",
                                   `c_caal` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "CaAl喂丝量",
                                   `c_ca` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "Ca喂丝量",
                                   `c_argon_blow` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "净吹氩时间",
                                   `c_lf_moveout_temp` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "LF搬出温度",
                                   `c_vod_press_vac` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "VOD保压真空度",
                                   `c_vod_keep_time` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "VOD保持时间",
                                   `c_vod_moveout_time` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "VOD搬出温度",
                                   `c_rh_press_vac` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "RH保压真空度",
                                   `c_rh_keep_time` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "RH保持时间",
                                   `c_rh_moveout_time` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "RH搬出温度",
                                   `c_lf_lianjiao` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "LF炼焦",
                                   `c_vod_lianjiao` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "VOD炼焦",
                                   `c_rh_lianjiao` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "RH炼焦",
                                   `c_lime` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "石灰",
                                   `c_fluorite` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "萤石",
                                   `c_silica_fume` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "硅灰石",
                                   `c_refining_slag` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精炼渣",
                                   `c_slag_lotion` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "渣洗剂",
                                   `c_quartz_sand` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "石英砂",
                                   `c_calcium_carbide` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电石",
                                   `c_lf_morecast_temp` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "LF连浇搬出温度",
                                   `c_rh_moveout_cast_time` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "RH连浇搬出温度",
                                   `c_vacuum_circulation` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "真空环流",
                                   `c_w_slag_h_time` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "白渣保持时间",
                                   `c_lf_time_bz` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精炼标准时间",
                                   `c_tfemno` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "TFE+MNO",
                                   `c_ca_ball` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钙渣球",
                                   `c_alcalinity_min` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "碱度最小",
                                   `c_alcalinity_max` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "碱度最大",
                                   `c_backup1` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用1",
                                   `c_backup2` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用2",
                                   `c_backup3` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用3",
                                   `c_backup4` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用4",
                                   `c_backup5` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用5",
                                   `c_backup6` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用6",
                                   `c_backup7` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用7",
                                   `c_backup8` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用8",
                                   `c_backup9` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用9",
                                   `c_backup10` VARCHAR(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                                   `n_enabled_mark` INT(10) NULL,
                                   `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                   `dt_create_date_time` DATETIME NULL,
                                   `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                   `dt_modify_date_time` DATETIME NULL,
                                   `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                   PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "精炼工艺参数质量设计";
-- t_pss_techk_rout_lg DDL
CREATE TABLE `t_pss_techk_rout_lg` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                                    `c_quality_id` BIGINT(19) NOT NULL Comment "质量编码id",
                                    `c_quality_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质量编码",
                                    `c_quality_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                    `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                    `c_pro_line` BIGINT(19) NULL Comment "产线id",
                                    `c_pro_line_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线code",
                                    `c_pro_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线name",
                                    `c_tech_route1` BIGINT(19) NULL Comment "工序1",
                                    `c_tech_route1_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序1code",
                                    `c_tech_route1_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序1name",
                                    `c_tech_route2` BIGINT(19) NULL Comment "工序2",
                                    `c_tech_route2_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序2code",
                                    `c_tech_route2_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序2name",
                                    `c_tech_route3` BIGINT(19) NULL Comment "工序3",
                                    `c_tech_route3_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序3code",
                                    `c_tech_route3_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序3name",
                                    `c_tech_route4` BIGINT(19) NULL Comment "工序4",
                                    `c_tech_route4_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序4code",
                                    `c_tech_route4_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序4name",
                                    `c_tech_route5` BIGINT(19) NULL Comment "工序5",
                                    `c_tech_route5_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序5code",
                                    `c_tech_route5_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序5name",
                                    `c_tech_route6` BIGINT(19) NULL Comment "工序6",
                                    `c_tech_route6_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序6code",
                                    `c_tech_route6_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序6name",
                                    `c_tech_route7` BIGINT(19) NULL Comment "工序7",
                                    `c_tech_route7_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序7code",
                                    `c_tech_route7_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序7name",
                                    `c_tech_route8` BIGINT(19) NULL Comment "工序8",
                                    `c_tech_route8_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序8code",
                                    `c_tech_route8_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序8name",
                                    `c_is_cold` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否堆垛缓冷",
                                    `tech_route0` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工艺路径1",
                                    `c_memo` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                    `c_doc_num1` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "公司文件编号",
                                    `c_doc_num2` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "厂文件编号",
                                    `c_tech_route` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工艺路径2",
                                    `c_fine_route` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精整路径汇总",
                                    `c_backup1` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用1",
                                    `c_backup2` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用2",
                                    `c_backup3` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用3",
                                    `c_backup4` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用4",
                                    `c_backup5` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用5",
                                    `c_backup6` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用6",
                                    `c_backup7` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用7",
                                    `c_backup8` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用8",
                                    `c_backup9` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用9",
                                    `c_backup10` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                                    `n_enabled_mark` INT(10) NULL,
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME NULL,
                                    `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                    `dt_modify_date_time` DATETIME NULL,
                                    `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "炼钢工艺路径";
-- t_pss_techk_rout_lg_info DDL
CREATE TABLE `t_pss_techk_rout_lg_info` (`n_id` BIGINT(19) NOT NULL Comment "主键",
                                         `order_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "订单号",
                                         `c_qual_id` BIGINT(19) NOT NULL Comment "质量编码id",
                                         `c_qual_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "质量编码",
                                         `c_qual_code_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码名称",
                                         `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                         `c_stl_grd_desc` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种名称/描述",
                                         `c_pro_line` BIGINT(19) NULL Comment "产线id",
                                         `c_pro_line_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线code",
                                         `c_pro_line_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线name",
                                         `c_tech_route1` BIGINT(19) NULL Comment "工序1",
                                         `c_tech_route1_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序1code",
                                         `c_tech_route1_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序1name",
                                         `c_tech_route2` BIGINT(19) NULL Comment "工序2",
                                         `c_tech_route2_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序2code",
                                         `c_tech_route2_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序2name",
                                         `c_tech_route3` BIGINT(19) NULL Comment "工序3",
                                         `c_tech_route3_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序3code",
                                         `c_tech_route3_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序3name",
                                         `c_tech_route4` BIGINT(19) NULL Comment "工序4",
                                         `c_tech_route4_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序4code",
                                         `c_tech_route4_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序4name",
                                         `c_tech_route5` BIGINT(19) NULL Comment "工序5",
                                         `c_tech_route5_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序5code",
                                         `c_tech_route5_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序5name",
                                         `c_tech_route6` BIGINT(19) NULL Comment "工序6",
                                         `c_tech_route6_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序6code",
                                         `c_tech_route6_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序6name",
                                         `c_tech_route7` BIGINT(19) NULL Comment "工序7",
                                         `c_tech_route7_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序7code",
                                         `c_tech_route7_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序7name",
                                         `c_tech_route8` BIGINT(19) NULL Comment "工序8",
                                         `c_tech_route8_code` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序8code",
                                         `c_tech_route8_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工序8name",
                                         `c_is_cold` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否堆垛缓冷",
                                         `tech_route0` VARCHAR(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工艺路径1",
                                         `c_memo` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                         `c_doc_num1` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "公司文件编号",
                                         `c_doc_num2` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "厂文件编号",
                                         `c_tech_route` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "工艺路径2",
                                         `c_fine_route` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "精整路径汇总",
                                         `c_backup1` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用1",
                                         `c_backup2` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用2",
                                         `c_backup3` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用3",
                                         `c_backup4` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用4",
                                         `c_backup5` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用5",
                                         `c_backup6` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用6",
                                         `c_backup7` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用7",
                                         `c_backup8` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用8",
                                         `c_backup9` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用9",
                                         `c_backup10` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备用10",
                                         `n_enabled_mark` INT(10) NULL,
                                         `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                         `dt_create_date_time` DATETIME NULL,
                                         `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                         `dt_modify_date_time` DATETIME NULL,
                                         `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                         PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = "炼钢工艺路径质量设计结果";
-- t_pss_test_crd DDL
CREATE TABLE `t_pss_test_crd` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                               `c_testcrd_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "委托单编号",
                               `c_tst_knd` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "试验模式",
                               `c_smp_id` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "试样号",
                               `c_quality_code` VARCHAR(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "质量编码",
                               `c_fpoststate` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "交货状态",
                               `c_stl_grd_cd` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种",
                               `c_std_spec` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "执行标准",
                               `c_smp_cut_proc` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "委托单状态（A：等待发送、B：等待试验、C：试验完毕、D：性能判定完毕）",
                               `c_lcns_cd` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "认证类型（预留在画面上作人工录入使用）",
                               `c_rescue_fl` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否是性能挽救",
                               `c_memo` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                               `n_ord_thk` DECIMAL(10,3) NULL Comment "订单厚度",
                               `c_smp_lot` VARCHAR(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验批号",
                               `c_heat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉号",
                               `c_smp_plate_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "取样线卷号",
                               `c_smp_plate_cnt` INT(10) NULL Comment "委托单包含线卷数",
                               `c_snd_emp_id` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "复验委托单发送人",
                               `c_if_del` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "接收到检化验系统删除通知时是否删除，1、删除；0、不删除",
                               `c_snd_time` DATETIME NULL Comment "委托单发送时间",
                               `c_smp_empid` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "取样人",
                               `dt_smp_time` DATETIME NULL Comment "取样时间",
                               `c_smp_crew` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "取样班次",
                               `c_line` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                               `c_mtal_rlt` VARCHAR(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "性能等级",
                               `c_mat_item` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                               `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                               `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                               `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                               `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                               `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                               `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                               PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "检验委托单主体";
-- t_pss_test_crd_dtl DDL
CREATE TABLE `t_pss_test_crd_dtl` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                                   `c_testcrd_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "委托单编号",
                                   `c_tst_knd` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "试验模式",
                                   `n_smp_proj_cd_no` INT(10) NOT NULL Comment "质检项目序号",
                                   `c_smp_proj_cd` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "检验项目代码",
                                   `c_smp_proj_nm` VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验项目名称",
                                   `n_smp_cnt` INT(10) NOT NULL Comment "试验次数",
                                   `c_funit` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量单位",
                                   `c_fvaluetype` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "值类型",
                                   `c_fdefaultvalue` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "缺省值",
                                   `c_fprojectattribut` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "项目属性",
                                   `n_fismodify` INT(10) NULL Comment "是否修约",
                                   `n_fprecision` DECIMAL(7,3) NULL Comment "数据精度",
                                   `c_flowcompare` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "下限比较符",
                                   `n_flower` DECIMAL(16,5) NULL Comment "下限值",
                                   `c_fupcompare` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上限比较符",
                                   `n_fupper` DECIMAL(16,5) NULL Comment "上限值",
                                   `c_smp_proj_cd_rlt` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "单项等级",
                                   `c_memo` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "试验备注",
                                   `c_plate_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "取样钢板",
                                   `c_smp_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "试样号",
                                   `c_data_source` VARCHAR(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "委托生成活检化验返回实绩时填写气体元素",
                                   `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                   `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                   `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                   `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                   `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                   `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                   PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "检验委托单明细";
-- t_pss_transfer_lg DDL
CREATE TABLE `t_pss_transfer_lg` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键id",
                                  `c_line_cd` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线",
                                  `c_yard_cd` VARCHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库代码",
                                  `c_mat_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "件次号",
                                  `c_prod_lot_id` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "批次号",
                                  `c_source_area` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "源地",
                                  `c_source_area_lev` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "源地层号",
                                  `c_target_area` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地",
                                  `c_target_area_lev` VARCHAR(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目的地层号",
                                  `c_memo` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                  `c_shift` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班次",
                                  `c_crew` VARCHAR(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "班别",
                                  `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                  `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                  `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                  `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                  `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                  `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                  INDEX `idx_c_mat_id`(`c_mat_id` ASC) USING BTREE,
                                  PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "炼钢倒垛记录";
-- t_pss_verify_item_manger DDL
CREATE TABLE `t_pss_verify_item_manger` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                         `c_verify_item_class` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验项目分类",
                                         `c_verify_item_code` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验项目编码",
                                         `c_verify_item_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验项目名称",
                                         `c_verify_item_sort_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "检验项目简称",
                                         `c_measures_unit` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计量单位",
                                         `c_stl_grd_eng_sort_name` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种英文简称",
                                         `n_precision` INT(10) NULL Comment "数据精度",
                                         `c_project_attribute_code` INT(10) NULL Comment "项目属性;存放编码",
                                         `c_value_type` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "数据类型",
                                         `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                         `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                         `dt_create_date_time` DATETIME NULL,
                                         `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                         `dt_modify_date_time` DATETIME NULL,
                                         `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                         PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "检验项目管理;";
-- t_pss_ware_house DDL
CREATE TABLE `t_pss_ware_house` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                 `c_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "仓库代码",
                                 `c_name` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "仓库名称",
                                 `c_company` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "所属公司",
                                 `c_line` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "所属产线",
                                 `c_type` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库类型",
                                 `c_erp` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "ERP库号",
                                 `c_address` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "仓库地址",
                                 `c_status` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态",
                                 `n_high` DECIMAL(12,2) NULL Comment "高储",
                                 `n_low` DECIMAL(12,2) NULL Comment "低储",
                                 `n_safe` DECIMAL(12,2) NULL Comment "安全库存",
                                 `c_tel` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "电话",
                                 `c_manager` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "负责人",
                                 `c_remark` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                 `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                 `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                 `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                 `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "仓库代码定义";
-- t_pss_ware_house_loc DDL
CREATE TABLE `t_pss_ware_house_loc` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                     `c_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "代码",
                                     `c_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "设置类型",
                                     `c_name` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "名称",
                                     `n_col` INT(10) NULL Comment "列（数）",
                                     `n_row` INT(10) NULL Comment "行（数）",
                                     `c_parent_code` VARCHAR(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "上级节点",
                                     `c_remark` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                     `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                     `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                     `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                     `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                     `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                     `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                     PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "库位树设置";
-- t_pss_wgt_one_mater DDL
CREATE TABLE `t_pss_wgt_one_mater` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                    `c_stl_grd_cd` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种代码",
                                    `c_stl_grd_desc` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢种描述/名称",
                                    `c_spec` VARCHAR(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "规格",
                                    `n_density` DECIMAL(15,4) NULL Comment "密度",
                                    `n_wgt_one_mater` DECIMAL(15,4) NULL Comment "米重",
                                    `c_calculation_method` VARCHAR(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计算方式;0为米重，1为密度",
                                    `c_ccm_id` BIGINT(19) NULL Comment "铸机号id",
                                    `c_ccm_no` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "铸机号code",
                                    `c_ccm_name` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "铸机号name",
                                    `n_enabled_mark` INT(10) NULL,
                                    `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                    `dt_create_date_time` DATETIME NULL,
                                    `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                    `dt_modify_date_time` DATETIME NULL,
                                    `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                    PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1947228068823891971 ROW_FORMAT = Dynamic COMMENT = "钢种单米重;";
-- t_pss_y_mat_stock_record DDL
CREATE TABLE `t_pss_y_mat_stock_record` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键ID",
                                         `material_id` BIGINT(19) NOT NULL Comment "物料ID",
                                         `c_doc_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "单据类型(in:入库单,out:出库单)",
                                         `c_doc_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "单据编号",
                                         `c_batch_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "批次号",
                                         `c_operation_type` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "操作类型(入库/出库)",
                                         `n_quantity` DECIMAL(10,2) NOT NULL Comment "操作数量",
                                         `n_before_stock` DECIMAL(10,2) NOT NULL Comment "操作前库存",
                                         `n_after_stock` DECIMAL(10,2) NOT NULL Comment "操作后库存",
                                         `dt_operation_time` DATETIME NOT NULL Comment "操作时间",
                                         `c_operator` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "操作人",
                                         `c_related_no` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "关联单号",
                                         `c_remark` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                         `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                         `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                         `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                         `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                         `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                         `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                         PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1940946457986179074 ROW_FORMAT = Dynamic COMMENT = "物料库存记录表";
-- t_psss_y_mat_info DDL
CREATE TABLE `t_psss_y_mat_info` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键ID",
                                  `c_material_code` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "物料编码",
                                  `c_material_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "物料名称",
                                  `c_material_type` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "物料类型(raw:原辅料,alloy:合金,scrap:废钢)",
                                  `c_specification` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "规格型号",
                                  `c_unit_id` BIGINT(19) NOT NULL Comment "单位id",
                                  `c_unit_code` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "单位code",
                                  `c_unit_name` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL Comment "单位name",
                                  `n_max_stock` DECIMAL(10,2) NOT NULL Comment "库存上限",
                                  `n_min_stock` DECIMAL(10,2) NOT NULL Comment "库存下限",
                                  `n_current_stock` DECIMAL(10,2) NOT NULL Comment "当前库存",
                                  `c_remark` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "备注",
                                  `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                  `dt_create_date_time` DATETIME(3) NULL Comment "创建时间",
                                  `n_modify_user_id` BIGINT(19) NULL Comment "最后修改人",
                                  `dt_modify_date_time` DATETIME(3) NULL Comment "最后修改时间",
                                  `n_delete_mark` INT(10) NULL Comment "逻辑删除标记",
                                  `n_enabled_mark` INT(10) NULL Comment "是否有效/启用标记",
                                  PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1940967476209754114 ROW_FORMAT = Dynamic COMMENT = "物料基本信息表";
-- t_smes_heat_plan DDL
CREATE TABLE `t_smes_heat_plan` (`n_id` BIGINT(19) NOT NULL AUTO_INCREMENT Comment "主键",
                                 `c_plan_heat_id` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "计划炉次号;计划炉次号",
                                 `c_ld_no` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "钢包号;钢包号",
                                 `c_bof_no` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "转炉座次号;转炉座次号",
                                 `c_lf_no` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "LF座次号;LF座次号",
                                 `c_rh_no` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "RH座次号;RH座次号",
                                 `c_ccm_no` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "连铸座次号;连铸座次号",
                                 `c_aim_stl_grd` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "目标钢种;目标钢种",
                                 `dt_aim_str_time_1` DATETIME(3) NULL Comment "工序1开始时刻;工序1开始时刻",
                                 `dt_aim_end_time_1` DATETIME(3) NULL Comment "工序1结束时刻;工序1结束时刻",
                                 `n_aim_tmp_1` DECIMAL(15,4) NULL Comment "工序1目标温度;工序1目标温度",
                                 `dt_aim_str_time_2` DATETIME(3) NULL Comment "工序2开始时刻;工序2开始时刻",
                                 `dt_aim_end_time_2` DATETIME(3) NULL Comment "工序2结束时刻;工序2结束时刻",
                                 `n_aim_tmp_2` DECIMAL(15,4) NULL Comment "工序2目标温度;工序2目标温度",
                                 `dt_aim_str_time_3` DATETIME(3) NULL Comment "工序3开始时刻;工序3开始时刻",
                                 `dt_aim_end_time_3` DATETIME(3) NULL Comment "工序3结束时刻;工序3结束时刻",
                                 `n_aim_tmp_3` DECIMAL(15,4) NULL Comment "工序3目标温度;工序3目标温度",
                                 `dt_aim_str_time_4` DATETIME(3) NULL Comment "工序4开始时刻;工序4开始时刻",
                                 `dt_aim_end_time_4` DATETIME(3) NULL Comment "工序4结束时刻;工序4结束时刻",
                                 `n_aim_tmp_4` DECIMAL(15,4) NULL Comment "工序4目标温度;工序4目标温度",
                                 `dt_aim_str_time_5` DATETIME(3) NULL Comment "工序5开始时刻;工序5开始时刻",
                                 `dt_aim_end_time_5` DATETIME(3) NULL Comment "工序5结束时刻;工序5结束时刻",
                                 `n_aim_tmp_5` DECIMAL(15,4) NULL Comment "工序5目标温度;工序5目标温度",
                                 `dt_aim_str_time_6` DATETIME(3) NULL Comment "工序6开始时刻;工序6开始时刻",
                                 `dt_aim_end_time_6` DATETIME(3) NULL Comment "工序6结束时刻;工序6结束时刻",
                                 `n_aim_tmp_6` DECIMAL(15,4) NULL Comment "工序6目标温度;工序6目标温度",
                                 `dt_aim_str_time_7` DATETIME(3) NULL Comment "工序7开始时刻;工序7开始时刻",
                                 `dt_aim_end_time_7` DATETIME(3) NULL Comment "工序7结束时刻;工序7结束时刻",
                                 `n_aim_tmp_7` DECIMAL(15,4) NULL Comment "工序7目标温度;工序7目标温度",
                                 `dt_aim_str_time_8` DATETIME(3) NULL Comment "工序8开始时刻;工序8开始时刻",
                                 `dt_aim_end_time_8` DATETIME(3) NULL Comment "工序8开始时刻;工序8结束时刻",
                                 `n_aim_tmp_8` DECIMAL(15,4) NULL Comment "工序8目标温度;工序8目标温度",
                                 `dt_aim_str_time_9` DATETIME(3) NULL Comment "工序9开始时刻;工序9开始时刻",
                                 `dt_aim_end_time_9` DATETIME(3) NULL Comment "工序9结束时刻;工序9结束时刻",
                                 `n_aim_tmp_9` DECIMAL(15,4) NULL Comment "工序9目标温度;工序9目标温度",
                                 `dt_aim_str_time_10` DATETIME(3) NULL Comment "工序10开始时刻;工序10开始时刻",
                                 `dt_aim_end_time_10` DATETIME(3) NULL Comment "工序10结束时刻;工序10结束时刻",
                                 `n_aim_tmp_10` DECIMAL(15,4) NULL Comment "工序10目标温度;工序10目标温度",
                                 `n_mat_cont` DECIMAL(15,4) NULL Comment "铁水废钢比;铁水废钢比",
                                 `n_aim_tap_wgt` DECIMAL(15,4) NULL Comment "计划出钢量;计划出钢量",
                                 `c_work_shop` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "产线;产线",
                                 `c_mat_qul_cd` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "材质编码;材质编码",
                                 `c_status` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "状态;状态",
                                 `c_heat_id` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "炉次号;炉次号",
                                 `c_roll_specs` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "轧制规格;轧制规格",
                                 `c_roll_len` DECIMAL(15,4) NULL Comment "浇铸长度;浇铸长度",
                                 `c_roll_width` DECIMAL(15,4) NULL Comment "轧制宽度;轧制宽度",
                                 `c_roll_land` DECIMAL(15,4) NULL Comment "轧制厚度;轧制厚度",
                                 `n_cast_heat_seq` INT(10) NULL Comment "中包炉数;中包炉数",
                                 `c_cast_edt_seq` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "浇次号;浇次号",
                                 `c_bxn_fl` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "保性能;保性能",
                                 `dt_msg_datetime` DATETIME(3) NULL Comment "下达时间;下达时间",
                                 `c_is_exp` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "是否异常炉;是否异常炉",
                                 `c_cus_req` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "客户要求;客户要求",
                                 `c_thk_range` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "厚度组;厚度组",
                                 `n_ccm_thk` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "浇铸厚度;浇铸厚度",
                                 `n_ccm_wth_1` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "浇铸宽度;浇铸宽度",
                                 `n_ccm_wth` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL Comment "浇铸宽度;浇铸宽度",
                                 `n_enabled_mark` INT(10) NULL Comment "是否启用;默认为0,1为未启用",
                                 `n_create_user_id` BIGINT(19) NULL Comment "创建人",
                                 `dt_create_date_time` DATETIME NULL,
                                 `n_modify_user_id` BIGINT(19) NULL Comment "修改人",
                                 `dt_modify_date_time` DATETIME NULL,
                                 `n_delete_mark` INT(10) NULL Comment "删除标记;默认为0,1为删除",
                                 PRIMARY KEY (`n_id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 1 ROW_FORMAT = Dynamic COMMENT = "产销下发炼钢MES计划表";
-- undo_log DDL
CREATE TABLE `undo_log` (`id` BIGINT(19) NOT NULL AUTO_INCREMENT,
                         `branch_id` BIGINT(19) NOT NULL,
                         `xid` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                         `context` VARCHAR(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                         `rollback_info` LONGBLOB NOT NULL,
                         `log_status` INT(10) NOT NULL,
                         `log_created` DATETIME NOT NULL,
                         `log_modified` DATETIME NOT NULL,
                         UNIQUE INDEX `ux_undo_log`(`xid` ASC,`branch_id` ASC) USING BTREE,
                         PRIMARY KEY (`id`)) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci AUTO_INCREMENT = 65 ROW_FORMAT = Dynamic;
