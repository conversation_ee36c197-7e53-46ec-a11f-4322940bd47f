<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>aitos-service</artifactId>
        <groupId>com.aitos</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>aitos-service-pss</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-service-pss-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.biezhi</groupId>
            <artifactId>TinyPinyin</artifactId>
            <version>2.0.3.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.github.stuxuhai</groupId>
            <artifactId>jpinyin</artifactId>
            <version>1.1.8</version>
        </dependency>

        <dependency>
            <groupId>org.dromara.sms4j</groupId>
            <artifactId>sms4j-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>me.zhyd.oauth</groupId>
            <artifactId>JustAuth</artifactId>
        </dependency>

        <!-- excel导入导出 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>


        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>com.syyai.spring.boot</groupId>
            <artifactId>ureport-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-common-satoken</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-commom-oss</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-common-advice</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-common-core</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-common-datasource</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-common-generate</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-common-mybatis</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-common-redis</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-service-magicapi-api</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-service-workflow-api</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-service-system-api</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-service-organization-api</artifactId>
            <version>${aitos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc7</artifactId>
            <version>12.1.0.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.aitos</groupId>
            <artifactId>aitos-service-masterdata-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../../lib/api/aitos-service-masterdata-api-1.0.0-SNAPSHOT-aitos.jar</systemPath>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
