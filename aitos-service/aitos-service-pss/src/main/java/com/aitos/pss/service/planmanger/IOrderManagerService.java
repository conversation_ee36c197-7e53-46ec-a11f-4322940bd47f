
package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssOrderInfoDto;
import com.aitos.pss.dto.planmanger.TPssOrderInfoPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssOrderInfoDto;
import com.aitos.pss.entity.planmanger.TPssOrderInfo;
import com.aitos.pss.vo.planmanger.TPssOrderInfoPageVo;
import com.aitos.pss.vo.planmanger.TPssOrderInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
public interface IOrderManagerService extends IService<TPssOrderInfo> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssOrderInfoPageVo> queryPage(@Valid TPssOrderInfoPageDto dto);

    /**
     * 详情查询
     * @param id
     * @return
     */
    TPssOrderInfoVo queryInfo(Long id);

    /**
     * 新增数据
     * @param dto
     * @return
     */
    TPssOrderInfoVo add(@Valid AddTPssOrderInfoDto dto);

    /**
     * 修改数据
     * @param dto
     * @return
     */
    TPssOrderInfoVo update(@Valid UpdateTPssOrderInfoDto dto);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    Boolean deleteBathById(@Valid List<Long> ids);

    /**
     * 订单管理质量设计
     * @param dtoList
     * @return
     */
    Boolean qualitySJ(@Valid List<UpdateTPssOrderInfoDto> dtoList);

    /**
     * 订单管理质量设计 - 取消
     * @param dtoList
     * @return
     */
    Boolean qualityQXSJ(@Valid List<UpdateTPssOrderInfoDto> dtoList);

    /**
     * order merge
     * @param dtoList
     * @return
     */
    Boolean orderMerge(@Valid List<UpdateTPssOrderInfoDto> dtoList);

    /**
     * order split
     * @param dto
     * @return
     */
    Boolean orderSplit(@Valid UpdateTPssOrderInfoDto dto);

    /**
     * 交期预测
     * @param dto
     * @return
     */
    TPssOrderInfoVo completePrediction(@Valid UpdateTPssOrderInfoDto dto);

    /**
     * 坯料替代
     * @param dto
     * @return
     */
    TPssOrderInfoVo mateSubstitution(@Valid UpdateTPssOrderInfoDto dto);
}
