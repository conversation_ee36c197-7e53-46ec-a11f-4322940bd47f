package com.aitos.pss.enums;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

/**
 * 订单进程状态
 */
@Getter
@Log4j2
public enum OrderStatusEnum {

    NEW_ORDER("PA0001", "新建订单"),
    ORDER_CANCELLED("PA0002", "订单撤销"),
    QUALITY_DESIGN_COMPLETED("PA0003", "质量设计完成"),
    ORDER_COMPLETED("PA0004", "订单已完成"),
    TASK_ISSUED("PA0005", "任务单已下达"),
    BILLET_DESIGN_COMPLETED("PA0006", "坯料设计完成"),
    HEAT_DESIGN_COMPLETED("PA0007", "炉次设计完成"),
    POURING_DESIGN_COMPLETED("PA0008", "浇次设计完成")
    ;

    private final String code;
    private final String description;

    OrderStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据状态码获取枚举
     */
    public static OrderStatusEnum getByCode(String code) {
        for (OrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     */
    public static OrderStatusEnum getByDescription(String description) {
        for (OrderStatusEnum status : values()) {
            if (status.getDescription().equals(description)) {
                return status;
            }
        }
        return null;
    }
}