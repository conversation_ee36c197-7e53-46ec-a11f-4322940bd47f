
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.mybatis.base.MPJDeepAndJoinBaseService;
import com.aitos.pss.dto.qualitymanger.AddTPssQualStdLibMainDto;
import com.aitos.pss.dto.qualitymanger.TPssQualStdLibMainPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualStdLibMainDto;
import com.aitos.pss.entity.qualitymanger.TPssQualStdLibMain;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibMainPageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibMainVo;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/

public interface IQualStdLibMainService extends MPJDeepAndJoinBaseService<TPssQualStdLibMain> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssQualStdLibMainPageVo> queryPage(@Valid TPssQualStdLibMainPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssQualStdLibMainVo queryInfo(Long id);

    /**
    * 新增
    *
    * @param dto
    * @return
    */
    TPssQualStdLibMainVo add(AddTPssQualStdLibMainDto dto);

    /**
    * 更新
    *
    * @param dto
    * @return
    */
    TPssQualStdLibMainVo update(UpdateTPssQualStdLibMainDto dto);

    /**
    * 删除
    *
    * @param ids
    * @return
    */
    Boolean delete(List<Long> ids);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssQualStdLibMainPageDto dto, Boolean isTemplate);

    /**
     * 下发数据
     * @param dtoList
     * @return
     */
    Boolean distribute(@Valid List<UpdateTPssQualStdLibMainDto> dtoList);

    /**
     * query list
     * @param dto
     * @return
     */
    List<TPssQualStdLibMainPageVo> queryList(@Valid TPssQualStdLibMainPageDto dto);
}
