package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssStlCpctDto;
import com.aitos.pss.dto.planmanger.TPssStlCpctPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssStlCpctDto;
import com.aitos.pss.entity.planmanger.TPssStlCpct;
import com.aitos.pss.vo.planmanger.TPssStlCpctPageVo;
import com.aitos.pss.vo.planmanger.TPssStlCpctVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/

public interface ITPssStlCpctService extends IService<TPssStlCpct> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssStlCpctPageVo> queryPage(TPssStlCpctPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssStlCpctVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssStlCpctVo add(@Valid AddTPssStlCpctDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssStlCpctVo update(@Valid UpdateTPssStlCpctDto dto);
}
