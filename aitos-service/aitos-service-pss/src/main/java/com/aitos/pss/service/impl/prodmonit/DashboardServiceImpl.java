package com.aitos.pss.service.impl.prodmonit;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.exception.MyException;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.prodmonit.DashboardAggregationDto;
import com.aitos.pss.dto.prodmonit.QueryDashboardDto;
import com.aitos.pss.entity.prodmonit.TPssEquipments;
import com.aitos.pss.entity.prodmonit.TPssIndicatorData;
import com.aitos.pss.entity.prodmonit.TPssIndicators;
import com.aitos.pss.entity.prodmonit.TPssViolations;
import com.aitos.pss.enums.ProductionSalesIndicatorStatus;
import com.aitos.pss.service.prodmonit.*;
import com.aitos.pss.vo.chart.ChartVo;
import com.aitos.pss.vo.chart.LineChartSeries;
import com.aitos.pss.vo.chart.MultiLineChartSeries;
import com.aitos.pss.vo.chart.PieChartItem;
import com.aitos.pss.vo.prodmonit.TPssIndicatorDataPageVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class DashboardServiceImpl implements IDashboardService {

    private final IDeviceManageService deviceManageService;

    private final IIndicatorConfigService indicatorConfigService;

    private final IIndicatorDataService indicatorDataService;

    private final IViolationManageService violationManageService;

    @Override
    public DashboardAggregationDto queryAggregation(QueryDashboardDto dto) {
        LocalDateTime dtCreateDateTimeStart = dto.getDtCreateDateTimeStart();
        LocalDateTime dtCreateDateTimeEnd = dto.getDtCreateDateTimeEnd();
        if (Objects.isNull(dtCreateDateTimeStart) || Objects.isNull(dtCreateDateTimeEnd)) {
            throw new MyException("请输入时间范围");
        }

        DashboardAggregationDto dashboardAggregationDto = new DashboardAggregationDto();

        // 查询设备
        List<TPssEquipments> equipmentsList =
                deviceManageService.list(
                        Wrappers.<TPssEquipments>lambdaQuery()
                                .between(
                                        Objects.nonNull(dtCreateDateTimeStart) && Objects.nonNull(dtCreateDateTimeEnd),
                                        TPssEquipments::getDtCreateDateTime,
                                        dtCreateDateTimeStart,
                                        dtCreateDateTimeEnd)
                );
        Map<String, List<TPssEquipments>> cStatusAndEquipmentsMap =
                equipmentsList.stream().collect(Collectors.groupingBy(TPssEquipments::getCStatus));
        dashboardAggregationDto.setEquipmentNum(BigDecimal.valueOf(equipmentsList.size()));
        dashboardAggregationDto.setEquipmentOnlineNum(BigDecimal.valueOf(cStatusAndEquipmentsMap.getOrDefault("1", new ArrayList<>()).size()));

        // 查询生产指标达标率
        List<TPssIndicatorData> indicatorDataList =
                indicatorDataService.list(
                        Wrappers.<TPssIndicatorData>lambdaQuery()
                                .between(
                                        Objects.nonNull(dtCreateDateTimeStart) && Objects.nonNull(dtCreateDateTimeEnd),
                                        TPssIndicatorData::getDtCreateDateTime,
                                        dtCreateDateTimeStart,
                                        dtCreateDateTimeEnd)
                );
        List<TPssIndicatorData> normalIndicatorDataList =
                indicatorDataList
                        .stream()
                        .filter(item -> Objects.equals(item.getCStatus(), ProductionSalesIndicatorStatus.NORMAL.getCode()))
                        .collect(Collectors.toList());
        BigDecimal PCR = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(indicatorDataList)) {

            PCR = BigDecimal.valueOf(normalIndicatorDataList.size())
                    .divide(BigDecimal.valueOf(indicatorDataList.size()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP);
        }
        dashboardAggregationDto.setPCR(PCR);

        LocalDateTime dtYesterdayCreateDateTimeStart = dtCreateDateTimeStart.minusDays(1);
        LocalDateTime dtYesterdayCreateDateTimeEnd = dtCreateDateTimeEnd.minusDays(1);
        List<TPssIndicatorData> yesterdayIndicatorDataList =
                indicatorDataService.list(
                        Wrappers.<TPssIndicatorData>lambdaQuery()
                                .between(
                                        Objects.nonNull(dtYesterdayCreateDateTimeStart) && Objects.nonNull(dtYesterdayCreateDateTimeEnd),
                                        TPssIndicatorData::getDtCreateDateTime,
                                        dtYesterdayCreateDateTimeStart,
                                        dtYesterdayCreateDateTimeEnd)
                );
        List<TPssIndicatorData> yesterdayNormalIndicatorDataList =
                yesterdayIndicatorDataList
                        .stream()
                        .filter(item -> Objects.equals(item.getCStatus(), ProductionSalesIndicatorStatus.NORMAL.getCode()))
                        .collect(Collectors.toList());
        BigDecimal yesterdayPCR = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(yesterdayIndicatorDataList)) {
            yesterdayPCR = BigDecimal.valueOf(yesterdayNormalIndicatorDataList.size())
                    .divide(BigDecimal.valueOf(yesterdayIndicatorDataList.size()), 2, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP);
        }

        dashboardAggregationDto.setPCRFloatUp(PCR.subtract(yesterdayPCR));

        // 查询今日预警
        List<TPssIndicatorData> warningIndicatorDataList =
                indicatorDataList
                        .stream()
                        .filter(item -> Objects.equals(item.getCStatus(), ProductionSalesIndicatorStatus.WARNING.getCode()))
                        .collect(Collectors.toList());
        dashboardAggregationDto.setWarnNum(Long.valueOf(warningIndicatorDataList.size()));


        // 查询违规单
        List<TPssViolations> violationsList = violationManageService.list(
                Wrappers.<TPssViolations>lambdaQuery()
                        .between(
                                Objects.nonNull(dtCreateDateTimeStart) && Objects.nonNull(dtCreateDateTimeEnd),
                                TPssViolations::getDtCreateDateTime,
                                dtCreateDateTimeStart,
                                dtCreateDateTimeEnd)
        );
        List<TPssViolations> pendReviewViolationsList =
                violationsList.stream()
                        .filter(item -> Objects.equals(item.getCStatus(), "1"))
                        .collect(Collectors.toList());
        dashboardAggregationDto.setViolationNum(Long.valueOf(violationsList.size()));
        dashboardAggregationDto.setPendReviewViolationNum(Long.valueOf(pendReviewViolationsList.size()));

        return dashboardAggregationDto;
    }

    @Override
    public ChartVo<List<LineChartSeries>> queryPCR(QueryDashboardDto dto) {
        ChartVo<List<LineChartSeries>> chartVo = new ChartVo<>();
        chartVo.setTitle("生产指标实时监控");
        LocalDateTime dtCreateDateTimeStart = dto.getDtCreateDateTimeStart();
        LocalDateTime dtCreateDateTimeEnd = dto.getDtCreateDateTimeEnd();
        if (Objects.isNull(dtCreateDateTimeStart) || Objects.isNull(dtCreateDateTimeEnd)) {
            throw new MyException("请输入时间范围");
        }

        List<TPssIndicatorData> indicatorDataList =
                indicatorDataService.list(
                        Wrappers.<TPssIndicatorData>lambdaQuery()
                                .eq(TPssIndicatorData::getNIndicatorId, dto.getNIndicatorId())
                                .between(
                                        Objects.nonNull(dtCreateDateTimeStart) && Objects.nonNull(dtCreateDateTimeEnd),
                                        TPssIndicatorData::getDtCreateDateTime,
                                        dtCreateDateTimeStart,
                                        dtCreateDateTimeEnd)
                );
        List<TPssIndicatorDataPageVo> indicatorDataPageVoList =
                BeanUtil.copyToList(indicatorDataList, TPssIndicatorDataPageVo.class);
        List<String> xAxisList =
                indicatorDataPageVoList.stream()
                        .map(item -> {
                            LocalDateTime dtCreateDateTime = item.getDtCreateDateTime();
                            return dtCreateDateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
                        }).collect(Collectors.toList());
        chartVo.setXAxisList(xAxisList);

        List<LineChartSeries> lineChartSeriesList = Lists.newArrayList();
        chartVo.setData(lineChartSeriesList);
        // 当前值
        LineChartSeries currentLineChartSeries = new LineChartSeries();
        List<BigDecimal> nIndicatorValueList =
                indicatorDataPageVoList.stream()
                        .map(TPssIndicatorDataPageVo::getNIndicatorValue)
                        .collect(Collectors.toList());
        currentLineChartSeries.setName("当前值");
        currentLineChartSeries.setData(nIndicatorValueList);
        lineChartSeriesList.add(currentLineChartSeries);

        TPssIndicators indicators = indicatorConfigService.getById(dto.getNIndicatorId());
        if (Objects.isNull(indicators)) throw new MyException("当前指标未配置");
        // 标准值上限
        LineChartSeries maxLineChartSeries = new LineChartSeries();
        BigDecimal nNormalMax = indicators.getNNormalMax();
        List<BigDecimal> nNormalMaxList = Collections.nCopies(nIndicatorValueList.size(), nNormalMax);
        maxLineChartSeries.setName("标准上限");
        maxLineChartSeries.setData(nNormalMaxList);
        lineChartSeriesList.add(maxLineChartSeries);

        // 标准值下限
        LineChartSeries minLineChartSeries = new LineChartSeries();
        BigDecimal nNormalMin = indicators.getNNormalMin();
        List<BigDecimal> nNormalMinList = Collections.nCopies(nIndicatorValueList.size(), nNormalMin);
        minLineChartSeries.setName("标准下限");
        minLineChartSeries.setData(nNormalMinList);
        lineChartSeriesList.add(minLineChartSeries);

        return chartVo;
    }

    @Override
    public ChartVo<List<PieChartItem>> queryEquipmentStatus(QueryDashboardDto dto) {
        LocalDateTime dtCreateDateTimeStart = dto.getDtCreateDateTimeStart();
        LocalDateTime dtCreateDateTimeEnd = dto.getDtCreateDateTimeEnd();
        if (Objects.isNull(dtCreateDateTimeStart) || Objects.isNull(dtCreateDateTimeEnd)) {
            throw new MyException("请输入时间范围");
        }

        ChartVo<List<PieChartItem>> chartVo = new ChartVo<>();
        chartVo.setTitle("设备状态分布图");
        List<PieChartItem> pieChartItemList = Lists.newArrayList();
        chartVo.setData(pieChartItemList);
        List<TPssEquipments> equipmentsList =
                deviceManageService.list(
                        Wrappers.<TPssEquipments>lambdaQuery()
                                .between(
                                        Objects.nonNull(dtCreateDateTimeStart) && Objects.nonNull(dtCreateDateTimeEnd),
                                        TPssEquipments::getDtCreateDateTime,
                                        dtCreateDateTimeStart,
                                        dtCreateDateTimeEnd)
                );
        Map<String, List<TPssEquipments>> cStatusAndEquipmentsMap =
                equipmentsList.stream().collect(Collectors.groupingBy(TPssEquipments::getCStatus));

        BigDecimal onlineCount = BigDecimal.valueOf(cStatusAndEquipmentsMap.getOrDefault("1",new ArrayList<>()).size());
        PieChartItem onlinePieChartItem = new PieChartItem();
        pieChartItemList.add(onlinePieChartItem);
        onlinePieChartItem.setName("在线设备");
        onlinePieChartItem.setValue(onlineCount);

        BigDecimal offCount = BigDecimal.valueOf(cStatusAndEquipmentsMap.getOrDefault("0",new ArrayList<>()).size());
        PieChartItem offPieChartItem = new PieChartItem();
        pieChartItemList.add(offPieChartItem);
        offPieChartItem.setName("离线设备");
        offPieChartItem.setValue(offCount);

        return chartVo;
    }

    @Override
    public ChartVo<List<MultiLineChartSeries>> queryTemperatureTrend(QueryDashboardDto dto) {
        LocalDateTime dtCreateDateTimeStart = dto.getDtCreateDateTimeStart();
        LocalDateTime dtCreateDateTimeEnd = dto.getDtCreateDateTimeEnd();
        if (Objects.isNull(dtCreateDateTimeStart) || Objects.isNull(dtCreateDateTimeEnd)) {
            throw new MyException("请输入时间范围");
        }

        List<TPssIndicatorData> indicatorDataList =
                indicatorDataService.list(
                        Wrappers.<TPssIndicatorData>lambdaQuery()
                                .eq(TPssIndicatorData::getCIndicatorName, "温度")
                                .between(
                                        Objects.nonNull(dtCreateDateTimeStart) && Objects.nonNull(dtCreateDateTimeEnd),
                                        TPssIndicatorData::getDtCreateDateTime,
                                        dtCreateDateTimeStart,
                                        dtCreateDateTimeEnd)
                );
        Map<String, List<TPssIndicatorData>> equipmentAndIndicatorDatasMap =
                indicatorDataList.stream().collect(Collectors.groupingBy(TPssIndicatorData::getCEquipmentName));

        List<MultiLineChartSeries> multiLineChartSeriesList = Lists.newArrayList();
        ChartVo<List<MultiLineChartSeries>> chartVo = new ChartVo<>();
        chartVo.setTitle("24小时温度趋势");
        chartVo.setData(multiLineChartSeriesList);
        for (String cEquipmentName : equipmentAndIndicatorDatasMap.keySet()) {
            MultiLineChartSeries multiLineChartSeries = new MultiLineChartSeries();
            multiLineChartSeriesList.add(multiLineChartSeries);
            List<TPssIndicatorData> currentIndicatorDataList = equipmentAndIndicatorDatasMap.get(cEquipmentName);
            List<String> xAxisList =
                    currentIndicatorDataList.stream()
                            .map(item -> {
                                LocalDateTime dtCreateDateTime = item.getDtCreateDateTime();
                                return dtCreateDateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
                            }).collect(Collectors.toList());
            List<BigDecimal> data =
                    currentIndicatorDataList.stream().map(TPssIndicatorData::getNIndicatorValue).collect(Collectors.toList());
            multiLineChartSeries.setName(cEquipmentName);
            multiLineChartSeries.setXAxisList(xAxisList);
            multiLineChartSeries.setData(data);
        }

        return chartVo;
    }
}
