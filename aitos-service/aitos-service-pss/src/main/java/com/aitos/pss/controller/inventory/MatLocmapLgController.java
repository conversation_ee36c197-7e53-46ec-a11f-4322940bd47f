package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.TPssMatLocmapLgPageDto;
import com.aitos.pss.service.inventory.IMatLocmapLgService;
import com.aitos.pss.vo.inventory.TPssMatLocmapLgPageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
* @title: 铸坯库位显示
* <AUTHOR>
* @Date: 2025-07-16
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/matlocmaplg")
@Tag(name = "/pss"  + "/matlocmaplg",description = "铸坯库位显示代码")
@AllArgsConstructor
public class MatLocmapLgController {


    private final IMatLocmapLgService matLocmapLgService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssMatLocmapLg列表(分页)")
    @SaCheckPermission(value = "matlocmaplg:detail", orRole = RoleConstants.ADMIN)
    public RT<Map<String, List<TPssMatLocmapLgPageVo>>> page(@Valid TPssMatLocmapLgPageDto dto){
        return RT.ok(matLocmapLgService.queryPage(dto));
    }



}
