package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPssMatStockLimitDto;
import com.aitos.pss.dto.inventory.TPssMatStockLimitPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMatStockLimitDto;
import com.aitos.pss.service.inventory.IPssMatStockLimitService;
import com.aitos.pss.vo.inventory.TPssMatStockLimitPageVo;
import com.aitos.pss.vo.inventory.TPssMatStockLimitVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 库存量上下限维护
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssmatstocklimit")
@Tag(name = "/pss"  + "/pssmatstocklimit",description = "库存量上下限维护代码")
@AllArgsConstructor
public class PssMatStockLimitController {


    private final IPssMatStockLimitService pssMatStockLimitService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssMatStockLimit列表(分页)")
    @SaCheckPermission(value = "pssmatstocklimit:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssMatStockLimitPageVo>> page(@Valid TPssMatStockLimitPageDto dto){
        return RT.ok( pssMatStockLimitService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssMatStockLimit信息")
    @SaCheckPermission(value = "pssmatstocklimit:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssMatStockLimitVo> info(@RequestParam Long id){
        return RT.ok(pssMatStockLimitService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssMatStockLimit")
    @SaCheckPermission(value = "pssmatstocklimit:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "库存量上下限维护新增数据")
    public RT<Long> add(@Valid @RequestBody AddTPssMatStockLimitDto dto){
        return RT.ok(pssMatStockLimitService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssMatStockLimit")
    @SaCheckPermission(value = "pssmatstocklimit:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "库存量上下限维护修改数据")
    public RT<Boolean> update(@Valid @RequestBody UpdateTPssMatStockLimitDto dto){
        return RT.ok(pssMatStockLimitService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "pssmatstocklimit:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "库存量上下限维护删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(pssMatStockLimitService.removeBatchByIds(ids));
    }

}
