
package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssOrderCombinDto;
import com.aitos.pss.dto.planmanger.TPssOrderCombinPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssOrderCombinDto;
import com.aitos.pss.entity.planmanger.TPssOrderCombin;
import com.aitos.pss.vo.planmanger.TPssOrderCombinPageVo;
import com.aitos.pss.vo.planmanger.TPssOrderCombinVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/

public interface IOrderCombinService extends IService<TPssOrderCombin> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssOrderCombinPageVo> queryPage(@Valid TPssOrderCombinPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssOrderCombinVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssOrderCombinVo add(@Valid AddTPssOrderCombinDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssOrderCombinVo update(UpdateTPssOrderCombinDto dto);
}
