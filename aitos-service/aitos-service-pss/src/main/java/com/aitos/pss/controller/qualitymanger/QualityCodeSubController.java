package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssQualityCodeSubDto;
import com.aitos.pss.dto.qualitymanger.TPssQualityCodeSubPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualityCodeSubDto;
import com.aitos.pss.service.qualitymanger.IQualityCodeSubService;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodeSubPageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodeSubVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 质量编码管理
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/qualitycodesub")
@Tag(name = "/pss"  + "/qualitycodesub",description = "质量编码管理子表代码")
@AllArgsConstructor
public class QualityCodeSubController {


    private final IQualityCodeSubService qualityCodeSubService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssQualityCodeSub列表(分页)")
    public RT<PageOutput<TPssQualityCodeSubPageVo>> queryPage(@Valid TPssQualityCodeSubPageDto dto) {

        return RT.ok(qualityCodeSubService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssQualityCodeSub信息")
    public RT<TPssQualityCodeSubVo> queryInfo(@RequestParam Long id) {

        return RT.ok(qualityCodeSubService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary = "新增TPssQualityCodeSub")
    @AitLog(value = "质量编码管理新增数据")
    public RT<TPssQualityCodeSubVo> add(@Valid @RequestBody AddTPssQualityCodeSubDto dto) {

        return RT.ok(qualityCodeSubService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssQualityCode")
    @AitLog(value = "质量编码管理修改数据")
    public RT<TPssQualityCodeSubVo> update(@Valid @RequestBody UpdateTPssQualityCodeSubDto dto) {

        return RT.ok(qualityCodeSubService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "质量编码管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids) {

        return RT.ok(qualityCodeSubService.removeByIds(ids));
    }
}