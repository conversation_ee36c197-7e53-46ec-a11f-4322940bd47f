package com.aitos.pss.controller.qualitymanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssTechEafDto;
import com.aitos.pss.dto.qualitymanger.TPssTechEafPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechEafDto;
import com.aitos.pss.entity.qualitymanger.TPssTechEaf;
import com.aitos.pss.service.qualitymanger.ITechEafService;
import com.aitos.pss.vo.qualitymanger.TPssTechEafPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechEafVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 炼钢工艺参数管理-电炉工艺参数
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/techeaf")
@Tag(name = "/pss"  + "/techeaf",description = "炼钢工艺参数管理-电炉工艺参数代码")
@AllArgsConstructor
public class TechEafController {


    private final ITechEafService techEafService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssTechEaf列表(分页)")
    @SaCheckPermission(value = "techeaf:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssTechEafPageVo>> page(@Valid TPssTechEafPageDto dto){

        return RT.ok(techEafService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssTechEaf信息")
    @SaCheckPermission(value = "techeaf:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssTechEafVo> info(@RequestParam Long id){

        return RT.ok(techEafService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssTechEaf")
    @SaCheckPermission(value = "techeaf:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-电炉工艺参数新增数据")
    public RT<TPssTechEafVo> add(@Valid @RequestBody AddTPssTechEafDto dto){

        return RT.ok(techEafService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssTechEaf")
    @SaCheckPermission(value = "techeaf:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-电炉工艺参数修改数据")
    public RT<TPssTechEafVo> update(@Valid @RequestBody UpdateTPssTechEafDto dto){

        return RT.ok(techEafService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "techeaf:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-电炉工艺参数删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(techEafService.removeBatchByIds(ids));
    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "炼钢工艺参数管理-电炉工艺参数导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        techEafService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "炼钢工艺参数管理-电炉工艺参数导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssTechEafPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return techEafService.exportData(dto,isTemplate);
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssTechEafDto dto){
        TPssTechEaf tPssTechEaf = BeanUtil.toBean(dto, TPssTechEaf.class);

        return RT.ok(techEafService.updateById(tPssTechEaf));
    }
}