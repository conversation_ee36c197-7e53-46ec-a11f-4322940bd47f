package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssTechkRoutLgDto;
import com.aitos.pss.dto.qualitymanger.TPssTechkRoutLgPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechkRoutLgDto;
import com.aitos.pss.entity.qualitymanger.TPssTechkRoutLg;
import com.aitos.pss.mapper.qualitymanger.TPssTechkRoutLgMapper;
import com.aitos.pss.service.qualitymanger.ITechkRoutLgService;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TechkRoutLgServiceImpl extends ServiceImpl<TPssTechkRoutLgMapper, TPssTechkRoutLg> implements ITechkRoutLgService {

    @Override
    public PageOutput<TPssTechkRoutLgPageVo> queryPage(TPssTechkRoutLgPageDto dto) {
        LambdaQueryWrapper<TPssTechkRoutLg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualityId()),TPssTechkRoutLg::getCQualityId,dto.getCQualityId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssTechkRoutLg::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(Objects.nonNull(dto.getCProLine()),TPssTechkRoutLg::getCProLine,dto.getCProLine())
                .orderByDesc(TPssTechkRoutLg::getNId)
                .select(TPssTechkRoutLg.class,x -> VoToColumnUtil.fieldsToColumns(TPssTechkRoutLgPageVo.class).contains(x.getProperty()));
        IPage<TPssTechkRoutLg> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssTechkRoutLgPageVo.class);
    }

    @Override
    public TPssTechkRoutLgVo queryInfo(Long id) {
        TPssTechkRoutLg tPssTechkRoutLg = this.baseMapper.selectById(id);
        if (tPssTechkRoutLg == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssTechkRoutLg, TPssTechkRoutLgVo.class);
    }

    @Override
    public TPssTechkRoutLgVo add(AddTPssTechkRoutLgDto dto) {
        TPssTechkRoutLg tPssTechkRoutLg = BeanUtil.toBean(dto, TPssTechkRoutLg.class);
        this.baseMapper.insert(tPssTechkRoutLg);

        return BeanUtil.copyProperties(tPssTechkRoutLg, TPssTechkRoutLgVo.class);
    }

    @Override
    public TPssTechkRoutLgVo update(UpdateTPssTechkRoutLgDto dto) {
        TPssTechkRoutLg tPssTechkRoutLg = BeanUtil.toBean(dto, TPssTechkRoutLg.class);
        this.baseMapper.updateById(tPssTechkRoutLg);

        return BeanUtil.copyProperties(tPssTechkRoutLg, TPssTechkRoutLgVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssTechkRoutLgPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssTechkRoutLgPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssTechkRoutLg.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssTechkRoutLgPageDto dto, Boolean isTemplate) {
        List<TPssTechkRoutLgPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssTechkRoutLgPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "TechkRoutLg" + ExcelTypeEnum.XLSX.getValue());
    }
}
