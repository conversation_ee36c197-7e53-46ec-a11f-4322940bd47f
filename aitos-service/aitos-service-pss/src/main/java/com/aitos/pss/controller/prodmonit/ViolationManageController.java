package com.aitos.pss.controller.prodmonit;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.prodmonit.AddTPssViolationsDto;
import com.aitos.pss.dto.prodmonit.TPssViolationsPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssViolationsDto;
import com.aitos.pss.service.prodmonit.IViolationManageService;
import com.aitos.pss.vo.prodmonit.TPssViolationsPageVo;
import com.aitos.pss.vo.prodmonit.TPssViolationsVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 违规单管理
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/violationmanage")
@Tag(name = "/pss"  + "/violationmanage",description = "违规单管理代码")
@AllArgsConstructor
public class ViolationManageController {


    private final IViolationManageService violationManageService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssViolations列表(分页)")
    @SaCheckPermission(value = "violationmanage:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssViolationsPageVo>> page(@Valid TPssViolationsPageDto dto){

        return RT.ok(violationManageService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssViolations信息")
    @SaCheckPermission(value = "violationmanage:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssViolationsVo> info(@RequestParam Long id){

        return RT.ok(violationManageService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssViolations")
    @SaCheckPermission(value = "violationmanage:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "违规单管理新增数据")
    public RT<TPssViolationsVo> add(@Valid @RequestBody AddTPssViolationsDto dto){

        return RT.ok(violationManageService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssViolations")
    @SaCheckPermission(value = "violationmanage:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "违规单管理修改数据")
    public RT<TPssViolationsVo> update(@Valid @RequestBody UpdateTPssViolationsDto dto){

        return RT.ok(violationManageService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "violationmanage:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "违规单管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(violationManageService.removeBatchByIds(ids));

    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "违规单管理导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        return RT.ok(violationManageService.importData(file));
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "违规单管理导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssViolationsPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return violationManageService.exportData(dto,isTemplate);
    }
}