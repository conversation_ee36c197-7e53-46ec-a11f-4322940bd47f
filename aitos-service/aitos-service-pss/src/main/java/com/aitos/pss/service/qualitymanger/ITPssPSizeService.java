package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssPSizeDto;
import com.aitos.pss.dto.qualitymanger.TPssPSizePageDto;
import com.aitos.pss.entity.qualitymanger.TPssPSize;
import com.aitos.pss.vo.qualitymanger.TPssPSizePageVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

public interface ITPssPSizeService extends IService<TPssPSize> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssPSizePageVo> queryPage(TPssPSizePageDto dto);

    /**
     * 尺寸判定
     * @param dtoList
     * @return
     */
    Boolean sizeDecision(@Valid List<AddTPssPSizeDto> dtoList);
}