package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.TPssBatchSamplingPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssBatchSamplingDto;
import com.aitos.pss.service.qualitymanger.IBatchSamplingService;
import com.aitos.pss.vo.qualitymanger.TPssBatchSamplingPageVo;
import com.aitos.pss.vo.qualitymanger.TPssBatchSamplingVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


@RestController
@RequestMapping("/pss" + "/batchsampling")
@Tag(name = "/pss"  + "/batchsampling",description = "炼钢/轧钢坯料取样代码")
@AllArgsConstructor
public class BatchSamplingController {

    private final IBatchSamplingService batchSamplingService;

    @GetMapping(value = "/page")
    @Operation(summary = "列表(分页)")
    public RT<PageOutput<TPssBatchSamplingPageVo>> page(@Valid TPssBatchSamplingPageDto dto){

        return RT.ok(batchSamplingService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询信息")
    public RT<TPssBatchSamplingVo> info(@RequestParam Long id){

        return RT.ok(batchSamplingService.queryInfo(id));
    }

    @PostMapping("/recheck")
    @Operation(summary = "复检")
    @AitLog(value = "复检")
    public RT<Boolean> recheck(@Valid @RequestBody List<UpdateTPssBatchSamplingDto> dtoList){
        return RT.ok(batchSamplingService.recheck(dtoList));

    }
}