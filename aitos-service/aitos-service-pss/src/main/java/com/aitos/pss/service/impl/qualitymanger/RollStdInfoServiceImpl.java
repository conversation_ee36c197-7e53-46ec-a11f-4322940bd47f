package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssRollStdInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssRollStdInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssRollStdInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssRollStdInfo;
import com.aitos.pss.mapper.qualitymanger.TPssRollStdInfoMapper;
import com.aitos.pss.service.qualitymanger.IRollStdInfoService;
import com.aitos.pss.vo.qualitymanger.TPssRollStdInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssRollStdInfoVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class RollStdInfoServiceImpl extends ServiceImpl<TPssRollStdInfoMapper, TPssRollStdInfo> implements IRollStdInfoService {

    @Override
    public PageOutput<TPssRollStdInfoPageVo> queryPage(TPssRollStdInfoPageDto dto) {
        LambdaQueryWrapper<TPssRollStdInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualId()),TPssRollStdInfo::getCQualId,dto.getCQualId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssRollStdInfo::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssRollStdInfo::getNEnabledMark,dto.getNEnabledMark())
                .like(StrUtil.isNotBlank(dto.getCOrderNo()),TPssRollStdInfo::getCOrderNo,dto.getCOrderNo())
                .between(
                        ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()),
                        TPssRollStdInfo::getDtCreateDateTime,
                        dto.getDtCreateDateTimeStart(),
                        dto.getDtCreateDateTimeEnd())
                .orderByDesc(TPssRollStdInfo::getNId)
                .select(TPssRollStdInfo.class,x -> VoToColumnUtil.fieldsToColumns(TPssRollStdInfoPageVo.class).contains(x.getProperty()));
        IPage<TPssRollStdInfo> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssRollStdInfoPageVo.class);
    }

    @Override
    public TPssRollStdInfoVo queryInfo(Long id) {
        TPssRollStdInfo tPssRollStdInfo = this.baseMapper.selectById(id);
        if (tPssRollStdInfo == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssRollStdInfo, TPssRollStdInfoVo.class);
    }

    @Override
    public TPssRollStdInfoVo add(AddTPssRollStdInfoDto dto) {
        TPssRollStdInfo tPssRollStdInfo = BeanUtil.toBean(dto, TPssRollStdInfo.class);
        this.baseMapper.insert(tPssRollStdInfo);

        return BeanUtil.copyProperties(tPssRollStdInfo, TPssRollStdInfoVo.class);
    }

    @Override
    public TPssRollStdInfoVo update(UpdateTPssRollStdInfoDto dto) {
        TPssRollStdInfo tPssRollStdInfo = BeanUtil.toBean(dto, TPssRollStdInfo.class);
        this.baseMapper.updateById(tPssRollStdInfo);

        return BeanUtil.copyProperties(tPssRollStdInfo, TPssRollStdInfoVo.class);
    }
}
