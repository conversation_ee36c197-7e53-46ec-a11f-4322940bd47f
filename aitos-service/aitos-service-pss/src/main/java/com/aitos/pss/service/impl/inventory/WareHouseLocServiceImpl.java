package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.AddTPssWareHouseLocDto;
import com.aitos.pss.dto.inventory.TPssWareHouseLocPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssWareHouseLocDto;
import com.aitos.pss.entity.inventory.TPssWareHouseLoc;
import com.aitos.pss.mapper.inventory.TPssWareHouseLocMapper;
import com.aitos.pss.service.inventory.IWareHouseLocService;
import com.aitos.pss.vo.inventory.TPssWareHouseLocPageVo;
import com.aitos.pss.vo.inventory.TPssWareHouseLocVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class WareHouseLocServiceImpl extends ServiceImpl<TPssWareHouseLocMapper, TPssWareHouseLoc> implements IWareHouseLocService {

    @Override
    public PageOutput<TPssWareHouseLocPageVo> queryPage(TPssWareHouseLocPageDto dto) {
        LambdaQueryWrapper<TPssWareHouseLoc> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCCode()),TPssWareHouseLoc::getCCode,dto.getCCode())
                .like(StrUtil.isNotBlank(dto.getCName()),TPssWareHouseLoc::getCName,dto.getCName())
                .eq(ObjectUtil.isNotNull(dto.getNRow()),TPssWareHouseLoc::getNRow,dto.getNRow())
                .like(StrUtil.isNotBlank(dto.getCType()),TPssWareHouseLoc::getCType,dto.getCType())
                .like(StrUtil.isNotBlank(dto.getCParentCode()),TPssWareHouseLoc::getCParentCode,dto.getCParentCode())
                .eq(ObjectUtil.isNotNull(dto.getNCol()),TPssWareHouseLoc::getNCol,dto.getNCol())
                .like(StrUtil.isNotBlank(dto.getCRemark()),TPssWareHouseLoc::getCRemark,dto.getCRemark())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssWareHouseLoc::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssWareHouseLoc::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssWareHouseLoc::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssWareHouseLoc::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssWareHouseLoc::getDtCreateDateTime)
                .select(TPssWareHouseLoc.class,x -> VoToColumnUtil.fieldsToColumns(TPssWareHouseLocPageVo.class).contains(x.getProperty()));
        IPage<TPssWareHouseLoc> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssWareHouseLocPageVo.class);
    }

    @Override
    public TPssWareHouseLocVo queryInfo(Long id) {
        TPssWareHouseLoc tPssWareHouseLoc = this.baseMapper.selectById(id);
        if (tPssWareHouseLoc == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssWareHouseLoc, TPssWareHouseLocVo.class);
    }

    @Override
    public TPssWareHouseLocVo add(AddTPssWareHouseLocDto dto) {
        TPssWareHouseLoc tPssWareHouseLoc = BeanUtil.toBean(dto, TPssWareHouseLoc.class);
        this.baseMapper.insert(tPssWareHouseLoc);

        return BeanUtil.copyProperties(tPssWareHouseLoc, TPssWareHouseLocVo.class);
    }

    @Override
    public TPssWareHouseLocVo update(UpdateTPssWareHouseLocDto dto) {
        TPssWareHouseLoc tPssWareHouseLoc = BeanUtil.toBean(dto, TPssWareHouseLoc.class);
        this.baseMapper.updateById(tPssWareHouseLoc);

        return BeanUtil.copyProperties(tPssWareHouseLoc, TPssWareHouseLocVo.class);
    }
}
