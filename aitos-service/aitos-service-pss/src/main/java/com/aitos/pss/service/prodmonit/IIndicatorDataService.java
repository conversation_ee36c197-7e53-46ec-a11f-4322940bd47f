
package com.aitos.pss.service.prodmonit;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.prodmonit.AddTPssIndicatorDataDto;
import com.aitos.pss.dto.prodmonit.TPssIndicatorDataPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssIndicatorDataDto;
import com.aitos.pss.entity.prodmonit.TPssIndicatorData;
import com.aitos.pss.vo.prodmonit.TPssIndicatorDataPageVo;
import com.aitos.pss.vo.prodmonit.TPssIndicatorDataVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/

public interface IIndicatorDataService extends IService<TPssIndicatorData> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssIndicatorDataPageVo> queryPage(@Valid TPssIndicatorDataPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssIndicatorDataVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssIndicatorDataVo add(@Valid AddTPssIndicatorDataDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssIndicatorDataVo update(@Valid UpdateTPssIndicatorDataDto dto);

    /**
     * importData
     * @param file
     * @return
     */
    Void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssIndicatorDataPageDto dto, Boolean isTemplate);
}
