package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabFaceDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabFacePageDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabFace;
import com.aitos.pss.entity.qualitymanger.TPssSlabJudge;
import com.aitos.pss.entity.qualitymanger.TPssSlabRes;
import com.aitos.pss.enums.PssCheEnum;
import com.aitos.pss.enums.PssQCEnum;
import com.aitos.pss.enums.PssQFaceEnum;
import com.aitos.pss.enums.PssQSizeEnum;
import com.aitos.pss.mapper.qualitymanger.TPssSlabFaceMapper;
import com.aitos.pss.service.qualitymanger.ISlabJudgeService;
import com.aitos.pss.service.qualitymanger.ISlabResService;
import com.aitos.pss.service.qualitymanger.ITPssSlabFaceService;
import com.aitos.pss.vo.qualitymanger.TPssSlabFacePageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabFaceVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssSlabFaceServiceImpl extends MPJBaseServiceImpl<TPssSlabFaceMapper, TPssSlabFace> implements ITPssSlabFaceService {

    private final ISlabResService slabResService;

    private final ISlabJudgeService slabJudgeService;

    @Override
    public Boolean factDecision(List<AddTPssSlabFaceDto> dtoList) {

        if (CollectionUtils.isEmpty(dtoList)) {
            return Boolean.FALSE;
        }

        Set<String> cSlabIdSet = dtoList.stream().map(AddTPssSlabFaceDto::getCSlabId).collect(Collectors.toSet());
        List<TPssSlabRes> slabResList = slabResService.list(Wrappers.<TPssSlabRes>lambdaQuery().in(TPssSlabRes::getCMatId, cSlabIdSet));
        Map<String, TPssSlabRes> cMatIdAndTPssSlabResMap =
                slabResList
                        .stream()
                        .collect(Collectors.toMap(TPssSlabRes::getCMatId, v -> v, (k1, k2) -> k1));

        for (AddTPssSlabFaceDto addTPssSlabFaceDto : dtoList) {
            TPssSlabRes tPssSlabRes = cMatIdAndTPssSlabResMap.get(addTPssSlabFaceDto.getCSlabId());
            // 表面等级
            if (Objects.equals(addTPssSlabFaceDto.getCFaceRlt(), PssQFaceEnum.QUALIFIED.getCode())) {
                // 不合格
                TPssSlabJudge slabJudge =
                        slabJudgeService.getOne(
                                Wrappers.<TPssSlabJudge>lambdaQuery()
                                        .eq(TPssSlabJudge::getCSlabId, addTPssSlabFaceDto.getCSlabId())
                        );
                if (Objects.isNull(slabJudge)) {
                    slabJudge = new TPssSlabJudge();
                    BeanUtil.copyProperties(addTPssSlabFaceDto, slabJudge);
                    slabJudge.setCChemRlt(PssCheEnum.QUALIFIED.getCode());
                    slabJudge.setCBodyRlt(PssQFaceEnum.QUALIFIED.getCode());
                    slabJudge.setCSizeRlt(PssQSizeEnum.QUALIFIED.getCode());
                    slabJudge.setCFaceRlt(PssQFaceEnum.QUALIFIED.getCode());
                    slabJudge.setCJudgeRlt(PssQCEnum.QUALIFIED.getCode());

                    tPssSlabRes.setCProdGrd(PssQCEnum.QUALIFIED.getCode());

                    slabJudgeService.save(slabJudge);
                    continue;
                }
                if (!Objects.equals(slabJudge.getCFaceRlt(), PssQFaceEnum.QUALIFIED.getCode())) {
                    slabJudge.setCJudgeRlt(PssQCEnum.PENDING.getCode());
                    tPssSlabRes.setCProdGrd(PssQCEnum.QUALIFIED.getCode());
                }
                slabJudge.setCFaceRlt(addTPssSlabFaceDto.getCFaceRlt());
                slabJudgeService.updateById(slabJudge);
            }


            tPssSlabRes.setCSurfGrd(addTPssSlabFaceDto.getCFaceRlt());
        }

        List<TPssSlabFace> slabFaceList = BeanUtil.copyToList(dtoList, TPssSlabFace.class);
        saveBatch(slabFaceList);

        slabResService.updateBatchById(slabResList);

        return Boolean.TRUE;
    }

    @Override
    public TPssSlabFaceVo add(AddTPssSlabFaceDto dto) {
        TPssSlabFace slabFace = BeanUtil.toBean(dto, TPssSlabFace.class);
        this.baseMapper.insert(slabFace);

        return BeanUtil.copyProperties(slabFace, TPssSlabFaceVo.class);
    }

    @Override
    public PageOutput<TPssSlabFacePageVo> queryPage(TPssSlabFacePageDto dto) {
        LambdaQueryWrapper<TPssSlabFace> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(dto.getCSlabId()), TPssSlabFace::getCSlabId, dto.getCSlabId());

        IPage<TPssSlabFace> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssSlabFacePageVo.class);
    }
}
