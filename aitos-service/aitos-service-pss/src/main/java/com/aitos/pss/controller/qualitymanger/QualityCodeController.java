package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssQualityCodeDto;
import com.aitos.pss.dto.qualitymanger.TPssQualityCodePageDto;
import com.aitos.pss.dto.qualitymanger.TranTPssQualityCodeDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualityCodeDto;
import com.aitos.pss.service.qualitymanger.IQualityCodeService;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodePageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 质量编码管理
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/qualitycode")
@Tag(name = "/pss"  + "/qualitycode",description = "质量编码管理代码")
@AllArgsConstructor
public class QualityCodeController {


    private final IQualityCodeService qualityCodeService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssQualityCode列表(分页)")
    public RT<PageOutput<TPssQualityCodePageVo>> page(@Valid TPssQualityCodePageDto dto){

        return RT.ok(qualityCodeService.queryPage(dto));
    }

    @GetMapping(value = "/list")
    @Operation(summary = "TPssQualityCode列表")
    public RT<List<TPssQualityCodePageVo>> queryList(@Valid TPssQualityCodePageDto dto){

        return RT.ok(qualityCodeService.queryList(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssQualityCode信息")
    public RT<TPssQualityCodeVo> queryInfo(@RequestParam Long id){

        return RT.ok(qualityCodeService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssQualityCode")
    @AitLog(value = "质量编码管理新增数据")
    public RT<Long> add(@Valid @RequestBody AddTPssQualityCodeDto dto){

        return RT.ok(qualityCodeService.add(dto));
    }

    @PostMapping("/copy")
    @Operation(summary =  "新增TPssQualityCode")
    @AitLog(value = "质量编码管理新增数据")
    public RT<Long> copy(@Valid @RequestBody AddTPssQualityCodeDto dto){

        return RT.ok(qualityCodeService.copy(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssQualityCode")
    @AitLog(value = "质量编码管理修改数据")
    public RT<Boolean> update(@Valid @RequestBody UpdateTPssQualityCodeDto dto){

        return RT.ok(qualityCodeService.update(dto));

    }

    @PostMapping("/quality-code-tran")
    @Operation(summary = "质量编码转化")
    @AitLog(value = "质量编码转化")
    public RT<TPssQualityCodeVo> qualityCodeTran(@Valid @RequestBody TranTPssQualityCodeDto dto){

        return RT.ok(qualityCodeService.updqualityCodeTranate(dto));

    }

    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "质量编码管理导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        qualityCodeService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "质量编码管理导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssQualityCodePageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return qualityCodeService.exportData(dto,isTemplate);
    }
}