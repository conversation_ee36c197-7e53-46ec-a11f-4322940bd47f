package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssPosTimeDto;
import com.aitos.pss.dto.planmanger.TPssPosTimePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssPosTimeDto;
import com.aitos.pss.entity.planmanger.TPssPosTime;
import com.aitos.pss.mapper.planmanger.TPssPosTimeMapper;
import com.aitos.pss.service.planmanger.ITPssPosTimeService;
import com.aitos.pss.vo.planmanger.TPssPosTimePageVo;
import com.aitos.pss.vo.planmanger.TPssPosTimeVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssPosTimeServiceImpl extends ServiceImpl<TPssPosTimeMapper, TPssPosTime> implements ITPssPosTimeService {

    @Override
    public PageOutput<TPssPosTimePageVo> queryPage(TPssPosTimePageDto dto) {
        LambdaQueryWrapper<TPssPosTime> queryWrapper =
                Wrappers.<TPssPosTime>lambdaQuery()
                        .eq(Objects.nonNull(dto.getCProLine()),TPssPosTime::getCProLine,dto.getCProLine())
                        .eq(Objects.nonNull(dto.getCProcess()),TPssPosTime::getCProcess,dto.getCProcess())
                        .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssPosTime::getCStlGrdCd,dto.getCStlGrdCd())
                        .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssPosTime::getNEnabledMark,dto.getNEnabledMark())
                        .eq(ObjectUtil.isNotNull(dto.getNWorkTime()),TPssPosTime::getNWorkTime,dto.getNWorkTime())
                        .orderByDesc(TPssPosTime::getNId)
                        .select(TPssPosTime.class,x -> VoToColumnUtil.fieldsToColumns(TPssPosTimePageVo.class).contains(x.getProperty()));
        IPage<TPssPosTime> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssPosTimePageVo.class);
    }

    @Override
    public TPssPosTimeVo queryInfo(Long id) {
        TPssPosTime tPssPosTime = this.baseMapper.selectById(id);
        if (tPssPosTime == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssPosTime, TPssPosTimeVo.class);
    }

    @Override
    public TPssPosTimeVo add(AddTPssPosTimeDto dto) {
        TPssPosTime tPssPosTime = BeanUtil.toBean(dto, TPssPosTime.class);
        this.baseMapper.insert(tPssPosTime);

        return BeanUtil.copyProperties(tPssPosTime, TPssPosTimeVo.class);
    }

    @Override
    public TPssPosTimeVo update(UpdateTPssPosTimeDto dto) {
        TPssPosTime tPssPosTime = BeanUtil.toBean(dto, TPssPosTime.class);
        this.baseMapper.updateById(tPssPosTime);

        return BeanUtil.copyProperties(tPssPosTime, TPssPosTimeVo.class);
    }

}
