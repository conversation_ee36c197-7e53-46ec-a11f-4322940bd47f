package com.aitos.pss.controller.costmanger;

import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.costmanger.TPssOrderCostPageDto;
import com.aitos.pss.service.costmanger.IOrderCostService;
import com.aitos.pss.vo.costmanger.TPssOrderCostChartVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
* @title: 订单成本管理
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/
@RestController
@RequestMapping("/pss/ordercost")
@Tag(name = "/pss/ordercost", description = "订单成本管理")
@AllArgsConstructor
public class OrderCostController {

    private final IOrderCostService orderCostService;

    @GetMapping()
    @Operation(summary = "面板查询")
    public RT<TPssOrderCostChartVo> queryPanel(@Valid TPssOrderCostPageDto dto) {
        return RT.ok(orderCostService.queryPanel(dto));
    }
} 