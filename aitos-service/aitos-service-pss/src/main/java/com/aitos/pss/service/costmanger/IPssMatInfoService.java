
package com.aitos.pss.service.costmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.costmanger.AddTPssMatInfoDto;
import com.aitos.pss.dto.costmanger.TPssMatInfoPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssMatInfoDto;
import com.aitos.pss.entity.costmanger.TPssMatInfo;
import com.aitos.pss.vo.costmanger.TPssMatInfoPageVo;
import com.aitos.pss.vo.costmanger.TPssMatInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/

public interface IPssMatInfoService extends IService<TPssMatInfo> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssMatInfoPageVo> queryPage(@Valid TPssMatInfoPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssMatInfoVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssMatInfoVo add(@Valid AddTPssMatInfoDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssMatInfoVo update(@Valid UpdateTPssMatInfoDto dto);
}
