
package com.aitos.pss.service.costmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.costmanger.AddTPssMatMathRuleDto;
import com.aitos.pss.dto.costmanger.TPssMatMathRulePageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssMatMathRuleDto;
import com.aitos.pss.entity.costmanger.TPssMatMathRule;
import com.aitos.pss.vo.costmanger.TPssMatMathRulePageVo;
import com.aitos.pss.vo.costmanger.TPssMatMathRuleVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/

public interface IMatMathRuleService extends IService<TPssMatMathRule> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssMatMathRulePageVo> queryPage(@Valid TPssMatMathRulePageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssMatMathRuleVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssMatMathRuleVo add(@Valid AddTPssMatMathRuleDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssMatMathRuleVo update(@Valid UpdateTPssMatMathRuleDto dto);
}
