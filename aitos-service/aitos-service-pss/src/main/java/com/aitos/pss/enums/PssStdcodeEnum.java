package com.aitos.pss.enums;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import java.util.Objects;

/**
 * <AUTHOR>
 *
 * 协议标准
 *
 * @version 1.0.0
 * @date 2025/6/10 14:57
 */
@Getter
@Log4j2
public enum PssStdcodeEnum {

    /** 国标 */
    NS("1", "国标"),

    /** 判定 */
    JUDGE("2", "判定"),

    /** 内控 */
    IC("3", "内控"),

    /** 特殊 */
    SPECIAL("4", "特殊"),
    ;

    private final String code;
    private final String description;

    PssStdcodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 安全查询方法（不抛异常）
    public static PssStdcodeEnum getByCode(String code) {

        for (PssStdcodeEnum result : values()) {
            if (Objects.equals(result.code, code)) {
                return result;
            }
        }

        log.warn("无效的检测结果编码: {}", code);
        return null;
    }

    // 安全查询方法（不抛异常）
    public static PssStdcodeEnum getByDescription(String description) {

        for (PssStdcodeEnum result : values()) {
            if (result.description.equals(description)) {
                return result;
            }
        }

        log.warn("无效的检测结果描述: {}", description);
        return null;
    }

}
