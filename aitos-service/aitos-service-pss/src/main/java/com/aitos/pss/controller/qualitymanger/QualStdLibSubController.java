package com.aitos.pss.controller.qualitymanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssQualStdLibSubDto;
import com.aitos.pss.dto.qualitymanger.TPssQualStdLibSubPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualStdLibSubDto;
import com.aitos.pss.service.qualitymanger.IQualStdLibSubService;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibSubPageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibSubVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 质检标准库子表
* <AUTHOR>
* @Date: 2025-07-09
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/qualstdlibsub")
@Tag(name = "/pss"  + "/qualstdlibsub",description = "质检标准库子表代码")
@AllArgsConstructor
public class QualStdLibSubController {


    private final IQualStdLibSubService qualStdLibSubService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssQualStdLibSub列表(分页)")
    @SaCheckPermission(value = "qualstdlibsub:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssQualStdLibSubPageVo>> page(@Valid TPssQualStdLibSubPageDto dto){

        return RT.ok(qualStdLibSubService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssQualStdLibSub信息")
    @SaCheckPermission(value = "qualstdlibsub:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssQualStdLibSubVo> info(@RequestParam Long id){

        return RT.ok(qualStdLibSubService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssQualStdLibSub")
    @SaCheckPermission(value = "qualstdlibsub:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "质检标准库子表新增数据")
    public RT<TPssQualStdLibSubVo> add(@Valid @RequestBody AddTPssQualStdLibSubDto dto){

        return RT.ok(qualStdLibSubService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssQualStdLibSub")
    @SaCheckPermission(value = "qualstdlibsub:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "质检标准库子表修改数据")
    public RT<TPssQualStdLibSubVo> update(@Valid @RequestBody UpdateTPssQualStdLibSubDto dto){

        return RT.ok(qualStdLibSubService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "qualstdlibsub:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "质检标准库子表删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(qualStdLibSubService.removeBatchByIds(ids));
    }

}