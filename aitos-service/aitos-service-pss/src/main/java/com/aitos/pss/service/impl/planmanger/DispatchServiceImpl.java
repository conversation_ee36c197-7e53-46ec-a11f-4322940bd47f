package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.enums.YesOrNoEnum;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssDispatchDto;
import com.aitos.pss.dto.planmanger.AddTPssMatResDto;
import com.aitos.pss.dto.planmanger.TPssDispatchPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssDispatchDto;
import com.aitos.pss.entity.planmanger.TPssDispatch;
import com.aitos.pss.entity.planmanger.TPssMatRes;
import com.aitos.pss.entity.planmanger.TPssRollSch;
import com.aitos.pss.mapper.planmanger.TPssDispatchMapper;
import com.aitos.pss.service.planmanger.IDispatchService;
import com.aitos.pss.service.planmanger.ITPssRollSchService;
import com.aitos.pss.service.qualitymanger.IMatResService;
import com.aitos.pss.vo.planmanger.TPssDispatchPageVo;
import com.aitos.pss.vo.planmanger.TPssDispatchVo;
import com.aitos.system.client.v2.ICodeRuleClientV2;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-28
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class DispatchServiceImpl extends MPJBaseServiceImpl<TPssDispatchMapper, TPssDispatch> implements IDispatchService {

    private final ITPssRollSchService rollSchService;

    private final ICodeRuleClientV2 codeRuleClientV2;

    private final IMatResService resService;


    @Override
    public PageOutput<TPssDispatchPageVo> queryPage(TPssDispatchPageDto dto) {
        LambdaQueryWrapper<TPssDispatch> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(TPssDispatch::getCPreStatus, YesOrNoEnum.NO.getCode())
                .like(StringUtils.isNotBlank(dto.getCDispatchId()),TPssDispatch::getCDispatchId, dto.getCDispatchId())
                .eq(StringUtils.isNotBlank(dto.getCStlGrdCd()),TPssDispatch::getCStlGrdCd, dto.getCStlGrdCd())
                .eq(StringUtils.isNotBlank(dto.getCFpoststateid()),TPssDispatch::getCFpoststateid, dto.getCFpoststateid())
                .eq(StringUtils.isNotBlank(dto.getCFpoststateid()),TPssDispatch::getCFpoststateid, dto.getCFpoststateid())
                .between(
                        ObjectUtil.isNotNull(dto.getDtReleaseTimeStart()) && ObjectUtil.isNotNull(dto.getDtReleaseTimeEnd()),
                        TPssDispatch::getDtReleaseTime,dto.getDtReleaseTimeStart(),
                        dto.getDtReleaseTimeEnd())
                .between(
                        ObjectUtil.isNotNull(dto.getDtSendTimeStart()) && ObjectUtil.isNotNull(dto.getDtSendTimeEnd()),
                        TPssDispatch::getDtSendTime,
                        dto.getDtSendTimeStart(),dto.getDtSendTimeEnd())
                .orderByDesc(TPssDispatch::getNId)
                .select(TPssDispatch.class,x -> VoToColumnUtil.fieldsToColumns(TPssDispatchPageVo.class).contains(x.getProperty()));
        IPage<TPssDispatch> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssDispatchPageVo.class);
    }

    @Override
    public TPssDispatchVo queryInfo(Long id) {
        TPssDispatch tPssDispatch = this.baseMapper.selectById(id);
        if (tPssDispatch == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssDispatch, TPssDispatchVo.class);
    }

    @Override
    @Transactional
    public TPssDispatchVo add(AddTPssDispatchDto dto) {
        TPssDispatch tPssDispatch = BeanUtil.toBean(dto, TPssDispatch.class);
        this.baseMapper.insert(tPssDispatch);

        return BeanUtil.copyProperties(tPssDispatch, TPssDispatchVo.class);
    }

    @Override
    @Transactional
    public TPssDispatchVo update(UpdateTPssDispatchDto dto) {
        TPssDispatch tPssDispatch = BeanUtil.toBean(dto, TPssDispatch.class);
        this.baseMapper.updateById(tPssDispatch);

        return BeanUtil.copyProperties(tPssDispatch, TPssDispatchVo.class);
    }

    @Override
    @Transactional
    public Boolean deleteBatchId(List<Long> ids) {
        this.baseMapper.deleteByIds(ids);

        return Boolean.TRUE;
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssDispatchPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssDispatchPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssDispatch.class));

    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssDispatchPageDto dto, Boolean isTemplate) {
        List<TPssDispatchPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssDispatchPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "Dispatch" + ExcelTypeEnum.XLSX.getValue());
    }

    @Override
    @Transactional
    public Boolean orderUpOrDown(UpdateTPssDispatchDto dto) {
        if (Objects.equals(YesOrNoEnum.YES.getCode(), dto.getCPreStatus())) {
            // 上线
            Long uoOrderCount =
                    this.baseMapper.selectCount(
                            Wrappers.<TPssDispatch>lambdaQuery()
                                    .eq(TPssDispatch::getCPreStatus, YesOrNoEnum.YES.getCode())
                    );
            if (uoOrderCount > 0) {
                throw new MyException("只能上线一个工单");
            }
        }

        TPssDispatch dispatch = BeanUtil.copyProperties(dto, TPssDispatch.class);
        this.baseMapper.updateById(dispatch);

        return Boolean.TRUE;
    }

    @Override
    public PageOutput<TPssDispatchPageVo> queryUpOrder() {
        PageOutput<TPssDispatchPageVo> pageOutput = new PageOutput<>();

        List<TPssDispatch> tPssDispatchList =
                this.baseMapper.selectList(
                        Wrappers.<TPssDispatch>lambdaQuery()
                                .eq(TPssDispatch::getCPreStatus,YesOrNoEnum.YES.getCode())
                                .last("limit 1")
                );

        List<TPssDispatchPageVo> tPssDispatchPageVos = BeanUtil.copyToList(tPssDispatchList, TPssDispatchPageVo.class);
        pageOutput.setList(tPssDispatchPageVos);

        return pageOutput;
    }


    @Override
    @Transactional
    public Boolean placeOrder(List<AddTPssMatResDto> dtoList) {
        TPssDispatch dispatch =
                this.baseMapper.selectOne(
                        Wrappers.<TPssDispatch>lambdaQuery()
                                .eq(TPssDispatch::getCPreStatus,YesOrNoEnum.YES.getCode())
                                .last("limit 1")
                );
        if (Objects.isNull(dispatch)) {
            throw new MyException("没有已上线的工单不允许挂单");
        }

        // 获取当前调度单下已生成的最新轧制计划
        TPssRollSch lastRollSch =
                rollSchService.getOne(
                        Wrappers.<TPssRollSch>lambdaQuery()
                                .eq(TPssRollSch::getCDispatchId, dispatch.getCDispatchId())
                                .orderByDesc(TPssRollSch::getNSeqInMill)
                                .last("limit 1")
                );

        TPssRollSch rollSch = BeanUtil.copyProperties(dispatch, TPssRollSch.class);
        rollSch.setCSchId(codeRuleClientV2.generateAndUse("rollSch",dispatch.getCLineNo()).getDataOrThrow());
        rollSch.setCPrevSchId(Objects.isNull(lastRollSch) ? null : lastRollSch.getCSchId());
        rollSch.setNSeqInMill(Objects.isNull(lastRollSch) ? null : lastRollSch.getNSeqInMill().add(BigDecimal.ONE));
        rollSch.setCMakeFl(String.valueOf(YesOrNoEnum.NO.getCode()));
        rollSch.setCStlGrdCd(dispatch.getCStlGrdCd());
        rollSch.setCMatItem(dispatch.getCMatItem());

        // 挂单重量、挂单数量
        BigDecimal nDofinalWgt = BigDecimal.ZERO;
        BigDecimal nDofinalCount = BigDecimal.ZERO;
        for (AddTPssMatResDto addTPssMatResDto : dtoList) {
            addTPssMatResDto.setCPlanMatId(rollSch.getCSchId());
            nDofinalWgt = nDofinalWgt.add(addTPssMatResDto.getNMatWgt());
            nDofinalCount = nDofinalCount.add(BigDecimal.ONE);
            addTPssMatResDto.setCRollSchId(rollSch.getCSchId());
            addTPssMatResDto.setCPreOut(rollSch.getCLineNo());
        }

        rollSch.setNDofinalWgt(nDofinalWgt);
        rollSch.setNDofinalCount(nDofinalCount);
        rollSchService.save(rollSch);

        // TODO 然后根据坯料号修改t_pss_mat_locmap_zg对应数据的状态，改为待出炉

        List<TPssMatRes> matResList = BeanUtil.copyToList(dtoList, TPssMatRes.class);
        resService.updateBatchById(matResList);


        return Boolean.TRUE;
    }
}
