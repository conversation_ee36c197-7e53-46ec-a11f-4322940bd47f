
package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.mybatis.base.MPJDeepAndJoinBaseService;
import com.aitos.pss.dto.planmanger.AddTPssDispatchDto;
import com.aitos.pss.dto.planmanger.AddTPssMatResDto;
import com.aitos.pss.dto.planmanger.TPssDispatchPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssDispatchDto;
import com.aitos.pss.entity.planmanger.TPssDispatch;
import com.aitos.pss.vo.planmanger.TPssDispatchPageVo;
import com.aitos.pss.vo.planmanger.TPssDispatchVo;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-28
* @Version 1.0
*/

public interface IDispatchService extends MPJDeepAndJoinBaseService<TPssDispatch> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssDispatchPageVo> queryPage(@Valid TPssDispatchPageDto dto);

    /**
     * 详情查询
     * @param id
     * @return
     */
    TPssDispatchVo queryInfo(Long id);

    /**
    * 新增
    *
    * @param dto
    * @return
    */
    TPssDispatchVo add(AddTPssDispatchDto dto);

    /**
    * 更新
    *
    * @param dto
    * @return
    */
    TPssDispatchVo update(UpdateTPssDispatchDto dto);

    /**
    * 删除
    *
    * @param ids
    * @return
    */
    Boolean deleteBatchId(List<Long> ids);

    /**
     * 导入
     * @param file
     * @return
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * 导出
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssDispatchPageDto dto, Boolean isTemplate);

    /**
     * 挂单
     * @param dtoList
     * @return
     */
    Boolean placeOrder(List<AddTPssMatResDto> dtoList);

    /**
     * 工单上/下线
     * @param dto
     * @return
     */
    Boolean orderUpOrDown(@Valid UpdateTPssDispatchDto dto);

    /**
     * 查询已上线工单
     * @return
     */
    PageOutput<TPssDispatchPageVo> queryUpOrder();
}
