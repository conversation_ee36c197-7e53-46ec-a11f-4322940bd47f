
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssPJudgeDto;
import com.aitos.pss.dto.qualitymanger.TPssPJudgePageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssPJudgeDto;
import com.aitos.pss.entity.qualitymanger.TPssPJudge;
import com.aitos.pss.vo.qualitymanger.TPssPJudgePageVo;
import com.aitos.pss.vo.qualitymanger.TPssPJudgeVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-05
* @Version 1.0
*/

public interface IPJudgeService extends IService<TPssPJudge> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssPJudgePageVo> queryPage(@Valid TPssPJudgePageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssPJudgeVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssPJudgeVo add(@Valid AddTPssPJudgeDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssPJudgeVo update(@Valid UpdateTPssPJudgeDto dto);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssPJudgePageDto dto, Boolean isTemplate);
}
