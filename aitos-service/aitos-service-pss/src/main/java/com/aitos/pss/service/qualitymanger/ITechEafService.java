
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssTechEafDto;
import com.aitos.pss.dto.qualitymanger.TPssTechEafPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechEafDto;
import com.aitos.pss.entity.qualitymanger.TPssTechEaf;
import com.aitos.pss.vo.qualitymanger.TPssTechEafPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechEafVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/

public interface ITechEafService extends IService<TPssTechEaf> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssTechEafPageVo> queryPage(@Valid TPssTechEafPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssTechEafVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssTechEafVo add(@Valid AddTPssTechEafDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssTechEafVo update(@Valid UpdateTPssTechEafDto dto);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssTechEafPageDto dto, Boolean isTemplate);
}
