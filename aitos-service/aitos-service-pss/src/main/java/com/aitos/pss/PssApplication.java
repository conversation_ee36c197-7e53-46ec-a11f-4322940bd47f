package com.aitos.pss;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * @Author: tzx
 * @Date: 2023/9/19 15:04
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.aitos")
@ComponentScan(value = "com.aitos")
@MapperScan(value = "com.aitos.**.mapper")
public class PssApplication {
    public static void main(String[] args) {
        SpringApplication.run(PssApplication.class, args);
    }
}
