
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPssMaterialInspectionDto;
import com.aitos.pss.dto.inventory.TPssMaterialInspectionPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMaterialInspectionDto;
import com.aitos.pss.entity.inventory.TPssMaterialInspection;
import com.aitos.pss.vo.inventory.TPssMaterialInspectionPageVo;
import com.aitos.pss.vo.inventory.TPssMaterialInspectionVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/

public interface IMaterialInspectionService extends IService<TPssMaterialInspection> {
    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssMaterialInspectionPageVo> page(TPssMaterialInspectionPageDto dto);

    /**
     * 详细查询
     * @param id
     * @return
     */
    TPssMaterialInspectionVo info(Long id);

    /**
     * 新增
     * @param dto
     * @return
     */
    Long add(AddTPssMaterialInspectionDto dto);

    /**
     * 更新
     * @param dto
     * @return
     */
    Boolean update(UpdateTPssMaterialInspectionDto dto);


}
