package com.aitos.pss.controller.costmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.costmanger.AddTPssFcsMatPriceDto;
import com.aitos.pss.dto.costmanger.TPssFcsMatPricePageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsMatPriceDto;
import com.aitos.pss.service.costmanger.IFcsMatpriceService;
import com.aitos.pss.vo.costmanger.TPssFcsMatPricePageVo;
import com.aitos.pss.vo.costmanger.TPssFcsMatPriceVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 计划价格配置
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/fcsmatprice")
@Tag(name = "/pss"  + "/fcsmatprice",description = "计划价格配置代码")
@AllArgsConstructor
public class FcsMatpriceController {


    private final IFcsMatpriceService fcsMatpriceService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssFcsMatPrice列表(分页)")
    public RT<PageOutput<TPssFcsMatPricePageVo>> page(@Valid TPssFcsMatPricePageDto dto){

        return RT.ok(fcsMatpriceService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssFcsMatPrice信息")
    public RT<TPssFcsMatPriceVo> info(@RequestParam Long id){

        return RT.ok(fcsMatpriceService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssFcsMatPrice")
    @AitLog(value = "计划价格配置新增数据")
    public RT<TPssFcsMatPriceVo> add(@Valid @RequestBody AddTPssFcsMatPriceDto dto){

        return RT.ok(fcsMatpriceService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssFcsMatPrice")
    @AitLog(value = "计划价格配置修改数据")
    public RT<TPssFcsMatPriceVo> update(@Valid @RequestBody UpdateTPssFcsMatPriceDto dto){

        return RT.ok(fcsMatpriceService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "计划价格配置删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(fcsMatpriceService.removeBatchByIds(ids));
    }

}
