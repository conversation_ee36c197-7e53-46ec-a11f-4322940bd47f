package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssPSizeDto;
import com.aitos.pss.dto.qualitymanger.TPssPSizePageDto;
import com.aitos.pss.service.qualitymanger.ITPssPSizeService;
import com.aitos.pss.vo.qualitymanger.TPssPSizePageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/pss" + "/psize")
@Tag(name = "/pss"  + "/psize",description = "轧钢表尺判定管理")
@AllArgsConstructor
public class TPssPSizeController {

    private ITPssPSizeService pSizeService;

    @GetMapping("page")
    public RT<PageOutput<TPssPSizePageVo>> queryPage(TPssPSizePageDto dto) {

        return RT.ok(pSizeService.queryPage(dto));
    }

    @Operation(summary =  "尺寸判定")
    @AitLog(value = "尺寸判定")
    @PostMapping("/size-decision")
    public RT<Boolean> sizeDecision(@Valid @RequestBody List<AddTPssPSizeDto> dtoList){

        return RT.ok(pSizeService.sizeDecision(dtoList));
    }
}