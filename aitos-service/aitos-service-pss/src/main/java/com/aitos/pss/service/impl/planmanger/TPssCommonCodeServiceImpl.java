package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssCommonCodeDto;
import com.aitos.pss.dto.planmanger.TPssCommonCodePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssCommonCodeDto;
import com.aitos.pss.entity.planmanger.TPssCommonCode;
import com.aitos.pss.mapper.planmanger.TPssCommonCodeMapper;
import com.aitos.pss.service.planmanger.ITPssCommonCodeService;
import com.aitos.pss.vo.planmanger.TPssCommonCodePageVo;
import com.aitos.pss.vo.planmanger.TPssCommonCodeVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-15
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssCommonCodeServiceImpl extends ServiceImpl<TPssCommonCodeMapper, TPssCommonCode> implements ITPssCommonCodeService {

    @Override
    public PageOutput<TPssCommonCodePageVo> queryPage(TPssCommonCodePageDto dto) {
        LambdaQueryWrapper<TPssCommonCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCCode()),TPssCommonCode::getCCode,dto.getCCode())
                .like(StrUtil.isNotBlank(dto.getCName()),TPssCommonCode::getCName,dto.getCName())
                .like(StrUtil.isNotBlank(dto.getCManaNo()),TPssCommonCode::getCManaNo,dto.getCManaNo())
                .orderByDesc(TPssCommonCode::getNId)
                .select(TPssCommonCode.class,x -> VoToColumnUtil.fieldsToColumns(TPssCommonCodePageVo.class).contains(x.getProperty()));
        IPage<TPssCommonCode> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssCommonCodePageVo.class);
    }

    @Override
    public TPssCommonCodeVo queryInfo(Long id) {
        TPssCommonCode tPssCommonCode = this.baseMapper.selectById(id);
        if (tPssCommonCode == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssCommonCode, TPssCommonCodeVo.class);
    }

    @Override
    public TPssCommonCodeVo add(AddTPssCommonCodeDto dto) {
        if (!this.baseMapper.selectList(new LambdaQueryWrapper<TPssCommonCode>()
                .eq(StrUtil.isNotBlank(dto.getCCode()),TPssCommonCode::getCCode,dto.getCCode())
                .select(TPssCommonCode::getCCode))
                .isEmpty()) {
            throw new MyException("代码不能重复");
        }

        if (!this.baseMapper.selectList(new LambdaQueryWrapper<TPssCommonCode>()
                .eq(StrUtil.isNotBlank(dto.getCManaNo()),TPssCommonCode::getCManaNo,dto.getCManaNo())
                .select(TPssCommonCode::getCManaNo))
                .isEmpty()) {
            throw new MyException("代码管理号不能重复");
        }

        TPssCommonCode tPssCommonCode = BeanUtil.toBean(dto, TPssCommonCode.class);
        this.baseMapper.insert(tPssCommonCode);

        return BeanUtil.copyProperties(tPssCommonCode, TPssCommonCodeVo.class);
    }

    @Override
    public TPssCommonCodeVo update(UpdateTPssCommonCodeDto dto) {
        if (!this.baseMapper.selectList(new LambdaQueryWrapper<TPssCommonCode>()
                        .eq(StrUtil.isNotBlank(dto.getCCode()),TPssCommonCode::getCCode,dto.getCCode())
                        .ne(TPssCommonCode::getNId,dto.getNId())
                        .select(TPssCommonCode::getCCode))
                        .isEmpty() ) {
            throw new MyException("代码不能重复");
        }

        if (!this.baseMapper.selectList(new LambdaQueryWrapper<TPssCommonCode>()
                        .eq(StrUtil.isNotBlank(dto.getCManaNo()),TPssCommonCode::getCManaNo,dto.getCManaNo())
                        .select(TPssCommonCode::getCManaNo)
                        .ne(TPssCommonCode::getNId,dto.getNId()))
                        .isEmpty()) {
            throw new MyException("代码管理号不能重复");
        }

        TPssCommonCode tPssCommonCode = BeanUtil.toBean(dto, TPssCommonCode.class);
        this.baseMapper.updateById(tPssCommonCode);

        return BeanUtil.copyProperties(tPssCommonCode, TPssCommonCodeVo.class);
    }
}
