package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssStlMingleDto;
import com.aitos.pss.dto.planmanger.TPssStlMinglePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssStlMingleDto;
import com.aitos.pss.entity.planmanger.TPssStlMingle;
import com.aitos.pss.vo.planmanger.TPssStlMinglePageVo;
import com.aitos.pss.vo.planmanger.TPssStlMingleVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/

public interface ITPssStlMingleService extends IService<TPssStlMingle> {

    /**
     * query info
     * @param dto
     * @return
     */
    PageOutput<TPssStlMinglePageVo> queryPage(TPssStlMinglePageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssStlMingleVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssStlMingleVo add(@Valid AddTPssStlMingleDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssStlMingleVo update(@Valid UpdateTPssStlMingleDto dto);
}
