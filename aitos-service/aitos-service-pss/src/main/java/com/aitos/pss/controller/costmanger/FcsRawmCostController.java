package com.aitos.pss.controller.costmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.costmanger.AddTPssFcsRawmCostDto;
import com.aitos.pss.dto.costmanger.TPssFcsRawmCostPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsRawmCostDto;
import com.aitos.pss.service.costmanger.IFcsRawmCostService;
import com.aitos.pss.vo.costmanger.TPssFcsRawmCostPageVo;
import com.aitos.pss.vo.costmanger.TPssFcsRawmCostVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 工序成本核算
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/fcsrawmcost")
@Tag(name = "/pss"  + "/fcsrawmcost",description = "工序成本核算代码")
@AllArgsConstructor
public class FcsRawmCostController {


    private final IFcsRawmCostService fcsRawmCostService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssFcsRawmCost列表(分页)")
    public RT<PageOutput<TPssFcsRawmCostPageVo>> page(@Valid TPssFcsRawmCostPageDto dto){

        return RT.ok(fcsRawmCostService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssFcsRawmCost信息")
    public RT<TPssFcsRawmCostVo> info(@RequestParam Long id){

        return RT.ok(fcsRawmCostService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssFcsRawmCost")
    @AitLog(value = "工序成本核算新增数据")
    public RT<TPssFcsRawmCostVo> add(@Valid @RequestBody AddTPssFcsRawmCostDto dto){

        return RT.ok(fcsRawmCostService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssFcsRawmCost")
    @AitLog(value = "工序成本核算修改数据")
    public RT<TPssFcsRawmCostVo> update(@Valid @RequestBody UpdateTPssFcsRawmCostDto dto){

        return RT.ok(fcsRawmCostService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "工序成本核算删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(fcsRawmCostService.removeBatchByIds(ids));
    }

    @PostMapping("/approve")
    @Operation(summary = "审核")
    @AitLog(value = "审核")
    public RT<Boolean> approve(@Valid @RequestBody List<UpdateTPssFcsRawmCostDto> dtoList){

        return RT.ok(fcsRawmCostService.approve(dtoList));
    }

    @PostMapping("/approve-cancel")
    @Operation(summary = "取消审核")
    @AitLog(value = "取消审核")
    public RT<Boolean> approveCancel(@Valid @RequestBody List<UpdateTPssFcsRawmCostDto> dtoList){

        return RT.ok(fcsRawmCostService.approveCancel(dtoList));
    }

}