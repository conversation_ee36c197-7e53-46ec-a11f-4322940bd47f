
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabHeatStdInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabHeatStdInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabHeatStdInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabHeatStdInfo;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/

public interface ISlabHeatStdInfoService extends IService<TPssSlabHeatStdInfo> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssSlabHeatStdInfoPageVo> queryPage(@Valid TPssSlabHeatStdInfoPageDto dto);

    /**
     * query indo
     * @param id
     * @return
     */
    TPssSlabHeatStdInfoVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssSlabHeatStdInfoVo add(@Valid AddTPssSlabHeatStdInfoDto dto);

    /**
     *
     * @param dto
     * @return
     */
    TPssSlabHeatStdInfoVo update(@Valid UpdateTPssSlabHeatStdInfoDto dto);
}
