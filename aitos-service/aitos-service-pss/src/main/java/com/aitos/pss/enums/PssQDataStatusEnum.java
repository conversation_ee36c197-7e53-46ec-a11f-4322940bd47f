package com.aitos.pss.enums;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Getter
@Slf4j
public enum PssQDataStatusEnum {

    /** 待判 */
    CREATED(1, "新建"),

    /** 合格品 */
    CONFIRM(2, "已转化"),
    ;
    
    private final Integer code;
    
    private final String description;

    PssQDataStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    // 安全查询方法（不抛异常）
    public static PssQDataStatusEnum getByCode(Integer code) {

        for (PssQDataStatusEnum status : values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }

        log.warn("无效的质检状态编码: {}", code);
        return null;
    }

    // 安全查询方法（不抛异常）
    public static PssQDataStatusEnum getByDescription(String description) {

        for (PssQDataStatusEnum status : values()) {
            if (status.description.equals(description)) {
                return status;
            }
        }

        log.warn("无效的质检状态描述: {}", description);
        return null;
    }
}
