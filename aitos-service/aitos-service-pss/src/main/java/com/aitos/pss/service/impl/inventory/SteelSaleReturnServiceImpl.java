package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.AddTPssSteelSaleReturnDto;
import com.aitos.pss.dto.inventory.TPssSteelSaleReturnPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssSteelSaleReturnDto;
import com.aitos.pss.entity.inventory.TPssSteelSaleReturn;
import com.aitos.pss.mapper.inventory.TPssSteelSaleReturnMapper;
import com.aitos.pss.service.inventory.ISteelSaleReturnService;
import com.aitos.pss.vo.inventory.TPssSteelSaleReturnPageVo;
import com.aitos.pss.vo.inventory.TPssSteelSaleReturnVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class SteelSaleReturnServiceImpl extends ServiceImpl<TPssSteelSaleReturnMapper, TPssSteelSaleReturn> implements ISteelSaleReturnService {

    @Override
    public PageOutput<TPssSteelSaleReturnPageVo> queryPage(TPssSteelSaleReturnPageDto dto) {
        LambdaQueryWrapper<TPssSteelSaleReturn> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getStockNo()),TPssSteelSaleReturn::getStockNo,dto.getStockNo())
                .like(StrUtil.isNotBlank(dto.getMaterialNo()),TPssSteelSaleReturn::getMaterialNo,dto.getMaterialNo())
                .like(StrUtil.isNotBlank(dto.getSpec()),TPssSteelSaleReturn::getSpec,dto.getSpec())
                .eq(ObjectUtil.isNotNull(dto.getReturnQty()),TPssSteelSaleReturn::getReturnQty,dto.getReturnQty())
                .between(ObjectUtil.isNotNull(dto.getReturnTimeStart()) && ObjectUtil.isNotNull(dto.getReturnTimeEnd()),TPssSteelSaleReturn::getReturnTime,dto.getReturnTimeStart(),dto.getReturnTimeEnd())
                .like(StrUtil.isNotBlank(dto.getReturnReason()),TPssSteelSaleReturn::getReturnReason,dto.getReturnReason())
                .like(StrUtil.isNotBlank(dto.getStatus()),TPssSteelSaleReturn::getStatus,dto.getStatus())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssSteelSaleReturn::getNEnabledMark,dto.getNEnabledMark())
                .like(StrUtil.isNotBlank(dto.getDeliveryNo()),TPssSteelSaleReturn::getDeliveryNo,dto.getDeliveryNo())
                .like(StrUtil.isNotBlank(dto.getBatchNo()),TPssSteelSaleReturn::getBatchNo,dto.getBatchNo())
                .like(StrUtil.isNotBlank(dto.getSteelGrade()),TPssSteelSaleReturn::getSteelGrade,dto.getSteelGrade())
                .eq(ObjectUtil.isNotNull(dto.getWeight()),TPssSteelSaleReturn::getWeight,dto.getWeight())
                .eq(ObjectUtil.isNotNull(dto.getReturnWeight()),TPssSteelSaleReturn::getReturnWeight,dto.getReturnWeight())
                .like(StrUtil.isNotBlank(dto.getReturnUser()),TPssSteelSaleReturn::getReturnUser,dto.getReturnUser())
                .like(StrUtil.isNotBlank(dto.getOutNo()),TPssSteelSaleReturn::getOutNo,dto.getOutNo())
                .like(StrUtil.isNotBlank(dto.getRemark()),TPssSteelSaleReturn::getRemark,dto.getRemark())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssSteelSaleReturn::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssSteelSaleReturn::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssSteelSaleReturn::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssSteelSaleReturn::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssSteelSaleReturn::getDtCreateDateTime)
                .select(TPssSteelSaleReturn.class,x -> VoToColumnUtil.fieldsToColumns(TPssSteelSaleReturnPageVo.class).contains(x.getProperty()));
        IPage<TPssSteelSaleReturn> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssSteelSaleReturnPageVo.class);
    }

    @Override
    public TPssSteelSaleReturnVo queryInfo(Long id) {
        TPssSteelSaleReturn tPssSteelSaleReturn = this.baseMapper.selectById(id);
        if (tPssSteelSaleReturn == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssSteelSaleReturn, TPssSteelSaleReturnVo.class);
    }

    @Override
    public TPssSteelSaleReturnVo add(AddTPssSteelSaleReturnDto dto) {
        TPssSteelSaleReturn tPssSteelSaleReturn = BeanUtil.toBean(dto, TPssSteelSaleReturn.class);
        this.baseMapper.insert(tPssSteelSaleReturn);

        return BeanUtil.copyProperties(tPssSteelSaleReturn, TPssSteelSaleReturnVo.class);
    }

    @Override
    public TPssSteelSaleReturnVo update(UpdateTPssSteelSaleReturnDto dto) {
        TPssSteelSaleReturn tPssSteelSaleReturn = BeanUtil.toBean(dto, TPssSteelSaleReturn.class);
        this.baseMapper.updateById(tPssSteelSaleReturn);

        return BeanUtil.copyProperties(tPssSteelSaleReturn, TPssSteelSaleReturnVo.class);
    }
}
