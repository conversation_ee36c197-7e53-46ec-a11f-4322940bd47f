package com.aitos.pss.controller.planmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssDispatchDto;
import com.aitos.pss.dto.planmanger.AddTPssMatResDto;
import com.aitos.pss.dto.planmanger.TPssDispatchPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssDispatchDto;
import com.aitos.pss.service.planmanger.IDispatchService;
import com.aitos.pss.vo.planmanger.TPssDispatchPageVo;
import com.aitos.pss.vo.planmanger.TPssDispatchVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 物料匹配管理
* <AUTHOR>
* @Date: 2025-05-28
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/dispatch")
@Tag(name = "/pss"  + "/dispatch",description = "物料匹配管理代码")
@AllArgsConstructor
public class DispatchController {


    private final IDispatchService dispatchService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssDispatch列表(分页)")
    public RT<PageOutput<TPssDispatchPageVo>> queryPage(@Valid TPssDispatchPageDto dto){

        return RT.ok(dispatchService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssDispatch信息")
    public RT<TPssDispatchVo> queryInfo(@RequestParam Long id){

        return RT.ok(dispatchService.queryInfo(id));
    }

    @PostMapping
    @Operation(summary =  "新增TPssDispatch")
    @AitLog(value = "物料匹配管理新增数据")
    public RT<TPssDispatchVo> add(@Valid @RequestBody AddTPssDispatchDto dto){

        return RT.ok(dispatchService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssDispatch")
    @AitLog(value = "物料匹配管理修改数据")
    public RT<TPssDispatchVo> update(@Valid @RequestBody UpdateTPssDispatchDto dto){

        return RT.ok(dispatchService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "物料匹配管理删除数据")
    public RT<Boolean> deleteBatchId(@Valid @RequestBody List<Long> ids){
        return RT.ok(dispatchService.deleteBatchId(ids));

    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "物料匹配管理导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {
        dispatchService.importData(file);

        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "物料匹配管理导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssDispatchPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return dispatchService.exportData(dto,isTemplate);
    }

    @GetMapping(value = "/query-up-order")
    @Operation(summary = "查询上线的工单")
    public RT<PageOutput<TPssDispatchPageVo>> queryUpOrder(){

        return RT.ok(dispatchService.queryUpOrder());
    }

    @PostMapping("/order-up-or-down")
    @Operation(summary =  "工单上/下线")
    @AitLog(value = "工单上/下线")
    public RT<Boolean> orderUpOrDown(@Valid @RequestBody UpdateTPssDispatchDto dto){

        return RT.ok(dispatchService.orderUpOrDown(dto));
    }

    @PostMapping("/place-order")
    @Operation(summary = "挂单")
    public RT<Boolean> placeOrder(@RequestBody List<AddTPssMatResDto> dtoList) {

        return RT.ok(dispatchService.placeOrder(dtoList));
    }
}