package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssTechkRoutLgInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechkRoutLgInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechkRoutLgInfoDto;
import com.aitos.pss.service.qualitymanger.ITechkRoutLgInfoService;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 炼钢工艺路径参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/techkroutlginfo")
@Tag(name = "/pss"  + "/techkroutlginfo",description = "炼钢工艺路径参数质量设计结果代码")
@AllArgsConstructor
public class TechkRoutLgInfoController {


    private final ITechkRoutLgInfoService techkRoutLgInfoService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssTechkRoutLgInfo列表(分页)")
    public RT<PageOutput<TPssTechkRoutLgInfoPageVo>> page(@Valid TPssTechkRoutLgInfoPageDto dto){

        return RT.ok(techkRoutLgInfoService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssTechkRoutLgInfo信息")
    public RT<TPssTechkRoutLgInfoVo> info(@RequestParam Long id){

        return RT.ok(techkRoutLgInfoService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssTechkRoutLgInfo")
    @AitLog(value = "炼钢工艺路径参数质量设计结果新增数据")
    public RT<TPssTechkRoutLgInfoVo> add(@Valid @RequestBody AddTPssTechkRoutLgInfoDto dto){

        return RT.ok(techkRoutLgInfoService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssTechkRoutLgInfo")
    @AitLog(value = "炼钢工艺路径参数质量设计结果修改数据")
    public RT<TPssTechkRoutLgInfoVo> update(@Valid @RequestBody UpdateTPssTechkRoutLgInfoDto dto){

        return RT.ok(techkRoutLgInfoService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "炼钢工艺路径参数质量设计结果删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(techkRoutLgInfoService.removeBatchByIds(ids));

    }

}