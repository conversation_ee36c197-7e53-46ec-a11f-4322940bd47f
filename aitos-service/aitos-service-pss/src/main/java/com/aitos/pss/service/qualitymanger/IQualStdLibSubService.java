
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssQualStdLibSubDto;
import com.aitos.pss.dto.qualitymanger.TPssQualStdLibSubPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualStdLibSubDto;
import com.aitos.pss.entity.qualitymanger.TPssQualStdLibSub;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibSubPageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibSubVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-09
* @Version 1.0
*/

public interface IQualStdLibSubService extends IService<TPssQualStdLibSub> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssQualStdLibSubPageVo> queryPage(@Valid TPssQualStdLibSubPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssQualStdLibSubVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssQualStdLibSubVo add(@Valid AddTPssQualStdLibSubDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssQualStdLibSubVo update(@Valid UpdateTPssQualStdLibSubDto dto);
}
