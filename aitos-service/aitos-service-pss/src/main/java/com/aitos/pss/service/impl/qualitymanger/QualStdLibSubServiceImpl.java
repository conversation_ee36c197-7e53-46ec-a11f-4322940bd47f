package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssQualStdLibSubDto;
import com.aitos.pss.dto.qualitymanger.TPssQualStdLibSubPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualStdLibSubDto;
import com.aitos.pss.entity.qualitymanger.TPssQualStdLibSub;
import com.aitos.pss.mapper.qualitymanger.TPssQualStdLibSubMapper;
import com.aitos.pss.service.qualitymanger.IQualStdLibSubService;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibSubPageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibSubVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-09
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class QualStdLibSubServiceImpl extends ServiceImpl<TPssQualStdLibSubMapper, TPssQualStdLibSub> implements IQualStdLibSubService {

    @Override
    public PageOutput<TPssQualStdLibSubPageVo> queryPage(TPssQualStdLibSubPageDto dto) {
        LambdaQueryWrapper<TPssQualStdLibSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StrUtil.isNotBlank(dto.getCStdcode()),TPssQualStdLibSub::getCStdcode,dto.getCStdcode())
                .like(Objects.nonNull(dto.getCQualityStandardLibItemCode()),TPssQualStdLibSub::getCQualityStandardLibItemCode,dto.getCQualityStandardLibItemCode())
                .like(StrUtil.isNotBlank(dto.getCQualityStandardLibItemName()),TPssQualStdLibSub::getCQualityStandardLibItemName,dto.getCQualityStandardLibItemName())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssQualStdLibSub::getNEnabledMark,dto.getNEnabledMark())
                .between(
                        ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),
                        TPssQualStdLibSub::getDtCreateDateTime,
                        dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .orderByDesc(TPssQualStdLibSub::getDtCreateDateTime)
                .select(TPssQualStdLibSub.class,x -> VoToColumnUtil.fieldsToColumns(TPssQualStdLibSubPageVo.class).contains(x.getProperty()));
        IPage<TPssQualStdLibSub> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssQualStdLibSubPageVo.class);
    }

    @Override
    public TPssQualStdLibSubVo queryInfo(Long id) {
        TPssQualStdLibSub tPssQualStdLibSub = this.baseMapper.selectById(id);
        if (tPssQualStdLibSub == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssQualStdLibSub, TPssQualStdLibSubVo.class);
    }

    @Override
    public TPssQualStdLibSubVo add(AddTPssQualStdLibSubDto dto) {
        TPssQualStdLibSub tPssQualStdLibSub = BeanUtil.toBean(dto, TPssQualStdLibSub.class);
        this.baseMapper.insert(tPssQualStdLibSub);

        return BeanUtil.copyProperties(tPssQualStdLibSub, TPssQualStdLibSubVo.class);
    }

    @Override
    public TPssQualStdLibSubVo update(UpdateTPssQualStdLibSubDto dto) {
        TPssQualStdLibSub tPssQualStdLibSub = BeanUtil.toBean(dto, TPssQualStdLibSub.class);
        this.baseMapper.updateById(tPssQualStdLibSub);

        return BeanUtil.copyProperties(tPssQualStdLibSub, TPssQualStdLibSubVo.class);
    }
}
