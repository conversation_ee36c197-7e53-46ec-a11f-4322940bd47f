package com.aitos.pss.controller.planmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssCheatPlanDto;
import com.aitos.pss.dto.planmanger.TPssCheatPlanPageDto;
import com.aitos.pss.dto.planmanger.UpdateCheatPlanStationAdjustDto;
import com.aitos.pss.dto.planmanger.UpdateTPssCheatPlanBathDto;
import com.aitos.pss.service.planmanger.ITPssCheatPlanService;
import com.aitos.pss.vo.planmanger.TPssCheatPlanPageVo;
import com.aitos.pss.vo.planmanger.TPssCheatPlanVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 炼钢计划任务调度
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssheatidplanmanger")
@Tag(name = "/pss"  + "/pssheatidplanmanger",description = "炼钢计划任务调度代码")
@AllArgsConstructor
public class PssheatidplanmangerController {


    private final ITPssCheatPlanService pssheatidplanmangerService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssCheatPlan列表(分页)")
    public RT<PageOutput<TPssCheatPlanPageVo>> queryPage(@Valid TPssCheatPlanPageDto dto){

        return RT.ok(pssheatidplanmangerService.queryPage(dto));
    }

    @GetMapping(value = "/list")
    @Operation(summary = "TPssCheatPlan列表(分页)")
    public RT<List<TPssCheatPlanPageVo>> queryList(@Valid TPssCheatPlanPageDto dto){

        return RT.ok(pssheatidplanmangerService.queryList(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssCheatPlan信息")
    public RT<TPssCheatPlanVo> queryInfo(@RequestParam Long id){

        return RT.ok(pssheatidplanmangerService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssCheatPlan")
    @AitLog(value = "炼钢计划任务调度新增数据")
    public RT<Boolean> add(@Valid @RequestBody List<AddTPssCheatPlanDto> dtoList){

        return RT.ok(pssheatidplanmangerService.add(dtoList));
    }

    @PutMapping
    @Operation(summary = "修改TPssCheatPlan")
    @AitLog(value = "炼钢计划任务调度修改数据")
    public RT<Boolean> update(@Valid @RequestBody UpdateTPssCheatPlanBathDto dto){

        return RT.ok(pssheatidplanmangerService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "炼钢计划任务调度删除数据")
    public RT<Boolean> deleteBathId(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssheatidplanmangerService.deleteBathId(ids));

    }

    @PostMapping("/station-adjust")
    @Operation(summary = "工位调整")
    @AitLog(value = "工位调整")
    public RT<Boolean> stationAdjust(@Valid @RequestBody UpdateCheatPlanStationAdjustDto dto){

        return RT.ok(pssheatidplanmangerService.stationAdjust(dto));
    }

    @PostMapping("/plan-operation")
    @Operation(summary = "计划操作 1 计划下达   0 下达取消")
    @AitLog(value = "计划操作")
    public RT<Boolean> planOperation(@Valid @RequestBody UpdateTPssCheatPlanBathDto dto){

        return RT.ok(pssheatidplanmangerService.planOperation(dto));
    }

}