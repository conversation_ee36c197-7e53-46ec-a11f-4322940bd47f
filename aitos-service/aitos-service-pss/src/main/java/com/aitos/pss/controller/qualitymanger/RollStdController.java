package com.aitos.pss.controller.qualitymanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssRollStdDto;
import com.aitos.pss.dto.qualitymanger.TPssRollStdPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssRollStdDto;
import com.aitos.pss.entity.qualitymanger.TPssRollStd;
import com.aitos.pss.service.qualitymanger.IRollStdService;
import com.aitos.pss.vo.qualitymanger.TPssRollStdPageVo;
import com.aitos.pss.vo.qualitymanger.TPssRollStdVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 轧钢工艺参数管理-轧制工艺参数
* <AUTHOR>
* @Date: 2025-05-21
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/rollstd")
@Tag(name = "/pss"  + "/rollstd",description = "轧钢工艺参数管理-轧制工艺参数代码")
@AllArgsConstructor
public class RollStdController {


    private final IRollStdService rollStdService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssRollStd列表(分页)")
    public RT<PageOutput<TPssRollStdPageVo>> page(@Valid TPssRollStdPageDto dto){

        return RT.ok(rollStdService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssRollStd信息")
    public RT<TPssRollStdVo> info(@RequestParam Long id){

        return RT.ok(rollStdService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssRollStd")
    @AitLog(value = "轧钢工艺参数管理-轧制工艺参数新增数据")
    public RT<TPssRollStdVo> add(@Valid @RequestBody AddTPssRollStdDto dto){

        return RT.ok(rollStdService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssRollStd")
    @SaCheckPermission(value = "rollstd:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "轧钢工艺参数管理-轧制工艺参数修改数据")
    public RT<TPssRollStdVo> update(@Valid @RequestBody UpdateTPssRollStdDto dto){

        return RT.ok(rollStdService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "轧钢工艺参数管理-轧制工艺参数删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(rollStdService.removeBatchByIds(ids));
    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "轧钢工艺参数管理-轧制工艺参数导入数据")
    public RT<Boolean> importData(@RequestParam MultipartFile file) throws IOException {

        rollStdService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "轧钢工艺参数管理-轧制工艺参数导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssRollStdPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return rollStdService.exportData(dto,isTemplate);
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssRollStdDto dto){
        TPssRollStd tPssRollStd = BeanUtil.toBean(dto, TPssRollStd.class);

        return RT.ok(rollStdService.updateById(tPssRollStd));
    }
}