package com.aitos.pss.service.impl.prodmonit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.prodmonit.AddTPssViolationsDto;
import com.aitos.pss.dto.prodmonit.TPssViolationsPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssViolationsDto;
import com.aitos.pss.entity.prodmonit.TPssViolations;
import com.aitos.pss.mapper.prodmonit.TPssViolationsMapper;
import com.aitos.pss.service.prodmonit.IViolationManageService;
import com.aitos.pss.vo.prodmonit.TPssViolationsPageVo;
import com.aitos.pss.vo.prodmonit.TPssViolationsVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class ViolationManageServiceImpl extends ServiceImpl<TPssViolationsMapper, TPssViolations> implements IViolationManageService {

    @Override
    public PageOutput<TPssViolationsPageVo> queryPage(TPssViolationsPageDto dto) {
        LambdaQueryWrapper<TPssViolations> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCCode()),TPssViolations::getCCode,dto.getCCode())
                .eq(Objects.nonNull(dto.getNEquipmentId()),TPssViolations::getNEquipmentId,dto.getNEquipmentId())
                .eq(ObjectUtil.isNotNull(dto.getNIndicatorId()),TPssViolations::getNIndicatorId,dto.getNIndicatorId())
                .eq(StrUtil.isNotBlank(dto.getCStatus()),TPssViolations::getCStatus,dto.getCStatus())
                .between(ObjectUtil.isNotNull(dto.getDtViolationDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtViolationDateTimeEnd()),TPssViolations::getDtViolationDateTime,dto.getDtViolationDateTimeStart(),dto.getDtViolationDateTimeEnd())
                .orderByDesc(TPssViolations::getNId)
                .select(TPssViolations.class,x -> VoToColumnUtil.fieldsToColumns(TPssViolationsPageVo.class).contains(x.getProperty()));
        IPage<TPssViolations> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssViolationsPageVo.class);
    }

    @Override
    public TPssViolationsVo queryInfo(Long id) {
        TPssViolations tPssViolations = this.baseMapper.selectById(id);
        if (tPssViolations == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssViolations, TPssViolationsVo.class);
    }

    @Override
    public TPssViolationsVo add(AddTPssViolationsDto dto) {
        TPssViolations tPssViolations = BeanUtil.toBean(dto, TPssViolations.class);
        this.baseMapper.insert(tPssViolations);

        return BeanUtil.copyProperties(tPssViolations, TPssViolationsVo.class);
    }

    @Override
    public TPssViolationsVo update(UpdateTPssViolationsDto dto) {
        TPssViolations tPssViolations = BeanUtil.toBean(dto, TPssViolations.class);
        this.baseMapper.updateById(tPssViolations);

        return BeanUtil.copyProperties(tPssViolations, TPssViolationsVo.class);
    }

    @Override
    public Void importData(MultipartFile file) throws IOException {
        List<TPssViolationsPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssViolationsPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssViolations.class));

        return null;
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssViolationsPageDto dto, Boolean isTemplate) {
        List<TPssViolationsPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssViolationsPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "ViolationManage" + ExcelTypeEnum.XLSX.getValue());
    }
}
