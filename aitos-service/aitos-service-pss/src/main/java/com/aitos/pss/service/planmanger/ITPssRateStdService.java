package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssRateStdDto;
import com.aitos.pss.dto.planmanger.TPssRateStdPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssRateStdDto;
import com.aitos.pss.entity.planmanger.TPssRateStd;
import com.aitos.pss.vo.planmanger.TPssRateStdPageVo;
import com.aitos.pss.vo.planmanger.TPssRateStdVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/

public interface ITPssRateStdService extends IService<TPssRateStd> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssRateStdPageVo> queryPage(@Valid TPssRateStdPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssRateStdVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssRateStdVo add(@Valid AddTPssRateStdDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssRateStdVo update(@Valid UpdateTPssRateStdDto dto);

    /**
     * 判断当前钢种收得率是否唯一
     * @param dto
     * @return
     */
    Boolean checkSingle(UpdateTPssRateStdDto dto);
}
