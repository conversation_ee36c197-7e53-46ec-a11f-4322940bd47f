package com.aitos.pss.controller.qualitymanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssMatResDto;
import com.aitos.pss.dto.planmanger.UpdateTPssMatResDto;
import com.aitos.pss.dto.qualitymanger.TPssMatResPageDto;
import com.aitos.pss.service.qualitymanger.IMatResService;
import com.aitos.pss.vo.planmanger.TPssMatResVo;
import com.aitos.pss.vo.qualitymanger.TPssMatResPageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 轧钢表尺判定管理
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/matres")
@Tag(name = "/pss"  + "/matres",description = "轧钢表尺判定管理代码")
@AllArgsConstructor
public class MatResController {


    private final IMatResService matResService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssMatRes列表(分页)")
    public RT<PageOutput<TPssMatResPageVo>> page(@Valid TPssMatResPageDto dto){

        return RT.ok(matResService.queryPage(dto));
    }

    @GetMapping(value = "/main-mate-page")
    @Operation(summary = "TPssMatRes列表(分页)")
    public RT<PageOutput<TPssMatResPageVo>> mainMatePage(@Valid TPssMatResPageDto dto){

        return RT.ok(matResService.mainMatePage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssMatRes信息")
    public RT<TPssMatResVo> info(@RequestParam Long id){

        return RT.ok(matResService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssMatRes")
    @AitLog(value = "轧钢表尺判定管理新增数据")
    public RT<TPssMatResVo> add(@Valid @RequestBody AddTPssMatResDto dto){

        return RT.ok(matResService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssMatRes")
    @SaCheckPermission(value = "matres:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "轧钢表尺判定管理修改数据")
    public RT<Boolean> update(@Valid @RequestBody UpdateTPssMatResDto dto){

        return RT.ok(matResService.editJudge(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "matres:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "轧钢表尺判定管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(matResService.delete(ids));

    }

    @PostMapping("/sample-confirm")
    @Operation(summary = "取样确认")
    @AitLog(value = "取样确认")
    public RT<TPssMatResVo> sampleConfirm(@Valid @RequestBody UpdateTPssMatResDto dto){
        return RT.ok(matResService.sampleConfirm(dto));

    }

    @PostMapping("/judge-abolish")
    @Operation(summary = "判废")
    @AitLog(value = "判废")
    public RT<Boolean> judgeAbolish(@Valid @RequestBody List<UpdateTPssMatResDto> dtoList){
        return RT.ok(matResService.judgeAbolish(dtoList));

    }

    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "轧钢表尺判定管理导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        matResService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "轧钢表尺判定管理导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssMatResPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return matResService.exportData(dto,isTemplate);
    }
}