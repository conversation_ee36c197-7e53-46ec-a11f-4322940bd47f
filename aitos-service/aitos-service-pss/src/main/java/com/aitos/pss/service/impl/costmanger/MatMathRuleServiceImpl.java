package com.aitos.pss.service.impl.costmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.costmanger.AddTPssMatMathRuleDto;
import com.aitos.pss.dto.costmanger.TPssMatMathRulePageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssMatMathRuleDto;
import com.aitos.pss.entity.costmanger.TPssMatMathRule;
import com.aitos.pss.mapper.costmanger.TPssMatMathRuleMapper;
import com.aitos.pss.service.costmanger.IMatMathRuleService;
import com.aitos.pss.vo.costmanger.TPssMatMathRulePageVo;
import com.aitos.pss.vo.costmanger.TPssMatMathRuleVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class MatMathRuleServiceImpl extends ServiceImpl<TPssMatMathRuleMapper, TPssMatMathRule> implements IMatMathRuleService {

    @Override
    public PageOutput<TPssMatMathRulePageVo> queryPage(TPssMatMathRulePageDto dto) {
        LambdaQueryWrapper<TPssMatMathRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getCBofId()),TPssMatMathRule::getCBofId,dto.getCBofId())
                .eq(ObjectUtil.isNotNull(dto.getCProcId()),TPssMatMathRule::getCProcId,dto.getCProcId())
                .eq(ObjectUtil.isNotNull(dto.getCProLine()),TPssMatMathRule::getCProLine,dto.getCProLine())
                .orderByDesc(TPssMatMathRule::getNCreateUserId)
                .select(TPssMatMathRule.class,x -> VoToColumnUtil.fieldsToColumns(TPssMatMathRulePageVo.class).contains(x.getProperty()));
        IPage<TPssMatMathRule> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssMatMathRulePageVo.class);
    }

    @Override
    public TPssMatMathRuleVo queryInfo(Long id) {
        TPssMatMathRule tPssMatMathRule = this.baseMapper.selectById(id);
        if (tPssMatMathRule == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssMatMathRule, TPssMatMathRuleVo.class);
    }

    @Override
    public TPssMatMathRuleVo add(AddTPssMatMathRuleDto dto) {
        TPssMatMathRule tPssMatMathRule = BeanUtil.toBean(dto, TPssMatMathRule.class);
        this.baseMapper.insert(tPssMatMathRule);

        return BeanUtil.copyProperties(tPssMatMathRule, TPssMatMathRuleVo.class);
    }

    @Override
    public TPssMatMathRuleVo update(UpdateTPssMatMathRuleDto dto) {
        TPssMatMathRule tPssMatMathRule = BeanUtil.toBean(dto, TPssMatMathRule.class);
        this.baseMapper.updateById(tPssMatMathRule);
        return BeanUtil.copyProperties(tPssMatMathRule, TPssMatMathRuleVo.class);
    }
}
