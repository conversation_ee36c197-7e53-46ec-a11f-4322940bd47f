package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssTechLfInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechLfInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechLfInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssTechLfInfo;
import com.aitos.pss.mapper.qualitymanger.TPssTechLfInfoMapper;
import com.aitos.pss.service.qualitymanger.ITechIfInfoService;
import com.aitos.pss.vo.qualitymanger.TPssTechLfInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechLfInfoVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TechIfInfoServiceImpl extends ServiceImpl<TPssTechLfInfoMapper, TPssTechLfInfo> implements ITechIfInfoService {

    @Override
    public PageOutput<TPssTechLfInfoPageVo> queryPage(TPssTechLfInfoPageDto dto) {
        LambdaQueryWrapper<TPssTechLfInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualId()),TPssTechLfInfo::getCQualId,dto.getCQualId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssTechLfInfo::getCStlGrdCd,dto.getCStlGrdCd())
                .between(
                        ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),
                        TPssTechLfInfo::getDtCreateDateTime,
                        dto.getDtCreateDateTimeStart(),
                        dto.getDtCreateDateTimeEnd())
                .orderByDesc(TPssTechLfInfo::getNId)
                .select(TPssTechLfInfo.class,x -> VoToColumnUtil.fieldsToColumns(TPssTechLfInfoPageVo.class).contains(x.getProperty()));
        IPage<TPssTechLfInfo> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssTechLfInfoPageVo.class);
    }

    @Override
    public TPssTechLfInfoVo queryInfo(Long id) {
        TPssTechLfInfo tPssTechLfInfo = this.baseMapper.selectById(id);
        if (tPssTechLfInfo == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssTechLfInfo, TPssTechLfInfoVo.class);
    }

    @Override
    public TPssTechLfInfoVo add(AddTPssTechLfInfoDto dto) {
        TPssTechLfInfo tPssTechLfInfo = BeanUtil.toBean(dto, TPssTechLfInfo.class);
        this.baseMapper.insert(tPssTechLfInfo);

        return BeanUtil.copyProperties(tPssTechLfInfo, TPssTechLfInfoVo.class);
    }

    @Override
    public TPssTechLfInfoVo update(UpdateTPssTechLfInfoDto dto) {
        TPssTechLfInfo tPssTechLfInfo = BeanUtil.toBean(dto, TPssTechLfInfo.class);
        this.baseMapper.updateById(tPssTechLfInfo);

        return BeanUtil.copyProperties(tPssTechLfInfo, TPssTechLfInfoVo.class);
    }
}
