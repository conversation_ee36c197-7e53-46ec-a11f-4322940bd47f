package com.aitos.pss.enums;

/**
 * 产销指标数据状态枚举
 */
public enum ProductionSalesIndicatorStatus {
    NORMAL("1", "正常"),
    WARNING("2", "预警"),
    VIOLATION("3", "违规");

    private final String code;
    private final String description;

    ProductionSalesIndicatorStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ProductionSalesIndicatorStatus fromCode(String code) {
        for (ProductionSalesIndicatorStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 严格模式根据code获取枚举
     * @param code 状态码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果code不存在
     */
    public static ProductionSalesIndicatorStatus fromCodeStrict(String code) {
        ProductionSalesIndicatorStatus status = fromCode(code);
        if (status == null) {
            throw new IllegalArgumentException("无效的产销指标状态码: " + code);
        }
        return status;
    }
}