
package com.aitos.pss.service.costmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.costmanger.AddTPssFcsRawmCostDto;
import com.aitos.pss.dto.costmanger.TPssFcsRawmCostPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsRawmCostDto;
import com.aitos.pss.entity.costmanger.TPssFcsRawmCost;
import com.aitos.pss.vo.costmanger.TPssFcsRawmCostPageVo;
import com.aitos.pss.vo.costmanger.TPssFcsRawmCostVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/

public interface IFcsRawmCostService extends IService<TPssFcsRawmCost> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssFcsRawmCostPageVo> queryPage(@Valid TPssFcsRawmCostPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssFcsRawmCostVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssFcsRawmCostVo add(@Valid AddTPssFcsRawmCostDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssFcsRawmCostVo update(@Valid UpdateTPssFcsRawmCostDto dto);

    /**
     * approve
     * @param dtoList
     * @return
     */
    Boolean approve(@Valid List<UpdateTPssFcsRawmCostDto> dtoList);

    /**
     * 取消审核
     * @param dtoList
     * @return
     */
    Boolean approveCancel(@Valid List<UpdateTPssFcsRawmCostDto> dtoList);
}
