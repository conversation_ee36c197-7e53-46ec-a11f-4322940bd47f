package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.TPssMatLocmapLgPageDto;
import com.aitos.pss.entity.inventory.TPssMatLocmapLg;
import com.aitos.pss.mapper.inventory.TPssMatLocmapLgMapper;
import com.aitos.pss.service.inventory.IMatLocmapLgService;
import com.aitos.pss.vo.inventory.TPssMatLocmapLgPageVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-16
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class MatLocmapLgServiceImpl extends ServiceImpl<TPssMatLocmapLgMapper, TPssMatLocmapLg> implements IMatLocmapLgService {


    @Override
    public Map<String, List<TPssMatLocmapLgPageVo>> queryPage(TPssMatLocmapLgPageDto dto) {
                LambdaQueryWrapper<TPssMatLocmapLg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCLocLvl()),TPssMatLocmapLg::getCLocLvl,dto.getCLocLvl())
                .like(StrUtil.isNotBlank(dto.getCLineCode()),TPssMatLocmapLg::getCLineCode,dto.getCLineCode())
                .eq(ObjectUtil.isNotNull(dto.getCYardId()),TPssMatLocmapLg::getCYardId,dto.getCYardId())
                .like(StrUtil.isNotBlank(dto.getCYardName()),TPssMatLocmapLg::getCYardName,dto.getCYardName())
                .eq(ObjectUtil.isNotNull(dto.getNMatCnt()),TPssMatLocmapLg::getNMatCnt,dto.getNMatCnt())
                .like(StrUtil.isNotBlank(dto.getCMatLotId()),TPssMatLocmapLg::getCMatLotId,dto.getCMatLotId())
                .eq(ObjectUtil.isNotNull(dto.getCMateId()),TPssMatLocmapLg::getCMateId,dto.getCMateId())
                .like(StrUtil.isNotBlank(dto.getCMateName()),TPssMatLocmapLg::getCMateName,dto.getCMateName())
                .like(StrUtil.isNotBlank(dto.getCStlGrdDesc()),TPssMatLocmapLg::getCStlGrdDesc,dto.getCStlGrdDesc())
                .like(StrUtil.isNotBlank(dto.getCMatQulCode()),TPssMatLocmapLg::getCMatQulCode,dto.getCMatQulCode())
                .like(StrUtil.isNotBlank(dto.getCMatItem()),TPssMatLocmapLg::getCMatItem,dto.getCMatItem())
                .eq(ObjectUtil.isNotNull(dto.getNMatWth()),TPssMatLocmapLg::getNMatWth,dto.getNMatWth())
                .eq(ObjectUtil.isNotNull(dto.getNMatLen()),TPssMatLocmapLg::getNMatLen,dto.getNMatLen())
                .eq(ObjectUtil.isNotNull(dto.getNMatWgtCal()),TPssMatLocmapLg::getNMatWgtCal,dto.getNMatWgtCal())
                .like(StrUtil.isNotBlank(dto.getCTaskListId()),TPssMatLocmapLg::getCTaskListId,dto.getCTaskListId())
                .like(StrUtil.isNotBlank(dto.getCSaleNo()),TPssMatLocmapLg::getCSaleNo,dto.getCSaleNo())
                .like(StrUtil.isNotBlank(dto.getCOrderIdFinal()),TPssMatLocmapLg::getCOrderIdFinal,dto.getCOrderIdFinal())
                .like(StrUtil.isNotBlank(dto.getCPreOut()),TPssMatLocmapLg::getCPreOut,dto.getCPreOut())
                .like(StrUtil.isNotBlank(dto.getCProdGrd()),TPssMatLocmapLg::getCProdGrd,dto.getCProdGrd())
                .like(StrUtil.isNotBlank(dto.getCCarNo()),TPssMatLocmapLg::getCCarNo,dto.getCCarNo())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssMatLocmapLg::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssMatLocmapLg::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssMatLocmapLg::getNEnabledMark,dto.getNEnabledMark())
                .like(StrUtil.isNotBlank(dto.getCLoc()),TPssMatLocmapLg::getCLoc,dto.getCLoc())
                .eq(ObjectUtil.isNotNull(dto.getCLineId()),TPssMatLocmapLg::getCLineId,dto.getCLineId())
                .like(StrUtil.isNotBlank(dto.getCLineName()),TPssMatLocmapLg::getCLineName,dto.getCLineName())
                .like(StrUtil.isNotBlank(dto.getCYardCode()),TPssMatLocmapLg::getCYardCode,dto.getCYardCode())
                .like(StrUtil.isNotBlank(dto.getCMatId()),TPssMatLocmapLg::getCMatId,dto.getCMatId())
                .like(StrUtil.isNotBlank(dto.getCLotId()),TPssMatLocmapLg::getCLotId,dto.getCLotId())
                .like(StrUtil.isNotBlank(dto.getCMatType()),TPssMatLocmapLg::getCMatType,dto.getCMatType())
                .like(StrUtil.isNotBlank(dto.getCMateCode()),TPssMatLocmapLg::getCMateCode,dto.getCMateCode())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssMatLocmapLg::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(ObjectUtil.isNotNull(dto.getCMatQulId()),TPssMatLocmapLg::getCMatQulId,dto.getCMatQulId())
                .like(StrUtil.isNotBlank(dto.getCMatQulName()),TPssMatLocmapLg::getCMatQulName,dto.getCMatQulName())
                .eq(ObjectUtil.isNotNull(dto.getNMatThk()),TPssMatLocmapLg::getNMatThk,dto.getNMatThk())
                .eq(ObjectUtil.isNotNull(dto.getNMatDia()),TPssMatLocmapLg::getNMatDia,dto.getNMatDia())
                .eq(ObjectUtil.isNotNull(dto.getNMatWgt()),TPssMatLocmapLg::getNMatWgt,dto.getNMatWgt())
                .like(StrUtil.isNotBlank(dto.getCOrdFl()),TPssMatLocmapLg::getCOrdFl,dto.getCOrdFl())
                .like(StrUtil.isNotBlank(dto.getCOrderNo()),TPssMatLocmapLg::getCOrderNo,dto.getCOrderNo())
                .like(StrUtil.isNotBlank(dto.getCSaleSn()),TPssMatLocmapLg::getCSaleSn,dto.getCSaleSn())
                .between(ObjectUtil.isNotNull(dto.getDtProdTimeStart()) && ObjectUtil.isNotNull(dto.getDtProdTimeEnd()),TPssMatLocmapLg::getDtProdTime,dto.getDtProdTimeStart(),dto.getDtProdTimeEnd())
                .like(StrUtil.isNotBlank(dto.getCStatus()),TPssMatLocmapLg::getCStatus,dto.getCStatus())
                .like(StrUtil.isNotBlank(dto.getCCheckerFl()),TPssMatLocmapLg::getCCheckerFl,dto.getCCheckerFl())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssMatLocmapLg::getNCreateUserId,dto.getNCreateUserId())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssMatLocmapLg::getNModifyUserId,dto.getNModifyUserId())
                .eq(TPssMatLocmapLg::getNDeleteMark,0)
                    .orderByDesc(TPssMatLocmapLg::getNId)
                .select(TPssMatLocmapLg.class,x -> VoToColumnUtil.fieldsToColumns(TPssMatLocmapLgPageVo.class).contains(x.getProperty()));
        List list = this.list(queryWrapper);
        List<TPssMatLocmapLgPageVo> tPssMatLocmapLgPageVoList = BeanUtil.copyToList(list, TPssMatLocmapLgPageVo.class);
        Map<String, List<TPssMatLocmapLgPageVo>> result = tPssMatLocmapLgPageVoList.stream().collect(Collectors.groupingBy(TPssMatLocmapLgPageVo::getCLoc));
        return result;

    }
}
