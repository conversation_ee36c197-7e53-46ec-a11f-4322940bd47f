package com.aitos.pss.service.impl.costmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.costmanger.AddTPssFcsMatPriceDto;
import com.aitos.pss.dto.costmanger.TPssFcsMatPricePageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsMatPriceDto;
import com.aitos.pss.entity.costmanger.TPssFcsMatPrice;
import com.aitos.pss.mapper.costmanger.TPssFcsMatPriceMapper;
import com.aitos.pss.service.costmanger.IFcsMatpriceService;
import com.aitos.pss.vo.costmanger.TPssFcsMatPricePageVo;
import com.aitos.pss.vo.costmanger.TPssFcsMatPriceVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class FcsMatpriceServiceImpl extends ServiceImpl<TPssFcsMatPriceMapper, TPssFcsMatPrice> implements IFcsMatpriceService {

    @Override
    public PageOutput<TPssFcsMatPricePageVo> queryPage(TPssFcsMatPricePageDto dto) {
        LambdaQueryWrapper<TPssFcsMatPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCCostItemName()),TPssFcsMatPrice::getCCostItemName,dto.getCCostItemName())
                .like(StrUtil.isNotBlank(dto.getCMatCode()),TPssFcsMatPrice::getCMatCode,dto.getCMatCode())
                .eq(ObjectUtil.isNotNull(dto.getCUnitId()),TPssFcsMatPrice::getCUnitId,dto.getCUnitId())
                .like(StrUtil.isNotBlank(dto.getCUnitName()),TPssFcsMatPrice::getCUnitName,dto.getCUnitName())
                .eq(ObjectUtil.isNotNull(dto.getCActPrice()),TPssFcsMatPrice::getCActPrice,dto.getCActPrice())
                .eq(ObjectUtil.isNotNull(dto.getCState()),TPssFcsMatPrice::getCState,dto.getCState())
                .eq(Objects.nonNull(dto.getCProcId()),TPssFcsMatPrice::getCProcId,dto.getCProcId())
                .eq(Objects.nonNull(dto.getCProLine()),TPssFcsMatPrice::getCProLine,dto.getCProLine())
                .like(StrUtil.isNotBlank(dto.getCCostItemCode()),TPssFcsMatPrice::getCCostItemCode,dto.getCCostItemCode())
                .eq(ObjectUtil.isNotNull(dto.getCMatId()),TPssFcsMatPrice::getCMatId,dto.getCMatId())
                .like(StrUtil.isNotBlank(dto.getCMatName()),TPssFcsMatPrice::getCMatName,dto.getCMatName())
                .like(StrUtil.isNotBlank(dto.getCUnitCode()),TPssFcsMatPrice::getCUnitCode,dto.getCUnitCode())
                .eq(ObjectUtil.isNotNull(dto.getCPlanPrice()),TPssFcsMatPrice::getCPlanPrice,dto.getCPlanPrice())
                .eq(ObjectUtil.isNotNull(dto.getCRecovery()),TPssFcsMatPrice::getCRecovery,dto.getCRecovery())
                .like(StrUtil.isNotBlank(dto.getCMatTypeName()),TPssFcsMatPrice::getCMatTypeName,dto.getCMatTypeName())
                .like(StrUtil.isNotBlank(dto.getCMatTypeCd()),TPssFcsMatPrice::getCMatTypeCd,dto.getCMatTypeCd())
                .like(StrUtil.isNotBlank(dto.getCMemo()),TPssFcsMatPrice::getCMemo,dto.getCMemo())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssFcsMatPrice::getNCreateUserId,dto.getNCreateUserId())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssFcsMatPrice::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssFcsMatPrice::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssFcsMatPrice::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssFcsMatPrice::getNId)
                .select(TPssFcsMatPrice.class,x -> VoToColumnUtil.fieldsToColumns(TPssFcsMatPricePageVo.class).contains(x.getProperty()));
        IPage<TPssFcsMatPrice> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssFcsMatPricePageVo.class);
    }

    @Override
    public TPssFcsMatPriceVo queryInfo(Long id) {
        TPssFcsMatPrice tPssFcsMatPrice = this.baseMapper.selectById(id);
        if (tPssFcsMatPrice == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssFcsMatPrice, TPssFcsMatPriceVo.class);
    }

    @Override
    public TPssFcsMatPriceVo add(AddTPssFcsMatPriceDto dto) {
        TPssFcsMatPrice tPssFcsMatPrice = BeanUtil.toBean(dto, TPssFcsMatPrice.class);
        this.baseMapper.insert(tPssFcsMatPrice);

        return BeanUtil.copyProperties(tPssFcsMatPrice, TPssFcsMatPriceVo.class);
    }

    @Override
    public TPssFcsMatPriceVo update(UpdateTPssFcsMatPriceDto dto) {
        TPssFcsMatPrice tPssFcsMatPrice = BeanUtil.toBean(dto, TPssFcsMatPrice.class);
        this.baseMapper.updateById(tPssFcsMatPrice);

        return BeanUtil.copyProperties(tPssFcsMatPrice, TPssFcsMatPriceVo.class);
    }
}
