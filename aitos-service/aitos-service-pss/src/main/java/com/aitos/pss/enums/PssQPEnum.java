package com.aitos.pss.enums;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import java.util.Objects;

/**
 * <AUTHOR>
 *
 * 性能等级
 *
 * @version 1.0.0
 * @date 2025/6/10 14:51
 */
@Getter
@Log4j2
public enum PssQPEnum {

    /** 合格品 */
    QUALIFIED("1", "合格品"),

    /** 不合格 */
    UNQUALIFIED("2", "不合格"),
    ;

    private final String code;
    private final String description;

    PssQPEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code安全获取枚举（找不到时记录warn日志并返回null）
     */
    public static PssQPEnum getByCode(String code) {

        for (PssQPEnum result : values()) {
            if (Objects.equals(result.code, code)) {
                return result;
            }
        }

        log.warn("无效的判定编码: {}", code);
        return null;
    }

    /**
     * 根据description安全获取枚举（找不到时记录warn日志并返回null）
     */
    public static PssQPEnum getByDescription(String description) {

        for (PssQPEnum result : values()) {
            if (result.description.equals(description)) {
                return result;
            }
        }

        log.warn("无效的判定描述: {}", description);
        return null;
    }

}
