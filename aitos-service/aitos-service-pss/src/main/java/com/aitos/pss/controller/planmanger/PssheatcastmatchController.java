package com.aitos.pss.controller.planmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssTaskListDto;
import com.aitos.pss.dto.planmanger.TPssTaskListPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssTaskListDto;
import com.aitos.pss.dto.planmanger.UpdateTPssTaskListStoveOrderDto;
import com.aitos.pss.service.planmanger.ITPssTaskListService;
import com.aitos.pss.vo.planmanger.TPssTaskListPageVo;
import com.aitos.pss.vo.planmanger.TPssTaskListVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 炉次浇次设计管理
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssheatcastmatch")
@Tag(name = "/pss"  + "/pssheatcastmatch",description = "炉次浇次设计管理代码")
@AllArgsConstructor
public class PssheatcastmatchController {


    private final ITPssTaskListService pssheatcastmatchService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssTaskList列表(分页)")
    public RT<PageOutput<TPssTaskListPageVo>> queryPage(@Valid TPssTaskListPageDto dto){

        return RT.ok(pssheatcastmatchService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssTaskList信息")
    public RT<TPssTaskListVo> queryInfo(@RequestParam Long id){

        return RT.ok(pssheatcastmatchService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssTaskList")
    @AitLog(value = "炉次浇次设计管理新增数据")
    public RT<TPssTaskListVo> add(@Valid @RequestBody AddTPssTaskListDto dto){

        return RT.ok(pssheatcastmatchService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssTaskList")
    @AitLog(value = "炉次浇次设计管理修改数据")
    public RT<TPssTaskListVo> update(@Valid @RequestBody UpdateTPssTaskListDto dto){

        return RT.ok(pssheatcastmatchService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "炉次浇次设计管理删除数据")
    public RT<Boolean> deleteBathById(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssheatcastmatchService.deleteBathById(ids));

    }

    @PostMapping("/stove-order")
    @Operation(summary = "炉次设计")
    @AitLog(value = "炉次设计")
    public RT<Boolean> stoveOrder(@Valid @RequestBody UpdateTPssTaskListStoveOrderDto dto){

        return RT.ok(pssheatcastmatchService.stoveOrder(dto));
    }

    @PostMapping("/pour-order")
    @Operation(summary = "浇次设计")
    @AitLog(value = "浇次设计")
    public RT<Boolean> pourOrder(@Valid @RequestBody List<UpdateTPssTaskListDto> dtoList){

        return RT.ok(pssheatcastmatchService.pourOrder(dtoList));
    }

}