
package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssCheatPlanDto;
import com.aitos.pss.dto.planmanger.TPssCheatPlanPageDto;
import com.aitos.pss.dto.planmanger.UpdateCheatPlanStationAdjustDto;
import com.aitos.pss.dto.planmanger.UpdateTPssCheatPlanBathDto;
import com.aitos.pss.entity.planmanger.TPssCheatPlan;
import com.aitos.pss.vo.planmanger.TPssCheatPlanPageVo;
import com.aitos.pss.vo.planmanger.TPssCheatPlanVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/

public interface ITPssCheatPlanService extends IService<TPssCheatPlan> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssCheatPlanPageVo> queryPage(@Valid TPssCheatPlanPageDto dto);

    /**
     * query list
     * @param dto
     * @return
     */
    List<TPssCheatPlanPageVo> queryList(@Valid TPssCheatPlanPageDto dto);

    /**
     * 详情
     * @param id
     * @return
     */
    TPssCheatPlanVo queryInfo(Long id);

    /**
     * 新增数据
     * @param dtoList
     * @return
     */
    Boolean add(@Valid List<AddTPssCheatPlanDto> dtoList);

    /**
     * 更新数据
     * @param dto
     * @return
     */
    Boolean update(@Valid UpdateTPssCheatPlanBathDto dto);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    Boolean deleteBathId(@Valid List<Long> ids);

    /**
     * 工位调整
     * @param dto
     * @return
     */
    Boolean stationAdjust(@Valid UpdateCheatPlanStationAdjustDto dto);

    /**
     * 计划操作 1 计划下达   0 下达取消
     * @param dto
     * @return
     */
    Boolean planOperation(@Valid UpdateTPssCheatPlanBathDto dto);
}
