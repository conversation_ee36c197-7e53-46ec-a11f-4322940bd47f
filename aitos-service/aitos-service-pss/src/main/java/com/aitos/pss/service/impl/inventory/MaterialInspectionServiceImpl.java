package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.AddTPssMaterialInspectionDto;
import com.aitos.pss.dto.inventory.TPssMaterialInspectionPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMaterialInspectionDto;
import com.aitos.pss.entity.inventory.TPssMaterialInspection;
import com.aitos.pss.mapper.inventory.TPssMaterialInspectionMapper;
import com.aitos.pss.service.inventory.IMaterialInspectionService;
import com.aitos.pss.vo.inventory.TPssMaterialInspectionPageVo;
import com.aitos.pss.vo.inventory.TPssMaterialInspectionVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class MaterialInspectionServiceImpl extends ServiceImpl<TPssMaterialInspectionMapper, TPssMaterialInspection> implements IMaterialInspectionService {

    @Override
    public PageOutput<TPssMaterialInspectionPageVo> page(TPssMaterialInspectionPageDto dto) {
        LambdaQueryWrapper<TPssMaterialInspection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCItem()),TPssMaterialInspection::getCItem,dto.getCItem())
                .like(StrUtil.isNotBlank(dto.getCResult()),TPssMaterialInspection::getCResult,dto.getCResult())
                .like(StrUtil.isNotBlank(dto.getCInspector()),TPssMaterialInspection::getCInspector,dto.getCInspector())
                .eq(ObjectUtil.isNotNull(dto.getNReceiptId()),TPssMaterialInspection::getNReceiptId,dto.getNReceiptId())
                .like(StrUtil.isNotBlank(dto.getCStandard()),TPssMaterialInspection::getCStandard,dto.getCStandard())
                .eq(ObjectUtil.isNotNull(dto.getNIsQualified()),TPssMaterialInspection::getNIsQualified,dto.getNIsQualified())
                .between(ObjectUtil.isNotNull(dto.getDtInspectionTimeStart()) && ObjectUtil.isNotNull(dto.getDtInspectionTimeEnd()),TPssMaterialInspection::getDtInspectionTime,dto.getDtInspectionTimeStart(),dto.getDtInspectionTimeEnd())
                .like(StrUtil.isNotBlank(dto.getCRemark()),TPssMaterialInspection::getCRemark,dto.getCRemark())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssMaterialInspection::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssMaterialInspection::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssMaterialInspection::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssMaterialInspection::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssMaterialInspection::getDtCreateDateTime)
                .select(TPssMaterialInspection.class,x -> VoToColumnUtil.fieldsToColumns(TPssMaterialInspectionPageVo.class).contains(x.getProperty()));
        IPage<TPssMaterialInspection> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        return ConventPage.getPageOutput(page, TPssMaterialInspectionPageVo.class);
    }

    @Override
    public TPssMaterialInspectionVo info(Long id) {
        TPssMaterialInspection tPssMaterialInspection = this.baseMapper.selectById(id);
        if (tPssMaterialInspection == null) {
            throw new MyException("找不到此数据！");
        }
        return BeanUtil.toBean(tPssMaterialInspection, TPssMaterialInspectionVo.class);
    }

    @Override
    public Long add(AddTPssMaterialInspectionDto dto) {
        TPssMaterialInspection tPssMaterialInspection = BeanUtil.toBean(dto, TPssMaterialInspection.class);
        this.baseMapper.insert(tPssMaterialInspection);
        return tPssMaterialInspection.getNId();
    }

    @Override
    public Boolean update(UpdateTPssMaterialInspectionDto dto) {
        TPssMaterialInspection tPssMaterialInspection = BeanUtil.toBean(dto, TPssMaterialInspection.class);
        this.baseMapper.updateById(tPssMaterialInspection);
        return Boolean.TRUE;
    }
}
