package com.aitos.pss.utils;

import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 *环形链表
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/24 15:05
 */
public final class CircularListUtil {

    public static class Node<T> {
        T data;
        Node<T> next;

        Node(T data) {
            this.data = data;
            this.next = null;
        }

        public T getData() {
            return data;
        }

        public void setData(T data) {
            this.data = data;
        }

        public Node<T> getNext() {
            return next;
        }

        public void setNext(Node<T> next) {
            this.next = next;
        }
    }

    public static <T>Node<T> constructorCircularList(List<T> tList) {
        Node<T> head = null;

        if (CollectionUtils.isEmpty(tList)) return head;

        for (T t : tList) {
            Node<T> newNode = new Node<>(t);
            if (head == null) {
                head = newNode;
                head.next = head;
            } else {
                Node<T> current = head;
                while (current.next != head) {
                    current = current.next;
                }
                current.next = newNode;
                newNode.next = head;
            }
        }
        return head;
    }

}
