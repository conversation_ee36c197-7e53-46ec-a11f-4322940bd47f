package com.aitos.pss.controller.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssStlMingleDto;
import com.aitos.pss.dto.planmanger.TPssStlMinglePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssStlMingleDto;
import com.aitos.pss.entity.planmanger.TPssStlMingle;
import com.aitos.pss.service.planmanger.ITPssStlMingleService;
import com.aitos.pss.vo.planmanger.TPssStlMinglePageVo;
import com.aitos.pss.vo.planmanger.TPssStlMingleVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 浇次标准-混浇钢种标准
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/psscastcnvtsdis")
@Tag(name = "/pss"  + "/psscastcnvtsdis",description = "浇次标准-混浇钢种标准代码")
@AllArgsConstructor
public class TPssStlMingleController {


    private final ITPssStlMingleService pssCastCnvtsDisService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssStlMingle列表(分页)")
    public RT<PageOutput<TPssStlMinglePageVo>> page(@Valid TPssStlMinglePageDto dto){

        return RT.ok(pssCastCnvtsDisService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssStlMingle信息")
    public RT<TPssStlMingleVo> info(@RequestParam Long id){

        return RT.ok(pssCastCnvtsDisService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssStlMingle")
    @AitLog(value = "浇次标准-混浇钢种标准新增数据")
    public RT<TPssStlMingleVo> add(@Valid @RequestBody AddTPssStlMingleDto dto){

        return RT.ok(pssCastCnvtsDisService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssStlMingle")
    @AitLog(value = "浇次标准-混浇钢种标准修改数据")
    public RT<TPssStlMingleVo> update(@Valid @RequestBody UpdateTPssStlMingleDto dto){

        return RT.ok(pssCastCnvtsDisService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "浇次标准-混浇钢种标准删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(pssCastCnvtsDisService.removeBatchByIds(ids));
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssStlMingleDto dto){
        TPssStlMingle tPssStlMingle = BeanUtil.toBean(dto, TPssStlMingle.class);

        return RT.ok(pssCastCnvtsDisService.updateById(tPssStlMingle));
    }
}