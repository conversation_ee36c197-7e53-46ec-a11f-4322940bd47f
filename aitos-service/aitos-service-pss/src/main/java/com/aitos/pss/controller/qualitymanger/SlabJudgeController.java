package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabJudgeDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabJudgePageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabJudgeDto;
import com.aitos.pss.service.qualitymanger.ISlabJudgeService;
import com.aitos.pss.vo.qualitymanger.TPssSlabJudgePageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabJudgeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 炼钢综判管理
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/slabjudge")
@Tag(name = "/pss"  + "/slabjudge",description = "炼钢综判管理代码")
@AllArgsConstructor
public class SlabJudgeController {


    private final ISlabJudgeService slabJudgeService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssSlabJudge列表(分页)")
    public RT<PageOutput<TPssSlabJudgePageVo>> page(@Valid TPssSlabJudgePageDto dto){

        return RT.ok(slabJudgeService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssSlabJudge信息")
    public RT<TPssSlabJudgeVo> info(@RequestParam Long id){

        return RT.ok(slabJudgeService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssSlabJudge")
    @AitLog(value = "炼钢综判管理新增数据")
    public RT<TPssSlabJudgeVo> add(@Valid @RequestBody AddTPssSlabJudgeDto dto){

        return RT.ok(slabJudgeService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssSlabJudge")
    @AitLog(value = "炼钢综判管理修改数据")
    public RT<TPssSlabJudgeVo> update(@Valid @RequestBody UpdateTPssSlabJudgeDto dto){

        return RT.ok(slabJudgeService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "炼钢综判管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(slabJudgeService.removeBatchByIds(ids));

    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "炼钢综判管理导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        slabJudgeService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "炼钢综判管理导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssSlabJudgePageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return slabJudgeService.exportData(dto,isTemplate);
    }
}