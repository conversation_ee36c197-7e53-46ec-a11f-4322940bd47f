package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssCastCnvtsDto;
import com.aitos.pss.dto.planmanger.TPssCastCnvtsPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssCastCnvtsDto;
import com.aitos.pss.entity.planmanger.TPssCastCnvts;
import com.aitos.pss.vo.planmanger.TPssCastCnvtsPageVo;
import com.aitos.pss.vo.planmanger.TPssCastCnvtsVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/

public interface ITPssCastCnvtsService extends IService<TPssCastCnvts> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssCastCnvtsPageVo> queryPage(TPssCastCnvtsPageDto dto);

    /**
     * 详情查询
     * @param id
     * @return
     */
    TPssCastCnvtsVo queryInfo(Long id);

    /**
     * 新增数据
     * @param dto
     * @return
     */
    TPssCastCnvtsVo add(@Valid AddTPssCastCnvtsDto dto);

    /**
     * 更新数据
     * @param dto
     * @return
     */
    TPssCastCnvtsVo update(@Valid UpdateTPssCastCnvtsDto dto);
}
