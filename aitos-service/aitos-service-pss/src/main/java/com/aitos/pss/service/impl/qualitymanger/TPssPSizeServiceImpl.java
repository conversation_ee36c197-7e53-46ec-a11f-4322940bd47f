package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssPSizeDto;
import com.aitos.pss.dto.qualitymanger.TPssPSizePageDto;
import com.aitos.pss.entity.qualitymanger.TPssPSize;
import com.aitos.pss.mapper.qualitymanger.TPssPSizeMapper;
import com.aitos.pss.service.qualitymanger.ITPssPSizeService;
import com.aitos.pss.vo.qualitymanger.TPssPSizePageVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssPSizeServiceImpl extends ServiceImpl<TPssPSizeMapper, TPssPSize> implements ITPssPSizeService {

    @Override
    public PageOutput<TPssPSizePageVo> queryPage(TPssPSizePageDto dto) {
        LambdaQueryWrapper<TPssPSize> queryWrapper = Wrappers.<TPssPSize>lambdaQuery();
        queryWrapper
                .eq(StringUtils.isNotBlank(dto.getCPlateId()), TPssPSize::getCPlateId, dto.getCPlateId());
        IPage<TPssPSize> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssPSizePageVo.class);
    }

    @Override
    public Boolean sizeDecision(List<AddTPssPSizeDto> dtoList) {
        List<TPssPSize> pSizeList = BeanUtil.copyToList(dtoList, TPssPSize.class);
        this.baseMapper.insert(pSizeList);

        // TODO 补充逻辑

        return Boolean.TRUE;
    }
}