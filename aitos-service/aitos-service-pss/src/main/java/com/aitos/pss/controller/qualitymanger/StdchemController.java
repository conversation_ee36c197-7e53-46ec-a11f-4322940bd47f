package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssStdchemDto;
import com.aitos.pss.dto.qualitymanger.TPssStdchemPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdchemDto;
import com.aitos.pss.service.qualitymanger.IStdchemService;
import com.aitos.pss.vo.qualitymanger.TPssStdchemPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdchemVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 质量设计成分结果查看
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/stdchem")
@Tag(name = "/pss"  + "/stdchem",description = "质量设计成分结果查看代码")
@AllArgsConstructor
public class StdchemController {


    private final IStdchemService stdchemService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssStdchem列表(分页)")
    public RT<PageOutput<TPssStdchemPageVo>> page(@Valid TPssStdchemPageDto dto){

        return RT.ok(stdchemService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssStdchem信息")
    public RT<TPssStdchemVo> info(@RequestParam Long id){

        return RT.ok(stdchemService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssStdchem")
    @AitLog(value = "质量设计成分结果查看新增数据")
    public RT<TPssStdchemVo> add(@Valid @RequestBody AddTPssStdchemDto dto){

        return RT.ok(stdchemService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssStdchem")
    @AitLog(value = "质量设计成分结果查看修改数据")
    public RT<TPssStdchemVo> update(@Valid @RequestBody UpdateTPssStdchemDto dto){

        return RT.ok(stdchemService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "质量设计成分结果查看删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(stdchemService.removeBatchByIds(ids));
    }

}