package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPssSteelSaleReturnDto;
import com.aitos.pss.dto.inventory.TPssSteelSaleReturnPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssSteelSaleReturnDto;
import com.aitos.pss.service.inventory.ISteelSaleReturnService;
import com.aitos.pss.vo.inventory.TPssSteelSaleReturnPageVo;
import com.aitos.pss.vo.inventory.TPssSteelSaleReturnVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 退货管理
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/steelsalereturn")
@Tag(name = "/pss"  + "/steelsalereturn",description = "退货管理代码")
@AllArgsConstructor
public class SteelSaleReturnController {


    private final ISteelSaleReturnService steelSaleReturnService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssSteelSaleReturn列表(分页)")
    @SaCheckPermission(value = "steelsalereturn:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssSteelSaleReturnPageVo>> page(@Valid TPssSteelSaleReturnPageDto dto){

        return RT.ok(steelSaleReturnService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssSteelSaleReturn信息")
    @SaCheckPermission(value = "steelsalereturn:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssSteelSaleReturnVo> info(@RequestParam Long id){

        return RT.ok(steelSaleReturnService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssSteelSaleReturn")
    @SaCheckPermission(value = "steelsalereturn:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "退货管理新增数据")
    public RT<TPssSteelSaleReturnVo> add(@Valid @RequestBody AddTPssSteelSaleReturnDto dto){

        return RT.ok(steelSaleReturnService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssSteelSaleReturn")
    @SaCheckPermission(value = "steelsalereturn:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "退货管理修改数据")
    public RT<TPssSteelSaleReturnVo> update(@Valid @RequestBody UpdateTPssSteelSaleReturnDto dto){

        return RT.ok(steelSaleReturnService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "steelsalereturn:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "退货管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(steelSaleReturnService.removeBatchByIds(ids));
    }

}