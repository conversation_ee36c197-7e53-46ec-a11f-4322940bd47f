
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPssMatOutboundRecordDto;
import com.aitos.pss.dto.inventory.TPssMatOutboundRecordPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMatOutboundRecordDto;
import com.aitos.pss.entity.inventory.TPssMatOutboundRecord;
import com.aitos.pss.vo.inventory.TPssMatOutboundRecordPageVo;
import com.aitos.pss.vo.inventory.TPssMatOutboundRecordVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/

public interface IMatOutboundRecordService extends IService<TPssMatOutboundRecord> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssMatOutboundRecordPageVo> queryPage(@Valid TPssMatOutboundRecordPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssMatOutboundRecordVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssMatOutboundRecordVo add(@Valid AddTPssMatOutboundRecordDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssMatOutboundRecordVo update(@Valid UpdateTPssMatOutboundRecordDto dto);
}
