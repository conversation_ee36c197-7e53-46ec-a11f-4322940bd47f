package com.aitos.pss.service.impl.prodmonit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.prodmonit.AddTPssEquipmentsDto;
import com.aitos.pss.dto.prodmonit.TPssEquipmentsPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssEquipmentsDto;
import com.aitos.pss.entity.prodmonit.TPssEquipments;
import com.aitos.pss.mapper.prodmonit.TPssEquipmentsMapper;
import com.aitos.pss.service.prodmonit.IDeviceManageService;
import com.aitos.pss.service.prodmonit.IIndicatorDataService;
import com.aitos.pss.vo.prodmonit.TPssEquipmentsPageVo;
import com.aitos.pss.vo.prodmonit.TPssEquipmentsVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class DeviceManageServiceImpl extends ServiceImpl<TPssEquipmentsMapper, TPssEquipments> implements IDeviceManageService {

    private final IIndicatorDataService indicatorDataService;

    @Override
    public PageOutput<TPssEquipmentsPageVo> queryPage(TPssEquipmentsPageDto dto) {
        LambdaQueryWrapper<TPssEquipments> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCCode()),TPssEquipments::getCCode,dto.getCCode())
                .like(StrUtil.isNotBlank(dto.getCLocation()),TPssEquipments::getCLocation,dto.getCLocation())
                .like(StrUtil.isNotBlank(dto.getCName()),TPssEquipments::getCName,dto.getCName())
                .like(StrUtil.isNotBlank(dto.getCType()),TPssEquipments::getCType,dto.getCType())
                .like(StrUtil.isNotBlank(dto.getCStatus()),TPssEquipments::getCStatus,dto.getCStatus())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssEquipments::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                    .orderByDesc(TPssEquipments::getNId)
                .select(TPssEquipments.class,x -> VoToColumnUtil.fieldsToColumns(TPssEquipmentsPageVo.class).contains(x.getProperty()));
        IPage<TPssEquipments> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        PageOutput<TPssEquipmentsPageVo> pageOutput = ConventPage.getPageOutput(page, TPssEquipmentsPageVo.class);
        extendedAttr(pageOutput.getList());

        return ;
    }

    private void extendedAttr(List<TPssEquipmentsPageVo> voList) {
        if (CollectionUtils.isEmpty(voList)) return;

        List<String> equipmentIdList = voList.stream().map(TPssEquipmentsPageVo::getNId).collect(Collectors.toList());

    }

    @Override
    public List<TPssEquipmentsVo> queryList(TPssEquipmentsPageDto dto) {
        LambdaQueryWrapper<TPssEquipments> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCCode()),TPssEquipments::getCCode,dto.getCCode())
                .like(StrUtil.isNotBlank(dto.getCLocation()),TPssEquipments::getCLocation,dto.getCLocation())
                .like(StrUtil.isNotBlank(dto.getCName()),TPssEquipments::getCName,dto.getCName())
                .like(StrUtil.isNotBlank(dto.getCType()),TPssEquipments::getCType,dto.getCType())
                .like(StrUtil.isNotBlank(dto.getCStatus()),TPssEquipments::getCStatus,dto.getCStatus())
                .eq(ObjectUtil.isNotNull(dto.getNId()),TPssEquipments::getNId,dto.getNId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssEquipments::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .orderByDesc(TPssEquipments::getNId)
                .select(TPssEquipments.class,x -> VoToColumnUtil.fieldsToColumns(TPssEquipmentsPageVo.class).contains(x.getProperty()));
        return BeanUtil.copyToList(this.baseMapper.selectList(queryWrapper), TPssEquipmentsVo.class);
    }

    @Override
    public TPssEquipmentsVo queryInfo(Long id) {
        TPssEquipments tPssEquipments = this.baseMapper.selectById(id);
        if (tPssEquipments == null) {
            throw new MyException("找不到此数据！");
        }
        return BeanUtil.copyProperties(tPssEquipments,TPssEquipmentsVo.class);
    }

    @Override
    public TPssEquipmentsVo add(AddTPssEquipmentsDto dto) {
        Long dbCount =
                this.baseMapper.selectCount(Wrappers.<TPssEquipments>lambdaQuery().eq(TPssEquipments::getCCode, dto.getCCode()));
        if (dbCount > 0) throw new MyException("设备编码不能重复");

        TPssEquipments tPssEquipments = BeanUtil.toBean(dto, TPssEquipments.class);
        this.baseMapper.insert(tPssEquipments);

        return BeanUtil.copyProperties(tPssEquipments, TPssEquipmentsVo.class);
    }

    @Override
    public TPssEquipmentsVo update(UpdateTPssEquipmentsDto dto) {
        Long dbCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssEquipments>lambdaQuery()
                                .eq(TPssEquipments::getCCode, dto.getCCode())
                                .ne(TPssEquipments::getNId, dto.getNId())
                );
        if (dbCount > 0) throw new MyException("设备编码不能重复");

        TPssEquipments tPssEquipments = BeanUtil.toBean(dto, TPssEquipments.class);
        this.baseMapper.updateById(tPssEquipments);

        return BeanUtil.copyProperties(tPssEquipments, TPssEquipmentsVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssEquipmentsPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssEquipmentsPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssEquipments.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssEquipmentsPageDto dto, Boolean isTemplate) {
        List<TPssEquipmentsPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() :  queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssEquipmentsPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return RT.fileStream(resultBot.toByteArray(), "DeviceManage" + ExcelTypeEnum.XLSX.getValue());
    }
}
