
package com.aitos.pss.service.prodmonit;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.prodmonit.AddTPssEquipmentsDto;
import com.aitos.pss.dto.prodmonit.TPssEquipmentsPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssEquipmentsDto;
import com.aitos.pss.entity.prodmonit.TPssEquipments;
import com.aitos.pss.vo.prodmonit.TPssEquipmentsPageVo;
import com.aitos.pss.vo.prodmonit.TPssEquipmentsVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/

public interface IDeviceManageService extends IService<TPssEquipments> {
    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssEquipmentsPageVo> queryPage(TPssEquipmentsPageDto dto);

    /**
     * query list
     * @param dto
     * @return
     */
    List<TPssEquipmentsVo> queryList(TPssEquipmentsPageDto dto);

    /**
     * 详情查询
     * @param id
     * @return
     */
    TPssEquipmentsVo queryInfo(Long id);

    /**
     * 新增
     * @param dto
     * @return
     */
    TPssEquipmentsVo add(AddTPssEquipmentsDto dto);

    /**
     * 更新
     * @param dto
     * @return
     */
    TPssEquipmentsVo update(UpdateTPssEquipmentsDto dto);

    /**
     * 数据导入
     * @param file
     * @throws IOException
     */

    void importData(MultipartFile file) throws IOException;

    /**
     * 数据导出
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(TPssEquipmentsPageDto dto, Boolean isTemplate);
}
