package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssPFaceDto;
import com.aitos.pss.dto.qualitymanger.TPssPFacePageDto;
import com.aitos.pss.entity.qualitymanger.TPssPFace;
import com.aitos.pss.mapper.qualitymanger.TPssPFaceMapper;
import com.aitos.pss.service.qualitymanger.ITPssPFaceService;
import com.aitos.pss.vo.qualitymanger.TPssPFacePageVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssPFaceServiceImpl extends ServiceImpl<TPssPFaceMapper, TPssPFace> implements ITPssPFaceService {

    @Override
    public PageOutput<TPssPFacePageVo> queryPage(TPssPFacePageDto dto) {
        LambdaQueryWrapper<TPssPFace> queryWrapper =
                Wrappers.<TPssPFace>lambdaQuery()
                        .eq(StringUtils.isNotBlank(dto.getCPlateId()), TPssPFace::getCPlateId, dto.getCPlateId());
        IPage<TPssPFace> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssPFacePageVo.class);
    }

    @Override
    public Boolean factDecision(List<AddTPssPFaceDto> dtoList) {
        // TODO 补充逻辑
        List<TPssPFace> tPssPFaces = BeanUtil.copyToList(dtoList, TPssPFace.class);
        this.baseMapper.insert(tPssPFaces);

//        dtoList.stream().filter(item -> Objects.equals(PssQFaceEnum.TEMPORARY_HOLD))

        return Boolean.TRUE;
    }
}