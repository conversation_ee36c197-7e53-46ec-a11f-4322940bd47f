package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssTechCcmInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechCcmInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechCcmInfoDto;
import com.aitos.pss.service.qualitymanger.ITechCcmInfoService;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 连铸质量工艺参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/techccminfo")
@Tag(name = "/pss"  + "/techccminfo",description = "连铸质量工艺参数质量设计结果代码")
@AllArgsConstructor
public class TechCcmInfoController {


    private final ITechCcmInfoService techCcmInfoService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssTechCcmInfo列表(分页)")
    public RT<PageOutput<TPssTechCcmInfoPageVo>> page(@Valid TPssTechCcmInfoPageDto dto){

        return RT.ok(techCcmInfoService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssTechCcmInfo信息")
    public RT<TPssTechCcmInfoVo> info(@RequestParam Long id){

        return RT.ok(techCcmInfoService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssTechCcmInfo")
    @AitLog(value = "连铸质量工艺参数质量设计结果新增数据")
    public RT<TPssTechCcmInfoVo> add(@Valid @RequestBody AddTPssTechCcmInfoDto dto){

        return RT.ok(techCcmInfoService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssTechCcmInfo")
    @AitLog(value = "连铸质量工艺参数质量设计结果修改数据")
    public RT<TPssTechCcmInfoVo> update(@Valid @RequestBody UpdateTPssTechCcmInfoDto dto){

        return RT.ok(techCcmInfoService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "连铸质量工艺参数质量设计结果删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(techCcmInfoService.removeBatchByIds(ids));
    }

}