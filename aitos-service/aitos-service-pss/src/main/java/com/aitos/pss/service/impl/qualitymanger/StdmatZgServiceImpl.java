package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssStdmatZgDto;
import com.aitos.pss.dto.qualitymanger.TPssStdmatZgPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdmatZgDto;
import com.aitos.pss.entity.qualitymanger.TPssStdmatZg;
import com.aitos.pss.mapper.qualitymanger.TPssStdmatZgMapper;
import com.aitos.pss.service.qualitymanger.IStdmatZgService;
import com.aitos.pss.vo.qualitymanger.TPssStdmatZgPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdmatZgVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class StdmatZgServiceImpl extends ServiceImpl<TPssStdmatZgMapper, TPssStdmatZg> implements IStdmatZgService {

    @Override
    public PageOutput<TPssStdmatZgPageVo> queryPage(TPssStdmatZgPageDto dto) {
        LambdaQueryWrapper<TPssStdmatZg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCOpStdName()),TPssStdmatZg::getCOpStdName,dto.getCOpStdName())
                .eq(Objects.nonNull(dto.getCQualityId()),TPssStdmatZg::getCQualityId,dto.getCQualityId())
                .eq(Objects.nonNull(dto.getCVerifyItemId()),TPssStdmatZg::getCVerifyItemId,dto.getCVerifyItemId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssStdmatZg::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(StrUtil.isNotBlank(dto.getCSmpDcsCd()),TPssStdmatZg::getCSmpDcsCd,dto.getCSmpDcsCd())
                .eq(StrUtil.isNotBlank(dto.getCStdClass()),TPssStdmatZg::getCStdClass,dto.getCStdClass())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssStdmatZg::getNEnabledMark,dto.getNEnabledMark())
                .orderByDesc(TPssStdmatZg::getNId)
                .select(TPssStdmatZg.class,x -> VoToColumnUtil.fieldsToColumns(TPssStdmatZgPageVo.class).contains(x.getProperty()));
        IPage<TPssStdmatZg> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssStdmatZgPageVo.class);
    }

    @Override
    public TPssStdmatZgVo queryInfo(Long id) {
        TPssStdmatZg tPssStdmatZg = this.baseMapper.selectById(id);
        if (tPssStdmatZg == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssStdmatZg,TPssStdmatZgVo.class);
    }

    @Override
    public TPssStdmatZgVo add(AddTPssStdmatZgDto dto) {
        TPssStdmatZg tPssStdmatZg = BeanUtil.toBean(dto, TPssStdmatZg.class);
        if (Objects.nonNull(dto.getCSmpMax()) && Objects.nonNull(dto.getCSmpMin())) {
            if (dto.getCSmpMax().compareTo(dto.getCSmpMin()) < 0) throw new MyException("上限不能小于下限");
        }
        if (Objects.nonNull(dto.getCSmpAvgMax()) && Objects.nonNull(dto.getCSmpAvgMin())) {
            if (dto.getCSmpAvgMax().compareTo(dto.getCSmpAvgMin()) < 0) throw new MyException("平均上限不能小于下限");
        }

        this.baseMapper.insert(tPssStdmatZg);

        return BeanUtil.copyProperties(tPssStdmatZg,TPssStdmatZgVo.class);
    }

    @Override
    public TPssStdmatZgVo update(UpdateTPssStdmatZgDto dto) {
        TPssStdmatZg tPssStdmatZg = BeanUtil.toBean(dto, TPssStdmatZg.class);
        if (Objects.nonNull(dto.getCSmpMax()) && Objects.nonNull(dto.getCSmpMin())) {
            if (dto.getCSmpMax().compareTo(dto.getCSmpMin()) < 0) throw new MyException("上限不能小于下限");
        }
        if (Objects.nonNull(dto.getCSmpAvgMax()) && Objects.nonNull(dto.getCSmpAvgMin())) {
            if (dto.getCSmpAvgMax().compareTo(dto.getCSmpAvgMin()) < 0) throw new MyException("平均上限不能小于下限");
        }

        this.baseMapper.updateById(tPssStdmatZg);

        return BeanUtil.copyProperties(tPssStdmatZg,TPssStdmatZgVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssStdmatZgPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssStdmatZgPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssStdmatZg.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssStdmatZgPageDto dto, Boolean isTemplate) {
        List<TPssStdmatZgPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssStdmatZgPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "StdmatZg" + ExcelTypeEnum.XLSX.getValue());
    }
}
