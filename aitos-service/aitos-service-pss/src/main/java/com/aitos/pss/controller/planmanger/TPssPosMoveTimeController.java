package com.aitos.pss.controller.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssPosMoveTimeDto;
import com.aitos.pss.dto.planmanger.TPssPosMoveTimePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssPosMoveTimeDto;
import com.aitos.pss.entity.planmanger.TPssPosMoveTime;
import com.aitos.pss.service.planmanger.ITPssPosMoveTimeService;
import com.aitos.pss.vo.planmanger.TPssPosMoveTimePageVo;
import com.aitos.pss.vo.planmanger.TPssPosMoveTimeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 工序时间标准-钢包移动时间标准
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssprocessstddis")
@Tag(name = "/pss"  + "/pssprocessstddis",description = "工序时间标准-钢包移动时间标准代码")
@AllArgsConstructor
public class TPssPosMoveTimeController {


    private final ITPssPosMoveTimeService pssProcessStdDisService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssPosMoveTime列表(分页)")
    public RT<PageOutput<TPssPosMoveTimePageVo>> page(@Valid TPssPosMoveTimePageDto dto){

        return RT.ok(pssProcessStdDisService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssPosMoveTime信息")
    public RT<TPssPosMoveTimeVo> info(@RequestParam Long id){

        return RT.ok(pssProcessStdDisService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssPosMoveTime")
    @AitLog(value = "工序时间标准-钢包移动时间标准新增数据")
    public RT<TPssPosMoveTimeVo> add(@Valid @RequestBody AddTPssPosMoveTimeDto dto){

        return RT.ok(pssProcessStdDisService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssPosMoveTime")
    @AitLog(value = "工序时间标准-钢包移动时间标准修改数据")
    public RT<TPssPosMoveTimeVo> update(@Valid @RequestBody UpdateTPssPosMoveTimeDto dto){

        return RT.ok(pssProcessStdDisService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "工序时间标准-钢包移动时间标准删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(pssProcessStdDisService.removeBatchByIds(ids));
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssPosMoveTimeDto dto){
        TPssPosMoveTime tPssPosMoveTime = BeanUtil.copyProperties(dto, TPssPosMoveTime.class);

        return RT.ok(pssProcessStdDisService.updateById(tPssPosMoveTime));
    }

}