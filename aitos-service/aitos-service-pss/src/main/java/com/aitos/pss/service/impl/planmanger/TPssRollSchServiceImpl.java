package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.TPssRollSchPageDto;
import com.aitos.pss.entity.planmanger.TPssRollSch;
import com.aitos.pss.mapper.planmanger.TPssRollSchMapper;
import com.aitos.pss.service.planmanger.ITPssRollSchService;
import com.aitos.pss.vo.planmanger.TPssRollSchVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssRollSchServiceImpl extends ServiceImpl<TPssRollSchMapper, TPssRollSch> implements ITPssRollSchService {

    @Override
    public PageOutput<TPssRollSchVo> queryPage(TPssRollSchPageDto dto) {
        if (StringUtils.isBlank(dto.getCDispatchId())) {
            return null;
        }

        LambdaQueryWrapper<TPssRollSch> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StringUtils.isNoneBlank(dto.getCSchId()),TPssRollSch::getCSchId, dto.getCSchId())
                .eq(StringUtils.isNoneBlank(dto.getCDispatchId()),TPssRollSch::getCDispatchId, dto.getCDispatchId())
                .eq(StringUtils.isNoneBlank(dto.getCLineNo()),TPssRollSch::getCLineNo, dto.getCLineNo())
                .like(StringUtils.isNoneBlank(dto.getCHeatId()),TPssRollSch::getCHeatId, dto.getCHeatId())
                .orderByDesc(TPssRollSch::getNId)
                .select(TPssRollSch.class,x -> VoToColumnUtil.fieldsToColumns(TPssRollSchVo.class).contains(x.getProperty()));
        IPage<TPssRollSch> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        PageOutput<TPssRollSchVo> pageOutput = ConventPage.getPageOutput(page, TPssRollSchVo.class);

        return pageOutput;
    }

    @Override
    public TPssRollSchVo queryInfo(Long id) {
        TPssRollSch rollSch = this.baseMapper.selectById(id);
        if (rollSch == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(rollSch, TPssRollSchVo.class);
    }
}