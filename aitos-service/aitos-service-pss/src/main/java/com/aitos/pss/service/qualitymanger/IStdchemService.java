
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssStdchemDto;
import com.aitos.pss.dto.qualitymanger.TPssStdchemPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdchemDto;
import com.aitos.pss.entity.qualitymanger.TPssStdchem;
import com.aitos.pss.vo.qualitymanger.TPssStdchemPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdchemVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/

public interface IStdchemService extends IService<TPssStdchem> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssStdchemPageVo> queryPage(@Valid TPssStdchemPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssStdchemVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssStdchemVo add(@Valid AddTPssStdchemDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssStdchemVo update(@Valid UpdateTPssStdchemDto dto);
}
