package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssTechkRoutLgInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechkRoutLgInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechkRoutLgInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssTechkRoutLgInfo;
import com.aitos.pss.mapper.qualitymanger.TPssTechkRoutLgInfoMapper;
import com.aitos.pss.service.qualitymanger.ITechkRoutLgInfoService;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgInfoVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TechkRoutLgInfoServiceImpl extends ServiceImpl<TPssTechkRoutLgInfoMapper, TPssTechkRoutLgInfo> implements ITechkRoutLgInfoService {

    @Override
    public PageOutput<TPssTechkRoutLgInfoPageVo> queryPage(TPssTechkRoutLgInfoPageDto dto) {
        LambdaQueryWrapper<TPssTechkRoutLgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualId()),TPssTechkRoutLgInfo::getCQualId,dto.getCQualId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssTechkRoutLgInfo::getCStlGrdCd,dto.getCStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCIsCold()),TPssTechkRoutLgInfo::getCIsCold,dto.getCIsCold())
                .like(StrUtil.isNotBlank(dto.getOrderId()),TPssTechkRoutLgInfo::getOrderId,dto.getOrderId())
                .between(
                        ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),
                        TPssTechkRoutLgInfo::getDtCreateDateTime,
                        dto.getDtCreateDateTimeStart(),
                        dto.getDtCreateDateTimeEnd())
                .orderByDesc(TPssTechkRoutLgInfo::getNId)
                .select(TPssTechkRoutLgInfo.class,x -> VoToColumnUtil.fieldsToColumns(TPssTechkRoutLgInfoPageVo.class).contains(x.getProperty()));
        IPage<TPssTechkRoutLgInfo> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssTechkRoutLgInfoPageVo.class);
    }

    @Override
    public TPssTechkRoutLgInfoVo queryInfo(Long id) {
        TPssTechkRoutLgInfo tPssTechkRoutLgInfo = this.baseMapper.selectById(id);
        if (tPssTechkRoutLgInfo == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssTechkRoutLgInfo, TPssTechkRoutLgInfoVo.class);
    }

    @Override
    public TPssTechkRoutLgInfoVo add(AddTPssTechkRoutLgInfoDto dto) {
        TPssTechkRoutLgInfo tPssTechkRoutLgInfo = BeanUtil.toBean(dto, TPssTechkRoutLgInfo.class);
        this.baseMapper.insert(tPssTechkRoutLgInfo);

        return BeanUtil.copyProperties(tPssTechkRoutLgInfo, TPssTechkRoutLgInfoVo.class);
    }

    @Override
    public TPssTechkRoutLgInfoVo update(UpdateTPssTechkRoutLgInfoDto dto) {
        TPssTechkRoutLgInfo tPssTechkRoutLgInfo = BeanUtil.toBean(dto, TPssTechkRoutLgInfo.class);
        this.baseMapper.updateById(tPssTechkRoutLgInfo);

        return BeanUtil.copyProperties(tPssTechkRoutLgInfo, TPssTechkRoutLgInfoVo.class);
    }
}
