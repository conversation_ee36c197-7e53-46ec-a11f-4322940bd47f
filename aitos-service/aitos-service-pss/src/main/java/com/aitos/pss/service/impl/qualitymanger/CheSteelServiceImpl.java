package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssCheSteelDto;
import com.aitos.pss.dto.qualitymanger.TPssCheSteelPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssCheSteelDto;
import com.aitos.pss.entity.qualitymanger.TPssCheSteel;
import com.aitos.pss.mapper.qualitymanger.TPssCheSteelMapper;
import com.aitos.pss.service.qualitymanger.ICheSteelService;
import com.aitos.pss.vo.qualitymanger.TPssCheSteelPageVo;
import com.aitos.pss.vo.qualitymanger.TPssCheSteelVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class CheSteelServiceImpl extends ServiceImpl<TPssCheSteelMapper, TPssCheSteel> implements ICheSteelService {

    @Override
    public PageOutput<TPssCheSteelPageVo> queryPage(TPssCheSteelPageDto dto) {
        LambdaQueryWrapper<TPssCheSteel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCSampleid()),TPssCheSteel::getCSampleid,dto.getCSampleid())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssCheSteel::getCStlGrdCd,dto.getCStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCSampletype()),TPssCheSteel::getCSampletype,dto.getCSampletype())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssCheSteel::getNEnabledMark,dto.getNEnabledMark())
                .like(StrUtil.isNotBlank(dto.getCSmpId()),TPssCheSteel::getCSmpId,dto.getCSmpId())
                .like(StrUtil.isNotBlank(dto.getCJudge()),TPssCheSteel::getCJudge,dto.getCJudge())
                .orderByDesc(TPssCheSteel::getDtCreateDateTime)
                .select(TPssCheSteel.class,x -> VoToColumnUtil.fieldsToColumns(TPssCheSteelPageVo.class).contains(x.getProperty()));
        IPage<TPssCheSteel> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        PageOutput<TPssCheSteelPageVo> pageOutput = ConventPage.getPageOutput(page, TPssCheSteelPageVo.class);
        expandAttrFill(pageOutput.getList());

        return pageOutput;
    }

    @Override
    public TPssCheSteelVo queryInfo(Long id) {
        TPssCheSteel tPssCheSteel = this.baseMapper.selectById(id);
        if (tPssCheSteel == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssCheSteel, TPssCheSteelVo.class);
    }

    @Override
    public TPssCheSteelVo add(AddTPssCheSteelDto dto) {
        TPssCheSteel tPssCheSteel = BeanUtil.toBean(dto, TPssCheSteel.class);
        this.baseMapper.insert(tPssCheSteel);

        return BeanUtil.copyProperties(tPssCheSteel, TPssCheSteelVo.class);
    }

    @Override
    public TPssCheSteelVo update(UpdateTPssCheSteelDto dto) {
        TPssCheSteel tPssCheSteel = BeanUtil.toBean(dto, TPssCheSteel.class);
        this.baseMapper.updateById(tPssCheSteel);

        return BeanUtil.copyProperties(tPssCheSteel, TPssCheSteelVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssCheSteelPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssCheSteelPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);

        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssCheSteel.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssCheSteelPageDto dto, Boolean isTemplate) {
        List<TPssCheSteelPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : ((PageOutput<TPssCheSteelPageVo>) queryPage(dto)).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssCheSteelPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "CheSteel" + ExcelTypeEnum.XLSX.getValue());
    }

    private void expandAttrFill(List<TPssCheSteelPageVo> cheSteelPageVoList) {
        for (TPssCheSteelPageVo cheSteelPageVo : cheSteelPageVoList) {

        }
    }
}
