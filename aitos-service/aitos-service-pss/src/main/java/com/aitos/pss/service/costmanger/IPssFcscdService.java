
package com.aitos.pss.service.costmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.costmanger.AddTPssFcsCdDto;
import com.aitos.pss.dto.costmanger.TPssFcsCdPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsCdDto;
import com.aitos.pss.entity.costmanger.TPssFcsCd;
import com.aitos.pss.vo.costmanger.TPssFcsCdPageVo;
import com.aitos.pss.vo.costmanger.TPssFcsCdVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/

public interface IPssFcscdService extends IService<TPssFcsCd> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssFcsCdPageVo> queryPage(@Valid TPssFcsCdPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssFcsCdVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssFcsCdVo add(@Valid AddTPssFcsCdDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssFcsCdVo update(@Valid UpdateTPssFcsCdDto dto);

    Boolean removeBatch(@Valid List<Long> ids);
}
