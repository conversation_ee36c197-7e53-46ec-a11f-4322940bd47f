
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssTechLfInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechLfInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechLfInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssTechLfInfo;
import com.aitos.pss.vo.qualitymanger.TPssTechLfInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechLfInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/

public interface ITechIfInfoService extends IService<TPssTechLfInfo> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssTechLfInfoPageVo> queryPage(@Valid TPssTechLfInfoPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssTechLfInfoVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssTechLfInfoVo add(@Valid AddTPssTechLfInfoDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssTechLfInfoVo update(@Valid UpdateTPssTechLfInfoDto dto);
}
