package com.aitos.pss.controller.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.TPssRollSchPageDto;
import com.aitos.pss.service.planmanger.ITPssRollSchService;
import com.aitos.pss.vo.planmanger.TPssRollSchVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 *
 *
 *
 * @version 1.0.0
 * @date 2025/6/10 19:10
 */
@RestController
@RequestMapping("/pss" + "/rollsch")
@Tag(name = "/pss"  + "/rollsch",description = "物料匹配管理代码")
@AllArgsConstructor
public class TPssRollSchController {

    private final ITPssRollSchService rollSchService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssDispatch列表(分页)")
    public RT<PageOutput<TPssRollSchVo>> page(@Valid TPssRollSchPageDto dto){

        return RT.ok(rollSchService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssDispatch信息")
    public RT<TPssRollSchVo> info(@RequestParam Long id){

        return RT.ok(rollSchService.queryInfo(id));
    }

}