
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPssMatYardinLgDto;
import com.aitos.pss.dto.inventory.TPssMatYardinLgPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMatYardinLgDto;
import com.aitos.pss.entity.inventory.TPssMatYardinLg;
import com.aitos.pss.vo.inventory.TPssMatYardinLgPageVo;
import com.aitos.pss.vo.inventory.TPssMatYardinLgVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/

public interface IMatYardinLgService extends IService<TPssMatYardinLg> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssMatYardinLgPageVo> queryPage(@Valid TPssMatYardinLgPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssMatYardinLgVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssMatYardinLgVo add(@Valid AddTPssMatYardinLgDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssMatYardinLgVo update(@Valid UpdateTPssMatYardinLgDto dto);
}
