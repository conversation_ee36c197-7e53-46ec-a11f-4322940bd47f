package com.aitos.pss.service.impl.costmanger;

import com.aitos.common.core.exception.MyException;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.costmanger.TPssOrderCostPageDto;
import com.aitos.pss.entity.costmanger.TPssOrderCost;
import com.aitos.pss.entity.planmanger.TPssCheatPlan;
import com.aitos.pss.entity.planmanger.TPssOrderInfo;
import com.aitos.pss.entity.planmanger.TPssTaskList;
import com.aitos.pss.mapper.costmanger.TPssOrderCostMapper;
import com.aitos.pss.mapper.planmanger.TPssOrderInfoMapper;
import com.aitos.pss.service.costmanger.IOrderCostService;
import com.aitos.pss.service.planmanger.ITPssCheatPlanService;
import com.aitos.pss.service.planmanger.ITPssTaskListService;
import com.aitos.pss.vo.costmanger.TPssOrderCostChartVo;
import com.aitos.pss.vo.costmanger.TPssOrderCostVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class OrderCostServiceImpl extends ServiceImpl<TPssOrderCostMapper, TPssOrderCost> implements IOrderCostService {

    private final TPssOrderInfoMapper orderInfoMapper;

    private final ITPssTaskListService taskListService;

    private final ITPssCheatPlanService cheatPlanService;

    @Override
    public TPssOrderCostChartVo queryPanel(TPssOrderCostPageDto dto) {

        String cOrderNo = dto.getCOrderNo();
        if (StringUtils.isBlank(cOrderNo)) throw new MyException("生产订单号不能为空");

        TPssOrderInfo orderInfo =
                orderInfoMapper.selectOne(Wrappers.<TPssOrderInfo>lambdaQuery().eq(TPssOrderInfo::getCOrderNo, cOrderNo));
        if (Objects.isNull(orderInfo)) return new TPssOrderCostChartVo();

        String cProductTaskListId = orderInfo.getCProductTaskListId();
        TPssTaskList taskList =
                taskListService.getOne(Wrappers.<TPssTaskList>lambdaQuery().eq(TPssTaskList::getCTaskListId, cProductTaskListId));
        if (Objects.isNull(taskList)) return new TPssOrderCostChartVo();
        String cTaskListId = taskList.getCTaskListId();

        List<TPssCheatPlan> chpEditList =
                cheatPlanService.list(Wrappers.<TPssCheatPlan>lambdaQuery().eq(TPssCheatPlan::getCTaskListId, cTaskListId));
        Set<String> heatIdSet = chpEditList.stream().map(TPssCheatPlan::getCHeatId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(heatIdSet)) return new TPssOrderCostChartVo();

        TPssOrderCostVo orderCostVo = this.baseMapper.queryPanel(dto);

        return null;
    }
}