package com.aitos.pss.controller.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssStlCpctDto;
import com.aitos.pss.dto.planmanger.TPssStlCpctPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssStlCpctDto;
import com.aitos.pss.entity.planmanger.TPssStlCpct;
import com.aitos.pss.service.planmanger.ITPssStlCpctService;
import com.aitos.pss.vo.planmanger.TPssStlCpctPageVo;
import com.aitos.pss.vo.planmanger.TPssStlCpctVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 熔量重量标准-炉熔量标准
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssstlcpct")
@Tag(name = "/pss"  + "/pssstlcpct",description = "熔量重量标准-炉熔量标准代码")
@AllArgsConstructor
public class TPssStlCpctController {


    private final ITPssStlCpctService pssStlCpctService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssStlCpct列表(分页)")
    public RT<PageOutput<TPssStlCpctPageVo>> page(@Valid TPssStlCpctPageDto dto){

        return RT.ok(pssStlCpctService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssStlCpct信息")
    public RT<TPssStlCpctVo> info(@RequestParam Long id){

        return RT.ok(pssStlCpctService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssStlCpct")
    @AitLog(value = "熔量重量标准-炉熔量标准新增数据")
    public RT<TPssStlCpctVo> add(@Valid @RequestBody AddTPssStlCpctDto dto){

        return RT.ok(pssStlCpctService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssStlCpct")
    @AitLog(value = "熔量重量标准-炉熔量标准修改数据")
    public RT<TPssStlCpctVo> update(@Valid @RequestBody UpdateTPssStlCpctDto dto){

        return RT.ok(pssStlCpctService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "熔量重量标准-炉熔量标准删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssStlCpctService.removeBatchByIds(ids));
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssStlCpctDto dto){
        TPssStlCpct tPssStlCpct = BeanUtil.toBean(dto, TPssStlCpct.class);

        return RT.ok(pssStlCpctService.updateById(tPssStlCpct));
    }

}