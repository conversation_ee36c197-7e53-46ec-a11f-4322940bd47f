package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.constant.GlobalConstant;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.masterdata.entity.MdGrade;
import com.aitos.masterdata.service.GradeClientService;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.TPssBatchSamplingPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssBatchSamplingDto;
import com.aitos.pss.entity.qualitymanger.TPssBatchSampling;
import com.aitos.pss.mapper.qualitymanger.TPssBatchSamplingMapper;
import com.aitos.pss.service.qualitymanger.IBatchSamplingService;
import com.aitos.pss.vo.qualitymanger.TPssBatchSamplingPageVo;
import com.aitos.pss.vo.qualitymanger.TPssBatchSamplingVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class BatchSamplingServiceImpl extends ServiceImpl<TPssBatchSamplingMapper, TPssBatchSampling> implements IBatchSamplingService {

    private final GradeClientService gradeClientService;

    @Override
    public PageOutput<TPssBatchSamplingPageVo> queryPage(TPssBatchSamplingPageDto dto) {
        LambdaQueryWrapper<TPssBatchSampling> batchSamplingLambdaQueryWrapper =
                Wrappers.<TPssBatchSampling>lambdaQuery()
                        .eq(StringUtils.isNotBlank(dto.getCStlGrdCd()), TPssBatchSampling::getCStlGrdCd, dto.getCStlGrdCd())
                        .eq(StringUtils.isNotBlank(dto.getCStlGrdDesc()), TPssBatchSampling::getCStlGrdDesc, dto.getCStlGrdDesc())
                        .eq(StringUtils.isNotBlank(dto.getCLineCd()), TPssBatchSampling::getCLineCd, dto.getCLineCd())
                        .like(StringUtils.isNotBlank(dto.getCHeatId()), TPssBatchSampling::getCHeatId, dto.getCHeatId())
                        .like(StringUtils.isNotBlank(dto.getCSmpLot()), TPssBatchSampling::getCSmpLot, dto.getCSmpLot())
                        .between(
                                Objects.nonNull(dto.getDtCreateDateStart()) && Objects.nonNull(dto.getDtCreateDateEnd()),
                                TPssBatchSampling::getDtCreateDateTime,
                                dto.getDtCreateDateStart(),dto.getDtCreateDateEnd()
                        );

        IPage<TPssBatchSampling> page = this.baseMapper.selectPage(ConventPage.getPage(dto), batchSamplingLambdaQueryWrapper);
        PageOutput<TPssBatchSamplingPageVo> pageOutput = ConventPage.getPageOutput(page, TPssBatchSamplingPageVo.class);
        fillAttribute(pageOutput.getList());

        return pageOutput;
    }

    @Override
    public Boolean recheck(List<UpdateTPssBatchSamplingDto> dtoList) {
        List<TPssBatchSampling> batchSamplingList = BeanUtil.copyToList(dtoList, TPssBatchSampling.class);
        for (TPssBatchSampling batchSampling : batchSamplingList) {
            batchSampling.setNSampNum(Objects.isNull(batchSampling.getNSampNum()) ? GlobalConstant.NUMBER_ONE : batchSampling.getNSampNum() + 1);
        }

        updateBatchById(batchSamplingList);


        return Boolean.TRUE;
    }

    @Override
    public TPssBatchSamplingVo queryInfo(Long id) {
        TPssBatchSampling batchSampling = this.baseMapper.selectById(id);
        if (batchSampling == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(batchSampling, TPssBatchSamplingVo.class);
    }

    private void fillAttribute(List<TPssBatchSamplingPageVo> batchSamplingPageVoList) {
        if (CollectionUtils.isEmpty(batchSamplingPageVoList)) {
            return;
        }

        Set<Long> cStlGrdCdIdSet =
                batchSamplingPageVoList.stream().map(item -> Long.valueOf(item.getCStlGrdCd())).collect(Collectors.toSet());
        Map<String, MdGrade> GradeVoIdAndNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(cStlGrdCdIdSet)) {
            List<MdGrade> gradeList = gradeClientService.list(new ArrayList<>(cStlGrdCdIdSet));
            GradeVoIdAndNameMap = gradeList.stream().collect(Collectors.toMap(item -> String.valueOf(item.getFid()),v -> v,(k1,k2) -> k1));
        }

        for (TPssBatchSamplingPageVo batchSamplingPageVo : batchSamplingPageVoList) {
            batchSamplingPageVo.setCStlGrdDesc(
                    GradeVoIdAndNameMap.getOrDefault(batchSamplingPageVo.getCStlGrdCd(),new MdGrade()).getFname()
            );
        }
    }
}