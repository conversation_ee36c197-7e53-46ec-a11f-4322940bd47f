package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.constant.GlobalConstant;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.masterdata.dto.MdFactTreePageDto;
import com.aitos.masterdata.feign.IFactTreeClient;
import com.aitos.masterdata.vo.MdFactTreeVo;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssTaskListDto;
import com.aitos.pss.dto.planmanger.TPssTaskListPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssTaskListDto;
import com.aitos.pss.dto.planmanger.UpdateTPssTaskListStoveOrderDto;
import com.aitos.pss.dto.qualitymanger.AddTPssPslabChosDto;
import com.aitos.pss.entity.planmanger.*;
import com.aitos.pss.entity.qualitymanger.*;
import com.aitos.pss.enums.OrderStatusEnum;
import com.aitos.pss.mapper.planmanger.TPssAggregatePlanMapper;
import com.aitos.pss.mapper.planmanger.TPssOrderInfoMapper;
import com.aitos.pss.mapper.planmanger.TPssTaskListMapper;
import com.aitos.pss.service.planmanger.*;
import com.aitos.pss.service.qualitymanger.*;
import com.aitos.pss.utils.CircularListUtil;
import com.aitos.pss.vo.planmanger.TPssTaskListPageVo;
import com.aitos.pss.vo.planmanger.TPssTaskListVo;
import com.aitos.system.client.v2.ICodeRuleClientV2;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssTaskListServiceImpl extends ServiceImpl<TPssTaskListMapper, TPssTaskList> implements ITPssTaskListService {

    private final ITechkRoutLgInfoService techkRoutLgInfoService;

    private final ITPssStlCpctService pssStlCpctService;

    private final IPslabChosService pslabChosService;
    
    private final ICodeRuleClientV2 codeRuleClientV2;

    private final IChpEditService chpEditService;

    private final ITPssCastCnvtsService pssCastCnvtsService;

    private final ICastpEditService castpEditService;

    private final ITPssCheatPlanService heatidplanmangerService;

    private final ITPssPosTimeService posTimeService;

    private final ITPssPosMoveTimeService posMoveTimeService;

    private final ICutPlanService cutPlanService;

    private final ITPssCheatPlanService cheatPlanService;

    private final IFactTreeClient factTreeClient;

    private final ITPssRateStdService rateStdService;

    private final IQualityCodeService qualityCodeService;

    private final TPssAggregatePlanMapper aggregatePlanMapper;

    private final IDispatchService dispatchService;

    private final IFinalSlabService finalSlabService;

    private final TPssOrderInfoMapper orderInfoMapper;

    @Override
    public PageOutput<TPssTaskListPageVo> queryPage(TPssTaskListPageDto dto) {
        LambdaQueryWrapper<TPssTaskList> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCOrderNo()),TPssTaskList::getCOrderNo,dto.getCOrderNo())
                .like(StrUtil.isNotBlank(dto.getCProdType()),TPssTaskList::getCProdType,dto.getCProdType())
                .between(ObjectUtil.isNotNull(dto.getDtCrtTimeStart()) && ObjectUtil.isNotNull(dto.getDtCrtTimeEnd()),TPssTaskList::getDtCrtTime,dto.getDtCrtTimeStart(),dto.getDtCrtTimeEnd())
                .like(StrUtil.isNotBlank(dto.getCTaskListId()),TPssTaskList::getCTaskListId,dto.getCTaskListId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssTaskList::getCStlGrdCd,dto.getCStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCStatus()),TPssTaskList::getCStatus,dto.getCStatus())
                .orderByDesc(TPssTaskList::getNId)
                .select(TPssTaskList.class,x -> VoToColumnUtil.fieldsToColumns(TPssTaskListPageVo.class).contains(x.getProperty()));
        IPage<TPssTaskList> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssTaskListPageVo.class);
    }

    @Override
    public TPssTaskListVo queryInfo(Long id) {
        TPssTaskList tPssTaskList = this.baseMapper.selectById(id);
        if (tPssTaskList == null) {
           throw new MyException("找不到此数据！");
        }

        return BeanUtil.toBean(tPssTaskList, TPssTaskListVo.class);
    }

    @Override
    public TPssTaskListVo add(AddTPssTaskListDto dto) {
        TPssTaskList tPssTaskList = BeanUtil.toBean(dto, TPssTaskList.class);
        this.baseMapper.insert(tPssTaskList);

        return BeanUtil.toBean(tPssTaskList, TPssTaskListVo.class);
    }

    @Override
    public TPssTaskListVo update(UpdateTPssTaskListDto dto) {
        TPssTaskList tPssTaskList = BeanUtil.toBean(dto, TPssTaskList.class);
        this.baseMapper.updateById(tPssTaskList);

        return BeanUtil.copyProperties(tPssTaskList, TPssTaskListVo.class);
    }

    @Override
    public Boolean deleteBathById(List<Long> ids) {
        this.baseMapper.deleteByIds(ids);

        return Boolean.TRUE;
    }

    @Override
    @GlobalTransactional
    public Boolean stoveOrder(UpdateTPssTaskListStoveOrderDto dto) {
        Integer isAlternate = dto.getIsAlternate();
        String stoveSeat = dto.getStoveSeat();
        if (StringUtils.isBlank(stoveSeat)) throw new MyException("参数不能为空");
        List<UpdateTPssTaskListDto> taskListDtoList = dto.getTaskListDtoList();
        if (CollectionUtils.isEmpty(taskListDtoList)) throw new MyException("参数不能为空");

        Set<String> statusSet =
                taskListDtoList
                        .stream()
                        .map(UpdateTPssTaskListDto::getCStatus)
                        .filter(item -> !Objects.equals(OrderStatusEnum.TASK_ISSUED.getCode(), item))
                        .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(statusSet)) throw new MyException("只有`任务单已下达状态`可以进行设计");

        // 获取电炉
        List<TPssStlCpct> stlCpctList  = Lists.newArrayList();
        if (Objects.equals(isAlternate, GlobalConstant.NUMBER_ONE)){
            // 查询工厂树当前座次父级下所有的座次
            MdFactTreePageDto factTreePageDto = new MdFactTreePageDto();
            factTreePageDto.setFpid(Long.valueOf(stoveSeat));
            List<MdFactTreeVo> dataOrThrow = factTreeClient.queryChild(factTreePageDto).getDataOrThrow();
            Set<Long> cBofIdSet = dataOrThrow.stream().map(MdFactTreeVo::getFid).collect(Collectors.toSet());
            stlCpctList =
                    pssStlCpctService.list(Wrappers.<TPssStlCpct>lambdaQuery().in(TPssStlCpct::getCBofId, cBofIdSet));
        }

        if (Objects.equals(isAlternate, GlobalConstant.NUMBER_ZERO)){
            stlCpctList = pssStlCpctService.list(Wrappers.<TPssStlCpct>lambdaQuery().eq(TPssStlCpct::getCBofId, stoveSeat));
        }
        if (CollectionUtils.isEmpty(stlCpctList)) throw new MyException("未获取到炉次信息");

        // 获取工艺路径设计结果
        Set<String> orderIdSet =
                taskListDtoList.stream().map(UpdateTPssTaskListDto::getCOrderNo).collect(Collectors.toSet());
        Map<String, UpdateTPssTaskListDto> cTaskListIdAndUpdateTPssTaskListDtoMap =
                taskListDtoList.stream().collect(Collectors.toMap(UpdateTPssTaskListDto::getCTaskListId, v -> v));
        List<TPssTechkRoutLgInfo> techkRoutLgInfoList =
                techkRoutLgInfoService.list(Wrappers.<TPssTechkRoutLgInfo>lambdaQuery().in(TPssTechkRoutLgInfo::getOrderId, orderIdSet));
        Map<String, TPssTechkRoutLgInfo> orderIdAndTPssTechkRoutLgInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(techkRoutLgInfoList)) {
            orderIdAndTPssTechkRoutLgInfoMap =
                    techkRoutLgInfoList.stream().collect(Collectors.toMap(TPssTechkRoutLgInfo::getOrderId,v -> v));
        }

        // 获取计划钢坯
        Set<String> cTaskListIdSet =
                taskListDtoList.stream().map(UpdateTPssTaskListDto::getCTaskListId).collect(Collectors.toSet());
        List<TPssPslabChos> pslabChosList =
                pslabChosService.list(Wrappers.<TPssPslabChos>lambdaQuery().in(TPssPslabChos::getCTaskListId, cTaskListIdSet));
        if (CollectionUtils.isEmpty(pslabChosList)) throw new MyException("未找到计划钢坯");

        // 生成炉次计划,将电炉转化为环形链表
        CircularListUtil.Node<TPssStlCpct> stlCpctHeadNode = CircularListUtil.constructorCircularList(stlCpctList);
        // 获取炉容量上限
        TPssStlCpct stlCpct = stlCpctHeadNode.getData();
        BigDecimal maxStoveWgt = BigDecimal.valueOf(stlCpct.getNHeatMax1());
        BigDecimal currentStoveWgt = BigDecimal.ZERO;
        BigDecimal currentSequNum = BigDecimal.ONE;
        // 生成炉次计划
        String cPlanHeatNo = codeRuleClientV2.generateAndUse("planStoveOrderCode").getDataOrThrow();
        List<TPssChpEdit> chpEditList = Lists.newArrayList();
        TPssChpEdit chpEdit = new TPssChpEdit();
        chpEditList.add(chpEdit);
        for (TPssPslabChos pslabChos : pslabChosList) {
            // 获取坯料重量
            BigDecimal slabWgt = pslabChos.getCSlabWgt();

            if (maxStoveWgt.subtract(currentStoveWgt).compareTo(slabWgt) < 0) {

                // 生成新的炉次计划
                chpEdit = new TPssChpEdit();
                cPlanHeatNo = codeRuleClientV2.generateAndUse("planStoveOrderCode").getDataOrThrow();
                currentStoveWgt = BigDecimal.ZERO;
                currentSequNum = BigDecimal.ONE;
                stlCpctHeadNode = stlCpctHeadNode.getNext();
                stlCpct = stlCpctHeadNode.getData();
                maxStoveWgt = BigDecimal.valueOf(stlCpct.getNHeatMax1());
                chpEditList.add(chpEdit);
            }

            // 计划炉次表中间表
            UpdateTPssTaskListDto updateTPssTaskListDto =
                    cTaskListIdAndUpdateTPssTaskListDtoMap.getOrDefault(pslabChos.getCTaskListId(), new UpdateTPssTaskListDto());
            chpEdit.setCPlanHeatId(cPlanHeatNo);
            chpEdit.setCStlGrdCd(pslabChos.getCStlGrdCd());
            chpEdit.setCStlGrdDesc(pslabChos.getCStlGrdDesc());
            chpEdit.setCMatQulId(pslabChos.getCMatQulId());
            chpEdit.setCMatQulCd(pslabChos.getCMatQulCd());
            chpEdit.setCMatQulName(pslabChos.getCMatQulName());
            TPssTechkRoutLgInfo techkRoutLgInfo = orderIdAndTPssTechkRoutLgInfoMap.get(updateTPssTaskListDto.getCOrderNo());
            if (Objects.nonNull(techkRoutLgInfo)) {
                String cPlRoute = String.join("*",
                        techkRoutLgInfo.getCTechRoute1Code(),
                        techkRoutLgInfo.getCTechRoute2Code(),
                        techkRoutLgInfo.getCTechRoute3Code(),
                        techkRoutLgInfo.getCTechRoute4Code()
                );
                chpEdit.setCPlRoute(cPlRoute);
            }

            chpEdit.setNCcmThk(pslabChos.getNSlabThk());   // 坯料中的数据
            chpEdit.setNCcmWth(pslabChos.getNSlabWth());
            chpEdit.setNPreHeatWgt(null); // 计划出两  次炉重量/收得率
            chpEdit.setCCcmWkst(updateTPssTaskListDto.getCSeatCode());
            chpEdit.setCLdWkst(stlCpct.getCBofNo());  // 电炉
            chpEdit.setCFirLfWkst(null); // 取精炼工序下的第一个精炼座次
            chpEdit.setTaskListId(pslabChos.getCTaskListId());

            pslabChos.setCHeatSeq(currentSequNum);
            currentSequNum = currentSequNum.add(BigDecimal.ONE);
            pslabChos.setCPlanHeatId(cPlanHeatNo);

            currentStoveWgt = currentStoveWgt.add(slabWgt);
        }
        pslabChosService.updateBatchById(pslabChosList);
        chpEditService.saveBatch(chpEditList);

        // 获取最后一炉，和最后一个坯料，进行补料
        TPssChpEdit lastChpEdit = chpEditList.get(chpEditList.size() - 1);
        TPssPslabChos lastPslabChos = pslabChosList.get(pslabChosList.size() - 1);
        BigDecimal slabWgt = lastPslabChos.getCSlabWgt();
        if (maxStoveWgt.subtract(currentStoveWgt).compareTo(slabWgt) >= 0) {
            List<AddTPssPslabChosDto> saveAddPslabChosList = Lists.newArrayList();
            while (maxStoveWgt.subtract(currentStoveWgt).compareTo(slabWgt) >= 0) {
                currentStoveWgt = currentStoveWgt.add(slabWgt);
                AddTPssPslabChosDto addTPssPslabChosDto = BeanUtil.copyProperties(lastPslabChos, AddTPssPslabChosDto.class);
                addTPssPslabChosDto.setCPlanSlabId(codeRuleClientV2.generateAndUse("PssPlanGrdCode").getDataOrThrow());
                addTPssPslabChosDto.setCHeatSeq(currentSequNum);
                currentSequNum = currentSequNum.add(BigDecimal.ONE);
                addTPssPslabChosDto.setCPlanHeatId(cPlanHeatNo);
                saveAddPslabChosList.add(addTPssPslabChosDto);
            }

            List<TPssPslabChos> newPslabChosList = BeanUtil.copyToList(saveAddPslabChosList, TPssPslabChos.class);
            pslabChosService.saveBatch(newPslabChosList);

            // 钢种收得率对象
            Set<String> cStlGrdCdSet = newPslabChosList.stream().map(TPssPslabChos::getCStlGrdCd).collect(Collectors.toSet());
            List<TPssRateStd> rateStdList =
                    rateStdService.list(Wrappers.<TPssRateStd>lambdaQuery().in(TPssRateStd::getCStlGrdCd, cStlGrdCdSet));
            Map<String, TPssRateStd> cStlGrdCdAndTPssRateStdMap =
                    rateStdList.stream().collect(Collectors.toMap(TPssRateStd::getCStlGrdCd, v -> v));

            // 质量编码
            Set<String> cMatQulCdSet = newPslabChosList.stream().map(TPssPslabChos::getCMatQulCd).collect(Collectors.toSet());
            List<TPssQualityCode> qualityCodeList =
                    qualityCodeService.list(Wrappers.<TPssQualityCode>lambdaQuery().in(TPssQualityCode::getCQualCode, cMatQulCdSet));
            Map<String, TPssQualityCode> codeAndQualityCodeMap =
                    qualityCodeList.stream().collect(Collectors.toMap(TPssQualityCode::getCQualCode, v -> v));

            // 生产任务单
            Set<String> cTaskIdSet = newPslabChosList.stream().map(TPssPslabChos::getCTaskListId).collect(Collectors.toSet());
            List<TPssAggregatePlan> aggregatePlanList =
                    aggregatePlanMapper.selectList(Wrappers.<TPssAggregatePlan>lambdaQuery().in(TPssAggregatePlan::getCProductTaskListId, cTaskIdSet));
            Map<String, TPssAggregatePlan> taskIdAndTPssAggregatePlanMap =
                    aggregatePlanList.stream().collect(Collectors.toMap(TPssAggregatePlan::getCProductTaskListId, v -> v));

            // 保存 t_pss_final_slab
            List<TPssFinalSlab> finalSlabList = new ArrayList<>();
            for (TPssPslabChos pslabChos : newPslabChosList) {
                TPssFinalSlab finalSlab = new TPssFinalSlab();
                finalSlab.setCFinalSlabId(pslabChos.getCPlanSlabId());
                finalSlab.setCSlabMatQulId(pslabChos.getCMatQulId());
                finalSlab.setCSlabMatQulCd(pslabChos.getCMatQulCd());
                finalSlab.setCSlabMatQulName(pslabChos.getCMatQulName());
                finalSlab.setNThk(pslabChos.getNSlabThk());
                finalSlab.setNWth(pslabChos.getNSlabWth());
                finalSlab.setNLth(pslabChos.getNSlabLen());
                finalSlab.setNCalWgt(pslabChos.getCSlabWgt());
                finalSlab.setCStlGrdCd(pslabChos.getCStlGrdCd());
                finalSlab.setCStlGrdDesc(pslabChos.getCStlGrdDesc());
                finalSlab.setNDegRatio(cStlGrdCdAndTPssRateStdMap.getOrDefault(pslabChos.getCStlGrdCd(), new TPssRateStd()).getNPlateSlabRate());
                finalSlab.setCStdSpec(codeAndQualityCodeMap.getOrDefault(pslabChos.getCMatQulCd(), new TPssQualityCode()).getCOpStdName());
                finalSlab.setNAsrollLth(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getNProdLenth());
                finalSlab.setNAsrollThk(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getNProdThk());
                finalSlab.setNAsrollWth(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getNProdWid());
                finalSlab.setCMatType(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getCProductType());
                finalSlab.setCOrderNo(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getCOrderNo());
                finalSlab.setCProductionLine(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getCPlanSteelLineId());
                finalSlab.setCProductionLineCode(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getCPlanSteelLineCd());
                finalSlab.setCProductionLineName(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getCPlanSteelLineName());
                finalSlab.setCProdMatId(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getCInventoryId());
                finalSlab.setCProdMatCode(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getCInventoryCd());
                finalSlab.setCProdMatName(taskIdAndTPssAggregatePlanMap.getOrDefault(pslabChos.getCTaskListId(), new TPssAggregatePlan()).getCInventoryName());
                finalSlabList.add(finalSlab);
            }

            // 调度单
            Set<String> orderNoSet = finalSlabList.stream().map(TPssFinalSlab::getCOrderNo).collect(Collectors.toSet());
            List<TPssDispatch> dispatchList =
                    dispatchService.list(Wrappers.<TPssDispatch>lambdaQuery().in(TPssDispatch::getCOrderNo, orderNoSet));
            Map<String, TPssDispatch> orderNoAndDispatchMap =
                    dispatchList.stream().collect(Collectors.toMap(TPssDispatch::getCOrderNo, Function.identity()));
            for (TPssFinalSlab finalSlab : finalSlabList) {
                finalSlab.setCDispatchId(orderNoAndDispatchMap.getOrDefault(finalSlab.getCOrderNo(), new TPssDispatch()).getCDispatchId());
            }
            finalSlabService.saveBatch(finalSlabList);
        }

        // 修改状态（订单、生产任务单、炉次浇次设计）
        aggregatePlanMapper.update(
                Wrappers.<TPssAggregatePlan>lambdaUpdate()
                        .in(TPssAggregatePlan::getCProductTaskListId, cTaskListIdSet)
                        .set(TPssAggregatePlan::getCPlanState, OrderStatusEnum.HEAT_DESIGN_COMPLETED.getCode())
        );
        orderInfoMapper.update(
                Wrappers.<TPssOrderInfo>lambdaUpdate()
                        .in(TPssOrderInfo::getCProductTaskListId, cTaskListIdSet)
                        .set(TPssOrderInfo::getCOrderState, OrderStatusEnum.HEAT_DESIGN_COMPLETED.getCode())
        );
        this.baseMapper.update(
                Wrappers.<TPssTaskList>lambdaUpdate()
                        .in(TPssTaskList::getCTaskListId, cTaskListIdSet)
                        .set(TPssTaskList::getCStatus, OrderStatusEnum.HEAT_DESIGN_COMPLETED.getCode())
        );

        return Boolean.TRUE;
    }

    @Override
    @GlobalTransactional
    public Boolean pourOrder(List<UpdateTPssTaskListDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) throw new MyException("参数不能为空");
        Set<String> statusSet =
                dtoList
                        .stream()
                        .map(UpdateTPssTaskListDto::getCStatus)
                        .filter(item -> !Objects.equals(OrderStatusEnum.HEAT_DESIGN_COMPLETED.getCode(), item))
                        .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(statusSet)) throw new MyException("只有`炉次设计完成`可以进行浇次设计");
        Set<String> cTaskListIdSet =
                dtoList.stream().map(UpdateTPssTaskListDto::getCTaskListId).collect(Collectors.toSet());
        Set<String> cMatQulCdSet =
                dtoList.stream().map(UpdateTPssTaskListDto::getCMatQulCd).collect(Collectors.toSet());
        Set<String> orderIdSet =
                dtoList.stream().map(UpdateTPssTaskListDto::getCOrderNo).collect(Collectors.toSet());

        // 获取生产任务单对应的所有炉次。t_pss_chp_edit
        List<TPssChpEdit> chpEditList =
                chpEditService.list(Wrappers.<TPssChpEdit>lambdaQuery().in(TPssChpEdit::getTaskListId, cTaskListIdSet));
        if (CollectionUtils.isEmpty(chpEditList)) throw new MyException("生产任务不存在炉次信息");
        Map<String, List<TPssChpEdit>> taskListIdAndTPssChpEditsMap =
                chpEditList.stream().collect(Collectors.groupingBy(TPssChpEdit::getTaskListId));

        // 获取当前质量编码的浇次炉次数标准t_pss_cast_cnvts
        List<TPssCastCnvts> castCnvtsList =
        pssCastCnvtsService.list(Wrappers.<TPssCastCnvts>lambdaQuery().in(TPssCastCnvts::getCMatQulCd, cMatQulCdSet));
        if (CollectionUtils.isEmpty(castCnvtsList)) throw new MyException("未获取到浇次炉次数标准");
        Map<String, TPssCastCnvts> cMatQulCdAndTPssCastCnvtsMap =
                castCnvtsList
                        .stream()
                        .collect(Collectors.toMap(TPssCastCnvts::getCMatQulCd, v -> v, (k1, k2) -> k1));

        // 工位冶炼时间t_pss_pos_time
        List<TPssPosTime> posTimeList = posTimeService.list();
        Map<String,Map<Long, TPssPosTime>> lineProGrdAndTPssPosTimeMap =
                posTimeList.stream()
                        .collect(Collectors.groupingBy(
                                TPssPosTime::getCStlGrdCd,  // 外层Map的key - 钢种代码
                                Collectors.toMap(
                                        TPssPosTime::getCProcess,  // 内层Map的key - 工序代码
                                        Function.identity(),        // 内层Map的value - TPssPosTime对象本身
                                        (existing, replacement) -> existing  // 合并规则：保留已存在的
                                )
                        ));

        // 钢包移动时间t_pss_pos_move_time
        List<TPssPosMoveTime> posMoveTimeList = posMoveTimeService.list();
        if (CollectionUtils.isEmpty(posMoveTimeList)) throw new MyException("未找到钢包移动时间");
        Map<String, TPssPosMoveTime> startEdnAndTPssPosMoveTimeMap =
                posMoveTimeList
                        .stream()
                        .collect(Collectors.toMap(item -> item.getCStartProcessCode() + item.getCStartProcessCode(), v -> v));

        // 工艺路径t_pss_techk_rout_lg_info
        List<TPssTechkRoutLgInfo> techkRoutLgInfoList = techkRoutLgInfoService.list(Wrappers.<TPssTechkRoutLgInfo>lambdaQuery().in(TPssTechkRoutLgInfo::getOrderId, orderIdSet));
        if (CollectionUtils.isEmpty(techkRoutLgInfoList)) throw new MyException("炼钢工艺路径参数为空");
        Map<String, TPssTechkRoutLgInfo> orderIdAndTPssTechkRoutLgInfoMap =
                techkRoutLgInfoList.stream().collect(Collectors.toMap(TPssTechkRoutLgInfo::getOrderId, v -> v));

        TPssCastpEdit castpEditServiceOne = castpEditService.getOne(Wrappers.<TPssCastpEdit>lambdaQuery().orderByDesc(TPssCastpEdit::getDtStartDt).last("LIMIT 1"));
        LocalDateTime dtStartDt = LocalDateTimeUtil.now();
        if (Objects.nonNull(castpEditServiceOne)) {
            dtStartDt = castpEditServiceOne.getDtStartDt();
        }
        TPssCheatPlan cheatPlanOne = heatidplanmangerService.getOne(Wrappers.<TPssCheatPlan>lambdaQuery().orderByDesc(TPssCheatPlan::getDtPreCcmEndTme).last("LIMIT 1"));
        LocalDateTime dtPreLdStrTme = LocalDateTimeUtil.now();
        if (Objects.nonNull(cheatPlanOne)) {
            dtPreLdStrTme = cheatPlanOne.getDtPreCcmEndTme();
        }

        // 计算需要多少浇次
        List<TPssCastpEdit> saveCastpEditList = Lists.newArrayList();
        for (UpdateTPssTaskListDto updateTPssTaskListDto : dtoList) {
            updateTPssTaskListDto.setCStatus(OrderStatusEnum.POURING_DESIGN_COMPLETED.getCode());
            String cTaskListId = updateTPssTaskListDto.getCTaskListId();
            List<TPssChpEdit> chpEditListGet = taskListIdAndTPssChpEditsMap.get(cTaskListId);
            if (CollectionUtils.isEmpty(chpEditListGet)) continue;
            // 炉次数量
            BigDecimal stoveNum = BigDecimal.valueOf(chpEditListGet.size());

            String cMatQulCd = updateTPssTaskListDto.getCMatQulCd();
            TPssCastCnvts castCnvtsGet = cMatQulCdAndTPssCastCnvtsMap.get(cMatQulCd);
            if (Objects.isNull(castCnvtsGet)) continue;
            // 每浇次最大炉数
            Long stoveNumMax = castCnvtsGet.getNHeatnumMax().longValue();

            // 生成浇次
            TPssCastpEdit castpEdit = new TPssCastpEdit();
            String cPlanCastId = codeRuleClientV2.generateAndUse("planPourOrderCode").getDataOrThrow();
            castpEdit.setCPlanCastId(cPlanCastId);
            castpEdit.setDtStartDt(dtStartDt.plusMinutes(30));
            castpEdit.setDtEndDt(castpEdit.getDtStartDt().plusHours(stoveNumMax));
            dtStartDt = castpEdit.getDtStartDt();
            saveCastpEditList.add(castpEdit);
            Long currentSequNum = 1L;
            TPssTechkRoutLgInfo tPssTechkRoutLgInfo = orderIdAndTPssTechkRoutLgInfoMap.get(updateTPssTaskListDto.getCOrderNo());
            if (Objects.isNull(tPssTechkRoutLgInfo)) {
                throw new MyException("炼钢工艺路径参数质量设计结果对象    没了");
            }
            for (TPssChpEdit chpEdit : chpEditListGet) {
                chpEdit.setCPlanCastId(cPlanCastId);

                chpEdit.setNCastHeatSeq(currentSequNum);

                BigDecimal workTime = getWorkTime(chpEdit, lineProGrdAndTPssPosTimeMap, tPssTechkRoutLgInfo);
                BigDecimal moveTime = getMoveTime(tPssTechkRoutLgInfo, startEdnAndTPssPosMoveTimeMap);


                chpEdit.setDtPreLdStrTme(dtPreLdStrTme.plusMinutes(30).plusMinutes(workTime.intValue()));
                chpEdit.setDtPreLdEndTme(chpEdit.getDtPreLdStrTme().plusMinutes(10));

                chpEdit.setDtFirLfSttime(chpEdit.getDtPreLdEndTme().plusMinutes(moveTime.intValue()));
                chpEdit.setDtFirLfEndtime(chpEdit.getDtFirLfSttime().plusMinutes(workTime.intValue()));

                chpEdit.setDtPlanLadleOpen(chpEdit.getDtFirLfEndtime().plusMinutes(moveTime.intValue()));

                chpEdit.setDtPreCcmStrTme(chpEdit.getDtPlanLadleOpen().plusMinutes(10));
                chpEdit.setDtPreCcmEndTme(chpEdit.getDtPreCcmStrTme().plusMinutes(workTime.intValue()));

                castpEdit.setNSlabThk(chpEdit.getNCcmThk());
                castpEdit.setNSlabWth(chpEdit.getNCcmWth());
                castpEdit.setCCastMachId(chpEdit.getCCcmWkst());
                castpEdit.setNHeatCnt(chpEdit.getNCastHeatCnt());
                castpEdit.setCStatus(OrderStatusEnum.POURING_DESIGN_COMPLETED.getCode());

                currentSequNum = currentSequNum + 1;
                if (currentSequNum > stoveNumMax) {
                    castpEdit = new TPssCastpEdit();
                    castpEdit.setCPlanCastId(cPlanCastId);
                    castpEdit.setDtStartDt(dtStartDt.plusMinutes(30));
                    castpEdit.setDtEndDt(castpEdit.getDtStartDt().plusHours(stoveNumMax));
                    dtStartDt = castpEdit.getDtStartDt();
                    castpEdit.setNCastEdtSeq(codeRuleClientV2.generateAndUse("planPourEditCode").getDataOrThrow());
                    currentSequNum = 1L;
                    cPlanCastId = codeRuleClientV2.generateAndUse("planPourOrderCode").getDataOrThrow();
                    saveCastpEditList.add(castpEdit);
                }

            }
        }

        chpEditService.updateBatchById(chpEditList);

        castpEditService.saveBatch(saveCastpEditList);

        List<TPssTaskList> tPssTaskLists = BeanUtil.copyToList(dtoList, TPssTaskList.class);
        this.baseMapper.updateById(tPssTaskLists);

        List<TPssPslabChos> pslabChosList =
                pslabChosService.list(Wrappers.<TPssPslabChos>lambdaQuery().in(TPssPslabChos::getCTaskListId, cTaskListIdSet));
        Map<String, List<TPssPslabChos>> cPlanHeatIdGroupMap =
                pslabChosList.stream().collect(Collectors.groupingBy(TPssPslabChos::getCPlanHeatId));

        // 将计划坯料临时表t_pss_pslab_chos中的数据插入到计划坯料表中t_pss_cut_plan
        List<TPssCutPlan> cutPlanList = BeanUtil.copyToList(pslabChosList, TPssCutPlan.class);
        cutPlanService.saveBatch(cutPlanList);

        // 根据最终设计完的浇次计划和炉次计划将设计好的炉次计划插入到t_pss_cheat_plan表中
        Map<String, List<TPssChpEdit>> cPlanCastIdGroupMap =
                chpEditList.stream().collect(Collectors.groupingBy(TPssChpEdit::getCPlanCastId));
        List<TPssCheatPlan> tCheatPlanList = Lists.newArrayList();
        for (TPssChpEdit chpEdit : chpEditList) {
            TPssCheatPlan cheatPlan = BeanUtil.copyProperties(chpEdit, TPssCheatPlan.class);
            cheatPlan.setNHeatEdtSeq(chpEdit.getNHeatEdtSeq());
            cheatPlan.setCPlanHeatId(chpEdit.getCPlanHeatId());
            cheatPlan.setCStlGrdCd(chpEdit.getCStlGrdCd());
            cheatPlan.setCStlGrdDesc(chpEdit.getCStlGrdDesc());
            cheatPlan.setCMatQulId(chpEdit.getCMatQulId());
            cheatPlan.setCMatQulCd(chpEdit.getCMatQulCd());
            cheatPlan.setCMatQulName(chpEdit.getCMatQulName());
            cheatPlan.setCPlRoute(chpEdit.getCPlRoute());
            cheatPlan.setNCcmThk(chpEdit.getNCcmThk());
            cheatPlan.setNCcmWth(chpEdit.getNCcmWth());
            cheatPlan.setNCcmTme(45L);
            cheatPlan.setNSlabCnt(Long.valueOf(cPlanHeatIdGroupMap.getOrDefault(chpEdit.getCPlanHeatId(),Lists.newArrayList()).size()));
            cheatPlan.setNPreHeatWgt(chpEdit.getNPreHeatWgt());
            cheatPlan.setNCastEdtSeq(chpEdit.getNCastEdtSeq());
            cheatPlan.setCPlanCastId(chpEdit.getCPlanCastId());
            cheatPlan.setNCastHeatSeq(chpEdit.getNCastHeatSeq());
            cheatPlan.setNCastHeatCnt(Long.valueOf(cPlanCastIdGroupMap.getOrDefault(chpEdit.getCPlanCastId(), new ArrayList<>()).size()));
            cheatPlan.setCLdWkstId(null);
            cheatPlan.setCLdWkst(chpEdit.getCLdWkst());
            cheatPlan.setCLdWkstName(null);
            cheatPlan.setDtPreLdStrTme(chpEdit.getDtPreLdStrTme());
            cheatPlan.setDtPreLdEndTme(chpEdit.getDtPreLdEndTme());
            cheatPlan.setCFirLfWkstId(null);
            cheatPlan.setCFirLfWkst(chpEdit.getCFirLfWkst());
            cheatPlan.setCFirLfWkstName(null);
            cheatPlan.setDtFirLfSttime(chpEdit.getDtFirLfSttime());
            cheatPlan.setDtFirLfEndtime(chpEdit.getDtFirLfEndtime());
            cheatPlan.setDtPlanLadleOpen(chpEdit.getDtPlanLadleOpen());
            cheatPlan.setCCcmWkst(chpEdit.getCCcmWkst());
            cheatPlan.setDtPreCcmStrTme(chpEdit.getDtPreCcmStrTme());
            cheatPlan.setDtPreCcmEndTme(chpEdit.getDtPreCcmEndTme());
            cheatPlan.setCStatus("1");
            cheatPlan.setNSlabLen(chpEdit.getCSlabLen());
            cheatPlan.setCTaskListId(chpEdit.getTaskListId());
            cheatPlan.setCPlanDatetime(LocalDateTime.now());
            tCheatPlanList.add(cheatPlan);
        }
        cheatPlanService.saveBatch(tCheatPlanList);

        return Boolean.TRUE;
    }

    private BigDecimal getMoveTime(TPssTechkRoutLgInfo tPssTechkRoutLgInfo, Map<String, TPssPosMoveTime> startEdnAndTPssPosMoveTimeMap) {
        String cTechRoute1Code = tPssTechkRoutLgInfo.getCTechRoute1Code();
        String cTechRoute2Code = tPssTechkRoutLgInfo.getCTechRoute2Code();
        String cTechRoute4Code = tPssTechkRoutLgInfo.getCTechRoute4Code();

        TPssPosMoveTime tPssPosMoveTime12 = startEdnAndTPssPosMoveTimeMap.get(cTechRoute1Code + cTechRoute2Code);
        if (Objects.nonNull(tPssPosMoveTime12)) return tPssPosMoveTime12.getNMoveTime();

        TPssPosMoveTime tPssPosMoveTime24 = startEdnAndTPssPosMoveTimeMap.get(cTechRoute2Code + cTechRoute4Code);
        if (Objects.nonNull(tPssPosMoveTime24)) return tPssPosMoveTime24.getNMoveTime();


        return BigDecimal.ZERO;
    }

    private static BigDecimal getWorkTime(TPssChpEdit tPssChpEdit, Map<String, Map<Long, TPssPosTime>> lineProGrdAndTPssPosTimeMap, TPssTechkRoutLgInfo tPssTechkRoutLgInfo) {
        Map<Long, TPssPosTime> stringTPssPosTimeMap = lineProGrdAndTPssPosTimeMap.get(tPssChpEdit.getCStlGrdCd());
        TPssPosTime posTime = null;
        String techRoute0 = tPssTechkRoutLgInfo.getTechRoute0();
        if (StringUtils.isNotBlank(techRoute0)) {
            List<String> list = Arrays.asList(techRoute0.split("/*"));
            for (String s : list) {
                TPssPosTime tPssPosTime = stringTPssPosTimeMap.get(s);
                if (Objects.nonNull(tPssPosTime)) {
                    posTime = tPssPosTime;
                }
            }

        }

        return Objects.isNull(posTime) ? BigDecimal.ZERO : posTime.getNWorkTime();
    }
}
