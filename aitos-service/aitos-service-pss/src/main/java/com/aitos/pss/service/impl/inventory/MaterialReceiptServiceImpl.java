package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.AddTPssMaterialReceiptDto;
import com.aitos.pss.dto.inventory.TPssMaterialReceiptPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMaterialReceiptDto;
import com.aitos.pss.entity.inventory.TPssMaterialReceipt;
import com.aitos.pss.mapper.inventory.TPssMaterialReceiptMapper;
import com.aitos.pss.service.inventory.IMaterialReceiptService;
import com.aitos.pss.vo.inventory.TPssMaterialReceiptPageVo;
import com.aitos.pss.vo.inventory.TPssMaterialReceiptVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class MaterialReceiptServiceImpl extends ServiceImpl<TPssMaterialReceiptMapper, TPssMaterialReceipt> implements IMaterialReceiptService {

    @Override
    public PageOutput<TPssMaterialReceiptPageVo> page(TPssMaterialReceiptPageDto dto) {
        LambdaQueryWrapper<TPssMaterialReceipt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCStorageNo()),TPssMaterialReceipt::getCStorageNo,dto.getCStorageNo())
                .like(StrUtil.isNotBlank(dto.getCMeasureNo()),TPssMaterialReceipt::getCMeasureNo,dto.getCMeasureNo())
                .like(StrUtil.isNotBlank(dto.getCPurchaseOrderLine()),TPssMaterialReceipt::getCPurchaseOrderLine,dto.getCPurchaseOrderLine())
                .like(StrUtil.isNotBlank(dto.getCMaterialName()),TPssMaterialReceipt::getCMaterialName,dto.getCMaterialName())
                .eq(ObjectUtil.isNotNull(dto.getNQuantity()),TPssMaterialReceipt::getNQuantity,dto.getNQuantity())
                .eq(ObjectUtil.isNotNull(dto.getNConfirmWeight()),TPssMaterialReceipt::getNConfirmWeight,dto.getNConfirmWeight())
                .between(ObjectUtil.isNotNull(dto.getDtReceiptDateStart()) && ObjectUtil.isNotNull(dto.getDtReceiptDateEnd()),TPssMaterialReceipt::getDtReceiptDate,dto.getDtReceiptDateStart(),dto.getDtReceiptDateEnd())
                .like(StrUtil.isNotBlank(dto.getCStorageLocation()),TPssMaterialReceipt::getCStorageLocation,dto.getCStorageLocation())
                .eq(StrUtil.isNotBlank(dto.getCStatus()),TPssMaterialReceipt::getCStatus,dto.getCStatus())
                .like(StrUtil.isNotBlank(dto.getCReceiptNo()),TPssMaterialReceipt::getCReceiptNo,dto.getCReceiptNo())
                .like(StrUtil.isNotBlank(dto.getCBatchNo()),TPssMaterialReceipt::getCBatchNo,dto.getCBatchNo())
                .like(StrUtil.isNotBlank(dto.getCPurchaseOrderNo()),TPssMaterialReceipt::getCPurchaseOrderNo,dto.getCPurchaseOrderNo())
                .like(StrUtil.isNotBlank(dto.getCMaterialCode()),TPssMaterialReceipt::getCMaterialCode,dto.getCMaterialCode())
                .eq(StrUtil.isNotBlank(dto.getCMaterialType()),TPssMaterialReceipt::getCMaterialType,dto.getCMaterialType())
                .eq(ObjectUtil.isNotNull(dto.getNMeasureWeight()),TPssMaterialReceipt::getNMeasureWeight,dto.getNMeasureWeight())
                .like(StrUtil.isNotBlank(dto.getCUnit()),TPssMaterialReceipt::getCUnit,dto.getCUnit())
                .like(StrUtil.isNotBlank(dto.getCSupplier()),TPssMaterialReceipt::getCSupplier,dto.getCSupplier())
                .like(StrUtil.isNotBlank(dto.getCStorageLocationName()),TPssMaterialReceipt::getCStorageLocationName,dto.getCStorageLocationName())
                .like(StrUtil.isNotBlank(dto.getCRemark()),TPssMaterialReceipt::getCRemark,dto.getCRemark())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssMaterialReceipt::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssMaterialReceipt::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssMaterialReceipt::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssMaterialReceipt::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssMaterialReceipt::getDtCreateDateTime)
                .select(TPssMaterialReceipt.class,x -> VoToColumnUtil.fieldsToColumns(TPssMaterialReceiptPageVo.class).contains(x.getProperty()));
        IPage<TPssMaterialReceipt> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        return ConventPage.getPageOutput(page, TPssMaterialReceiptPageVo.class);
    }

    @Override
    public TPssMaterialReceiptVo info(Long id) {
        TPssMaterialReceipt tPssMaterialReceipt = this.baseMapper.selectById(id);
        if (tPssMaterialReceipt == null) {
            throw new MyException("找不到此数据！");
        }
        return BeanUtil.toBean(tPssMaterialReceipt, TPssMaterialReceiptVo.class);
    }

    @Override
    public Long add(AddTPssMaterialReceiptDto dto) {
        TPssMaterialReceipt tPssMaterialReceipt = BeanUtil.toBean(dto, TPssMaterialReceipt.class);
        this.baseMapper.insert(tPssMaterialReceipt);
        return tPssMaterialReceipt.getNId();
    }

    @Override
    public Boolean update(UpdateTPssMaterialReceiptDto dto) {
        TPssMaterialReceipt tPssMaterialReceipt = BeanUtil.toBean(dto, TPssMaterialReceipt.class);
        this.baseMapper.updateById(tPssMaterialReceipt);
        return Boolean.TRUE;
    }
}
