package com.aitos.pss.service.impl.qualitymanger;

import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.entity.qualitymanger.TPssPslabChos;
import com.aitos.pss.mapper.qualitymanger.TPssPslabChosMapper;
import com.aitos.pss.service.qualitymanger.IPslabChosService;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class PslabChosServiceImpl extends ServiceImpl<TPssPslabChosMapper, TPssPslabChos> implements IPslabChosService {
}
