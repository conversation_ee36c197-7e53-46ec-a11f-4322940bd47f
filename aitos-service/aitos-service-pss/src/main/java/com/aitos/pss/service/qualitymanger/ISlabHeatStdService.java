
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabHeatStdDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabHeatStdPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabHeatStdDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabHeatStd;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdPageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-21
* @Version 1.0
*/

public interface ISlabHeatStdService extends IService<TPssSlabHeatStd> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssSlabHeatStdPageVo> queryPage(@Valid TPssSlabHeatStdPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssSlabHeatStdVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssSlabHeatStdVo add(@Valid AddTPssSlabHeatStdDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssSlabHeatStdVo update(@Valid UpdateTPssSlabHeatStdDto dto);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssSlabHeatStdPageDto dto, Boolean isTemplate);
}
