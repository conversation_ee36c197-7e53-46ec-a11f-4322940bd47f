package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssPFaceDto;
import com.aitos.pss.dto.qualitymanger.TPssPFacePageDto;
import com.aitos.pss.entity.qualitymanger.TPssPFace;
import com.aitos.pss.vo.qualitymanger.TPssPFacePageVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

public interface ITPssPFaceService extends IService<TPssPFace> {

    PageOutput<TPssPFacePageVo> queryPage(@Valid TPssPFacePageDto dto);

    /**
     * 表面判定
     * @param dtoList
     * @return
     */
    Boolean factDecision(@Valid List<AddTPssPFaceDto> dtoList);
}