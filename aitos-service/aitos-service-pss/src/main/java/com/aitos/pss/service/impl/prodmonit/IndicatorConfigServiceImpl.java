package com.aitos.pss.service.impl.prodmonit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.prodmonit.AddTPssIndicatorsDto;
import com.aitos.pss.dto.prodmonit.TPssIndicatorsPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssIndicatorsDto;
import com.aitos.pss.entity.prodmonit.TPssIndicators;
import com.aitos.pss.mapper.prodmonit.TPssIndicatorsMapper;
import com.aitos.pss.service.prodmonit.IIndicatorConfigService;
import com.aitos.pss.vo.prodmonit.TPssIndicatorsPageVo;
import com.aitos.pss.vo.prodmonit.TPssIndicatorsVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class IndicatorConfigServiceImpl extends ServiceImpl<TPssIndicatorsMapper, TPssIndicators> implements IIndicatorConfigService {
    @Override
    public PageOutput<TPssIndicatorsPageVo> queryPage(TPssIndicatorsPageDto dto) {
        LambdaQueryWrapper<TPssIndicators> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getNEquipmentId()),TPssIndicators::getNEquipmentId,dto.getNEquipmentId())
                .like(StrUtil.isNotBlank(dto.getCName()),TPssIndicators::getCName,dto.getCName())
                .between(
                        ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),
                        TPssIndicators::getDtCreateDateTime,
                        dto.getDtCreateDateTimeStart(),
                        dto.getDtCreateDateTimeEnd()
                ).orderByDesc(TPssIndicators::getNId)
                .select(TPssIndicators.class,x -> VoToColumnUtil.fieldsToColumns(TPssIndicatorsPageVo.class).contains(x.getProperty()));
        IPage<TPssIndicators> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssIndicatorsPageVo.class);
    }

    @Override
    public List<TPssIndicatorsPageVo> queryList(TPssIndicatorsPageDto dto) {
        LambdaQueryWrapper<TPssIndicators> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getNEquipmentId()),TPssIndicators::getNEquipmentId,dto.getNEquipmentId())
                .like(StrUtil.isNotBlank(dto.getCName()),TPssIndicators::getCName,dto.getCName())
                .between(
                        ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),
                        TPssIndicators::getDtCreateDateTime,
                        dto.getDtCreateDateTimeStart(),
                        dto.getDtCreateDateTimeEnd()
                ).orderByDesc(TPssIndicators::getNId)
                .select(TPssIndicators.class,x -> VoToColumnUtil.fieldsToColumns(TPssIndicatorsPageVo.class).contains(x.getProperty()));
        List<TPssIndicators> list = this.baseMapper.selectList(queryWrapper);

        return BeanUtil.copyToList(list, TPssIndicatorsPageVo.class);
    }

    @Override
    public TPssIndicatorsVo queryInfo(Long id) {
        TPssIndicators tPssIndicators = this.baseMapper.selectById(id);
        if (tPssIndicators == null) {
            throw new MyException("找不到此数据！");
        }
        return BeanUtil.copyProperties(tPssIndicators,TPssIndicatorsVo.class);
    }

    @Override
    public TPssIndicatorsVo add(AddTPssIndicatorsDto dto) {
        Long dbCount =
                this.baseMapper.selectCount(Wrappers.<TPssIndicators>lambdaQuery().eq(TPssIndicators::getCCode, dto.getCCode()));
        if (dbCount > 0) throw new MyException("指标编码不能重复");

        TPssIndicators tPssIndicators = BeanUtil.toBean(dto, TPssIndicators.class);
        this.baseMapper.insert(tPssIndicators);

        return BeanUtil.copyProperties(tPssIndicators, TPssIndicatorsVo.class);
    }

    @Override
    public TPssIndicatorsVo update(UpdateTPssIndicatorsDto dto) {
        Long dbCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssIndicators>lambdaQuery()
                                .eq(TPssIndicators::getCCode, dto.getCCode())
                                .ne(TPssIndicators::getNId, dto.getNId())
                );
        if (dbCount > 0) throw new MyException("指标编码不能重复");

        TPssIndicators tPssIndicators = BeanUtil.toBean(dto, TPssIndicators.class);
        this.baseMapper.updateById(tPssIndicators);

        return BeanUtil.copyProperties(tPssIndicators, TPssIndicatorsVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssIndicatorsPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssIndicatorsPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssIndicators.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssIndicatorsPageDto dto, Boolean isTemplate) {
        List<TPssIndicatorsPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssIndicatorsPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return RT.fileStream(resultBot.toByteArray(), "IndicatorConfig" + ExcelTypeEnum.XLSX.getValue());
    }
}
