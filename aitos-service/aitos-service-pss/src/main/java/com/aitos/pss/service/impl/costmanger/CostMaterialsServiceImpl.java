package com.aitos.pss.service.impl.costmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.costmanger.AddTPssFcsMatCodeDto;
import com.aitos.pss.dto.costmanger.TPssFcsMatCodePageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsMatCodeDto;
import com.aitos.pss.entity.costmanger.TPssFcsMatCode;
import com.aitos.pss.mapper.costmanger.TPssFcsMatCodeMapper;
import com.aitos.pss.service.costmanger.ICostMaterialsService;
import com.aitos.pss.vo.costmanger.TPssFcsMatCodePageVo;
import com.aitos.pss.vo.costmanger.TPssFcsMatCodeVo;
import com.aitos.system.client.v2.ICodeRuleClientV2;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-26
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class CostMaterialsServiceImpl extends ServiceImpl<TPssFcsMatCodeMapper, TPssFcsMatCode> implements ICostMaterialsService {

    private final ICodeRuleClientV2 codeRuleClientV2;

    @Override
    public PageOutput<TPssFcsMatCodePageVo> queryPage(TPssFcsMatCodePageDto dto) {
        LambdaQueryWrapper<TPssFcsMatCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCMatName()),TPssFcsMatCode::getCMatName,dto.getCMatName())
                .like(StrUtil.isNotBlank(dto.getCMatCode()),TPssFcsMatCode::getCMatCode,dto.getCMatCode())
                .select(TPssFcsMatCode.class,x -> VoToColumnUtil.fieldsToColumns(TPssFcsMatCodePageVo.class).contains(x.getProperty()));
        IPage<TPssFcsMatCode> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssFcsMatCodePageVo.class);
    }

    @Override
    public TPssFcsMatCodeVo queryInfo(Long id) {
        TPssFcsMatCode tPssFcsMatCode = this.baseMapper.selectById(id);
        if (tPssFcsMatCode == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssFcsMatCode, TPssFcsMatCodeVo.class);
    }

    @Override
    public TPssFcsMatCodeVo add(AddTPssFcsMatCodeDto dto) {
        TPssFcsMatCode tPssFcsMatCode = BeanUtil.toBean(dto, TPssFcsMatCode.class);
        tPssFcsMatCode.setCMatClassCode(codeRuleClientV2.generateAndUse("fcsMatCode").getDataOrThrow());
        this.baseMapper.insert(tPssFcsMatCode);

        return BeanUtil.copyProperties(tPssFcsMatCode, TPssFcsMatCodeVo.class);
    }

    @Override
    public TPssFcsMatCodeVo update(UpdateTPssFcsMatCodeDto dto) {
        TPssFcsMatCode tPssFcsMatCode = BeanUtil.toBean(dto, TPssFcsMatCode.class);
        this.baseMapper.updateById(tPssFcsMatCode);

        return BeanUtil.copyProperties(tPssFcsMatCode, TPssFcsMatCodeVo.class);
    }
}
