package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPssWareHouseDto;
import com.aitos.pss.dto.inventory.TPssWareHousePageDto;
import com.aitos.pss.dto.inventory.UpdateTPssWareHouseDto;
import com.aitos.pss.service.inventory.IWareHouseService;
import com.aitos.pss.vo.inventory.TPssWareHousePageVo;
import com.aitos.pss.vo.inventory.TPssWareHouseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 库区库位管理
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/warehouse")
@Tag(name = "/pss"  + "/warehouse",description = "库区库位管理代码")
@AllArgsConstructor
public class WareHouseController {


    private final IWareHouseService wareHouseService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssWareHouse列表(分页)")
    @SaCheckPermission(value = "warehouse:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssWareHousePageVo>> page(@Valid TPssWareHousePageDto dto){

        return RT.ok(wareHouseService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssWareHouse信息")
    @SaCheckPermission(value = "warehouse:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssWareHouseVo> info(@RequestParam Long id){

        return RT.ok(wareHouseService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssWareHouse")
    @SaCheckPermission(value = "warehouse:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "库区库位管理新增数据")
    public RT<TPssWareHouseVo> add(@Valid @RequestBody AddTPssWareHouseDto dto){

        return RT.ok(wareHouseService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssWareHouse")
    @SaCheckPermission(value = "warehouse:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "库区库位管理修改数据")
    public RT<TPssWareHouseVo> update(@Valid @RequestBody UpdateTPssWareHouseDto dto){
        return RT.ok(wareHouseService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "warehouse:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "库区库位管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(wareHouseService.removeBatchByIds(ids));
    }

}
