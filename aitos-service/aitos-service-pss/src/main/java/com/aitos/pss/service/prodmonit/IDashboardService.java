package com.aitos.pss.service.prodmonit;

import com.aitos.pss.dto.prodmonit.DashboardAggregationDto;
import com.aitos.pss.dto.prodmonit.QueryDashboardDto;
import com.aitos.pss.vo.chart.ChartVo;
import com.aitos.pss.vo.chart.LineChartSeries;
import com.aitos.pss.vo.chart.MultiLineChartSeries;
import com.aitos.pss.vo.chart.PieChartItem;

import javax.validation.Valid;
import java.util.List;

public interface IDashboardService {

    /**
     * 聚合查询
     * @param dto
     * @return
     */
    DashboardAggregationDto queryAggregation(@Valid QueryDashboardDto dto);

    /**
     * 生产指标实时监控
     * @param dto
     * @return
     */
    ChartVo<List<LineChartSeries>> queryPCR(@Valid QueryDashboardDto dto);

    /**
     * 设备状态
     * @param dto
     * @return
     */
    ChartVo<List<PieChartItem>> queryEquipmentStatus(@Valid QueryDashboardDto dto);

    /**
     * 温度趋势
     * @param dto
     * @return
     */
    ChartVo<List<MultiLineChartSeries>> queryTemperatureTrend(@Valid QueryDashboardDto dto);
}
