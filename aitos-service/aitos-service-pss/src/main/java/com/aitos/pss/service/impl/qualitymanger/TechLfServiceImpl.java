package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssTechLfDto;
import com.aitos.pss.dto.qualitymanger.TPssTechLfPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechLfDto;
import com.aitos.pss.entity.qualitymanger.TPssTechLf;
import com.aitos.pss.mapper.qualitymanger.TPssTechLfMapper;
import com.aitos.pss.service.qualitymanger.ITechLfService;
import com.aitos.pss.vo.qualitymanger.TPssTechLfPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechLfVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TechLfServiceImpl extends ServiceImpl<TPssTechLfMapper, TPssTechLf> implements ITechLfService {

    @Override
    public PageOutput<TPssTechLfPageVo> queryPage(TPssTechLfPageDto dto) {
        LambdaQueryWrapper<TPssTechLf> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualityId()), TPssTechLf::getCQualityId, dto.getCQualityId())
                .eq(Objects.nonNull(dto.getCStlGrdCd()),TPssTechLf::getCStlGrdCd,dto.getCStlGrdCd())
                .orderByDesc(TPssTechLf::getNId)
                .select(TPssTechLf.class,x -> VoToColumnUtil.fieldsToColumns(TPssTechLfPageVo.class).contains(x.getProperty()));
        IPage<TPssTechLf> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssTechLfPageVo.class);
    }

    @Override
    public TPssTechLfVo queryInfo(Long id) {
        TPssTechLf tPssTechLf = this.baseMapper.selectById(id);
        if (tPssTechLf == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssTechLf, TPssTechLfVo.class);
    }

    @Override
    public TPssTechLfVo add(AddTPssTechLfDto dto) {
        TPssTechLf tPssTechLf = BeanUtil.toBean(dto, TPssTechLf.class);
        this.baseMapper.insert(tPssTechLf);

        return BeanUtil.copyProperties(tPssTechLf, TPssTechLfVo.class);
    }

    @Override
    public TPssTechLfVo update(UpdateTPssTechLfDto dto) {
        TPssTechLf tPssTechLf = BeanUtil.toBean(dto, TPssTechLf.class);
        this.baseMapper.updateById(tPssTechLf);

        return BeanUtil.copyProperties(tPssTechLf, TPssTechLfVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssTechLfPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssTechLfPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssTechLf.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssTechLfPageDto dto, Boolean isTemplate) {
        List<TPssTechLfPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssTechLfPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "TechLf" + ExcelTypeEnum.XLSX.getValue());
    }
}
