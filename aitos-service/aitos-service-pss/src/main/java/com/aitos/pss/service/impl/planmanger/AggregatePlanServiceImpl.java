package com.aitos.pss.service.impl.planmanger;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.constant.GlobalConstant;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.*;
import com.aitos.pss.dto.qualitymanger.AddTPssPslabChosDto;
import com.aitos.pss.entity.planmanger.*;
import com.aitos.pss.entity.qualitymanger.TPssPslabChos;
import com.aitos.pss.enums.OrderStatusEnum;
import com.aitos.pss.mapper.planmanger.TPssAggregatePlanMapper;
import com.aitos.pss.mapper.planmanger.TPssOrderInfoMapper;
import com.aitos.pss.service.planmanger.*;
import com.aitos.pss.service.qualitymanger.IPslabChosService;
import com.aitos.pss.vo.planmanger.TPssAggregatePlanPageVo;
import com.aitos.pss.vo.planmanger.TPssAggregatePlanVo;
import com.aitos.system.client.v2.ICodeRuleClientV2;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.AllArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class AggregatePlanServiceImpl extends ServiceImpl<TPssAggregatePlanMapper, TPssAggregatePlan> implements IAggregatePlanService {

    private static final BigDecimal DENSITY = new BigDecimal("0.00000000785");

    private final ITPssRateStdService pssRateStdService;

    private final ICodeRuleClientV2 codeRuleClientV2;

    private final IDesignSlabTempService designSlabTempService;

    private final IFinalSlabService finalSlabService;

    private final IPslabChosService pslabChosService;

    private final TPssOrderInfoMapper orderInfoMapper;

    private final IDispatchService dispatchService;

    private final IOrderCombinService orderCombinService;

    private final ITPssTaskListService taskListService;

    @Override
    public PageOutput<TPssAggregatePlanPageVo> queryPage(TPssAggregatePlanPageDto dto) {
        LambdaQueryWrapper<TPssAggregatePlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCOrderNo()),TPssAggregatePlan::getCOrderNo,dto.getCOrderNo())
                .like(StrUtil.isNotBlank(dto.getCProductTaskListId()),TPssAggregatePlan::getCProductTaskListId,dto.getCProductTaskListId())
                .eq(Objects.nonNull(dto.getCPlanSteelLineId()),TPssAggregatePlan::getCPlanSteelLineId,dto.getCPlanSteelLineId())
                .eq(Objects.nonNull(dto.getCPlanLineId()),TPssAggregatePlan::getCPlanLineId,dto.getCPlanLineId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssAggregatePlan::getCStlGrdCd,dto.getCStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCProductType()),TPssAggregatePlan::getCProductType,dto.getCProductType())
                .like(StrUtil.isNotBlank(dto.getCPlanState()),TPssAggregatePlan::getCPlanState,dto.getCPlanState())
                .orderByDesc(TPssAggregatePlan::getNId)
                .select(TPssAggregatePlan.class,x -> VoToColumnUtil.fieldsToColumns(TPssAggregatePlanPageVo.class).contains(x.getProperty()));
        IPage<TPssAggregatePlan> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssAggregatePlanPageVo.class);
    }

    @Override
    public TPssAggregatePlanVo queryInfo(Long id) {
        TPssAggregatePlan tPssAggregatePlan = this.baseMapper.selectById(id);
        if (tPssAggregatePlan == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssAggregatePlan, TPssAggregatePlanVo.class);
    }

    @Override
    public TPssAggregatePlanVo add(AddTPssAggregatePlanDto dto) {
        TPssAggregatePlan tPssAggregatePlan = BeanUtil.toBean(dto, TPssAggregatePlan.class);
        this.baseMapper.insert(tPssAggregatePlan);

        return BeanUtil.copyProperties(tPssAggregatePlan, TPssAggregatePlanVo.class);
    }

    @Override
    public TPssAggregatePlanVo update(UpdateTPssAggregatePlanDto dto) {
        TPssAggregatePlan tPssAggregatePlan = BeanUtil.toBean(dto, TPssAggregatePlan.class);
        this.baseMapper.insert(tPssAggregatePlan);

        return BeanUtil.copyProperties(tPssAggregatePlan, TPssAggregatePlanVo.class);
    }

    @Override
    public Boolean deleteBathId(List<Long> ids) {
        this.baseMapper.deleteByIds(ids);

        return Boolean.TRUE;
    }

    @Override
    @GlobalTransactional
    public Boolean plineAllocation(UpdateTPssAggregatePlanDto dto) {
        dto.setNMatItem(dto.getNPlanSlabThk() + "*" + dto.getNPlanSlabWid());
        BigDecimal nPlanSteelWgt = dto.getNPlanSteelWgt();
        dto.setNPlanSteelWgt(null);
        this.baseMapper.updateById(BeanUtil.copyProperties(dto, TPssAggregatePlan.class));
        TPssAggregatePlan aggregatePlan = this.baseMapper.selectById(dto.getNId());

        // 计算单支重量
        BigDecimal nPlanSlabThk = aggregatePlan.getNPlanSlabThk();
        BigDecimal nPlanSlabWid = aggregatePlan.getNPlanSlabWid();
        BigDecimal nPlanSlabLen = aggregatePlan.getNPlanSlabLen();
        BigDecimal singleHeavy = DENSITY.multiply(nPlanSlabThk).multiply(nPlanSlabWid).multiply(nPlanSlabLen);

        // 计算总数量向上取整
        BigDecimal totalNum =
                nPlanSteelWgt
                        .divide(singleHeavy, MathContext.DECIMAL128)
                        .setScale(0, RoundingMode.UP);

        TPssRateStd rateStd =
                pssRateStdService.getOne(Wrappers.<TPssRateStd>lambdaQuery().eq(TPssRateStd::getCStlGrdCd, aggregatePlan.getCStlGrdCd()));
        BigDecimal nPlateSlabRate = rateStd.getNPlateSlabRate();
        BigDecimal nSlabStlRate = rateStd.getNSlabStlRate();

        // 插入 坯料设计临时表
        List<AddTPssFinalDesignSlabTempDto> addFinalDesignSlabTempDtoList = Lists.newArrayList();
        BigDecimal tempNum =
                nPlanSteelWgt
                        .divide(nPlateSlabRate, MathContext.DECIMAL128)
                        .divide(nSlabStlRate, MathContext.DECIMAL128)
                        .divide(singleHeavy, MathContext.DECIMAL128)
                        .setScale(0, RoundingMode.UP);
        AddTPssFinalDesignSlabTempDto addFinalDesignSlabTempDto = new AddTPssFinalDesignSlabTempDto();
        String taskCode = codeRuleClientV2.generateAndUse("PssTaskCode").getDataOrThrow();
        addFinalDesignSlabTempDto.setCCtaskListId(taskCode);
        addFinalDesignSlabTempDto.setCSlabMatQulId(aggregatePlan.getCMatQulId());
        addFinalDesignSlabTempDto.setCSlabMatQulCd(aggregatePlan.getCMatQulCode());
        addFinalDesignSlabTempDto.setCSlabMatQulName(aggregatePlan.getCMatQulName());
        addFinalDesignSlabTempDto.setNLth(nPlanSlabLen);
        addFinalDesignSlabTempDto.setNWth(nPlanSlabWid);
        addFinalDesignSlabTempDto.setNThk(nPlanSlabThk);
        addFinalDesignSlabTempDto.setNCalWgt(singleHeavy);
        addFinalDesignSlabTempDto.setCStlGrdCd(aggregatePlan.getCStlGrdCd());
        addFinalDesignSlabTempDto.setCStlGrdDesc(aggregatePlan.getCStlGrdDesc());
        addFinalDesignSlabTempDto.setNDegRatio(nPlateSlabRate);
        addFinalDesignSlabTempDto.setCOrderNo(aggregatePlan.getCOrderNo());
        for (int i = 0; i < Integer.parseInt(tempNum.toString()); i++) {
            AddTPssFinalDesignSlabTempDto addFinalDesignSlabTempDtoTmp =
                    BeanUtil.copyProperties(addFinalDesignSlabTempDto, AddTPssFinalDesignSlabTempDto.class);
            addFinalDesignSlabTempDtoTmp.setCSlabId(codeRuleClientV2.generateAndUse("PssPlanGrdCode").getDataOrThrow());

            addFinalDesignSlabTempDtoList.add(addFinalDesignSlabTempDtoTmp);
        }
        List<TPssFinalDesignSlabTemp> designSlabTempList =
                BeanUtil.copyToList(addFinalDesignSlabTempDtoList, TPssFinalDesignSlabTemp.class);
        designSlabTempService.saveBatch(designSlabTempList);

        // 插入 t_pss_final_slab
        List<AddTPssFinalSlabDto> addFinalSlabDtoList = Lists.newArrayList();
        for (TPssFinalDesignSlabTemp designSlabTemp : designSlabTempList) {
            AddTPssFinalSlabDto addFinalSlabDto = new AddTPssFinalSlabDto();
            addFinalSlabDto.setCFinalSlabId(String.valueOf(designSlabTemp.getCSchId()));
            addFinalSlabDto.setCStlGrdCd(designSlabTemp.getCStlGrdCd());
            addFinalSlabDto.setCStlGrdDesc(designSlabTemp.getCStlGrdDesc());
            addFinalSlabDto.setNDegRatio(designSlabTemp.getNDegRatio());
            addFinalSlabDto.setNAsrollLth(aggregatePlan.getNProdLenth());
            addFinalSlabDto.setNAsrollThk(aggregatePlan.getNProdThk());
            addFinalSlabDto.setNAsrollWth(aggregatePlan.getNProdWid());
            addFinalSlabDto.setCOrderNo(designSlabTemp.getCOrderNo());
            addFinalSlabDto.setCProductionLine(aggregatePlan.getCPlanLineId());
            addFinalSlabDto.setCProductionLineCode(aggregatePlan.getCPlanLineCd());
            addFinalSlabDto.setCProductionLineName(aggregatePlan.getCPlanLineName());
            addFinalSlabDto.setCProdMatId(aggregatePlan.getCInventoryId());
            addFinalSlabDto.setCProdMatCode(aggregatePlan.getCInventoryCd());
            addFinalSlabDto.setCProdMatName(aggregatePlan.getCInventoryName());
            addFinalSlabDtoList.add(addFinalSlabDto);
        }
        List<TPssFinalSlab> finalSlabList = BeanUtil.copyToList(addFinalSlabDtoList, TPssFinalSlab.class);
        finalSlabService.saveBatch(finalSlabList);

        // 插入 t_pss_pslab_chos
        TPssOrderInfo orderInfo =
                orderInfoMapper.selectOne(Wrappers.<TPssOrderInfo>lambdaQuery().eq(TPssOrderInfo::getCOrderNo, aggregatePlan.getCOrderNo()));
        List<AddTPssPslabChosDto> addPslabChosDtoList = Lists.newArrayList();
        for (TPssFinalDesignSlabTemp designSlabTemp : designSlabTempList) {
            AddTPssPslabChosDto addPslabChosDto = new AddTPssPslabChosDto();
            addPslabChosDto.setCPlanSlabId(designSlabTemp.getCSlabId());
            addPslabChosDto.setCTaskListId(taskCode);
            addPslabChosDto.setCStlGrdCd(designSlabTemp.getCStlGrdCd());
            addPslabChosDto.setCStlGrdDesc(designSlabTemp.getCStlGrdDesc());
            addPslabChosDto.setCMatQulId(designSlabTemp.getCSlabMatQulId());
            addPslabChosDto.setCMatQulCd(designSlabTemp.getCSlabMatQulCd());
            addPslabChosDto.setCMatQulName(designSlabTemp.getCSlabMatQulName());
            addPslabChosDto.setNSlabThk(designSlabTemp.getNThk());
            addPslabChosDto.setNSlabWth(designSlabTemp.getNWth());
            addPslabChosDto.setNSlabLen(designSlabTemp.getNLth());
            addPslabChosDto.setCSlabType(aggregatePlan.getCProductType());
            addPslabChosDto.setCUseStd(orderInfo.getCStdcode());
            addPslabChosDto.setCCastId(aggregatePlan.getCSeat());
            addPslabChosDto.setCCastCode(aggregatePlan.getCSeatCode());
            addPslabChosDto.setCCastName(aggregatePlan.getCSeatName());
            addPslabChosDto.setCMatId(aggregatePlan.getCInventoryId());
            addPslabChosDto.setCMatCd(aggregatePlan.getCInventoryCd());
            addPslabChosDto.setCMatName(aggregatePlan.getCInventoryName());
            addPslabChosDto.setCSlabWgt(nPlanSteelWgt);
            addPslabChosDtoList.add(addPslabChosDto);
        }
        List<TPssPslabChos> pslabChosList = BeanUtil.copyToList(addPslabChosDtoList, TPssPslabChos.class);
        pslabChosService.saveBatch(pslabChosList);


        // 插入t_pss_task_list
        List<Long> userChangesDeptIdList =
                (List<Long>) StpUtil.getTokenSession().get(GlobalConstant.LOGIN_USER_CHARGE_DEPT_LIST_KEY);
        AddTPssTaskListDto addTaskListDto = new AddTPssTaskListDto();
        addTaskListDto.setCTaskListId(taskCode);
        addTaskListDto.setCProdType(aggregatePlan.getCProductType());
        addTaskListDto.setCCrtDept(String.valueOf(userChangesDeptIdList.get(0)));
        addTaskListDto.setDtCrtTime(LocalDateTime.now());
        addTaskListDto.setCCrtEmp(String.valueOf(StpUtil.getLoginIdAsLong()));
        addTaskListDto.setCPrcDept(String.valueOf(userChangesDeptIdList.get(0)));
        addTaskListDto.setCStlGrdCd(aggregatePlan.getCStlGrdCd());
        addTaskListDto.setCStlGrdDesc(aggregatePlan.getCStlGrdDesc());
        addTaskListDto.setCMatQulId(aggregatePlan.getCMatQulId());
        addTaskListDto.setCMatQulCd(aggregatePlan.getCMatQulCode());
        addTaskListDto.setCMatQulName(aggregatePlan.getCMatQulName());
        addTaskListDto.setNSlabThk(nPlanSlabThk);
        addTaskListDto.setNSlabWth(nPlanSlabWid);
        addTaskListDto.setNSlabLen(nPlanSlabLen);
        addTaskListDto.setNSlabCnt(tempNum.longValue());
        addTaskListDto.setNSlabWgt(nPlanSteelWgt);
        addTaskListDto.setCStatus(OrderStatusEnum.TASK_ISSUED.getCode());
        addTaskListDto.setCOrderNo(aggregatePlan.getCOrderNo());
        taskListService.save(BeanUtil.copyProperties(addTaskListDto, TPssTaskList.class));

        // 删除临时表 t_pss_final_design_slab_temp
        designSlabTempService.remove(Wrappers.<TPssFinalDesignSlabTemp>lambdaQuery().eq(TPssFinalDesignSlabTemp::getCOrderNo, aggregatePlan.getCOrderNo()));

        // 插入 t_pss_dispatch
        AddTPssDispatchDto addTPssDispatchDto = new AddTPssDispatchDto();
        addTPssDispatchDto.setCDispatchId(codeRuleClientV2.generateAndUse("PssDispatchCode").getDataOrThrow());
        addTPssDispatchDto.setCLineId(orderInfo.getCProductLineId());
        addTPssDispatchDto.setCLineNo(orderInfo.getCProductLineCd());
        addTPssDispatchDto.setCLineName(orderInfo.getCProductLineName());
        addTPssDispatchDto.setCStlGrdCd(orderInfo.getCStlGrdCd());
        addTPssDispatchDto.setCStlGrdDesc(orderInfo.getCStlGrdName());
        addTPssDispatchDto.setNSpec(orderInfo.getNProdThk().toString() + "*" + orderInfo.getNProdWid().toString());
        addTPssDispatchDto.setNTalWgt(nPlanSteelWgt);
        addTPssDispatchDto.setNTalCnt(tempNum);
        addTPssDispatchDto.setCReleaseEmp(String.valueOf(StpUtil.getLoginIdAsLong()));
        addTPssDispatchDto.setDtReleaseTime(LocalDateTime.now());
        addTPssDispatchDto.setCProductPlanNo(aggregatePlan.getCAggregatePlanId());
        addTPssDispatchDto.setCSalePlanNo(orderInfo.getCSalesPlanId());
        addTPssDispatchDto.setCProdType(orderInfo.getCProductType());
        addTPssDispatchDto.setCSizeProperty(orderInfo.getCSizeProperty());
        addTPssDispatchDto.setCMatId(orderInfo.getCInventoryId());
        addTPssDispatchDto.setCMatCode(orderInfo.getCInventoryCd());
        addTPssDispatchDto.setCMatName(orderInfo.getCInventoryName());
        addTPssDispatchDto.setNProdLen(orderInfo.getNProdLenth());
        addTPssDispatchDto.setCMatQulId(orderInfo.getCQualId());
        addTPssDispatchDto.setCMatQulCd(orderInfo.getCQualCode());
        addTPssDispatchDto.setCMatQulName(orderInfo.getCQualCodeName());
        addTPssDispatchDto.setCStatus("1");
        addTPssDispatchDto.setCStdSpec(orderInfo.getCStdcode());
        addTPssDispatchDto.setNProductPlanSn(null);
        addTPssDispatchDto.setCPreStatus(0);
        addTPssDispatchDto.setCPreStatus(1);
        addTPssDispatchDto.setCExgProdLotFl("0");
        addTPssDispatchDto.setCCustomerCd(orderInfo.getCCustomerCd());
        addTPssDispatchDto.setCOrderNo(orderInfo.getCOrderNo());
        addTPssDispatchDto.setNPlanSingleWgt(singleHeavy);
        addTPssDispatchDto.setCSlabItem(nPlanSlabThk + "*" + nPlanSlabWid);

        dispatchService.save(BeanUtil.copyProperties(addTPssDispatchDto, TPssDispatch.class));

        // 更新 t_pss_final_slab 状态为任务单下达完成、调度令号
        finalSlabService.update(Wrappers.<TPssFinalSlab>lambdaUpdate()
                .eq(TPssFinalSlab::getCOrderNo, aggregatePlan.getCOrderNo())
                .set(TPssFinalSlab::getCStatus, OrderStatusEnum.TASK_ISSUED.getCode())
                .set(TPssFinalSlab::getCDispatchId, addTPssDispatchDto.getCDispatchId())
        );

        // 更新 t_pss_aggregate_plan 修改计划状态为任务单下达完成、任务单号
        this.baseMapper.update(Wrappers.<TPssAggregatePlan>lambdaUpdate()
                .eq(TPssAggregatePlan::getCOrderNo, aggregatePlan.getCOrderNo())
                .set(TPssAggregatePlan::getCPlanState, OrderStatusEnum.TASK_ISSUED.getCode())
                .set(TPssAggregatePlan::getCProductTaskListId, taskCode)
                .set(TPssAggregatePlan::getNPlanWgt, orderInfo.getNOrderWgt())
                .set(TPssAggregatePlan::getNPlanSlabCount, totalNum)
                .set(TPssAggregatePlan::getNPlanSteelWgt, nPlanSteelWgt.add(Objects.isNull(aggregatePlan.getNPlanSteelWgt()) ? BigDecimal.ZERO : aggregatePlan.getNPlanSteelWgt()))
        );

        // 更新 t_pss_order_combin 修改计划状态为任务单下达完成
        orderCombinService.update(Wrappers.<TPssOrderCombin>lambdaUpdate()
                .eq(TPssOrderCombin::getCOrderNo, aggregatePlan.getCOrderNo())
                .set(TPssOrderCombin::getCCombinState, OrderStatusEnum.TASK_ISSUED.getCode())
        );

        // 更新 t_pss_order_info 修改订单状态为任务单下达完成
        orderInfoMapper.update(Wrappers.<TPssOrderInfo>lambdaUpdate()
                .eq(TPssOrderInfo::getCOrderNo, aggregatePlan.getCOrderNo())
                .set(TPssOrderInfo::getCOrderState, OrderStatusEnum.TASK_ISSUED.getCode())
        );

        return Boolean.TRUE;
    }
}
