
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPssWareHouseDto;
import com.aitos.pss.dto.inventory.TPssWareHousePageDto;
import com.aitos.pss.dto.inventory.UpdateTPssWareHouseDto;
import com.aitos.pss.entity.inventory.TPssWareHouse;
import com.aitos.pss.vo.inventory.TPssWareHousePageVo;
import com.aitos.pss.vo.inventory.TPssWareHouseVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
public interface IWareHouseService extends IService<TPssWareHouse> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssWareHousePageVo> queryPage(@Valid TPssWareHousePageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssWareHouseVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssWareHouseVo add(@Valid AddTPssWareHouseDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssWareHouseVo update(@Valid UpdateTPssWareHouseDto dto);
}
