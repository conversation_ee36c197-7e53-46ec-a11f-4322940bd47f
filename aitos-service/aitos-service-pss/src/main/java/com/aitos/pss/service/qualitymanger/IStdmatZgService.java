
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssStdmatZgDto;
import com.aitos.pss.dto.qualitymanger.TPssStdmatZgPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdmatZgDto;
import com.aitos.pss.entity.qualitymanger.TPssStdmatZg;
import com.aitos.pss.vo.qualitymanger.TPssStdmatZgPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdmatZgVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/

public interface IStdmatZgService extends IService<TPssStdmatZg> {

    /**
     *
     * @param dto
     * @return
     */
    PageOutput<TPssStdmatZgPageVo> queryPage(@Valid TPssStdmatZgPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssStdmatZgVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssStdmatZgVo add(@Valid AddTPssStdmatZgDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssStdmatZgVo update(@Valid UpdateTPssStdmatZgDto dto);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssStdmatZgPageDto dto, Boolean isTemplate);
}
