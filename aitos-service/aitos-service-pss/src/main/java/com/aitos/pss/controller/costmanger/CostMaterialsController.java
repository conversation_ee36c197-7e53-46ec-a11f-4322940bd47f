package com.aitos.pss.controller.costmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.costmanger.AddTPssFcsMatCodeDto;
import com.aitos.pss.dto.costmanger.TPssFcsMatCodePageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsMatCodeDto;
import com.aitos.pss.service.costmanger.ICostMaterialsService;
import com.aitos.pss.vo.costmanger.TPssFcsMatCodePageVo;
import com.aitos.pss.vo.costmanger.TPssFcsMatCodeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 成本物料配置
* <AUTHOR>
* @Date: 2025-06-26
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/costmaterials")
@Tag(name = "/pss"  + "/costmaterials",description = "成本物料配置代码")
@AllArgsConstructor
public class CostMaterialsController {


    private final ICostMaterialsService costMaterialsService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssFcsMatCode列表(分页)")
    public RT<PageOutput<TPssFcsMatCodePageVo>> page(@Valid TPssFcsMatCodePageDto dto){

        return RT.ok(costMaterialsService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssFcsMatCode信息")
    public RT<TPssFcsMatCodeVo> info(@RequestParam Long id){

        return RT.ok(costMaterialsService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssFcsMatCode")
    @AitLog(value = "成本物料配置新增数据")
    public RT<TPssFcsMatCodeVo> add(@Valid @RequestBody AddTPssFcsMatCodeDto dto){

        return RT.ok(costMaterialsService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssFcsMatCode")
    @AitLog(value = "成本物料配置修改数据")
    public RT<TPssFcsMatCodeVo> update(@Valid @RequestBody UpdateTPssFcsMatCodeDto dto){

        return RT.ok(costMaterialsService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "成本物料配置删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(costMaterialsService.removeBatchByIds(ids));
    }

}