
package com.aitos.pss.service.costmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.costmanger.AddTPssFcsMatCdDto;
import com.aitos.pss.dto.costmanger.TPssFcsMatCdPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsMatCdDto;
import com.aitos.pss.entity.costmanger.TPssFcsMatCd;
import com.aitos.pss.vo.costmanger.TPssFcsMatCdPageVo;
import com.aitos.pss.vo.costmanger.TPssFcsMatCdVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/

public interface IPssFcsMatcdService extends IService<TPssFcsMatCd> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssFcsMatCdPageVo> queryPage(@Valid TPssFcsMatCdPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssFcsMatCdVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssFcsMatCdVo add(@Valid AddTPssFcsMatCdDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssFcsMatCdVo update(@Valid UpdateTPssFcsMatCdDto dto);
}
