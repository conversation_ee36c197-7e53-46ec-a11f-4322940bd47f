
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.mybatis.base.MPJDeepAndJoinBaseService;
import com.aitos.pss.dto.qualitymanger.AddTPssQualityCodeDto;
import com.aitos.pss.dto.qualitymanger.TPssQualityCodePageDto;
import com.aitos.pss.dto.qualitymanger.TranTPssQualityCodeDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualityCodeDto;
import com.aitos.pss.entity.qualitymanger.TPssQualityCode;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodePageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodeVo;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/

public interface IQualityCodeService extends MPJDeepAndJoinBaseService<TPssQualityCode> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssQualityCodePageVo> queryPage(@Valid TPssQualityCodePageDto dto);

    /**
     * 列表查询
     * @param dto
     * @return
     */
    List<TPssQualityCodePageVo> queryList(@Valid TPssQualityCodePageDto dto);

    /**
     * 详情查询
     * @param id
     * @return
     */
    TPssQualityCodeVo queryInfo(Long id);

    /**
    * 新增
    *
    * @param addTPssQualityCodeDto
    * @return
    */
    Long add(@Valid AddTPssQualityCodeDto addTPssQualityCodeDto);

    /**
     * 复制数据
     * @param dto
     * @return
     */
    Long copy(@Valid AddTPssQualityCodeDto dto);

    /**
    * 更新
    *
    * @param updateTPssQualityCodeDto
    * @return
    */
    Boolean update(UpdateTPssQualityCodeDto updateTPssQualityCodeDto);

    /**
     * 质量编码转化
     * @param dto
     * @return
     */
    TPssQualityCodeVo updqualityCodeTranate(@Valid TranTPssQualityCodeDto dto);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssQualityCodePageDto dto, Boolean isTemplate);
}
