
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssTechCcmInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechCcmInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechCcmInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssTechCcmInfo;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/

public interface ITechCcmInfoService extends IService<TPssTechCcmInfo> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssTechCcmInfoPageVo> queryPage(@Valid TPssTechCcmInfoPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssTechCcmInfoVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssTechCcmInfoVo add(@Valid AddTPssTechCcmInfoDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssTechCcmInfoVo update(@Valid UpdateTPssTechCcmInfoDto dto);
}
