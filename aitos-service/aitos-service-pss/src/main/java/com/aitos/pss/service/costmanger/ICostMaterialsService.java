
package com.aitos.pss.service.costmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.costmanger.AddTPssFcsMatCodeDto;
import com.aitos.pss.dto.costmanger.TPssFcsMatCodePageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsMatCodeDto;
import com.aitos.pss.entity.costmanger.TPssFcsMatCode;
import com.aitos.pss.vo.costmanger.TPssFcsMatCodePageVo;
import com.aitos.pss.vo.costmanger.TPssFcsMatCodeVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-26
* @Version 1.0
*/

public interface ICostMaterialsService extends IService<TPssFcsMatCode> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssFcsMatCodePageVo> queryPage(@Valid TPssFcsMatCodePageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssFcsMatCodeVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssFcsMatCodeVo add(@Valid AddTPssFcsMatCodeDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssFcsMatCodeVo update(@Valid UpdateTPssFcsMatCodeDto dto);
}
