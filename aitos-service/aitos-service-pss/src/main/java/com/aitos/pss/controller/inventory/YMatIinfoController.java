package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPsssYMatInfoDto;
import com.aitos.pss.dto.inventory.TPsssYMatInfoPageDto;
import com.aitos.pss.dto.inventory.UpdateTPsssYMatInfoDto;
import com.aitos.pss.service.inventory.IYMatIinfoService;
import com.aitos.pss.vo.inventory.TPsssYMatInfoPageVo;
import com.aitos.pss.vo.inventory.TPsssYMatInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 原辅料库存信息查询
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/ymatiinfo")
@Tag(name = "/pss"  + "/ymatiinfo",description = "原辅料库存信息查询代码")
@AllArgsConstructor
public class YMatIinfoController {
    private final IYMatIinfoService yMatIinfoService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPsssYMatInfo列表(分页)")
    @SaCheckPermission(value = "ymatiinfo:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPsssYMatInfoPageVo>> page(@Valid TPsssYMatInfoPageDto dto){
        return RT.ok(yMatIinfoService.queryPage(dto));
    }
    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPsssYMatInfo信息")
    @SaCheckPermission(value = "ymatiinfo:detail", orRole = RoleConstants.ADMIN)
    public RT<TPsssYMatInfoVo> info(@RequestParam Long id){
        return RT.ok(yMatIinfoService.queryInfo(id));
    }

    @PostMapping
    @Operation(summary =  "新增TPsssYMatInfo")
    @SaCheckPermission(value = "ymatiinfo:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料库存信息查询新增数据")
    public RT<Long> add(@Valid @RequestBody AddTPsssYMatInfoDto dto){
        return RT.ok(yMatIinfoService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPsssYMatInfo")
    @SaCheckPermission(value = "ymatiinfo:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料库存信息查询修改数据")
    public RT<Boolean> update(@Valid @RequestBody UpdateTPsssYMatInfoDto dto){
        return RT.ok(yMatIinfoService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "ymatiinfo:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料库存信息查询删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(yMatIinfoService.removeBatchByIds(ids));
    }

}
