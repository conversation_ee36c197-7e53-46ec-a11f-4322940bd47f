
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssTechkRoutLgDto;
import com.aitos.pss.dto.qualitymanger.TPssTechkRoutLgPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechkRoutLgDto;
import com.aitos.pss.entity.qualitymanger.TPssTechkRoutLg;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/

public interface ITechkRoutLgService extends IService<TPssTechkRoutLg> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssTechkRoutLgPageVo> queryPage(@Valid TPssTechkRoutLgPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssTechkRoutLgVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssTechkRoutLgVo add(@Valid AddTPssTechkRoutLgDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssTechkRoutLgVo update(@Valid UpdateTPssTechkRoutLgDto dto);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssTechkRoutLgPageDto dto, Boolean isTemplate);
}
