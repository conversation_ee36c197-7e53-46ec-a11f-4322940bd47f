
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.mybatis.base.MPJDeepAndJoinBaseService;
import com.aitos.pss.dto.planmanger.AddTPssMatResDto;
import com.aitos.pss.dto.planmanger.UpdateTPssMatResDto;
import com.aitos.pss.dto.qualitymanger.TPssMatResPageDto;
import com.aitos.pss.entity.planmanger.TPssMatRes;
import com.aitos.pss.vo.planmanger.TPssMatResVo;
import com.aitos.pss.vo.qualitymanger.TPssMatResPageVo;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/

public interface IMatResService extends MPJDeepAndJoinBaseService<TPssMatRes> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssMatResPageVo> queryPage(@Valid TPssMatResPageDto dto);

    /**
     * query main mate page
     * @param dto
     * @return
     */
    PageOutput<TPssMatResPageVo> mainMatePage(@Valid TPssMatResPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssMatResVo queryInfo(Long id);

    /**
    * 新增
    *
    * @param dto
    * @return
    */
    TPssMatResVo add(AddTPssMatResDto dto);

    /**
    * 更新
    *
    * @param tPssMatRes
    * @return
    */
    Boolean update(TPssMatRes tPssMatRes);

    /**
    * 删除
    *
    * @param ids
    * @return
    */
    Boolean delete(List<Long> ids);

    /**
     * 取样确认
     * @param dto
     * @return
     */
    TPssMatResVo sampleConfirm(@Valid UpdateTPssMatResDto dto);

    /**
     * 改判
     * @param dto
     * @return
     */
    Boolean editJudge(@Valid UpdateTPssMatResDto dto);

    /**
     * 判废
     * @param dtoList
     * @return
     */
    Boolean judgeAbolish(@Valid List<UpdateTPssMatResDto> dtoList);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssMatResPageDto dto, Boolean isTemplate);
}
