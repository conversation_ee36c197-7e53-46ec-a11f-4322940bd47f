
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPssMatStockLimitDto;
import com.aitos.pss.dto.inventory.TPssMatStockLimitPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMatStockLimitDto;
import com.aitos.pss.entity.inventory.TPssMatStockLimit;
import com.aitos.pss.vo.inventory.TPssMatStockLimitPageVo;
import com.aitos.pss.vo.inventory.TPssMatStockLimitVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/

public interface IPssMatStockLimitService extends IService<TPssMatStockLimit> {
    /**
     * 详情查询
     * @param id
     * @return
     */
    TPssMatStockLimitVo queryInfo(Long id);

    /**
     * 新增
     * @param dto
     * @return
     */
    Long add(AddTPssMatStockLimitDto dto);

    /**
     * 更新
     * @param dto
     * @return
     */
    Boolean update(UpdateTPssMatStockLimitDto dto);

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssMatStockLimitPageVo> queryPage(TPssMatStockLimitPageDto dto);
}
