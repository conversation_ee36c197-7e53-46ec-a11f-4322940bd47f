
package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssAggregatePlanDto;
import com.aitos.pss.dto.planmanger.TPssAggregatePlanPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssAggregatePlanDto;
import com.aitos.pss.entity.planmanger.TPssAggregatePlan;
import com.aitos.pss.vo.planmanger.TPssAggregatePlanPageVo;
import com.aitos.pss.vo.planmanger.TPssAggregatePlanVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/

public interface IAggregatePlanService extends IService<TPssAggregatePlan> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssAggregatePlanPageVo> queryPage(@Valid TPssAggregatePlanPageDto dto);

    /**
     * 详情
     * @param id
     * @return
     */
    TPssAggregatePlanVo queryInfo(Long id);

    /**
     * 新增数据
     * @param dto
     * @return
     */
    TPssAggregatePlanVo add(@Valid AddTPssAggregatePlanDto dto);

    /**
     * 更新数据
     * @param dto
     * @return
     */
    TPssAggregatePlanVo update(@Valid UpdateTPssAggregatePlanDto dto);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    Boolean deleteBathId(@Valid List<Long> ids);

    /**
     * 产线分配
     * @param dto
     * @return
     */
    Boolean plineAllocation(@Valid UpdateTPssAggregatePlanDto dto);
}
