package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssFinalDesignSlabTempDto;
import com.aitos.pss.dto.planmanger.TPssFinalDesignSlabTempPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssFinalDesignSlabTempDto;
import com.aitos.pss.entity.planmanger.TPssFinalDesignSlabTemp;
import com.aitos.pss.mapper.planmanger.TPssFinalDesignSlabTempMapper;
import com.aitos.pss.service.planmanger.IDesignSlabTempService;
import com.aitos.pss.vo.planmanger.TPssFinalDesignSlabTempPageVo;
import com.aitos.pss.vo.planmanger.TPssFinalDesignSlabTempVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-21
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class DesignSlabTempServiceImpl extends ServiceImpl<TPssFinalDesignSlabTempMapper, TPssFinalDesignSlabTemp> implements IDesignSlabTempService {

    @Override
    public PageOutput<TPssFinalDesignSlabTempPageVo> queryPage(TPssFinalDesignSlabTempPageDto dto) {
        LambdaQueryWrapper<TPssFinalDesignSlabTemp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .orderByDesc(TPssFinalDesignSlabTemp::getNId)
                .select(TPssFinalDesignSlabTemp.class,x -> VoToColumnUtil.fieldsToColumns(TPssFinalDesignSlabTempPageVo.class).contains(x.getProperty()));
        IPage<TPssFinalDesignSlabTemp> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssFinalDesignSlabTempPageVo.class);
    }

    @Override
    public TPssFinalDesignSlabTempVo queryInfo(Long id) {
        TPssFinalDesignSlabTemp tPssFinalDesignSlabTemp = this.baseMapper.selectById(id);
        if (tPssFinalDesignSlabTemp == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssFinalDesignSlabTemp, TPssFinalDesignSlabTempVo.class);
    }

    @Override
    public TPssFinalDesignSlabTempVo add(AddTPssFinalDesignSlabTempDto dto) {
        TPssFinalDesignSlabTemp tPssFinalDesignSlabTemp = BeanUtil.toBean(dto, TPssFinalDesignSlabTemp.class);
        this.baseMapper.insert(tPssFinalDesignSlabTemp);

        return BeanUtil.copyProperties(tPssFinalDesignSlabTemp, TPssFinalDesignSlabTempVo.class);
    }

    @Override
    public TPssFinalDesignSlabTempVo update(UpdateTPssFinalDesignSlabTempDto dto) {
        TPssFinalDesignSlabTemp tPssFinalDesignSlabTemp = BeanUtil.toBean(dto, TPssFinalDesignSlabTemp.class);
        this.baseMapper.updateById(tPssFinalDesignSlabTemp);

        return BeanUtil.copyProperties(tPssFinalDesignSlabTemp, TPssFinalDesignSlabTempVo.class);
    }
}
