package com.aitos.pss.controller.costmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.costmanger.AddTPssMatMathRuleDto;
import com.aitos.pss.dto.costmanger.TPssMatMathRulePageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssMatMathRuleDto;
import com.aitos.pss.service.costmanger.IMatMathRuleService;
import com.aitos.pss.vo.costmanger.TPssMatMathRulePageVo;
import com.aitos.pss.vo.costmanger.TPssMatMathRuleVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 投入产出计算规则维护
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/matmathrule")
@Tag(name = "/pss"  + "/matmathrule",description = "投入产出计算规则维护代码")
@AllArgsConstructor
public class MatMathRuleController {


    private final IMatMathRuleService matMathRuleService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssMatMathRule列表(分页)")
    public RT<PageOutput<TPssMatMathRulePageVo>> page(@Valid TPssMatMathRulePageDto dto){

        return RT.ok(matMathRuleService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssMatMathRule信息")
    public RT<TPssMatMathRuleVo> info(@RequestParam Long id){

        return RT.ok(matMathRuleService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssMatMathRule")
    @AitLog(value = "投入产出计算规则维护新增数据")
    public RT<TPssMatMathRuleVo> add(@Valid @RequestBody AddTPssMatMathRuleDto dto){

        return RT.ok(matMathRuleService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssMatMathRule")
    @AitLog(value = "投入产出计算规则维护修改数据")
    public RT<TPssMatMathRuleVo> update(@Valid @RequestBody UpdateTPssMatMathRuleDto dto){

        return RT.ok(matMathRuleService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "投入产出计算规则维护删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(matMathRuleService.removeBatchByIds(ids));
    }

}