
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssStdmatDto;
import com.aitos.pss.dto.qualitymanger.TPssStdmatPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdmatDto;
import com.aitos.pss.entity.qualitymanger.TPssStdmat;
import com.aitos.pss.vo.qualitymanger.TPssStdmatPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdmatVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/

public interface IStdmatService extends IService<TPssStdmat> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssStdmatPageVo> queryPage(@Valid TPssStdmatPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssStdmatVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssStdmatVo add(@Valid AddTPssStdmatDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssStdmatVo update(@Valid UpdateTPssStdmatDto dto);
}
