
package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssFinalDesignSlabTempDto;
import com.aitos.pss.dto.planmanger.TPssFinalDesignSlabTempPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssFinalDesignSlabTempDto;
import com.aitos.pss.entity.planmanger.TPssFinalDesignSlabTemp;
import com.aitos.pss.vo.planmanger.TPssFinalDesignSlabTempPageVo;
import com.aitos.pss.vo.planmanger.TPssFinalDesignSlabTempVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-21
* @Version 1.0
*/

public interface IDesignSlabTempService extends IService<TPssFinalDesignSlabTemp> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssFinalDesignSlabTempPageVo> queryPage(@Valid TPssFinalDesignSlabTempPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssFinalDesignSlabTempVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssFinalDesignSlabTempVo add(@Valid AddTPssFinalDesignSlabTempDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssFinalDesignSlabTempVo update(@Valid UpdateTPssFinalDesignSlabTempDto dto);
}
