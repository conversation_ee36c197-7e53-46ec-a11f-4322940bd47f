package com.aitos.pss.controller.prodmonit;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.prodmonit.AddTPssIndicatorsDto;
import com.aitos.pss.dto.prodmonit.TPssIndicatorsPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssIndicatorsDto;
import com.aitos.pss.service.prodmonit.IIndicatorConfigService;
import com.aitos.pss.vo.prodmonit.TPssIndicatorsPageVo;
import com.aitos.pss.vo.prodmonit.TPssIndicatorsVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 指标配置
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/indicatorconfig")
@Tag(name = "/pss"  + "/indicatorconfig",description = "指标配置代码")
@AllArgsConstructor
public class IndicatorConfigController {


    private final IIndicatorConfigService indicatorConfigService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssIndicators列表(分页)")
    @SaCheckPermission(value = "indicatorconfig:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssIndicatorsPageVo>> page(@Valid TPssIndicatorsPageDto dto){

        return RT.ok( indicatorConfigService.queryPage(dto));
    }

    @GetMapping(value = "/list")
    @Operation(summary = "TPssIndicators列表")
    @SaCheckPermission(value = "indicatorconfig:detail", orRole = RoleConstants.ADMIN)
    public RT<List<TPssIndicatorsPageVo>> list(@Valid TPssIndicatorsPageDto dto){

        return RT.ok( indicatorConfigService.queryList(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssIndicators信息")
    @SaCheckPermission(value = "indicatorconfig:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssIndicatorsVo> info(@RequestParam Long id){

        return RT.ok(indicatorConfigService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssIndicators")
    @SaCheckPermission(value = "indicatorconfig:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "指标配置新增数据")
    public RT<TPssIndicatorsVo> add(@Valid @RequestBody AddTPssIndicatorsDto dto){

        return RT.ok(indicatorConfigService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssIndicators")
    @SaCheckPermission(value = "indicatorconfig:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "指标配置修改数据")
    public RT<TPssIndicatorsVo> update(@Valid @RequestBody UpdateTPssIndicatorsDto dto){

        return RT.ok(indicatorConfigService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "indicatorconfig:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "指标配置删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(indicatorConfigService.removeBatchByIds(ids));
    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "指标配置导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        indicatorConfigService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "指标配置导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssIndicatorsPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return indicatorConfigService.exportData(dto, isTemplate);
    }
}
