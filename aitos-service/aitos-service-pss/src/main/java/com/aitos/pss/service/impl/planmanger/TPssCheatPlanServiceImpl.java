package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.*;
import com.aitos.pss.entity.planmanger.TPssCheatPlan;
import com.aitos.pss.entity.planmanger.TPssPosMoveTime;
import com.aitos.pss.entity.planmanger.TPssPosTime;
import com.aitos.pss.entity.qualitymanger.TPssCutPlan;
import com.aitos.pss.enums.PssPlanStatusEnum;
import com.aitos.pss.mapper.planmanger.TPssCheatPlanMapper;
import com.aitos.pss.service.planmanger.ITPssCheatPlanService;
import com.aitos.pss.service.planmanger.ITPssPosMoveTimeService;
import com.aitos.pss.service.planmanger.ITPssPosTimeService;
import com.aitos.pss.service.qualitymanger.ICutPlanService;
import com.aitos.pss.vo.planmanger.TPssCheatPlanPageVo;
import com.aitos.pss.vo.planmanger.TPssCheatPlanVo;
import com.aitos.system.client.v2.ICodeRuleClientV2;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssCheatPlanServiceImpl extends ServiceImpl<TPssCheatPlanMapper, TPssCheatPlan> implements ITPssCheatPlanService {

    private final ICodeRuleClientV2 codeRuleClientV2;

    private final ITPssPosTimeService posTimeService;

    private final ITPssPosMoveTimeService posMoveTimeService;

    private final ICutPlanService cutPlanService;

    @Override
    public PageOutput<TPssCheatPlanPageVo> queryPage(TPssCheatPlanPageDto dto) {
        LambdaQueryWrapper<TPssCheatPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCPlanHeatId()),TPssCheatPlan::getCPlanHeatId,dto.getCPlanHeatId())
                .eq(StrUtil.isNotBlank(dto.getCActStlGrdCd()),TPssCheatPlan::getCActStlGrdCd,dto.getCActStlGrdCd())
                .eq(Objects.nonNull(dto.getCMatQulId()),TPssCheatPlan::getCMatQulId,dto.getCMatQulId())
                .like(StrUtil.isNotBlank(dto.getCPlanCastId()),TPssCheatPlan::getCPlanCastId,dto.getCPlanCastId())
                .like(StrUtil.isNotBlank(dto.getCHeatId()),TPssCheatPlan::getCHeatId,dto.getCHeatId())
                .like(StrUtil.isNotBlank(dto.getCActMatQulCd()),TPssCheatPlan::getCActMatQulCd,dto.getCActMatQulCd())
                .like(StrUtil.isNotBlank(dto.getCStatus()),TPssCheatPlan::getCStatus,dto.getCStatus())
                .like(StrUtil.isNotBlank(dto.getCCastId()),TPssCheatPlan::getCCastId,dto.getCCastId())
                .like(StrUtil.isNotBlank(dto.getCTaskListId()),TPssCheatPlan::getCTaskListId,dto.getCTaskListId())
                .between(
                        ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),
                        TPssCheatPlan::getDtPreLdTime,dto.getDtCreateDateTimeStart(),
                        dto.getDtCreateDateTimeEnd()
                )
                .orderByDesc(TPssCheatPlan::getNId)
                .select(TPssCheatPlan.class,x -> VoToColumnUtil.fieldsToColumns(TPssCheatPlanPageVo.class).contains(x.getProperty()));
        IPage<TPssCheatPlan> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssCheatPlanPageVo.class);
    }

    @Override
    public List<TPssCheatPlanPageVo> queryList(TPssCheatPlanPageDto dto) {
        LambdaQueryWrapper<TPssCheatPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCPlanHeatId()),TPssCheatPlan::getCPlanHeatId,dto.getCPlanHeatId())
                .eq(StrUtil.isNotBlank(dto.getCActStlGrdCd()),TPssCheatPlan::getCActStlGrdCd,dto.getCActStlGrdCd())
                .eq(Objects.nonNull(dto.getCMatQulId()),TPssCheatPlan::getCMatQulId,dto.getCMatQulId())
                .like(StrUtil.isNotBlank(dto.getCPlanCastId()),TPssCheatPlan::getCPlanCastId,dto.getCPlanCastId())
                .like(StrUtil.isNotBlank(dto.getCHeatId()),TPssCheatPlan::getCHeatId,dto.getCHeatId())
                .like(StrUtil.isNotBlank(dto.getCActMatQulCd()),TPssCheatPlan::getCActMatQulCd,dto.getCActMatQulCd())
                .like(StrUtil.isNotBlank(dto.getCStatus()),TPssCheatPlan::getCStatus,dto.getCStatus())
                .like(StrUtil.isNotBlank(dto.getCCastId()),TPssCheatPlan::getCCastId,dto.getCCastId())
                .like(StrUtil.isNotBlank(dto.getCTaskListId()),TPssCheatPlan::getCTaskListId,dto.getCTaskListId())
                .between(
                        ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),
                        TPssCheatPlan::getDtPreLdTime,dto.getDtCreateDateTimeStart(),
                        dto.getDtCreateDateTimeEnd()
                )
                .orderByDesc(TPssCheatPlan::getNId)
                .select(TPssCheatPlan.class,x -> VoToColumnUtil.fieldsToColumns(TPssCheatPlanPageVo.class).contains(x.getProperty()));
        List<TPssCheatPlan> list = this.baseMapper.selectList(ConventPage.getPage(dto), queryWrapper);

        return BeanUtil.copyToList(list, TPssCheatPlanPageVo.class);
    }

    @Override
    public TPssCheatPlanVo queryInfo(Long id) {
        TPssCheatPlan tPssCheatPlan = this.baseMapper.selectById(id);
        if (tPssCheatPlan == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssCheatPlan, TPssCheatPlanVo.class);
    }

    @Override
    @Transactional
    public Boolean add(List<AddTPssCheatPlanDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) throw new MyException("参数不能为空");
        Set<String> nCastEdtSeqSet = dtoList.stream().map(AddTPssCheatPlanDto::getNCastEdtSeq).collect(Collectors.toSet());
        if (nCastEdtSeqSet.size() > 1) throw new MyException("请选择相同浇次号的数据新增");

        String nCastEdtSeq = nCastEdtSeqSet.stream().findFirst().orElse(null);
        Long tPssCheatPlanCount =
                this.baseMapper.selectCount(Wrappers.<TPssCheatPlan>lambdaQuery().eq(TPssCheatPlan::getNCastEdtSeq, nCastEdtSeq));

        // 工位冶炼时间t_pss_pos_time
        List<TPssPosTime> posTimeList = posTimeService.list();
        Map<String, Map<Long, TPssPosTime>> lineProGrdAndTPssPosTimeMap =
                posTimeList.stream()
                        .collect(Collectors.groupingBy(
                                TPssPosTime::getCStlGrdCd,  // 外层Map的key - 钢种代码
                                Collectors.toMap(
                                        TPssPosTime::getCProcess,  // 内层Map的key - 工序代码
                                        Function.identity(),        // 内层Map的value - TPssPosTime对象本身
                                        (existing, replacement) -> existing  // 合并规则：保留已存在的
                                )
                        ));

        // 钢包移动时间t_pss_pos_move_time
        List<TPssPosMoveTime> posMoveTimeList = posMoveTimeService.list();
        if (CollectionUtils.isEmpty(posMoveTimeList)) throw new MyException("未找到钢包移动时间");
        Map<Long, TPssPosMoveTime> startEdnAndTPssPosMoveTimeMap =
                posMoveTimeList
                        .stream()
                        .collect(Collectors.toMap(item -> item.getCStartProcess() + item.getCEndProcess(), v -> v));

        TPssCheatPlan cheatPlanOne = this.baseMapper.selectOne(Wrappers.<TPssCheatPlan>lambdaQuery().orderByDesc(TPssCheatPlan::getDtPreCcmEndTme).last("LIMIT 1"));
        LocalDateTime dtPreLdStrTme = LocalDateTimeUtil.now();
        if (Objects.nonNull(cheatPlanOne)) {
            dtPreLdStrTme = cheatPlanOne.getDtPreCcmEndTme();
        }

        for (AddTPssCheatPlanDto addTPssCheatPlanDto : dtoList) {
            addTPssCheatPlanDto.setNCastEdtSeq(codeRuleClientV2.generateAndUse("planPourEditCode").getDataOrThrow());
            addTPssCheatPlanDto.setNCastHeatSeq(++tPssCheatPlanCount);

            // 计算时间
            String cStlGrdCd = addTPssCheatPlanDto.getCStlGrdCd();
            BigDecimal workTime = getWorkTime(addTPssCheatPlanDto, lineProGrdAndTPssPosTimeMap);

            BigDecimal moveTime = getMoveTime(addTPssCheatPlanDto, startEdnAndTPssPosMoveTimeMap);

            addTPssCheatPlanDto.setDtPreLdStrTme(dtPreLdStrTme.plusMinutes(30).plusMinutes(workTime.intValue()));
            addTPssCheatPlanDto.setDtPreLdEndTme(addTPssCheatPlanDto.getDtPreLdStrTme().plusMinutes(10));

            addTPssCheatPlanDto.setDtFirLfSttime(addTPssCheatPlanDto.getDtPreLdEndTme().plusMinutes(moveTime.intValue()));
            addTPssCheatPlanDto.setDtFirLfEndtime(addTPssCheatPlanDto.getDtFirLfSttime().plusMinutes(workTime.intValue()));

            addTPssCheatPlanDto.setDtPlanLadleOpen(addTPssCheatPlanDto.getDtFirLfEndtime().plusMinutes(moveTime.intValue()));

            addTPssCheatPlanDto.setDtPreCcmStrTme(addTPssCheatPlanDto.getDtPlanLadleOpen().plusMinutes(10));
            addTPssCheatPlanDto.setDtPreCcmEndTme(addTPssCheatPlanDto.getDtPreCcmStrTme().plusMinutes(workTime.intValue()));
        }

        List<TPssCheatPlan> tPssCheatPlanList = BeanUtil.copyToList(dtoList, TPssCheatPlan.class);
        this.baseMapper.insert(tPssCheatPlanList);

        return Boolean.TRUE;
    }

    private BigDecimal getMoveTime(AddTPssCheatPlanDto addTPssCheatPlanDto, Map<Long, TPssPosMoveTime> startEdnAndTPssPosMoveTimeMap) {
        String cLdWkst = addTPssCheatPlanDto.getCLdWkst();
        String cFirLfWkst = addTPssCheatPlanDto.getCFirLfWkst();
        String cCcmWkst = addTPssCheatPlanDto.getCCcmWkst();

        TPssPosMoveTime tPssPosMoveTime12 = startEdnAndTPssPosMoveTimeMap.get(cLdWkst + cFirLfWkst);
        if (Objects.nonNull(tPssPosMoveTime12)) return tPssPosMoveTime12.getNMoveTime();

        TPssPosMoveTime tPssPosMoveTime24 = startEdnAndTPssPosMoveTimeMap.get(cFirLfWkst + cCcmWkst);
        if (Objects.nonNull(tPssPosMoveTime24)) return tPssPosMoveTime24.getNMoveTime();


        return BigDecimal.ZERO;
    }

    private static BigDecimal getWorkTime(AddTPssCheatPlanDto addTPssCheatPlanDto, Map<String, Map<Long, TPssPosTime>> lineProGrdAndTPssPosTimeMap) {
        Map<Long, TPssPosTime> stringTPssPosTimeMap = lineProGrdAndTPssPosTimeMap.get(addTPssCheatPlanDto.getCStlGrdCd());
        if (MapUtils.isEmpty(stringTPssPosTimeMap)) {
            return BigDecimal.ZERO;
        }

        if (Objects.nonNull(stringTPssPosTimeMap.get(addTPssCheatPlanDto.getCLdWkst()))) {
            TPssPosTime posTime = stringTPssPosTimeMap.get(addTPssCheatPlanDto.getCLdWkst());
            return posTime.getNWorkTime();
        }

        if (Objects.nonNull(stringTPssPosTimeMap.get(addTPssCheatPlanDto.getCFirLfWkst()))) {
            TPssPosTime posTime = stringTPssPosTimeMap.get(addTPssCheatPlanDto.getCFirLfWkst());
            return posTime.getNWorkTime();
        }

        if (Objects.nonNull(stringTPssPosTimeMap.get(addTPssCheatPlanDto.getCCcmWkst()))) {
            TPssPosTime posTime = stringTPssPosTimeMap.get(addTPssCheatPlanDto.getCCcmWkst());
            return posTime.getNWorkTime();
        }



        return BigDecimal.ZERO;
    }

    @Override
    public Boolean update(UpdateTPssCheatPlanBathDto dto) {
        if (
                Objects.isNull(dto) ||
                CollectionUtils.isEmpty(dto.getUpdateCheatPlanDtoList())
        ) {
            throw new MyException("参数不能为空");
        }
        List<UpdateTPssCheatPlanDto> updateCheatPlanDtoList = dto.getUpdateCheatPlanDtoList();
        Set<String> cPlanHeatIdSet =
                updateCheatPlanDtoList.stream().map(UpdateTPssCheatPlanDto::getCPlanHeatId).collect(Collectors.toSet());
        List<TPssCutPlan> cutPlanList =
                cutPlanService.list(Wrappers.<TPssCutPlan>lambdaQuery().in(TPssCutPlan::getCPlanHeatId, cPlanHeatIdSet));
        Map<String, TPssCutPlan> cPlanHeatIdAndTPssCutPlanMap =
                cutPlanList.stream().collect(Collectors.toMap(TPssCutPlan::getCPlanHeatId, v -> v));


        for (UpdateTPssCheatPlanDto updateTPssCheatPlanDto : updateCheatPlanDtoList) {
            updateTPssCheatPlanDto.setNCcmThk(dto.getNCcmThk());
            updateTPssCheatPlanDto.setNCcmWth(dto.getNCcmWth());
            updateTPssCheatPlanDto.setCCcmLen(String.valueOf(dto.getCCcmLen()));
            TPssCutPlan tPssCutPlan = cPlanHeatIdAndTPssCutPlanMap.get(updateTPssCheatPlanDto.getCPlanHeatId());
            if (Objects.isNull(tPssCutPlan)) continue;
            tPssCutPlan.setNProdThk(dto.getNCcmThk());
            tPssCutPlan.setNProdWth(dto.getNCcmWth());
            tPssCutPlan.setNProdLen(dto.getCCcmLen());
        }


        List<TPssCheatPlan> tPssCheatPlanList = BeanUtil.copyToList(updateCheatPlanDtoList, TPssCheatPlan.class);
        this.baseMapper.updateById(tPssCheatPlanList);
        cutPlanService.updateBatchById(cutPlanList);

        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteBathId(List<Long> ids) {
        this.baseMapper.deleteByIds(ids);

        return Boolean.TRUE;
    }

    @Override
    public Boolean stationAdjust(UpdateCheatPlanStationAdjustDto dto) {

        // 连铸
        if (dto.getIsCcm()) {
            ccm(dto);
        }

        // 转炉
        if (dto.getIsCLd()) {
            cld(dto);
        }

        // LF
        if (dto.getIsLF()) {
            lf(dto);
        }

        // 移动
        if (dto.getIsMove()) {
            move(dto);
        }

        // 交换
        if (dto.getIsSwap()) {
            swap(dto);
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean planOperation(UpdateTPssCheatPlanBathDto dto) {
        return Boolean.TRUE;
    }

    private void swap(UpdateCheatPlanStationAdjustDto dto) {
        List<UpdateTPssCheatPlanDto> dtoList = dto.getDtoList();

        if (!(CollectionUtils.isNotEmpty(dtoList) && dtoList.size() == 2)) throw new MyException("钢水交换必须选择两条数据");

        UpdateTPssCheatPlanDto source = dtoList.get(0);
        UpdateTPssCheatPlanDto target = dtoList.get(0);
        UpdateTPssCheatPlanDto tmp = BeanUtil.copyProperties(source, UpdateTPssCheatPlanDto.class);

        source.setCPlanHeatId(target.getCPlanHeatId());
        source.setCStatus(target.getCStatus());

        target.setCPlanHeatId(tmp.getCPlanHeatId());
        target.setCStatus(tmp.getCStatus());

        List<TPssCheatPlan> tPssCheatPlanList = BeanUtil.copyToList(dtoList, TPssCheatPlan.class);
        this.baseMapper.updateById(tPssCheatPlanList);

    }

    private void move(UpdateCheatPlanStationAdjustDto dto) {
        if (Objects.isNull(dto) || CollectionUtils.isEmpty(dto.getDtoList())) throw new MyException("参数不能为空");
        UpdateTPssCheatPlanDto updateTPssCheatPlanDto = dto.getDtoList().get(0);

        TPssCheatPlan tPssCheatPlan = this.baseMapper.selectById(updateTPssCheatPlanDto.getNId());

        TPssCheatPlan source = BeanUtil.copyProperties(tPssCheatPlan, TPssCheatPlan.class);

        Long tmpCastHeatSeq = source.getNCastHeatSeq();

        TPssCheatPlan target =
                this.baseMapper.selectOne(
                        Wrappers.<TPssCheatPlan>lambdaQuery()
                                .eq(TPssCheatPlan::getNCastEdtSeq, updateTPssCheatPlanDto.getNCastEdtSeq())
                                .eq(TPssCheatPlan::getNCastHeatSeq, updateTPssCheatPlanDto.getNCastHeatSeq())
                );

        source.setNCastHeatSeq(target.getNCastHeatSeq());
        target.setNCastHeatSeq(tmpCastHeatSeq);
        this.baseMapper.updateById(target);
        this.baseMapper.updateById(source);

    }

    private void lf(UpdateCheatPlanStationAdjustDto dto) {
        List<UpdateTPssCheatPlanDto> dtoList = dto.getDtoList();
        String lf = dto.getLF();
        // TODO 查询工厂树编码

        for (UpdateTPssCheatPlanDto updateTPssCheatPlanDto : dtoList) {
            if (!PssPlanStatusEnum.canLf(updateTPssCheatPlanDto.getCStatus())) throw new MyException("此状态不能LF");

            updateTPssCheatPlanDto.setCFirLfWkst(dto.getLF());
        }

        List<TPssCheatPlan> tPssCheatPlanList = BeanUtil.copyToList(dtoList, TPssCheatPlan.class);
        this.baseMapper.updateById(tPssCheatPlanList);
    }

    private void cld(UpdateCheatPlanStationAdjustDto dto) {
        List<UpdateTPssCheatPlanDto> dtoList = dto.getDtoList();
        String cLd = dto.getCLd();
        // TODO 查询工厂树编码

        for (UpdateTPssCheatPlanDto updateTPssCheatPlanDto : dtoList) {
            if (!PssPlanStatusEnum.canCld(updateTPssCheatPlanDto.getCStatus())) throw new MyException("此状态不能转炉");

            updateTPssCheatPlanDto.setCLdWkst(dto.getCLd());
        }

        List<TPssCheatPlan> tPssCheatPlanList = BeanUtil.copyToList(dtoList, TPssCheatPlan.class);
        this.baseMapper.updateById(tPssCheatPlanList);
    }

    private void ccm(UpdateCheatPlanStationAdjustDto dto) {
        List<UpdateTPssCheatPlanDto> dtoList = dto.getDtoList();
        String ccm = dto.getCcm();
        // TODO 查询工厂树编码

        for (UpdateTPssCheatPlanDto updateTPssCheatPlanDto : dtoList) {
            if (!PssPlanStatusEnum.canCcm(updateTPssCheatPlanDto.getCStatus())) throw new MyException("此状态不能连铸");

            updateTPssCheatPlanDto.setCCcmWkst(dto.getCcm());
        }

        List<TPssCheatPlan> tPssCheatPlanList = BeanUtil.copyToList(dtoList, TPssCheatPlan.class);
        this.baseMapper.updateById(tPssCheatPlanList);
    }
}
