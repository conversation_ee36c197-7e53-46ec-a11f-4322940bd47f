package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.AddTPssYMatStockRecordDto;
import com.aitos.pss.dto.inventory.TPssYMatStockRecordPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssYMatStockRecordDto;
import com.aitos.pss.entity.inventory.TPssYMatStockRecord;
import com.aitos.pss.mapper.inventory.TPssYMatStockRecordMapper;
import com.aitos.pss.service.inventory.IMatStockRecordService;
import com.aitos.pss.vo.inventory.TPssYMatStockRecordPageVo;
import com.aitos.pss.vo.inventory.TPssYMatStockRecordVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class MatStockRecordServiceImpl extends ServiceImpl<TPssYMatStockRecordMapper, TPssYMatStockRecord> implements IMatStockRecordService {

    @Override
    public PageOutput<TPssYMatStockRecordPageVo> ueryPage(TPssYMatStockRecordPageDto dto) {
        LambdaQueryWrapper<TPssYMatStockRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCDocType()),TPssYMatStockRecord::getCDocType,dto.getCDocType())
                .like(StrUtil.isNotBlank(dto.getCBatchNo()),TPssYMatStockRecord::getCBatchNo,dto.getCBatchNo())
                .eq(ObjectUtil.isNotNull(dto.getNQuantity()),TPssYMatStockRecord::getNQuantity,dto.getNQuantity())
                .eq(ObjectUtil.isNotNull(dto.getNAfterStock()),TPssYMatStockRecord::getNAfterStock,dto.getNAfterStock())
                .like(StrUtil.isNotBlank(dto.getCOperator()),TPssYMatStockRecord::getCOperator,dto.getCOperator())
                .like(StrUtil.isNotBlank(dto.getCRemark()),TPssYMatStockRecord::getCRemark,dto.getCRemark())
                .eq(ObjectUtil.isNotNull(dto.getMaterialId()),TPssYMatStockRecord::getMaterialId,dto.getMaterialId())
                .like(StrUtil.isNotBlank(dto.getCDocNo()),TPssYMatStockRecord::getCDocNo,dto.getCDocNo())
                .like(StrUtil.isNotBlank(dto.getCOperationType()),TPssYMatStockRecord::getCOperationType,dto.getCOperationType())
                .eq(ObjectUtil.isNotNull(dto.getNBeforeStock()),TPssYMatStockRecord::getNBeforeStock,dto.getNBeforeStock())
                .between(ObjectUtil.isNotNull(dto.getDtOperationTimeStart()) && ObjectUtil.isNotNull(dto.getDtOperationTimeEnd()),TPssYMatStockRecord::getDtOperationTime,dto.getDtOperationTimeStart(),dto.getDtOperationTimeEnd())
                .like(StrUtil.isNotBlank(dto.getCRelatedNo()),TPssYMatStockRecord::getCRelatedNo,dto.getCRelatedNo())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssYMatStockRecord::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssYMatStockRecord::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssYMatStockRecord::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssYMatStockRecord::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                    .orderByDesc(TPssYMatStockRecord::getDtCreateDateTime)
                .select(TPssYMatStockRecord.class,x -> VoToColumnUtil.fieldsToColumns(TPssYMatStockRecordPageVo.class).contains(x.getProperty()));
        IPage<TPssYMatStockRecord> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        PageOutput<TPssYMatStockRecordPageVo> pageOutput = ConventPage.getPageOutput(page, TPssYMatStockRecordPageVo.class);
        return pageOutput;
    }

    @Override
    public TPssYMatStockRecordVo queryInfo(Long id) {
        TPssYMatStockRecord tPssYMatStockRecord = this.baseMapper.selectById(id);
        if (tPssYMatStockRecord == null) {
            throw new MyException("找不到数据");
        }
        return BeanUtil.toBean(tPssYMatStockRecord, TPssYMatStockRecordVo.class);
    }

    @Override
    public Long add(AddTPssYMatStockRecordDto dto) {
        TPssYMatStockRecord tPssYMatStockRecord = BeanUtil.toBean(dto, TPssYMatStockRecord.class);
        this.baseMapper.insert(tPssYMatStockRecord);
        return tPssYMatStockRecord.getNId();
    }

    @Override
    public Boolean update(UpdateTPssYMatStockRecordDto dto) {
        TPssYMatStockRecord tPssYMatStockRecord = BeanUtil.toBean(dto, TPssYMatStockRecord.class);
        this.baseMapper.updateById(tPssYMatStockRecord);
        return Boolean.TRUE;
    }
}
