package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.dto.inventory.AddTPssWareHouseDto;
import com.aitos.pss.dto.inventory.TPssWareHousePageDto;
import com.aitos.pss.dto.inventory.UpdateTPssWareHouseDto;
import com.aitos.pss.entity.inventory.TPssWareHouse;
import com.aitos.pss.mapper.inventory.TPssWareHouseMapper;
import com.aitos.pss.service.inventory.IWareHouseService;
import com.aitos.pss.vo.inventory.TPssWareHousePageVo;
import com.aitos.pss.vo.inventory.TPssWareHouseVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@Service
@AllArgsConstructor
public class WareHouseServiceImpl extends ServiceImpl<TPssWareHouseMapper, TPssWareHouse> implements IWareHouseService {

    @Override
    public PageOutput<TPssWareHousePageVo> queryPage(TPssWareHousePageDto dto) {
        LambdaQueryWrapper<TPssWareHouse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCCompany()),TPssWareHouse::getCCompany,dto.getCCompany())
                .like(StrUtil.isNotBlank(dto.getCName()),TPssWareHouse::getCName,dto.getCName())
                .like(StrUtil.isNotBlank(dto.getCErp()),TPssWareHouse::getCErp,dto.getCErp())
                .like(StrUtil.isNotBlank(dto.getCStatus()),TPssWareHouse::getCStatus,dto.getCStatus())
                .eq(ObjectUtil.isNotNull(dto.getNLow()),TPssWareHouse::getNLow,dto.getNLow())
                .like(StrUtil.isNotBlank(dto.getCTel()),TPssWareHouse::getCTel,dto.getCTel())
                .like(StrUtil.isNotBlank(dto.getCLine()),TPssWareHouse::getCLine,dto.getCLine())
                .like(StrUtil.isNotBlank(dto.getCCode()),TPssWareHouse::getCCode,dto.getCCode())
                .like(StrUtil.isNotBlank(dto.getCType()),TPssWareHouse::getCType,dto.getCType())
                .like(StrUtil.isNotBlank(dto.getCAddress()),TPssWareHouse::getCAddress,dto.getCAddress())
                .eq(ObjectUtil.isNotNull(dto.getNHigh()),TPssWareHouse::getNHigh,dto.getNHigh())
                .eq(ObjectUtil.isNotNull(dto.getNSafe()),TPssWareHouse::getNSafe,dto.getNSafe())
                .like(StrUtil.isNotBlank(dto.getCManager()),TPssWareHouse::getCManager,dto.getCManager())
                .like(StrUtil.isNotBlank(dto.getCRemark()),TPssWareHouse::getCRemark,dto.getCRemark())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssWareHouse::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssWareHouse::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssWareHouse::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssWareHouse::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssWareHouse::getDtCreateDateTime)
                .select(TPssWareHouse.class,x -> VoToColumnUtil.fieldsToColumns(TPssWareHousePageVo.class).contains(x.getProperty()));
        IPage<TPssWareHouse> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssWareHousePageVo.class);
    }

    @Override
    public TPssWareHouseVo queryInfo(Long id) {
        TPssWareHouse tPssWareHouse = this.baseMapper.selectById(id);
        if (tPssWareHouse == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssWareHouse, TPssWareHouseVo.class);
    }

    @Override
    public TPssWareHouseVo add(AddTPssWareHouseDto dto) {
        TPssWareHouse tPssWareHouse = BeanUtil.toBean(dto, TPssWareHouse.class);
        this.baseMapper.insert(tPssWareHouse);

        return BeanUtil.copyProperties(tPssWareHouse, TPssWareHouseVo.class);
    }

    @Override
    public TPssWareHouseVo update(UpdateTPssWareHouseDto dto) {
        TPssWareHouse tPssWareHouse = BeanUtil.toBean(dto, TPssWareHouse.class);
        this.baseMapper.updateById(tPssWareHouse);

        return BeanUtil.copyProperties(tPssWareHouse, TPssWareHouseVo.class);
    }
}
