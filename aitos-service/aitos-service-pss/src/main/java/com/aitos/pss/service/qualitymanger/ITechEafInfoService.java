
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssTechEafInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechEafInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechEafInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssTechEafInfo;
import com.aitos.pss.vo.qualitymanger.TPssTechEafInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechEafInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
 * @title: service
 * <AUTHOR>
 * @Date: 2025-05-26
 * @Version 1.0
 */

public interface ITechEafInfoService extends IService<TPssTechEafInfo> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssTechEafInfoPageVo> queryPage(@Valid TPssTechEafInfoPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssTechEafInfoVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssTechEafInfoVo add(@Valid AddTPssTechEafInfoDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssTechEafInfoVo update(@Valid UpdateTPssTechEafInfoDto dto);
}
