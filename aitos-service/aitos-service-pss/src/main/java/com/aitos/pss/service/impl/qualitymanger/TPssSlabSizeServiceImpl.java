package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabSizeDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabSizePageDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabJudge;
import com.aitos.pss.entity.qualitymanger.TPssSlabRes;
import com.aitos.pss.entity.qualitymanger.TPssSlabSize;
import com.aitos.pss.enums.PssCheEnum;
import com.aitos.pss.enums.PssQCEnum;
import com.aitos.pss.enums.PssQFaceEnum;
import com.aitos.pss.enums.PssQSizeEnum;
import com.aitos.pss.mapper.qualitymanger.TPssSlabSizeMapper;
import com.aitos.pss.service.qualitymanger.ISlabJudgeService;
import com.aitos.pss.service.qualitymanger.ISlabResService;
import com.aitos.pss.service.qualitymanger.ITPssSlabSizeService;
import com.aitos.pss.vo.qualitymanger.TPssSlabSizePageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabSizeVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssSlabSizeServiceImpl extends ServiceImpl<TPssSlabSizeMapper, TPssSlabSize> implements ITPssSlabSizeService {

    private final ISlabResService slabResService;

    private final ISlabJudgeService slabJudgeService;

    @Override
    public PageOutput<TPssSlabSizePageVo> queryPage(TPssSlabSizePageDto dto) {
        LambdaQueryWrapper<TPssSlabSize> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(dto.getCSlabId()), TPssSlabSize::getCSlabId, dto.getCSlabId());

        IPage<TPssSlabSize> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssSlabSizePageVo.class);
    }

    @Override
    public TPssSlabSizeVo add(AddTPssSlabSizeDto dto) {
        TPssSlabSize slabSize = BeanUtil.toBean(dto, TPssSlabSize.class);
        this.baseMapper.insert(slabSize);

        return BeanUtil.copyProperties(slabSize, TPssSlabSizeVo.class);
    }

    @Override
    public Boolean sizeDecision(List<AddTPssSlabSizeDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Boolean.FALSE;
        }

        Set<String> cSlabIdSet = dtoList.stream().map(AddTPssSlabSizeDto::getCSlabId).collect(Collectors.toSet());
        List<TPssSlabRes> slabResList = slabResService.list(Wrappers.<TPssSlabRes>lambdaQuery().in(TPssSlabRes::getCMatId, cSlabIdSet));
        Map<String, TPssSlabRes> cMatIdAndTPssSlabResMap =
                slabResList
                        .stream()
                        .collect(Collectors.toMap(TPssSlabRes::getCMatId, v -> v, (k1, k2) -> k1));

        for (AddTPssSlabSizeDto addTPssSlabFaceDto : dtoList) {
            TPssSlabRes tPssSlabRes = cMatIdAndTPssSlabResMap.get(addTPssSlabFaceDto.getCSlabId());
            // 尺寸等级
            if (Objects.equals(addTPssSlabFaceDto.getCSizeRlt(), PssQSizeEnum.QUALIFIED.getCode())) {
                // 不合格
                TPssSlabJudge slabJudge =
                        slabJudgeService.getOne(
                                Wrappers.<TPssSlabJudge>lambdaQuery()
                                        .eq(TPssSlabJudge::getCSlabId, addTPssSlabFaceDto.getCSlabId())
                        );
                if (Objects.isNull(slabJudge)) {
                    slabJudge = new TPssSlabJudge();
                    BeanUtil.copyProperties(addTPssSlabFaceDto, slabJudge);
                    slabJudge.setCChemRlt(PssCheEnum.QUALIFIED.getCode());
                    slabJudge.setCBodyRlt(PssQFaceEnum.QUALIFIED.getCode());
                    slabJudge.setCSizeRlt(PssQSizeEnum.QUALIFIED.getCode());
                    slabJudge.setCFaceRlt(PssQFaceEnum.QUALIFIED.getCode());
                    slabJudge.setCJudgeRlt(PssQCEnum.QUALIFIED.getCode());

                    tPssSlabRes.setCProdGrd(PssQCEnum.QUALIFIED.getCode());

                    slabJudgeService.save(slabJudge);
                    continue;
                }
                slabJudge.setCSizeRlt(addTPssSlabFaceDto.getCSizeRlt());
                if (!Objects.equals(slabJudge.getCSizeRlt(), PssQSizeEnum.QUALIFIED.getCode())) {
                    slabJudge.setCSizeRlt(PssQCEnum.PENDING.getCode());
                    tPssSlabRes.setCProdGrd(PssQCEnum.UNQUALIFIED.getCode());
                }
                slabJudgeService.updateById(slabJudge);
            }


            tPssSlabRes.setCSizeGrd(addTPssSlabFaceDto.getCSizeRlt());
        }

        List<TPssSlabSize> slabFaceList = BeanUtil.copyToList(dtoList, TPssSlabSize.class);
        saveBatch(slabFaceList);

        slabResService.updateBatchById(slabResList);

        return Boolean.TRUE;
    }
}