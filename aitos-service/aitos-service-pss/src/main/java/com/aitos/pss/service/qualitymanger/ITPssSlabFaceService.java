
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabFaceDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabFacePageDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabFace;
import com.aitos.pss.vo.qualitymanger.TPssSlabFacePageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabFaceVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/

public interface ITPssSlabFaceService extends IService<TPssSlabFace> {
    Boolean factDecision(@Valid List<AddTPssSlabFaceDto> dtoList);

    /**
     * add
     * @param dto
     * @return
     */
    TPssSlabFaceVo add(@Valid AddTPssSlabFaceDto dto);

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssSlabFacePageVo> queryPage(@Valid TPssSlabFacePageDto dto);
}
