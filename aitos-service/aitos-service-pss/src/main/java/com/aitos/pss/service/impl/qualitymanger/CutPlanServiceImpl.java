package com.aitos.pss.service.impl.qualitymanger;

import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.entity.qualitymanger.TPssCutPlan;
import com.aitos.pss.mapper.qualitymanger.TPssCutPlanMapper;
import com.aitos.pss.service.qualitymanger.ICutPlanService;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class CutPlanServiceImpl extends ServiceImpl<TPssCutPlanMapper, TPssCutPlan> implements ICutPlanService {
}
