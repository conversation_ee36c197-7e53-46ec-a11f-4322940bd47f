package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPssMaterialInspectionDto;
import com.aitos.pss.dto.inventory.TPssMaterialInspectionPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMaterialInspectionDto;
import com.aitos.pss.service.inventory.IMaterialInspectionService;
import com.aitos.pss.vo.inventory.TPssMaterialInspectionPageVo;
import com.aitos.pss.vo.inventory.TPssMaterialInspectionVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 原辅料入库管理子表
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/materialinspection")
@Tag(name = "/pss"  + "/materialinspection",description = "原辅料入库管理子表代码")
@AllArgsConstructor
public class MaterialInspectionController {


    private final IMaterialInspectionService materialInspectionService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssMaterialInspection列表(分页)")
    @SaCheckPermission(value = "materialinspection:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssMaterialInspectionPageVo>> page(@Valid TPssMaterialInspectionPageDto dto){
        return RT.ok(materialInspectionService.page(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssMaterialInspection信息")
    @SaCheckPermission(value = "materialinspection:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssMaterialInspectionVo> info(@RequestParam Long id){
        return RT.ok(materialInspectionService.info(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssMaterialInspection")
    @SaCheckPermission(value = "materialinspection:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料入库管理子表新增数据")
    public RT<Long> add(@Valid @RequestBody AddTPssMaterialInspectionDto dto){
        return RT.ok(materialInspectionService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssMaterialInspection")
    @SaCheckPermission(value = "materialinspection:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料入库管理子表修改数据")
    public RT<Boolean> update(@Valid @RequestBody UpdateTPssMaterialInspectionDto dto){
        return RT.ok(materialInspectionService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "materialinspection:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料入库管理子表删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(materialInspectionService.removeBatchByIds(ids));

    }

}