package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.TPssRollSchPageDto;
import com.aitos.pss.entity.planmanger.TPssRollSch;
import com.aitos.pss.vo.planmanger.TPssRollSchVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

public interface ITPssRollSchService extends IService<TPssRollSch> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssRollSchVo> queryPage(@Valid TPssRollSchPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssRollSchVo queryInfo(Long id);
}