package com.aitos.pss.controller.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssStdchemLgDto;
import com.aitos.pss.dto.qualitymanger.TPssStdchemLgPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdchemLgDto;
import com.aitos.pss.entity.qualitymanger.TPssStdchemLg;
import com.aitos.pss.service.qualitymanger.IPssStdchemLgService;
import com.aitos.pss.vo.qualitymanger.TPssStdchemLgPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdchemLgVo;
import com.aitos.tenant.annotation.MyDs;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 成分标准管理-成分标准
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssstdchemlg")
@Tag(name = "/pss"  + "/pssstdchemlg",description = "成分标准管理-成分标准代码")
@AllArgsConstructor
@MyDs("1922933086873120769")
public class PssStdchemLgController {


    private final IPssStdchemLgService pssStdchemLgService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssStdchemLg列表(分页)")
    public RT<PageOutput<TPssStdchemLgPageVo>> page(@Valid TPssStdchemLgPageDto dto){

        return RT.ok(pssStdchemLgService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssStdchemLg信息")
    public RT<TPssStdchemLgVo> info(@RequestParam Long id){

        return RT.ok(pssStdchemLgService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssStdchemLg")
    @AitLog(value = "成分标准管理-成分标准新增数据")
    public RT<TPssStdchemLgVo> add(@Valid @RequestBody AddTPssStdchemLgDto dto){

        return RT.ok(pssStdchemLgService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssStdchemLg")
    @AitLog(value = "成分标准管理-成分标准修改数据")
    public RT<TPssStdchemLgVo> update(@Valid @RequestBody UpdateTPssStdchemLgDto dto){

        return RT.ok(pssStdchemLgService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "成分标准管理-成分标准删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssStdchemLgService.removeBatchByIds(ids));
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssStdchemLgDto dto){
        TPssStdchemLg tPssStdchemLg = BeanUtil.toBean(dto, TPssStdchemLg.class);

        return RT.ok(pssStdchemLgService.updateById(tPssStdchemLg));
    }
}