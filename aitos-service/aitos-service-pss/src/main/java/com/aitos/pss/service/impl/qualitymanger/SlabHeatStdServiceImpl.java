package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabHeatStdDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabHeatStdPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabHeatStdDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabHeatStd;
import com.aitos.pss.mapper.qualitymanger.TPssSlabHeatStdMapper;
import com.aitos.pss.service.qualitymanger.ISlabHeatStdService;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdPageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-21
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class SlabHeatStdServiceImpl extends ServiceImpl<TPssSlabHeatStdMapper, TPssSlabHeatStd> implements ISlabHeatStdService {

    @Override
    public PageOutput<TPssSlabHeatStdPageVo> queryPage(TPssSlabHeatStdPageDto dto) {
        LambdaQueryWrapper<TPssSlabHeatStd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualityId()),TPssSlabHeatStd::getCQualityId,dto.getCQualityId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssSlabHeatStd::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(ObjectUtil.isNotNull(dto.getNSlabThkMax()),TPssSlabHeatStd::getNSlabThkMax,dto.getNSlabThkMax())
                .eq(ObjectUtil.isNotNull(dto.getNFoutMaxTemp()),TPssSlabHeatStd::getNFoutMaxTemp,dto.getNFoutMaxTemp())
                .eq(ObjectUtil.isNotNull(dto.getNSlabScTempDiff()),TPssSlabHeatStd::getNSlabScTempDiff,dto.getNSlabScTempDiff())
                .like(ObjectUtil.isNotNull(dto.getCReserver1()),TPssSlabHeatStd::getCReserver1,dto.getCReserver1())
                .like(ObjectUtil.isNotNull(dto.getCReserver3()),TPssSlabHeatStd::getCReserver3,dto.getCReserver3())
                .like(ObjectUtil.isNotNull(dto.getCReserver5()),TPssSlabHeatStd::getCReserver5,dto.getCReserver5())
                .like(ObjectUtil.isNotNull(dto.getCReserver7()),TPssSlabHeatStd::getCReserver7,dto.getCReserver7())
                .like(ObjectUtil.isNotNull(dto.getCReserver9()),TPssSlabHeatStd::getCReserver9,dto.getCReserver9())
                .like(Objects.nonNull(dto.getNEnabledMark()),TPssSlabHeatStd::getNEnabledMark,dto.getNEnabledMark())
                .like(StrUtil.isNotBlank(dto.getCQualityCodeName()),TPssSlabHeatStd::getCQualityCodeName,dto.getCQualityCodeName())
                .eq(ObjectUtil.isNotNull(dto.getNSlabThkMin()),TPssSlabHeatStd::getNSlabThkMin,dto.getNSlabThkMin())
                .eq(ObjectUtil.isNotNull(dto.getNFoutAimTemp()),TPssSlabHeatStd::getNFoutAimTemp,dto.getNFoutAimTemp())
                .eq(ObjectUtil.isNotNull(dto.getNFoutMinTemp()),TPssSlabHeatStd::getNFoutMinTemp,dto.getNFoutMinTemp())
                .eq(ObjectUtil.isNotNull(dto.getNSlabHtTempDiff()),TPssSlabHeatStd::getNSlabHtTempDiff,dto.getNSlabHtTempDiff())
                .like(ObjectUtil.isNotNull(dto.getCReserver2()),TPssSlabHeatStd::getCReserver2,dto.getCReserver2())
                .like(ObjectUtil.isNotNull(dto.getCReserver4()),TPssSlabHeatStd::getCReserver4,dto.getCReserver4())
                .like(ObjectUtil.isNotNull(dto.getCReserver6()),TPssSlabHeatStd::getCReserver6,dto.getCReserver6())
                .like(ObjectUtil.isNotNull(dto.getCReserver8()),TPssSlabHeatStd::getCReserver8,dto.getCReserver8())
                .orderByDesc(TPssSlabHeatStd::getNId)
                .select(TPssSlabHeatStd.class,x -> VoToColumnUtil.fieldsToColumns(TPssSlabHeatStdPageVo.class).contains(x.getProperty()));
        IPage<TPssSlabHeatStd> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssSlabHeatStdPageVo.class);
    }

    @Override
    public TPssSlabHeatStdVo queryInfo(Long id) {
        TPssSlabHeatStd tPssSlabHeatStd = this.baseMapper.selectById(id);
        if (tPssSlabHeatStd == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssSlabHeatStd, TPssSlabHeatStdVo.class);
    }

    @Override
    public TPssSlabHeatStdVo add(AddTPssSlabHeatStdDto dto) {
        if (dto.getNFoutMaxTemp().compareTo(dto.getNFoutMinTemp()) <= 0) throw new MyException("出炉温度上限必须大于出炉温度下限");
        if (dto.getCReserver2().compareTo(dto.getCReserver1()) <= 0) throw new MyException("加热一段最高温度必须大于加热一段最低温度");
        if (dto.getCReserver4().compareTo(dto.getCReserver3()) <= 0) throw new MyException("加热二段最高温度必须大于加热二段最低温度");
        if (dto.getCReserver6().compareTo(dto.getCReserver5()) <= 0) throw new MyException("加热三段最高温度必须大于加热三段最低温度");
        if (dto.getCReserver8().compareTo(dto.getCReserver7()) <= 0) throw new MyException("加热四段最高温度必须大于加热四段最低温度");
        TPssSlabHeatStd tPssSlabHeatStd = BeanUtil.toBean(dto, TPssSlabHeatStd.class);
        this.baseMapper.insert(tPssSlabHeatStd);

        return BeanUtil.copyProperties(tPssSlabHeatStd, TPssSlabHeatStdVo.class);
    }

    @Override
    public TPssSlabHeatStdVo update(UpdateTPssSlabHeatStdDto dto) {
        if (dto.getNFoutMaxTemp().compareTo(dto.getNFoutMinTemp()) <= 0) throw new MyException("出炉温度上限必须大于出炉温度下限");
        if (dto.getCReserver2().compareTo(dto.getCReserver1()) <= 0) throw new MyException("加热一段最高温度必须大于加热一段最低温度");
        if (dto.getCReserver4().compareTo(dto.getCReserver3()) <= 0) throw new MyException("加热二段最高温度必须大于加热二段最低温度");
        if (dto.getCReserver6().compareTo(dto.getCReserver5()) <= 0) throw new MyException("加热三段最高温度必须大于加热三段最低温度");
        if (dto.getCReserver8().compareTo(dto.getCReserver7()) <= 0) throw new MyException("加热四段最高温度必须大于加热四段最低温度");
        TPssSlabHeatStd tPssSlabHeatStd = BeanUtil.toBean(dto, TPssSlabHeatStd.class);
        this.baseMapper.updateById(tPssSlabHeatStd);

        return BeanUtil.copyProperties(tPssSlabHeatStd, TPssSlabHeatStdVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssSlabHeatStdPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssSlabHeatStdPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssSlabHeatStd.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssSlabHeatStdPageDto dto, Boolean isTemplate) {
        List<TPssSlabHeatStdPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssSlabHeatStdPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "SlabHeatStd" + ExcelTypeEnum.XLSX.getValue());
    }
}
