
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPssYMatStockRecordDto;
import com.aitos.pss.dto.inventory.TPssYMatStockRecordPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssYMatStockRecordDto;
import com.aitos.pss.entity.inventory.TPssYMatStockRecord;
import com.aitos.pss.vo.inventory.TPssYMatStockRecordPageVo;
import com.aitos.pss.vo.inventory.TPssYMatStockRecordVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/

public interface IMatStockRecordService extends IService<TPssYMatStockRecord> {
    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssYMatStockRecordPageVo> ueryPage(TPssYMatStockRecordPageDto dto);
    /**
     * 详情查询
     * @param id
     * @return
     */
    TPssYMatStockRecordVo queryInfo(Long id);
    /**
     * 新增
     * @param dto
     * @return
     */
    Long add(AddTPssYMatStockRecordDto dto);
    /**
     * 更新
     * @param dto
     * @return
     */
    Boolean update(UpdateTPssYMatStockRecordDto dto);
}
