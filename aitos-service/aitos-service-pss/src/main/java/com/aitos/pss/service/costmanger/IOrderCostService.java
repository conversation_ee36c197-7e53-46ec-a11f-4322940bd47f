package com.aitos.pss.service.costmanger;

import com.aitos.pss.dto.costmanger.TPssOrderCostPageDto;
import com.aitos.pss.entity.costmanger.TPssOrderCost;
import com.aitos.pss.vo.costmanger.TPssOrderCostChartVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/

public interface IOrderCostService extends IService<TPssOrderCost> {

    /**
     * 查询
     * @param dto
     * @return
     */
    TPssOrderCostChartVo queryPanel(@Valid TPssOrderCostPageDto dto);
}