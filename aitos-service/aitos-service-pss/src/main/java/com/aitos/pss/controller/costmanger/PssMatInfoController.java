package com.aitos.pss.controller.costmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.costmanger.AddTPssMatInfoDto;
import com.aitos.pss.dto.costmanger.TPssMatInfoPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssMatInfoDto;
import com.aitos.pss.service.costmanger.IPssMatInfoService;
import com.aitos.pss.vo.costmanger.TPssMatInfoPageVo;
import com.aitos.pss.vo.costmanger.TPssMatInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 投入产出管理
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssmatinfo")
@Tag(name = "/pss"  + "/pssmatinfo",description = "投入产出管理代码")
@AllArgsConstructor
public class PssMatInfoController {


    private final IPssMatInfoService pssMatInfoService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssMatInfo列表(分页)")
    public RT<PageOutput<TPssMatInfoPageVo>> page(@Valid TPssMatInfoPageDto dto){

        return RT.ok(pssMatInfoService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssMatInfo信息")
    public RT<TPssMatInfoVo> info(@RequestParam Long id){

        return RT.ok(pssMatInfoService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssMatInfo")
    @AitLog(value = "投入产出管理新增数据")
    public RT<TPssMatInfoVo> add(@Valid @RequestBody AddTPssMatInfoDto dto){

        return RT.ok(pssMatInfoService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssMatInfo")
    @AitLog(value = "投入产出管理修改数据")
    public RT<TPssMatInfoVo> update(@Valid @RequestBody UpdateTPssMatInfoDto dto){

        return RT.ok(pssMatInfoService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "投入产出管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssMatInfoService.removeBatchByIds(ids));
    }

}