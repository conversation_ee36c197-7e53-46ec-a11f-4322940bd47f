package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssQualityCodeDto;
import com.aitos.pss.dto.qualitymanger.TPssQualityCodePageDto;
import com.aitos.pss.dto.qualitymanger.TranTPssQualityCodeDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualityCodeDto;
import com.aitos.pss.entity.qualitymanger.*;
import com.aitos.pss.enums.PssQDataStatusEnum;
import com.aitos.pss.mapper.qualitymanger.TPssQualityCodeMapper;
import com.aitos.pss.service.qualitymanger.*;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodePageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodeVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-13
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class QualityCodeServiceImpl extends MPJBaseServiceImpl<TPssQualityCodeMapper, TPssQualityCode> implements IQualityCodeService {

    private final IQualStdLibSubService qualStdLibSubService;

    private final ITPssQualityCodeLibService qualityCodeLibService;

    private final IQualityCodeSubService qualityCodeSubService;

    private final IQualStdLibMainService qualStdLibMainListService;

    private final IPssStdchemLgService stdchemLgService;

    @Override
    public PageOutput<TPssQualityCodePageVo> queryPage(TPssQualityCodePageDto dto) {
        LambdaQueryWrapper<TPssQualityCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCQualCodeName()),TPssQualityCode::getCQualCodeName,dto.getCQualCodeName())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssQualityCode::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(StrUtil.isNotBlank(dto.getCQualCode()),TPssQualityCode::getCQualCode,dto.getCQualCode())
                .orderByDesc(TPssQualityCode::getNId)
                .select(TPssQualityCode.class,x -> VoToColumnUtil.fieldsToColumns(TPssQualityCodePageVo.class).contains(x.getProperty()));
        IPage<TPssQualityCode> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssQualityCodePageVo.class);
    }

    @Override
    public List<TPssQualityCodePageVo> queryList(TPssQualityCodePageDto dto) {
        LambdaQueryWrapper<TPssQualityCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCQualCodeName()),TPssQualityCode::getCQualCodeName,dto.getCQualCodeName())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssQualityCode::getCStlGrdCd,dto.getCStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCQualCode()),TPssQualityCode::getCQualCode,dto.getCQualCode())
                .eq(Objects.nonNull(dto.getCStatus()),TPssQualityCode::getCStatus,dto.getCStatus())
                .orderByDesc(TPssQualityCode::getNId)
                .select(TPssQualityCode.class,x -> VoToColumnUtil.fieldsToColumns(TPssQualityCodePageVo.class).contains(x.getProperty()));
        List<TPssQualityCode> qualityCodeList = this.baseMapper.selectList(queryWrapper);

        return BeanUtil.copyToList(qualityCodeList,TPssQualityCodePageVo.class);
    }

    @Override
    public TPssQualityCodeVo queryInfo(Long id) {
        TPssQualityCode tPssQualityCode = this.baseMapper.selectById(id);
        if (tPssQualityCode == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.toBean(tPssQualityCode, TPssQualityCodeVo.class);
    }

    @Override
    @Transactional
    public Long add(AddTPssQualityCodeDto addTPssQualityCodeDto) {
        TPssQualityCode tPssQualityCode = BeanUtil.toBean(addTPssQualityCodeDto, TPssQualityCode.class);
        Long dbCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssQualityCode>lambdaQuery()
                                .eq(TPssQualityCode::getCQualCode, tPssQualityCode.getCQualCode())
                );
        if (dbCount > 0) {
            throw new MyException("质量编码不能重复喔~~~");
        }

        this.baseMapper.insert(tPssQualityCode);

        return tPssQualityCode.getNId();
    }

    @Override
    public Long copy(AddTPssQualityCodeDto dto) {
        TPssQualityCode tPssQualityCode = BeanUtil.toBean(dto, TPssQualityCode.class);
        Long dbCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssQualityCode>lambdaQuery()
                                .eq(TPssQualityCode::getCQualCode, tPssQualityCode.getCQualCode())
                );
        if (dbCount > 0) {
            throw new MyException("质量编码不能重复喔~~~");
        }

        // 查询子表内容
        TPssQualityCode dbQualityCode = this.baseMapper.selectById(dto.getNId());
        List<TPssQualityCodeSub> qualityCodeSubList =
                qualityCodeSubService.list(
                        Wrappers.<TPssQualityCodeSub>lambdaQuery()
                                .eq(TPssQualityCodeSub::getCQualCode, dbQualityCode.getCQualCode())
                );
        if (CollectionUtils.isNotEmpty(qualityCodeSubList)) {
            for (TPssQualityCodeSub qualityCodeSub : qualityCodeSubList) {
                qualityCodeSub.setNId(null);
                qualityCodeSub.setCQualCode(dto.getCQualCode());
                qualityCodeSub.setCQualCodeName(dto.getCQualCodeName());
            }
            qualityCodeSubService.saveBatch(qualityCodeSubList);
        }

        tPssQualityCode.setCStatus(PssQDataStatusEnum.CREATED.getCode());
        tPssQualityCode.setNId(null);
        this.baseMapper.insert(tPssQualityCode);

        return tPssQualityCode.getNId();
    }

    @Override
    @Transactional
    public Boolean update(UpdateTPssQualityCodeDto updateTPssQualityCodeDto) {
        if (Objects.equals(PssQDataStatusEnum.CONFIRM.getCode(),updateTPssQualityCodeDto.getCStatus())) {
            throw new MyException("已转化数据不可编辑");
        }

        Long dbCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssQualityCode>lambdaQuery()
                                .eq(TPssQualityCode::getCQualCode, updateTPssQualityCodeDto.getCQualCode())
                                .ne(TPssQualityCode::getNId, updateTPssQualityCodeDto.getNId())
                );
        if (dbCount > 0) {
            throw new MyException("质量编码不能重复喔~~~");
        }

        TPssQualityCode tPssQualityCode = BeanUtil.toBean(updateTPssQualityCodeDto, TPssQualityCode.class);
        this.baseMapper.updateById(tPssQualityCode);

        return Boolean.TRUE;
    }

    @Override
    public TPssQualityCodeVo updqualityCodeTranate(TranTPssQualityCodeDto dto) {
        List<TPssQualStdLibSub> qualStdLibSubList =
                qualStdLibSubService.list(
                        Wrappers.<TPssQualStdLibSub>lambdaQuery()
                                .eq(TPssQualStdLibSub::getCStdcode, dto.getCQualCode())
                );
        if (CollectionUtils.isNotEmpty(qualStdLibSubList)) {
            List<TPssQualityCodeLib> qualityCodeLibList = new ArrayList<>();
            for (TPssQualStdLibSub qualStdLibSub : qualStdLibSubList) {
                TPssQualityCodeLib qualityCodeLib = BeanUtil.toBean(qualStdLibSub, TPssQualityCodeLib.class);
                qualityCodeLib.setCQualCode(dto.getCQualCode());
                qualityCodeLib.setCQualCodeName(dto.getCQualCodeName());
                qualityCodeLib.setCStlGrdCd(dto.getCStlGrdCd());
                qualityCodeLib.setCStlGrdName(dto.getCStlGrdDesc());
                qualityCodeLib.setCQualStdLibCode(qualStdLibSub.getCStdcode());
                qualityCodeLib.setCQualStdLibItemCode(qualStdLibSub.getCQualityStandardLibItemCode());
                qualityCodeLib.setCQualStdLibItemName(qualStdLibSub.getCQualityStandardLibItemName());
                qualityCodeLibList.add(qualityCodeLib);
            }

            qualityCodeLibService.saveBatch(qualityCodeLibList);
        }

        //获得主表关联的子表的list
        List<TPssQualityCodeSub> qualityCodeSubList = qualityCodeSubService.list(
                Wrappers.<TPssQualityCodeSub>lambdaQuery()
                        .eq(TPssQualityCodeSub::getCQualCode, dto.getCQualCode())
        );

        //获得与子表关联的联质检标准库主表信息
        if (CollectionUtils.isNotEmpty(qualityCodeSubList)) {
            List<String> nfStdItemList = qualityCodeSubList.stream().map(TPssQualityCodeSub::getNFstditem).collect(Collectors.toList());
            List<TPssQualStdLibMain> beforeFilterList = qualStdLibMainListService.list(
                    Wrappers.<TPssQualStdLibMain>lambdaQuery()
                            .in(TPssQualStdLibMain::getCStdcode, nfStdItemList));

            //筛选质检标准库中质检标准类别（cStdtypeCode）是（HXCF）的所有数据
            List<TPssQualStdLibMain> afterFilterList = beforeFilterList.stream()
                    .filter(qualStdLibMain -> "HXCF".equals(qualStdLibMain.getCStdtypeCode())).collect(Collectors.toList());
            Map<String, TPssQualStdLibMain> stdLibMainMap = afterFilterList.stream().collect(Collectors.toMap(TPssQualStdLibMain::getCStdcode, v -> v));
            if (CollectionUtils.isNotEmpty(afterFilterList)) {
                //遍历质检标准库中过滤后主表的关联的子表信息
                List<String> cStdCodeList = afterFilterList.stream().map(TPssQualStdLibMain::getCStdcode).collect(Collectors.toList());
                List<TPssQualStdLibSub> list = qualStdLibSubService.list(
                        Wrappers.<TPssQualStdLibSub>lambdaQuery()
                                .in(TPssQualStdLibSub::getCStdcode, cStdCodeList)
                );

                //最终要插入表的list
                List<TPssStdchemLg> stdchemLgList = new ArrayList<>();
                //遍历质检标准库子表的信息并转换为TPssStdchemLg类
                for (TPssQualStdLibSub qualStdLibSub : list) {
                    TPssStdchemLg stdchemLg = BeanUtil.toBean(qualStdLibSub, TPssStdchemLg.class);
                    stdchemLg.setNId(null);
                    stdchemLg.setCStlGrdCd(dto.getCStlGrdCd());
                    stdchemLg.setCStlGrdDesc(dto.getCStlGrdDesc());
                    stdchemLg.setCChemCompMax(qualStdLibSub.getNUppervalue());
                    stdchemLg.setCChemCompMin(qualStdLibSub.getNLowervalue());
                    stdchemLg.setCCompCodeMin(qualStdLibSub.getCLowersymbol());
                    stdchemLg.setCCompCodeMax(qualStdLibSub.getCUppersymbol());
                    stdchemLg.setCQualityCode(dto.getCQualCode());
                    stdchemLg.setCStdClass(stdLibMainMap.getOrDefault(qualStdLibSub.getCStdcode(), new TPssQualStdLibMain()).getCValueType());
                    stdchemLg.setCChemCompCd(qualStdLibSub.getCQualityStandardLibItemName());
                    stdchemLgList.add(stdchemLg);
                }

                //插入数据
                stdchemLgService.saveBatch(stdchemLgList);
            }


        }

        TPssQualityCode qualityCode = BeanUtil.copyProperties(dto, TPssQualityCode.class);
        qualityCode.setCStatus(PssQDataStatusEnum.CONFIRM.getCode());
        updateById(qualityCode);

        return BeanUtil.copyProperties(qualityCode, TPssQualityCodeVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssQualityCodePageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssQualityCodePageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssQualityCode.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssQualityCodePageDto dto, Boolean isTemplate) {
        List<TPssQualityCodePageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssQualityCodePageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "QualityCode" + ExcelTypeEnum.XLSX.getValue());
    }

}
