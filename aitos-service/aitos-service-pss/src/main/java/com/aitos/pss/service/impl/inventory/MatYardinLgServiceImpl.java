package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.AddTPssMatYardinLgDto;
import com.aitos.pss.dto.inventory.TPssMatYardinLgPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMatYardinLgDto;
import com.aitos.pss.entity.inventory.TPssMatYardinLg;
import com.aitos.pss.mapper.inventory.TPssMatYardinLgMapper;
import com.aitos.pss.service.inventory.IMatYardinLgService;
import com.aitos.pss.vo.inventory.TPssMatYardinLgPageVo;
import com.aitos.pss.vo.inventory.TPssMatYardinLgVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class MatYardinLgServiceImpl extends ServiceImpl<TPssMatYardinLgMapper, TPssMatYardinLg> implements IMatYardinLgService {

    @Override
    public PageOutput<TPssMatYardinLgPageVo> queryPage(TPssMatYardinLgPageDto dto) {
        LambdaQueryWrapper<TPssMatYardinLg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getNId()),TPssMatYardinLg::getNId,dto.getNId())
                .like(StrUtil.isNotBlank(dto.getCYardinId()),TPssMatYardinLg::getCYardinId,dto.getCYardinId())
                .like(StrUtil.isNotBlank(dto.getCIfFlag()),TPssMatYardinLg::getCIfFlag,dto.getCIfFlag())
                .like(StrUtil.isNotBlank(dto.getCMatname()),TPssMatYardinLg::getCMatname,dto.getCMatname())
                .like(StrUtil.isNotBlank(dto.getCLineCd()),TPssMatYardinLg::getCLineCd,dto.getCLineCd())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssMatYardinLg::getCStlGrdCd,dto.getCStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCLotId()),TPssMatYardinLg::getCLotId,dto.getCLotId())
                .like(StrUtil.isNotBlank(dto.getCTaskListId()),TPssMatYardinLg::getCTaskListId,dto.getCTaskListId())
                .like(StrUtil.isNotBlank(dto.getCSaleNo()),TPssMatYardinLg::getCSaleNo,dto.getCSaleNo())
                .like(StrUtil.isNotBlank(dto.getCOrderIdFinal()),TPssMatYardinLg::getCOrderIdFinal,dto.getCOrderIdFinal())
                .eq(ObjectUtil.isNotNull(dto.getNMatThk()),TPssMatYardinLg::getNMatThk,dto.getNMatThk())
                .eq(ObjectUtil.isNotNull(dto.getNMatDia()),TPssMatYardinLg::getNMatDia,dto.getNMatDia())
                .eq(ObjectUtil.isNotNull(dto.getNMatCnt()),TPssMatYardinLg::getNMatCnt,dto.getNMatCnt())
                .eq(ObjectUtil.isNotNull(dto.getNMatWgt()),TPssMatYardinLg::getNMatWgt,dto.getNMatWgt())
                .like(StrUtil.isNotBlank(dto.getCFromYard()),TPssMatYardinLg::getCFromYard,dto.getCFromYard())
                .like(StrUtil.isNotBlank(dto.getCLoc()),TPssMatYardinLg::getCLoc,dto.getCLoc())
                .like(StrUtil.isNotBlank(dto.getCTransNo()),TPssMatYardinLg::getCTransNo,dto.getCTransNo())
                .like(StrUtil.isNotBlank(dto.getCYardinType()),TPssMatYardinLg::getCYardinType,dto.getCYardinType())
                .like(StrUtil.isNotBlank(dto.getCMemo()),TPssMatYardinLg::getCMemo,dto.getCMemo())
                .like(StrUtil.isNotBlank(dto.getCCrew()),TPssMatYardinLg::getCCrew,dto.getCCrew())
                .like(StrUtil.isNotBlank(dto.getCDataSource()),TPssMatYardinLg::getCDataSource,dto.getCDataSource())
                .like(StrUtil.isNotBlank(dto.getCUploadFlag()),TPssMatYardinLg::getCUploadFlag,dto.getCUploadFlag())
                .like(StrUtil.isNotBlank(dto.getCProdGrd()),TPssMatYardinLg::getCProdGrd,dto.getCProdGrd())
                .like(StrUtil.isNotBlank(dto.getCMatId()),TPssMatYardinLg::getCMatId,dto.getCMatId())
                .eq(ObjectUtil.isNotNull(dto.getNCnt()),TPssMatYardinLg::getNCnt,dto.getNCnt())
                .like(StrUtil.isNotBlank(dto.getCMatcode()),TPssMatYardinLg::getCMatcode,dto.getCMatcode())
                .like(StrUtil.isNotBlank(dto.getCMatType()),TPssMatYardinLg::getCMatType,dto.getCMatType())
                .like(StrUtil.isNotBlank(dto.getCYardCd()),TPssMatYardinLg::getCYardCd,dto.getCYardCd())
                .like(StrUtil.isNotBlank(dto.getCMatQulCd()),TPssMatYardinLg::getCMatQulCd,dto.getCMatQulCd())
                .like(StrUtil.isNotBlank(dto.getCMatLotId()),TPssMatYardinLg::getCMatLotId,dto.getCMatLotId())
                .like(StrUtil.isNotBlank(dto.getCOrderNo()),TPssMatYardinLg::getCOrderNo,dto.getCOrderNo())
                .like(StrUtil.isNotBlank(dto.getCSaleSn()),TPssMatYardinLg::getCSaleSn,dto.getCSaleSn())
                .like(StrUtil.isNotBlank(dto.getCMatItem()),TPssMatYardinLg::getCMatItem,dto.getCMatItem())
                .eq(ObjectUtil.isNotNull(dto.getNMatWth()),TPssMatYardinLg::getNMatWth,dto.getNMatWth())
                .eq(ObjectUtil.isNotNull(dto.getNMatLen()),TPssMatYardinLg::getNMatLen,dto.getNMatLen())
                .eq(ObjectUtil.isNotNull(dto.getNMatWgtCal()),TPssMatYardinLg::getNMatWgtCal,dto.getNMatWgtCal())
                .like(StrUtil.isNotBlank(dto.getCSource()),TPssMatYardinLg::getCSource,dto.getCSource())
                .like(StrUtil.isNotBlank(dto.getCToYard()),TPssMatYardinLg::getCToYard,dto.getCToYard())
                .like(StrUtil.isNotBlank(dto.getCTransType()),TPssMatYardinLg::getCTransType,dto.getCTransType())
                .like(StrUtil.isNotBlank(dto.getCJilianId()),TPssMatYardinLg::getCJilianId,dto.getCJilianId())
                .like(StrUtil.isNotBlank(dto.getCYardinRsn()),TPssMatYardinLg::getCYardinRsn,dto.getCYardinRsn())
                .like(StrUtil.isNotBlank(dto.getCShift()),TPssMatYardinLg::getCShift,dto.getCShift())
                .like(StrUtil.isNotBlank(dto.getCMonth()),TPssMatYardinLg::getCMonth,dto.getCMonth())
                .like(StrUtil.isNotBlank(dto.getCPgmId()),TPssMatYardinLg::getCPgmId,dto.getCPgmId())
                .eq(ObjectUtil.isNotNull(dto.getCMsgNo()),TPssMatYardinLg::getCMsgNo,dto.getCMsgNo())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssMatYardinLg::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssMatYardinLg::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssMatYardinLg::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssMatYardinLg::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssMatYardinLg::getDtCreateDateTime)
                .select(TPssMatYardinLg.class,x -> VoToColumnUtil.fieldsToColumns(TPssMatYardinLgPageVo.class).contains(x.getProperty()));
        IPage<TPssMatYardinLg> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        PageOutput<TPssMatYardinLgPageVo> pageOutput = ConventPage.getPageOutput(page, TPssMatYardinLgPageVo.class);

        return pageOutput;
    }

    @Override
    public TPssMatYardinLgVo queryInfo(Long id) {
        TPssMatYardinLg tPssMatYardinLg = this.baseMapper.selectById(id);
        if (tPssMatYardinLg == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssMatYardinLg, TPssMatYardinLgVo.class);
    }

    @Override
    public TPssMatYardinLgVo add(AddTPssMatYardinLgDto dto) {
        TPssMatYardinLg tPssMatYardinLg = BeanUtil.toBean(dto, TPssMatYardinLg.class);
        this.baseMapper.insert(tPssMatYardinLg);

        return BeanUtil.copyProperties(tPssMatYardinLg, TPssMatYardinLgVo.class);
    }

    @Override
    public TPssMatYardinLgVo update(UpdateTPssMatYardinLgDto dto) {
        TPssMatYardinLg tPssMatYardinLg = BeanUtil.toBean(dto, TPssMatYardinLg.class);
        this.baseMapper.updateById(tPssMatYardinLg);

        return BeanUtil.copyProperties(tPssMatYardinLg, TPssMatYardinLgVo.class);
    }
}
