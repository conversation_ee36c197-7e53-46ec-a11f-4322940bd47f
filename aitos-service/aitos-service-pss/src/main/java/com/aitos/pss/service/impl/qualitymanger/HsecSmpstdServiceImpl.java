package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssHsecSmpstdDto;
import com.aitos.pss.dto.qualitymanger.TPssHsecSmpstdPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssHsecSmpstdDto;
import com.aitos.pss.entity.qualitymanger.TPssHsecSmpstd;
import com.aitos.pss.mapper.qualitymanger.TPssHsecSmpstdMapper;
import com.aitos.pss.service.qualitymanger.IHsecSmpstdService;
import com.aitos.pss.vo.qualitymanger.TPssHsecSmpstdPageVo;
import com.aitos.pss.vo.qualitymanger.TPssHsecSmpstdVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-17
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class HsecSmpstdServiceImpl extends ServiceImpl<TPssHsecSmpstdMapper, TPssHsecSmpstd> implements IHsecSmpstdService {

    @Override
    public PageOutput<TPssHsecSmpstdPageVo> queryPage(TPssHsecSmpstdPageDto dto) {
        LambdaQueryWrapper<TPssHsecSmpstd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCQualityCode()),TPssHsecSmpstd::getCQualityCode,dto.getCQualityCode())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssHsecSmpstd::getCStlGrdCd,dto.getCStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCLineCd()),TPssHsecSmpstd::getCLineCd,dto.getCLineCd())
                .like(StrUtil.isNotBlank(dto.getCQualityCodeName()),TPssHsecSmpstd::getCQualityCodeName,dto.getCQualityCodeName())
                .like(StrUtil.isNotBlank(dto.getCStlGrdDesc()),TPssHsecSmpstd::getCStlGrdDesc,dto.getCStlGrdDesc())
                .eq(ObjectUtil.isNotNull(dto.getNThk()),TPssHsecSmpstd::getNThk,dto.getNThk())
                .eq(ObjectUtil.isNotNull(dto.getNSmpNum()),TPssHsecSmpstd::getNSmpNum,dto.getNSmpNum())
                .like(StrUtil.isNotBlank(dto.getCSpec()),TPssHsecSmpstd::getCSpec,dto.getCSpec())
                .like(StrUtil.isNotBlank(dto.getCSmpType()),TPssHsecSmpstd::getCSmpType,dto.getCSmpType())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssHsecSmpstd::getNEnabledMark,dto.getNEnabledMark())
                .orderByDesc(TPssHsecSmpstd::getDtCreateDateTime)
                .select(TPssHsecSmpstd.class,x -> VoToColumnUtil.fieldsToColumns(TPssHsecSmpstdPageVo.class).contains(x.getProperty()));
        IPage<TPssHsecSmpstd> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssHsecSmpstdPageVo.class);
    }

    @Override
    public TPssHsecSmpstdVo queryInfo(Long id) {
        TPssHsecSmpstd tPssHsecSmpstd = this.baseMapper.selectById(id);
        if (tPssHsecSmpstd == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssHsecSmpstd, TPssHsecSmpstdVo.class);
    }

    @Override
    public TPssHsecSmpstdVo add(AddTPssHsecSmpstdDto dto) {
        TPssHsecSmpstd tPssHsecSmpstd = BeanUtil.toBean(dto, TPssHsecSmpstd.class);
        this.baseMapper.insert(tPssHsecSmpstd);

        return BeanUtil.copyProperties(tPssHsecSmpstd, TPssHsecSmpstdVo.class);
    }

    @Override
    public TPssHsecSmpstdVo update(UpdateTPssHsecSmpstdDto dto) {
        TPssHsecSmpstd tPssHsecSmpstd = BeanUtil.toBean(dto, TPssHsecSmpstd.class);
        this.baseMapper.updateById(tPssHsecSmpstd);

        return BeanUtil.copyProperties(tPssHsecSmpstd, TPssHsecSmpstdVo.class);
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssHsecSmpstdPageDto dto, Boolean isTemplate) {
        List<TPssHsecSmpstdPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : (queryPage(dto)).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssHsecSmpstdPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "HsecSmpstd" + ExcelTypeEnum.XLSX.getValue());
    }
}
