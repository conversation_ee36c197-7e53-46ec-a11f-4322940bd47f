package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssCommonCodeDto;
import com.aitos.pss.dto.planmanger.TPssCommonCodePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssCommonCodeDto;
import com.aitos.pss.entity.planmanger.TPssCommonCode;
import com.aitos.pss.vo.planmanger.TPssCommonCodePageVo;
import com.aitos.pss.vo.planmanger.TPssCommonCodeVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-15
* @Version 1.0
*/

public interface ITPssCommonCodeService extends IService<TPssCommonCode> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssCommonCodePageVo> queryPage(@Valid TPssCommonCodePageDto dto);

    /**
     * 详情查询
     * @param id
     * @return
     */
    TPssCommonCodeVo queryInfo(Long id);

    /**
     * 新增数据
     * @param dto
     * @return
     */
    TPssCommonCodeVo add(@Valid AddTPssCommonCodeDto dto);

    /**
     * 更新数据
     * @param dto
     * @return
     */
    TPssCommonCodeVo update(UpdateTPssCommonCodeDto dto);
}
