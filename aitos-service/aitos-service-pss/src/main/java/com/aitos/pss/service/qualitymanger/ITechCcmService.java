
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssTechCcmDto;
import com.aitos.pss.dto.qualitymanger.TPssTechCcmPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechCcmDto;
import com.aitos.pss.entity.qualitymanger.TPssTechCcm;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/

public interface ITechCcmService extends IService<TPssTechCcm> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssTechCcmPageVo> queryPage(@Valid TPssTechCcmPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssTechCcmVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssTechCcmVo add(@Valid AddTPssTechCcmDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssTechCcmVo update(@Valid UpdateTPssTechCcmDto dto);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssTechCcmPageDto dto, Boolean isTemplate);
}
