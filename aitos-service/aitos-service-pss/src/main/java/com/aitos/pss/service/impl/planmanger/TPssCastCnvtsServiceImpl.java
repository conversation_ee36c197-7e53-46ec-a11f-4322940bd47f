package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssCastCnvtsDto;
import com.aitos.pss.dto.planmanger.TPssCastCnvtsPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssCastCnvtsDto;
import com.aitos.pss.entity.planmanger.TPssCastCnvts;
import com.aitos.pss.mapper.planmanger.TPssCastCnvtsMapper;
import com.aitos.pss.service.planmanger.ITPssCastCnvtsService;
import com.aitos.pss.vo.planmanger.TPssCastCnvtsPageVo;
import com.aitos.pss.vo.planmanger.TPssCastCnvtsVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssCastCnvtsServiceImpl extends ServiceImpl<TPssCastCnvtsMapper, TPssCastCnvts> implements ITPssCastCnvtsService {

    @Override
    public PageOutput<TPssCastCnvtsPageVo> queryPage(TPssCastCnvtsPageDto dto) {
        LambdaQueryWrapper<TPssCastCnvts> queryWrapper =
                Wrappers.<TPssCastCnvts>lambdaQuery()
                        .eq(Objects.nonNull(dto.getCProLine()),TPssCastCnvts::getCProLine,dto.getCProLine())
                        .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssCastCnvts::getCStlGrdCd,dto.getCStlGrdCd())
                        .eq(Objects.nonNull(dto.getCMatQulId()),TPssCastCnvts::getCMatQulId,dto.getCMatQulId())
                        .eq(ObjectUtil.isNotNull(dto.getNHeatnumMin()),TPssCastCnvts::getNHeatnumMin,dto.getNHeatnumMin())
                        .eq(ObjectUtil.isNotNull(dto.getNHeatnumMax()),TPssCastCnvts::getNHeatnumMax,dto.getNHeatnumMax())
                        .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssCastCnvts::getNEnabledMark,dto.getNEnabledMark())
                        .orderByDesc(TPssCastCnvts::getNId)
                        .select(TPssCastCnvts.class,x -> VoToColumnUtil.fieldsToColumns(TPssCastCnvtsPageVo.class).contains(x.getProperty()));
        IPage<TPssCastCnvts> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssCastCnvtsPageVo.class);
    }

    @Override
    public TPssCastCnvtsVo queryInfo(Long id) {
        TPssCastCnvts tPssCastCnvts = this.baseMapper.selectById(id);
        if (tPssCastCnvts == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssCastCnvts, TPssCastCnvtsVo.class);
    }

    @Override
    public TPssCastCnvtsVo add(AddTPssCastCnvtsDto dto) {
        TPssCastCnvts tPssCastCnvts = BeanUtil.toBean(dto, TPssCastCnvts.class);

        if (Objects.isNull(dto.getNHeatnumMax()) || Objects.isNull(dto.getNHeatnumMin())) throw new MyException("炉数上下限不能为空");
        if (dto.getNHeatnumMax().compareTo(dto.getNHeatnumMin()) < 0) throw new MyException("炉数上限不能小于下限");

        this.baseMapper.insert(tPssCastCnvts);

        return BeanUtil.copyProperties(tPssCastCnvts, TPssCastCnvtsVo.class);
    }

    @Override
    public TPssCastCnvtsVo update(UpdateTPssCastCnvtsDto dto) {
        TPssCastCnvts tPssCastCnvts = BeanUtil.toBean(dto, TPssCastCnvts.class);
        this.baseMapper.updateById(tPssCastCnvts);

        return BeanUtil.copyProperties(tPssCastCnvts, TPssCastCnvtsVo.class);
    }
}
