package com.aitos.pss.handler;

import cn.dev33.satoken.stp.StpUtil;
import com.aitos.common.core.enums.DeleteMark;
import com.aitos.common.mybatis.handler.MyMetaObjectHandler;
import com.aitos.pss.constant.PssPubFieldConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;


/**
 * @title 数据审计处理器
 * @desc 用于新增或者更新  自动插入 相应字段
 * <AUTHOR>
 * */
@Slf4j
@Component
@Primary
public class PssMetaObjectHandler extends MyMetaObjectHandler {


    /**
     * @title 新增自动填充
     * @desc fieldName 使用实体类字段名 而不是数据库字段名
     * */
    @Override
    public void insertFill(MetaObject metaObject) {
        super.insertFill(metaObject);

        long userId;
        try {
            userId = StpUtil.getLoginIdAsLong();
        } catch (Exception e) {
            log.error("获取当前登录用户Id失败", e);
            return;
        }

        this.setFieldValByName(PssPubFieldConstant.N_CREATE_USER_ID, userId, metaObject);

        this.setFieldValByName(PssPubFieldConstant.DT_CREATE_DATETIME, LocalDateTime.now(), metaObject);

        this.fillStrategy( metaObject, PssPubFieldConstant.N_DELETE_MARK, DeleteMark.NODELETE.getCode());

        this.setFieldValByName(PssPubFieldConstant.N_MODIFY_USER_ID, userId, metaObject);

        this.setFieldValByName(PssPubFieldConstant.DT_MODIFY_DATETIME, LocalDateTime.now(), metaObject);
    }


    /**
     * @title 修改自动填充
     * @desc fieldName 使用实体类字段名 而不是数据库字段名
     * */
    @Override
    public void updateFill(MetaObject metaObject) {
        super.updateFill(metaObject);

        long userId;
        try {
            userId = StpUtil.getLoginIdAsLong();
        } catch (Exception e) {
            log.error("获取当前登录用户Id失败", e);
            return;
        }

        this.setFieldValByName(PssPubFieldConstant.N_MODIFY_USER_ID, userId, metaObject);

        this.setFieldValByName(PssPubFieldConstant.DT_MODIFY_DATETIME, LocalDateTime.now(), metaObject);

    }

}
