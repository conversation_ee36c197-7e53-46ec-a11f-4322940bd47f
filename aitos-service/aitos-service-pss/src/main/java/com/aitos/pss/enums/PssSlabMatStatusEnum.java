package com.aitos.pss.enums;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

@Getter
@Log4j2
public enum PssSlabMatStatusEnum {

    /** 等待质量判定 */
    WAITING_QUALITY_JUDGMENT("A1", "等待质量判定"),

    /** 坯料炼钢待入库 */
    STEEL_MAKING_PENDING_STORAGE("A2", "坯料炼钢待入库"),

    /** 入库前待处理 */
    PENDING_PROCESS_BEFORE_STORAGE("A3", "入库前待处理"),

    /** 坯料炼钢在库 */
    STEEL_MAKING_IN_STORAGE("A4", "坯料炼钢在库"),

    /** 坯料炼钢待装车确认 */
    STEEL_MAKING_PENDING_LOADING_CONFIRMATION("A5", "坯料炼钢待装车确认"),

    /** 坯料炼钢装车完成 */
    STEEL_MAKING_LOADING_COMPLETED("A6", "坯料炼钢装车完成"),

    /** 无效坯 */
    INVALID_BILET("A7", "无效坯"),

    /** 退库待确认 */
    RETURN_PENDING_CONFIRMATION("A8", "退库待确认"),

    /** 等待化学成分 */
    WAITING_CHEMICAL_COMPOSITION("A9", "等待化学成分"),

    /** 坯料炼钢库锁定 */
    STEEL_MAKING_STORAGE_LOCKED("A10", "坯料炼钢库锁定"),

    /** 坯料炼钢出库待确认 */
    STEEL_MAKING_OUTBOUND_PENDING_CONFIRMATION("A11", "坯料炼钢出库待确认"),

    /** 出库结束 */
    OUTBOUND_COMPLETED("A12", "出库结束"),

    /** 原料待入库 */
    RAW_MATERIAL_PENDING_STORAGE("B13", "原料待入库"),

    /** 原料在库 */
    RAW_MATERIAL_IN_STORAGE("B14", "原料在库"),

    /** 坯料轧废 */
    ROLLING_SCRAP("B15", "坯料轧废"),

    /** 坯料轧钢待装车确认 */
    ROLLING_PENDING_LOADING_CONFIRMATION("B16", "坯料轧钢待装车确认"),

    /** 坯料轧钢装车完成 */
    ROLLING_LOADING_COMPLETED("B17", "坯料轧钢装车完成"),

    /** 实际钢坯脱单等待 */
    ACTUAL_BILET_UNBUNDLING_WAITING("B18", "实际钢坯脱单等待"),

    /** 实际钢坯挂单等待 */
    ACTUAL_BILET_BUNDLING_WAITING("B19", "实际钢坯挂单等待"),

    /** 实际钢坯替代等待 */
    ACTUAL_BILET_REPLACEMENT_WAITING("B20", "实际钢坯替代等待"),

    /** 坯料退库待确认 */
    ROLLING_RETURN_PENDING_CONFIRMATION("B21", "坯料退库待确认"),

    /** 实际钢坯装炉等待 */
    ACTUAL_BILET_FURNACE_LOADING_WAITING("B22", "实际钢坯装炉等待"),

    /** 实际钢坯出炉等待 */
    ACTUAL_BILET_FURNACE_UNLOADING_WAITING("B23", "实际钢坯出炉等待"),

    /** 实际钢坯轧制等待 */
    ACTUAL_BILET_ROLLING_WAITING("B24", "实际钢坯轧制等待"),

    /** 坯料轧制结束 */
    ROLLING_COMPLETED("B25", "坯料轧制结束"),

    /** 实际钢坯废品脱单等待 */
    ACTUAL_SCRAP_UNBUNDLING_WAITING("B26", "实际钢坯废品脱单等待"),

    /** 实际钢坯甩料退库等待 */
    ACTUAL_REJECT_RETURN_WAITING("B27", "实际钢坯甩料退库等待"),

    /** 坯料轧钢出库结束 */
    ROLLING_OUTBOUND_COMPLETED("B28", "坯料轧钢出库结束"),

    /** 炉后替废废钢 */
    FURNACE_SCRAP_REPLACEMENT("B29", "炉后替废废钢"),

    /** 原料退库结束 */
    RAW_MATERIAL_RETURN_COMPLETED("B30", "原料退库结束"),

    /** 初判等待 */
    PRELIMINARY_JUDGMENT_WAITING("C31", "初判等待"),

    /** 成品轧钢待入库 */
    FINISHED_ROLLING_PENDING_STORAGE("C32", "成品轧钢待入库"),

    /** 成品轧钢待质量综判 */
    FINISHED_ROLLING_PENDING_QUALITY_JUDGMENT("C33", "成品轧钢待质量综判"),

    /** 成品轧钢在库 */
    FINISHED_ROLLING_IN_STORAGE("C34", "成品轧钢在库"),

    /** 轧钢副产品待入库 */
    ROLLING_BYPRODUCT_PENDING_STORAGE("C35", "轧钢副产品待入库"),

    /** 成品轧钢待装车确认 */
    FINISHED_ROLLING_PENDING_LOADING_CONFIRMATION("C36", "成品轧钢待装车确认"),

    /** 成品轧钢装车完成 */
    FINISHED_ROLLING_LOADING_COMPLETED("C37", "成品轧钢装车完成"),

    /** 等待拆/合捆 */
    WAITING_BUNDLE_ADJUSTMENT("C38", "等待拆/合捆"),

    /** 成品退生产线待确认 */
    FINISHED_RETURN_PRODUCTION_LINE_PENDING_CONFIRMATION("C39", "成品退生产线待确认"),

    /** 成品调拨出库待确认 */
    FINISHED_TRANSFER_OUTBOUND_PENDING_CONFIRMATION("C40", "成品调拨出库待确认"),

    /** 成品出库结束 */
    FINISHED_OUTBOUND_COMPLETED("C41", "成品出库结束"),

    /** 成品调拨出库结束 */
    FINISHED_TRANSFER_OUTBOUND_COMPLETED("C42", "成品调拨出库结束"),

    /** 实际线卷创建初始等待 */
    ACTUAL_COIL_CREATION_INITIAL_WAITING("C43", "实际线卷创建初始等待"),

    /** 实际线卷挂钩打捆确认 */
    ACTUAL_COIL_HOOK_BUNDLING_CONFIRMED("C44", "实际线卷挂钩打捆确认"),

    /** 实际线卷中断等待 */
    ACTUAL_COIL_INTERRUPTION_WAITING("C45", "实际线卷中断等待"),

    /** 实际线卷下架等待 */
    ACTUAL_COIL_UNLOADING_WAITING("C46", "实际线卷下架等待"),

    /** 实际线卷轧废结束 */
    ACTUAL_COIL_ROLLING_SCRAP_COMPLETED("C47", "实际线卷轧废结束"),

    /** 等待入中间库 */
    WAITING_INTERMEDIATE_STORAGE("C48", "等待入中间库"),

    /** 正在缓冷 */
    SLOW_COOLING_IN_PROGRESS("C49", "正在缓冷"),

    /** 中间库在库等待 */
    INTERMEDIATE_STORAGE_WAITING("C50", "中间库在库等待"),

    /** 等待探伤 */
    WAITING_FLAW_DETECTION("C51", "等待探伤");

    private final String code;
    private final String description;

    PssSlabMatStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举
     */
    public static PssSlabMatStatusEnum getByCode(String code) {
        for (PssSlabMatStatusEnum result : values()) {
            if (result.getCode().equals(code)) {
                return result;
            }
        }

        return null;
    }

    /**
     * 根据description获取枚举
     */
    public static PssSlabMatStatusEnum getByDescription(String description) {
        for (PssSlabMatStatusEnum result : values()) {
            if (result.getDescription().equals(description)) {
                return result;
            }
        }

        return null;
    }

}
