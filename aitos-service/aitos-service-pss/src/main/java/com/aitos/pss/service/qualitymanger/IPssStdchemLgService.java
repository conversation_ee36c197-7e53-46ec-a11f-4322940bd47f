
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssStdchemLgDto;
import com.aitos.pss.dto.qualitymanger.TPssStdchemLgPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdchemLgDto;
import com.aitos.pss.entity.qualitymanger.TPssStdchemLg;
import com.aitos.pss.vo.qualitymanger.TPssStdchemLgPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdchemLgVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/

public interface IPssStdchemLgService extends IService<TPssStdchemLg> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssStdchemLgPageVo> queryPage(@Valid TPssStdchemLgPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssStdchemLgVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssStdchemLgVo add(@Valid AddTPssStdchemLgDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssStdchemLgVo update(@Valid UpdateTPssStdchemLgDto dto);
}
