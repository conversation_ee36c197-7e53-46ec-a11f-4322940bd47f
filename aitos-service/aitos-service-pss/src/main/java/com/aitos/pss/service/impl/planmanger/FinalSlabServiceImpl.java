package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssFinalSlabDto;
import com.aitos.pss.dto.planmanger.TPssFinalSlabPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssFinalSlabDto;
import com.aitos.pss.entity.planmanger.TPssFinalSlab;
import com.aitos.pss.mapper.planmanger.TPssFinalSlabMapper;
import com.aitos.pss.service.planmanger.IFinalSlabService;
import com.aitos.pss.vo.planmanger.TPssFinalSlabPageVo;
import com.aitos.pss.vo.planmanger.TPssFinalSlabVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-22
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class FinalSlabServiceImpl extends ServiceImpl<TPssFinalSlabMapper, TPssFinalSlab> implements IFinalSlabService {

    @Override
    public PageOutput<TPssFinalSlabPageVo> queryPage(TPssFinalSlabPageDto dto) {
        LambdaQueryWrapper<TPssFinalSlab> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .orderByDesc(TPssFinalSlab::getNId)
                .select(TPssFinalSlab.class,x -> VoToColumnUtil.fieldsToColumns(TPssFinalSlabPageVo.class).contains(x.getProperty()));
        IPage<TPssFinalSlab> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssFinalSlabPageVo.class);
    }

    @Override
    public TPssFinalSlabVo queryInfo(Long id) {
        TPssFinalSlab tPssFinalSlab = this.baseMapper.selectById(id);
        if (tPssFinalSlab == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssFinalSlab, TPssFinalSlabVo.class);
    }

    @Override
    public TPssFinalSlabVo add(AddTPssFinalSlabDto dto) {
        TPssFinalSlab tPssFinalSlab = BeanUtil.toBean(dto, TPssFinalSlab.class);
        this.baseMapper.insert(tPssFinalSlab);

        return BeanUtil.copyProperties(tPssFinalSlab, TPssFinalSlabVo.class);
    }

    @Override
    public TPssFinalSlabVo update(UpdateTPssFinalSlabDto dto) {
        TPssFinalSlab tPssFinalSlab = BeanUtil.toBean(dto, TPssFinalSlab.class);
        this.baseMapper.updateById(tPssFinalSlab);

        return BeanUtil.copyProperties(tPssFinalSlab, TPssFinalSlabVo.class);
    }
}
