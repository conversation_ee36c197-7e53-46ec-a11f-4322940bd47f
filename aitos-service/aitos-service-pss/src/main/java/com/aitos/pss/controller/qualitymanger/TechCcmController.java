package com.aitos.pss.controller.qualitymanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssTechCcmDto;
import com.aitos.pss.dto.qualitymanger.TPssTechCcmPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechCcmDto;
import com.aitos.pss.entity.qualitymanger.TPssTechCcm;
import com.aitos.pss.service.qualitymanger.ITechCcmService;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 炼钢工艺参数管理-连铸工艺参数
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/techccm")
@Tag(name = "/pss"  + "/techccm",description = "炼钢工艺参数管理-连铸工艺参数代码")
@AllArgsConstructor
public class TechCcmController {


    private final ITechCcmService techCcmService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssTechCcm列表(分页)")
    @SaCheckPermission(value = "techccm:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssTechCcmPageVo>> page(@Valid TPssTechCcmPageDto dto){

        return RT.ok(techCcmService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssTechCcm信息")
    @SaCheckPermission(value = "techccm:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssTechCcmVo> info(@RequestParam Long id){

        return RT.ok(techCcmService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssTechCcm")
    @SaCheckPermission(value = "techccm:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-连铸工艺参数新增数据")
    public RT<TPssTechCcmVo> add(@Valid @RequestBody AddTPssTechCcmDto dto){

        return RT.ok(techCcmService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssTechCcm")
    @SaCheckPermission(value = "techccm:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-连铸工艺参数修改数据")
    public RT<TPssTechCcmVo> update(@Valid @RequestBody UpdateTPssTechCcmDto dto){

        return RT.ok(techCcmService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "techccm:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-连铸工艺参数删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(techCcmService.removeBatchByIds(ids));
    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "炼钢工艺参数管理-连铸工艺参数导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        techCcmService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "炼钢工艺参数管理-连铸工艺参数导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssTechCcmPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return techCcmService.exportData(dto,isTemplate);
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssTechCcmDto dto){
        TPssTechCcm tPssTechCcm = BeanUtil.toBean(dto, TPssTechCcm.class);

        return RT.ok(techCcmService.updateById(tPssTechCcm));
    }
}