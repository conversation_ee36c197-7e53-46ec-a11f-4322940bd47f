
package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssTaskListDto;
import com.aitos.pss.dto.planmanger.TPssTaskListPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssTaskListDto;
import com.aitos.pss.dto.planmanger.UpdateTPssTaskListStoveOrderDto;
import com.aitos.pss.entity.planmanger.TPssTaskList;
import com.aitos.pss.vo.planmanger.TPssTaskListPageVo;
import com.aitos.pss.vo.planmanger.TPssTaskListVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/

public interface ITPssTaskListService extends IService<TPssTaskList> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssTaskListPageVo> queryPage(@Valid TPssTaskListPageDto dto);

    /**
     * 详情
     * @param id
     * @return
     */
    TPssTaskListVo queryInfo(Long id);

    /**
     * 新增数据
     * @param dto
     * @return
     */
    TPssTaskListVo add(@Valid AddTPssTaskListDto dto);

    /**
     * 更新数据
     * @param dto
     * @return
     */
    TPssTaskListVo update(@Valid UpdateTPssTaskListDto dto);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    Boolean deleteBathById(@Valid List<Long> ids);

    /**
     * 炉次设计
     * @param dto
     * @return
     */
    Boolean stoveOrder(@Valid UpdateTPssTaskListStoveOrderDto dto);

    /**
     * 浇次设计
     * @param dtoList
     * @return
     */
    Boolean pourOrder(@Valid List<UpdateTPssTaskListDto> dtoList);
}
