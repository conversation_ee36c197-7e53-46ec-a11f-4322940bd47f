package com.aitos.pss.controller.qualitymanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssStdmatZgDto;
import com.aitos.pss.dto.qualitymanger.TPssStdmatZgPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdmatZgDto;
import com.aitos.pss.entity.qualitymanger.TPssStdmatZg;
import com.aitos.pss.service.qualitymanger.IStdmatZgService;
import com.aitos.pss.vo.qualitymanger.TPssStdmatZgPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdmatZgVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 性能标准管理
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/stdmatzg")
@Tag(name = "/pss"  + "/stdmatzg",description = "性能标准管理代码")
@AllArgsConstructor
public class StdmatZgController {


    private final IStdmatZgService stdmatZgService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssStdmatZg列表(分页)")
    @SaCheckPermission(value = "stdmatzg:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssStdmatZgPageVo>> page(@Valid TPssStdmatZgPageDto dto){

        return RT.ok(stdmatZgService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssStdmatZg信息")
    @SaCheckPermission(value = "stdmatzg:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssStdmatZgVo> info(@RequestParam Long id){

        return RT.ok(stdmatZgService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssStdmatZg")
    @SaCheckPermission(value = "stdmatzg:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "性能标准管理新增数据")
    public RT<TPssStdmatZgVo> add(@Valid @RequestBody AddTPssStdmatZgDto dto){

        return RT.ok(stdmatZgService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssStdmatZg")
    @SaCheckPermission(value = "stdmatzg:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "性能标准管理修改数据")
    public RT<TPssStdmatZgVo> update(@Valid @RequestBody UpdateTPssStdmatZgDto dto){

        return RT.ok(stdmatZgService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "stdmatzg:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "性能标准管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(stdmatZgService.removeBatchByIds(ids));
    }

    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "性能标准管理导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        stdmatZgService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "性能标准管理导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssStdmatZgPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return stdmatZgService.exportData(dto,isTemplate);
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssStdmatZgDto dto){
        TPssStdmatZg tPssStdmatZg = BeanUtil.toBean(dto, TPssStdmatZg.class);

        return RT.ok(stdmatZgService.updateById(tPssStdmatZg));
    }
}