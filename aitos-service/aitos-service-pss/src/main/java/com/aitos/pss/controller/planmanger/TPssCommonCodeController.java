package com.aitos.pss.controller.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssCommonCodeDto;
import com.aitos.pss.dto.planmanger.TPssCommonCodePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssCommonCodeDto;
import com.aitos.pss.entity.planmanger.TPssCommonCode;
import com.aitos.pss.service.planmanger.ITPssCommonCodeService;
import com.aitos.pss.vo.planmanger.TPssCommonCodePageVo;
import com.aitos.pss.vo.planmanger.TPssCommonCodeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 公共编码维护
* <AUTHOR>
* @Date: 2025-05-15
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/psscommoncode")
@Tag(name = "/pss"  + "/psscommoncode",description = "公共编码维护代码")
@AllArgsConstructor
public class TPssCommonCodeController {


    private final ITPssCommonCodeService pssCommonCodeService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssCommonCode列表(分页)")
    public RT<PageOutput<TPssCommonCodePageVo>> page(@Valid TPssCommonCodePageDto dto){

        return RT.ok(pssCommonCodeService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssCommonCode信息")
    public RT<TPssCommonCodeVo> info(@RequestParam Long id){

        return RT.ok(pssCommonCodeService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssCommonCode")
    @AitLog(value = "公共编码维护新增数据")
    public RT<TPssCommonCodeVo> add(@Valid @RequestBody AddTPssCommonCodeDto dto){

        return RT.ok(pssCommonCodeService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssCommonCode")
    @AitLog(value = "公共编码维护修改数据")
    public RT<TPssCommonCodeVo> update(@Valid @RequestBody UpdateTPssCommonCodeDto dto){

        return RT.ok(pssCommonCodeService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "公共编码维护删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(pssCommonCodeService.removeBatchByIds(ids));
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssCommonCodeDto dto){
        TPssCommonCode tPssCommonCode = BeanUtil.copyProperties(dto, TPssCommonCode.class);

        return RT.ok(pssCommonCodeService.updateById(tPssCommonCode));
    }

}