package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssStlCpctDto;
import com.aitos.pss.dto.planmanger.TPssStlCpctPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssStlCpctDto;
import com.aitos.pss.entity.planmanger.TPssStlCpct;
import com.aitos.pss.mapper.planmanger.TPssStlCpctMapper;
import com.aitos.pss.service.planmanger.ITPssStlCpctService;
import com.aitos.pss.vo.planmanger.TPssStlCpctPageVo;
import com.aitos.pss.vo.planmanger.TPssStlCpctVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssStlCpctServiceImpl extends ServiceImpl<TPssStlCpctMapper, TPssStlCpct> implements ITPssStlCpctService {

    @Override
    public PageOutput<TPssStlCpctPageVo> queryPage(TPssStlCpctPageDto dto) {
        LambdaQueryWrapper<TPssStlCpct> queryWrapper =
                Wrappers.<TPssStlCpct>lambdaQuery()
                        .eq(Objects.nonNull(dto.getCBofId()),TPssStlCpct::getCBofId,dto.getCBofId())
                        .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssStlCpct::getCStlGrdCd,dto.getCStlGrdCd())
                        .eq(ObjectUtil.isNotNull(dto.getNHeatMax1()),TPssStlCpct::getNHeatMax1,dto.getNHeatMax1())
                        .eq(ObjectUtil.isNotNull(dto.getNHeatMin()),TPssStlCpct::getNHeatMin,dto.getNHeatMin())
                        .eq(ObjectUtil.isNotNull(dto.getNHeatRacvRadio()),TPssStlCpct::getNHeatRacvRadio,dto.getNHeatRacvRadio())
                        .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssStlCpct::getNEnabledMark,dto.getNEnabledMark())
                        .orderByDesc(TPssStlCpct::getNId)
                        .select(TPssStlCpct.class,x -> VoToColumnUtil.fieldsToColumns(TPssStlCpctPageVo.class).contains(x.getProperty()));
        IPage<TPssStlCpct> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssStlCpctPageVo.class);
    }

    @Override
    public TPssStlCpctVo queryInfo(Long id) {
        TPssStlCpct tPssStlCpct = this.baseMapper.selectById(id);
        if (tPssStlCpct == null) {
            throw new MyException("找不到此数据！");
        }
        return BeanUtil.copyProperties(tPssStlCpct, TPssStlCpctVo.class);
    }

    @Override
    public TPssStlCpctVo add(AddTPssStlCpctDto dto) {
        TPssStlCpct tPssStlCpct = BeanUtil.toBean(dto, TPssStlCpct.class);
        if (Objects.isNull(dto.getNHeatMax1()) || Objects.isNull(dto.getNHeatMin())) throw new MyException("钢水上下限不能为空");
        if (dto.getNHeatMax1().compareTo(dto.getNHeatMin()) < 0) throw new MyException("钢水上限不能小于下限");
        this.baseMapper.insert(tPssStlCpct);
        return BeanUtil.copyProperties(tPssStlCpct, TPssStlCpctVo.class);
    }

    @Override
    public TPssStlCpctVo update(UpdateTPssStlCpctDto dto) {
        TPssStlCpct tPssStlCpct = BeanUtil.toBean(dto, TPssStlCpct.class);
        if (Objects.isNull(dto.getNHeatMax1()) || Objects.isNull(dto.getNHeatMin())) throw new MyException("钢水上下限不能为空");
        if (dto.getNHeatMax1().compareTo(dto.getNHeatMin()) < 0) throw new MyException("钢水上限不能小于下限");
        this.baseMapper.updateById(tPssStlCpct);
        return BeanUtil.copyProperties(tPssStlCpct, TPssStlCpctVo.class);
    }
}
