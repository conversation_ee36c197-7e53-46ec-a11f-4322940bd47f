package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssRateStdDto;
import com.aitos.pss.dto.planmanger.TPssRateStdPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssRateStdDto;
import com.aitos.pss.entity.planmanger.TPssRateStd;
import com.aitos.pss.mapper.planmanger.TPssRateStdMapper;
import com.aitos.pss.service.planmanger.ITPssRateStdService;
import com.aitos.pss.vo.planmanger.TPssRateStdPageVo;
import com.aitos.pss.vo.planmanger.TPssRateStdVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssRateStdServiceImpl extends ServiceImpl<TPssRateStdMapper, TPssRateStd> implements ITPssRateStdService {

    @Override
    public PageOutput<TPssRateStdPageVo> queryPage(TPssRateStdPageDto dto) {
        LambdaQueryWrapper<TPssRateStd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssRateStd::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(ObjectUtil.isNotNull(dto.getNPlateSlabRate()),TPssRateStd::getNPlateSlabRate,dto.getNPlateSlabRate())
                .eq(Objects.nonNull(dto.getNEnabledMark()),TPssRateStd::getNEnabledMark,dto.getNEnabledMark())
                .eq(ObjectUtil.isNotNull(dto.getNSlabStlRate()),TPssRateStd::getNSlabStlRate,dto.getNSlabStlRate())
                .eq(ObjectUtil.isNotNull(dto.getNDensity()),TPssRateStd::getNDensity,dto.getNDensity())
                .orderByDesc(TPssRateStd::getNId)
                .select(TPssRateStd.class,x -> VoToColumnUtil.fieldsToColumns(TPssRateStdPageVo.class).contains(x.getProperty()));
        IPage<TPssRateStd> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssRateStdPageVo.class);
    }

    @Override
    public TPssRateStdVo queryInfo(Long id) {
        TPssRateStd tPssRateStd = this.baseMapper.selectById(id);
        if (tPssRateStd == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssRateStd, TPssRateStdVo.class);
    }

    @Override
    public TPssRateStdVo add(AddTPssRateStdDto dto) {
        TPssRateStd tPssRateStd = BeanUtil.toBean(dto, TPssRateStd.class);
        this.baseMapper.insert(tPssRateStd);

        return BeanUtil.copyProperties(tPssRateStd, TPssRateStdVo.class);
    }

    @Override
    public TPssRateStdVo update(UpdateTPssRateStdDto dto) {
        TPssRateStd tPssRateStd = BeanUtil.toBean(dto, TPssRateStd.class);
        this.baseMapper.updateById(tPssRateStd);

        return BeanUtil.copyProperties(tPssRateStd, TPssRateStdVo.class);
    }

    @Override
    public Boolean checkSingle(UpdateTPssRateStdDto dto) {
        Long dbCount = this.count(
                Wrappers.<TPssRateStd>lambdaQuery()
                        .eq(TPssRateStd::getCStlGrdCd, dto.getCStlGrdCd())
                        .ne(Objects.nonNull(dto.getNId()), TPssRateStd::getNId, dto.getNId())
        );


        return dbCount > 0 ? Boolean.FALSE : Boolean.TRUE;
    }
}
