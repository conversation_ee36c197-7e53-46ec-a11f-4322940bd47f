package com.aitos.pss.controller.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssPosTimeDto;
import com.aitos.pss.dto.planmanger.TPssPosTimePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssPosTimeDto;
import com.aitos.pss.entity.planmanger.TPssPosTime;
import com.aitos.pss.service.planmanger.ITPssPosTimeService;
import com.aitos.pss.vo.planmanger.TPssPosTimePageVo;
import com.aitos.pss.vo.planmanger.TPssPosTimeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 工序时间标准-钢种冶炼时间标准
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssprocessstd")
@Tag(name = "/pss"  + "/pssprocessstd",description = "工序时间标准-钢种冶炼时间标准代码")
@AllArgsConstructor
public class TPssPosTimeController {


    private final ITPssPosTimeService pssProcessStdService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssPosTime列表(分页)")
    public RT<PageOutput<TPssPosTimePageVo>> queryPage(@Valid TPssPosTimePageDto dto){

        return RT.ok(pssProcessStdService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssPosTime信息")
    public RT<TPssPosTimeVo> info(@RequestParam Long id){

        return RT.ok(pssProcessStdService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssPosTime")
    @AitLog(value = "工序时间标准-钢种冶炼时间标准新增数据")
    public RT<TPssPosTimeVo> add(@Valid @RequestBody AddTPssPosTimeDto dto){

        return RT.ok(pssProcessStdService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssPosTime")
    @AitLog(value = "工序时间标准-钢种冶炼时间标准修改数据")
    public RT<TPssPosTimeVo> update(@Valid @RequestBody UpdateTPssPosTimeDto dto){

        return RT.ok(pssProcessStdService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "工序时间标准-钢种冶炼时间标准删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(pssProcessStdService.removeBatchByIds(ids));
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssPosTimeDto dto){
        TPssPosTime tPssPosTime = BeanUtil.toBean(dto, TPssPosTime.class);
        return RT.ok(pssProcessStdService.updateById(tPssPosTime));
    }

}
