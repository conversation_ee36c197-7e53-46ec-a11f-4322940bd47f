package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssPJudgeDto;
import com.aitos.pss.dto.qualitymanger.TPssPJudgePageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssPJudgeDto;
import com.aitos.pss.service.qualitymanger.IPJudgeService;
import com.aitos.pss.vo.qualitymanger.TPssPJudgePageVo;
import com.aitos.pss.vo.qualitymanger.TPssPJudgeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 轧钢综判管理
* <AUTHOR>
* @Date: 2025-06-05
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pjudge")
@Tag(name = "/pss"  + "/pjudge",description = "轧钢综判管理代码")
@AllArgsConstructor
public class PJudgeController {


    private final IPJudgeService pJudgeService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssPJudge列表(分页)")
    public RT<PageOutput<TPssPJudgePageVo>> page(@Valid TPssPJudgePageDto dto){

        return RT.ok(pJudgeService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssPJudge信息")
    public RT<TPssPJudgeVo> info(@RequestParam Long id){

        return RT.ok(pJudgeService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssPJudge")
    @AitLog(value = "轧钢综判管理新增数据")
    public RT<TPssPJudgeVo> add(@Valid @RequestBody AddTPssPJudgeDto dto){

        return RT.ok(pJudgeService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssPJudge")
    @AitLog(value = "轧钢综判管理修改数据")
    public RT<TPssPJudgeVo> update(@Valid @RequestBody UpdateTPssPJudgeDto dto){

        return RT.ok(pJudgeService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "轧钢综判管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(pJudgeService.removeBatchByIds(ids));

    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "轧钢综判管理导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        pJudgeService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "轧钢综判管理导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssPJudgePageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return pJudgeService.exportData(dto, isTemplate);
    }
}