package com.aitos.pss.service.impl.costmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.costmanger.AddTPssFcsMatCdDto;
import com.aitos.pss.dto.costmanger.TPssFcsMatCdPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsMatCdDto;
import com.aitos.pss.entity.costmanger.TPssFcsMatCd;
import com.aitos.pss.mapper.costmanger.TPssFcsMatCdMapper;
import com.aitos.pss.service.costmanger.IPssFcsMatcdService;
import com.aitos.pss.vo.costmanger.TPssFcsMatCdPageVo;
import com.aitos.pss.vo.costmanger.TPssFcsMatCdVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class PssFcsMatcdServiceImpl extends ServiceImpl<TPssFcsMatCdMapper, TPssFcsMatCd> implements IPssFcsMatcdService {

    @Override
    public PageOutput<TPssFcsMatCdPageVo> queryPage(TPssFcsMatCdPageDto dto) {

        LambdaQueryWrapper<TPssFcsMatCd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getNId()),TPssFcsMatCd::getNId,dto.getNId())
                .like(StrUtil.isNotBlank(dto.getCCostItemName()),TPssFcsMatCd::getCCostItemName,dto.getCCostItemName())
                .like(StrUtil.isNotBlank(dto.getCMatClassName()),TPssFcsMatCd::getCMatClassName,dto.getCMatClassName())
                .like(StrUtil.isNotBlank(dto.getCUnit()),TPssFcsMatCd::getCUnit,dto.getCUnit())
                .eq(ObjectUtil.isNotNull(dto.getNCostSeq()),TPssFcsMatCd::getNCostSeq,dto.getNCostSeq())
                .like(StrUtil.isNotBlank(dto.getCMatCode()),TPssFcsMatCd::getCMatCode,dto.getCMatCode())
                .like(StrUtil.isNotBlank(dto.getCSeat()),TPssFcsMatCd::getCSeat,dto.getCSeat())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssFcsMatCd::getNCreateUserId,dto.getNCreateUserId())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssFcsMatCd::getNModifyUserId,dto.getNModifyUserId())
                .like(StrUtil.isNotBlank(dto.getCCostItemCode()),TPssFcsMatCd::getCCostItemCode,dto.getCCostItemCode())
                .like(StrUtil.isNotBlank(dto.getCMatClassCode()),TPssFcsMatCd::getCMatClassCode,dto.getCMatClassCode())
                .like(StrUtil.isNotBlank(dto.getCMemo()),TPssFcsMatCd::getCMemo,dto.getCMemo())
                .eq(Objects.nonNull(dto.getCProLine()),TPssFcsMatCd::getCProLine,dto.getCProLine())
                .eq(Objects.nonNull(dto.getCProcId()),TPssFcsMatCd::getCProcId,dto.getCProcId())
                .like(StrUtil.isNotBlank(dto.getCMatName()),TPssFcsMatCd::getCMatName,dto.getCMatName())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssFcsMatCd::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssFcsMatCd::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssFcsMatCd::getNEnabledMark,dto.getNEnabledMark())
                .orderByDesc(TPssFcsMatCd::getDtCreateDateTime)
                .select(TPssFcsMatCd.class,x -> VoToColumnUtil.fieldsToColumns(TPssFcsMatCdPageVo.class).contains(x.getProperty()));
        IPage<TPssFcsMatCd> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssFcsMatCdPageVo.class);
    }

    @Override
    public TPssFcsMatCdVo queryInfo(Long id) {
        TPssFcsMatCd tPssFcsMatCd = this.baseMapper.selectById(id);
        if (tPssFcsMatCd == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssFcsMatCd, TPssFcsMatCdVo.class);
    }

    @Override
    public TPssFcsMatCdVo add(AddTPssFcsMatCdDto dto) {
        TPssFcsMatCd tPssFcsMatCd = BeanUtil.toBean(dto, TPssFcsMatCd.class);
        Long dbCostSeqCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssFcsMatCd>lambdaQuery()
                                .eq(TPssFcsMatCd::getNCostSeq, dto.getNCostSeq())
                                .eq(TPssFcsMatCd::getCCostItemCode, dto.getCCostItemCode())
                );
        if (dbCostSeqCount > 0) throw new MyException("顺序不能重复");

        Long dbMatClassCodeCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssFcsMatCd>lambdaQuery()
                                .eq(TPssFcsMatCd::getCCostItemCode, dto.getCCostItemCode())
                                .eq(TPssFcsMatCd::getCMatClassCode, dto.getCMatClassCode())
                );
        if (dbMatClassCodeCount > 0) throw new MyException("同一成本小类下，成本项目编码不能重复");

        this.baseMapper.insert(tPssFcsMatCd);

        return BeanUtil.copyProperties(tPssFcsMatCd, TPssFcsMatCdVo.class);
    }

    @Override
    public TPssFcsMatCdVo update(UpdateTPssFcsMatCdDto dto) {
        TPssFcsMatCd tPssFcsMatCd = BeanUtil.toBean(dto, TPssFcsMatCd.class);
        Long dbCostSeqCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssFcsMatCd>lambdaQuery()
                                .eq(TPssFcsMatCd::getNCostSeq, dto.getNCostSeq())
                                .eq(TPssFcsMatCd::getCCostItemCode, dto.getCCostItemCode())
                                .ne(TPssFcsMatCd::getNId, dto.getNId())
                );
        if (dbCostSeqCount > 0) throw new MyException("顺序不能重复");

        Long dbMatClassCodeCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssFcsMatCd>lambdaQuery()
                                .eq(TPssFcsMatCd::getCCostItemCode, dto.getCCostItemCode())
                                .eq(TPssFcsMatCd::getCMatClassCode, dto.getCMatClassCode())
                                .ne(TPssFcsMatCd::getNId, dto.getNId())
                );
        if (dbMatClassCodeCount > 0) throw new MyException("同一成本小类下，成本项目编码不能重复");

        this.baseMapper.updateById(tPssFcsMatCd);

        return BeanUtil.copyProperties(tPssFcsMatCd, TPssFcsMatCdVo.class);
    }
}
