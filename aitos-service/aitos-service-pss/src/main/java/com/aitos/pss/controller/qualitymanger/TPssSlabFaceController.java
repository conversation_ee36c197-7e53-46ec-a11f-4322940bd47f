package com.aitos.pss.controller.qualitymanger;


import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabFaceDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabFacePageDto;
import com.aitos.pss.service.qualitymanger.ITPssSlabFaceService;
import com.aitos.pss.vo.qualitymanger.TPssSlabFacePageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabFaceVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/pss" + "/labface")
@Tag(name = "/pss"  + "/labface",description = "炼钢表尺判定管理代码")
@AllArgsConstructor
public class TPssSlabFaceController {

    private final ITPssSlabFaceService slabFaceService;

    @GetMapping("/page")
    @Operation(summary = "分页查询")
    public RT<PageOutput<TPssSlabFacePageVo>> queryPage(@Valid TPssSlabFacePageDto dto) {
        return RT.ok(slabFaceService.queryPage(dto));
    }

    @PostMapping
    @Operation(summary =  "新增")
    @AitLog(value = "炼钢工艺参数管理-精炼工艺参数新增数据")
    public RT<TPssSlabFaceVo> add(@Valid @RequestBody AddTPssSlabFaceDto dto){

        return RT.ok(slabFaceService.add(dto));
    }

    @Operation(summary =  "表面判定")
    @AitLog(value = "表面判定")
    @PostMapping("/fact-decision")
    public RT<Boolean> factDecision(@Valid @RequestBody List<AddTPssSlabFaceDto> dtoList){

        return RT.ok(slabFaceService.factDecision(dtoList));
    }

}
