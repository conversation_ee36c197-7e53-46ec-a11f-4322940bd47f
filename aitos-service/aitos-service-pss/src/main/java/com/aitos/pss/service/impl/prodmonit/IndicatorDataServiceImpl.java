package com.aitos.pss.service.impl.prodmonit;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.prodmonit.AddTPssIndicatorDataDto;
import com.aitos.pss.dto.prodmonit.AddTPssViolationsDto;
import com.aitos.pss.dto.prodmonit.TPssIndicatorDataPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssIndicatorDataDto;
import com.aitos.pss.entity.prodmonit.TPssIndicatorData;
import com.aitos.pss.entity.prodmonit.TPssIndicators;
import com.aitos.pss.enums.ProductionSalesIndicatorStatus;
import com.aitos.pss.mapper.prodmonit.TPssIndicatorDataMapper;
import com.aitos.pss.service.prodmonit.IIndicatorConfigService;
import com.aitos.pss.service.prodmonit.IIndicatorDataService;
import com.aitos.pss.service.prodmonit.IViolationManageService;
import com.aitos.pss.vo.prodmonit.TPssIndicatorDataPageVo;
import com.aitos.pss.vo.prodmonit.TPssIndicatorDataVo;
import com.aitos.system.client.v2.ICodeRuleClientV2;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class IndicatorDataServiceImpl extends ServiceImpl<TPssIndicatorDataMapper, TPssIndicatorData> implements IIndicatorDataService {

    private final IIndicatorConfigService indicatorConfigService;

    private final IViolationManageService violationManageService;

    private final ICodeRuleClientV2 codeRuleClientV2;

    @Override
    public PageOutput<TPssIndicatorDataPageVo> queryPage(TPssIndicatorDataPageDto dto) {
        LambdaQueryWrapper<TPssIndicatorData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .orderByDesc(TPssIndicatorData::getNId)
                .select(TPssIndicatorData.class,x -> VoToColumnUtil.fieldsToColumns(TPssIndicatorDataPageVo.class).contains(x.getProperty()));
        IPage<TPssIndicatorData> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssIndicatorDataPageVo.class);
    }

    @Override
    public TPssIndicatorDataVo queryInfo(Long id) {
        TPssIndicatorData tPssIndicatorData = this.baseMapper.selectById(id);
        if (tPssIndicatorData == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssIndicatorData, TPssIndicatorDataVo.class);
    }

    @Override
    public TPssIndicatorDataVo add(AddTPssIndicatorDataDto dto) {
        TPssIndicatorData tPssIndicatorData = BeanUtil.toBean(dto, TPssIndicatorData.class);
        TPssIndicators indicators =
                indicatorConfigService.getOne(Wrappers.<TPssIndicators>lambdaQuery().eq(TPssIndicators::getCCode, dto.getCIndicatorCode()));
        if (Objects.isNull(indicators)) {
            log.error("【"+ dto.getCIndicatorCode() + "】未配置，请先配置指标~~~~~");
            return null;
        }

        ProductionSalesIndicatorStatus productionSalesIndicatorStatus = checkStatus(dto.getNIndicatorValue(), indicators);
        dto.setCStatus(productionSalesIndicatorStatus.getCode());

        switch (productionSalesIndicatorStatus) {
            case NORMAL:
                break;

            case WARNING:
                break;

            case VIOLATION:
                AddTPssViolationsDto addViolationsDto = new AddTPssViolationsDto();
                addViolationsDto.setNIndicatorId(indicators.getNId());
                addViolationsDto.setCIndicatorCode(indicators.getCCode());
                addViolationsDto.setCIndicatorName(indicators.getCName());
                addViolationsDto.setNEquipmentId(indicators.getNEquipmentId());
                addViolationsDto.setCEquipmentCode(indicators.getCEquipmentCode());
                addViolationsDto.setCEquipmentName(indicators.getCEquipmentName());
                // TODO 补充自动编码
                addViolationsDto.setCCode(codeRuleClientV2.generateAndUse("").getDataOrThrow());
                addViolationsDto.setCViolationDescription("违规描述");
                addViolationsDto.setDtViolationDateTime(LocalDateTime.now());
                addViolationsDto.setNErrorValue(dto.getNIndicatorValue());
                // TODO 标准值？
                addViolationsDto.setNStandValue(null);
                addViolationsDto.setCStatus("1");
                // TODO 补充责任人、审批人
                addViolationsDto.setNApproverId(null);
                addViolationsDto.setNResponsibleUserId(null);
                break;

            default:
                throw new IllegalArgumentException("未知状态: " + productionSalesIndicatorStatus);
        }

        this.baseMapper.insert(tPssIndicatorData);

        return BeanUtil.copyProperties(tPssIndicatorData, TPssIndicatorDataVo.class);
    }

    /**
     * 判断指标值所处的状态
     * @param value 要判断的数值
     * @param ranges 包含各范围值的对象
     * @return 状态枚举
     * @throws IllegalArgumentException 如果数值不在任何定义范围内
     */
    public static ProductionSalesIndicatorStatus checkStatus(BigDecimal value, TPssIndicators ranges) {
        if (value == null || ranges == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 先检查是否在违规范围内
        if (isInRange(value, ranges.getNViolationMin(), ranges.getNViolationMax())) {
            return ProductionSalesIndicatorStatus.VIOLATION;
        }

        // 然后检查是否在预警范围内
        if (isInRange(value, ranges.getNWarningMin(), ranges.getNWarningMax())) {
            return ProductionSalesIndicatorStatus.WARNING;
        }

        // 最后检查是否在正常范围内
        if (isInRange(value, ranges.getNNormalMin(), ranges.getNNormalMax())) {
            return ProductionSalesIndicatorStatus.NORMAL;
        }

        throw new IllegalArgumentException("数值 " + value + " 不在任何定义范围内");
    }

    private static boolean isInRange(BigDecimal value, BigDecimal min, BigDecimal max) {
        // 处理边界值为null的情况
        if (min == null && max == null) return false;

        boolean lowerBound = min == null || value.compareTo(min) >= 0;
        boolean upperBound = max == null || value.compareTo(max) <= 0;

        return lowerBound && upperBound;
    }

    @Override
    public TPssIndicatorDataVo update(UpdateTPssIndicatorDataDto dto) {
        TPssIndicatorData tPssIndicatorData = BeanUtil.toBean(dto, TPssIndicatorData.class);
        this.baseMapper.updateById(tPssIndicatorData);

        return BeanUtil.copyProperties(tPssIndicatorData, TPssIndicatorDataVo.class);
    }

    @Override
    public Void importData(MultipartFile file) throws IOException {
        List<TPssIndicatorDataPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssIndicatorDataPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssIndicatorData.class));

        return null;
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssIndicatorDataPageDto dto, Boolean isTemplate) {
        List<TPssIndicatorDataPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssIndicatorDataPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "IndicatorData" + ExcelTypeEnum.XLSX.getValue());
    }
}
