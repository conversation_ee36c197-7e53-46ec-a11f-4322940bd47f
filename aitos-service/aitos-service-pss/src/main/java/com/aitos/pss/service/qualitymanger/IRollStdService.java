
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssRollStdDto;
import com.aitos.pss.dto.qualitymanger.TPssRollStdPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssRollStdDto;
import com.aitos.pss.entity.qualitymanger.TPssRollStd;
import com.aitos.pss.vo.qualitymanger.TPssRollStdPageVo;
import com.aitos.pss.vo.qualitymanger.TPssRollStdVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-21
* @Version 1.0
*/

public interface IRollStdService extends IService<TPssRollStd> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssRollStdPageVo> queryPage(@Valid TPssRollStdPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssRollStdVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssRollStdVo add(@Valid AddTPssRollStdDto dto);

    /**
     *
     * @param dto
     * @return
     */
    TPssRollStdVo update(@Valid UpdateTPssRollStdDto dto);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssRollStdPageDto dto, Boolean isTemplate);

}
