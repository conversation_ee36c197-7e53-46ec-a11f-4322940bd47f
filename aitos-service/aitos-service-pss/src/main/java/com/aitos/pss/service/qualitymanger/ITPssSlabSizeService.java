package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabSizeDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabSizePageDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabSize;
import com.aitos.pss.vo.qualitymanger.TPssSlabSizePageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabSizeVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

public interface ITPssSlabSizeService extends IService<TPssSlabSize> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssSlabSizePageVo> queryPage(@Valid TPssSlabSizePageDto dto);

    /**
     * add
     * @param dto
     * @return
     */
    TPssSlabSizeVo add(@Valid AddTPssSlabSizeDto dto);

    /**
     * 尺寸判定
     * @param dtoList
     * @return
     */
    Boolean sizeDecision(@Valid List<AddTPssSlabSizeDto> dtoList);
}