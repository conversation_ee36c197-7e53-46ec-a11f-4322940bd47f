package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPssWareHouseLocDto;
import com.aitos.pss.dto.inventory.TPssWareHouseLocPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssWareHouseLocDto;
import com.aitos.pss.service.inventory.IWareHouseLocService;
import com.aitos.pss.vo.inventory.TPssWareHouseLocPageVo;
import com.aitos.pss.vo.inventory.TPssWareHouseLocVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 库区库位管理子表
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/warehouseloc")
@Tag(name = "/pss"  + "/warehouseloc",description = "库区库位管理子表代码")
@AllArgsConstructor
public class WareHouseLocController {


    private final IWareHouseLocService wareHouseLocService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssWareHouseLoc列表(分页)")
    @SaCheckPermission(value = "warehouseloc:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssWareHouseLocPageVo>> page(@Valid TPssWareHouseLocPageDto dto){

        return RT.ok(wareHouseLocService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssWareHouseLoc信息")
    @SaCheckPermission(value = "warehouseloc:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssWareHouseLocVo> info(@RequestParam Long id){

        return RT.ok(wareHouseLocService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssWareHouseLoc")
    @SaCheckPermission(value = "warehouseloc:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "库区库位管理子表新增数据")
    public RT<TPssWareHouseLocVo> add(@Valid @RequestBody AddTPssWareHouseLocDto dto){

        return RT.ok(wareHouseLocService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssWareHouseLoc")
    @SaCheckPermission(value = "warehouseloc:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "库区库位管理子表修改数据")
    public RT<TPssWareHouseLocVo> update(@Valid @RequestBody UpdateTPssWareHouseLocDto dto){

        return RT.ok(wareHouseLocService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "warehouseloc:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "库区库位管理子表删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(wareHouseLocService.removeBatchByIds(ids));
    }

}