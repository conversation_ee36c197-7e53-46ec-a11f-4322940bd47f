package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssTechCcmInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechCcmInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechCcmInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssTechCcmInfo;
import com.aitos.pss.mapper.qualitymanger.TPssTechCcmInfoMapper;
import com.aitos.pss.service.qualitymanger.ITechCcmInfoService;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmInfoVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TechCcmInfoServiceImpl extends ServiceImpl<TPssTechCcmInfoMapper, TPssTechCcmInfo> implements ITechCcmInfoService {

    @Override
    public PageOutput<TPssTechCcmInfoPageVo> queryPage(TPssTechCcmInfoPageDto dto) {
        LambdaQueryWrapper<TPssTechCcmInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualId()),TPssTechCcmInfo::getCQualId,dto.getCQualId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssTechCcmInfo::getCStlGrdCd,dto.getCStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCConNo()),TPssTechCcmInfo::getCConNo,dto.getCConNo())
                .like(StrUtil.isNotBlank(dto.getOrderId()),TPssTechCcmInfo::getOrderId,dto.getOrderId())
                .orderByDesc(TPssTechCcmInfo::getNId)
                .select(TPssTechCcmInfo.class,x -> VoToColumnUtil.fieldsToColumns(TPssTechCcmInfoPageVo.class).contains(x.getProperty()));
        IPage<TPssTechCcmInfo> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssTechCcmInfoPageVo.class);
    }

    @Override
    public TPssTechCcmInfoVo queryInfo(Long id) {
        TPssTechCcmInfo tPssTechCcmInfo = this.baseMapper.selectById(id);
        if (tPssTechCcmInfo == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssTechCcmInfo, TPssTechCcmInfoVo.class);
    }

    @Override
    public TPssTechCcmInfoVo add(AddTPssTechCcmInfoDto dto) {
        TPssTechCcmInfo tPssTechCcmInfo = BeanUtil.toBean(dto, TPssTechCcmInfo.class);
        this.baseMapper.insert(tPssTechCcmInfo);

        return BeanUtil.copyProperties(tPssTechCcmInfo, TPssTechCcmInfoVo.class);
    }

    @Override
    public TPssTechCcmInfoVo update(UpdateTPssTechCcmInfoDto dto) {
        TPssTechCcmInfo tPssTechCcmInfo = BeanUtil.toBean(dto, TPssTechCcmInfo.class);
        this.baseMapper.updateById(tPssTechCcmInfo);

        return BeanUtil.copyProperties(tPssTechCcmInfo, TPssTechCcmInfoVo.class);
    }
}
