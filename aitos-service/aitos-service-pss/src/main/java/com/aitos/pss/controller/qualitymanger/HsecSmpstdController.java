package com.aitos.pss.controller.qualitymanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssHsecSmpstdDto;
import com.aitos.pss.dto.qualitymanger.TPssHsecSmpstdPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssHsecSmpstdDto;
import com.aitos.pss.service.qualitymanger.IHsecSmpstdService;
import com.aitos.pss.vo.qualitymanger.TPssHsecSmpstdPageVo;
import com.aitos.pss.vo.qualitymanger.TPssHsecSmpstdVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 组批规则维护
* <AUTHOR>
* @Date: 2025-06-17
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/hsecsmpstd")
@Tag(name = "/pss"  + "/hsecsmpstd",description = "组批规则维护代码")
@AllArgsConstructor
public class HsecSmpstdController {


    private final IHsecSmpstdService hsecSmpstdService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssHsecSmpstd列表(分页)")
    public RT<PageOutput<TPssHsecSmpstdPageVo>> page(@Valid TPssHsecSmpstdPageDto dto){

        return RT.ok(hsecSmpstdService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssHsecSmpstd信息")
    public RT<TPssHsecSmpstdVo> info(@RequestParam Long id){

        return RT.ok(hsecSmpstdService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssHsecSmpstd")
    @AitLog(value = "组批规则维护新增数据")
    public RT<TPssHsecSmpstdVo> add(@Valid @RequestBody AddTPssHsecSmpstdDto dto){

        return RT.ok(hsecSmpstdService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssHsecSmpstd")
    @AitLog(value = "组批规则维护修改数据")
    public RT<TPssHsecSmpstdVo> update(@Valid @RequestBody UpdateTPssHsecSmpstdDto dto){

        return RT.ok(hsecSmpstdService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "hsecsmpstd:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "组批规则维护删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(hsecSmpstdService.removeBatchByIds(ids));

    }

    @PostMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "组批规则维护导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssHsecSmpstdPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return hsecSmpstdService.exportData(dto,isTemplate);
    }
}