package com.aitos.pss.enums;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 计划状态枚举
 */
@Getter
public enum PssPlanStatusEnum {

    /** 坯料设计完成 */
    BLANK_DESIGN_COMPLETED("PA0010", "坯料设计完成"),

    /** 炉次设计完成 */
    HEAT_DESIGN_COMPLETED("PA0020", "炉次设计完成"),

    /** 浇次设计完成 */
    POUR_DESIGN_COMPLETED("PA0030", "浇次设计完成"),

    /** 已下达 */
    ISSUED("PA0040", "已下达"),

    /** 等待下达 */
    WAITING_ISSUE("PC0000", "等待下达"),

    /** 等待冶炼 */
    WAITING_SMELTING("PC0001", "等待冶炼"),

    /** 转炉准备 */
    CONVERTER_PREPARATION("PC0009", "转炉准备"),

    /** 转炉开始 */
    CONVERTER_START("PC0010", "转炉开始"),

    /** 转炉结束 */
    CONVERTER_END("PC0020", "转炉结束"),

    /** LF开始 */
    LF_START("PC0030", "LF开始"),

    /** LF处理接收 */
    LF_PROCESS_RECEIVED("PC0035", "LF处理接收"),

    /** LF结束 */
    LF_END("PC0040", "LF结束"),

    /** 连铸准备 */
    CONTINUOUS_CASTING_PREPARATION("PC0065", "连铸准备"),

    /** 连铸开始 */
    CONTINUOUS_CASTING_START("PC0070", "连铸开始"),

    /** 连铸结束 */
    CONTINUOUS_CASTING_END("PC0080", "连铸结束"),

    /** 切割开始 */
    CUTTING_START("PC0090", "切割开始"),

    /** 切割结束 */
    CUTTING_END("PC0100", "切割结束"),

    /** 撤销计划 */
    PLAN_CANCELLED("PC0110", "撤销计划"),

    /** 浇次结束 */
    POUR_END("PC0120", "浇次结束"),

    /** 异常炉次计划作废 */
    ABNORMAL_HEAT_PLAN_INVALID("PC0911", "异常炉次计划作废"),

    /** RH结束 */
    RH_END("PC0061", "RH结束");

    private static final Logger log = LoggerFactory.getLogger(PssPlanStatusEnum.class);

    private final String code;
    private final String description;

    PssPlanStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code安全获取枚举（找不到时记录warn日志并返回null）
     */
    public static PssPlanStatusEnum getByCode(String code) {
        if (code == null) {
            log.warn("传入的状态编码为null");
            return null;
        }

        for (PssPlanStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }

        log.warn("无效的计划状态编码: {}", code);
        return null;
    }

    /**
     * 根据description安全获取枚举（找不到时记录warn日志并返回null）
     */
    public static PssPlanStatusEnum getByDescription(String description) {
        if (description == null) {
            log.warn("传入的状态描述为null");
            return null;
        }

        for (PssPlanStatusEnum status : values()) {
            if (status.description.equals(description)) {
                return status;
            }
        }

        log.warn("无效的计划状态描述: {}", description);
        return null;
    }

    public static Boolean isCodeIn(String code, PssPlanStatusEnum... targetStatuses) {
        if (code == null || targetStatuses == null) {
            return Boolean.FALSE;
        }
        for (PssPlanStatusEnum status : targetStatuses) {
            if (status.getCode().equals(code)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    public static Boolean canCcm(String code) {
        return isCodeIn(code, PssPlanStatusEnum.WAITING_ISSUE,PssPlanStatusEnum.ISSUED,PssPlanStatusEnum.CONVERTER_PREPARATION);
    }

    public static Boolean canCld(String code) {
        return isCodeIn(
                code,
                WAITING_ISSUE,
                ISSUED,
                CONVERTER_PREPARATION,
                WAITING_SMELTING,
                CONVERTER_START,
                CONVERTER_END,
                LF_START,
                LF_PROCESS_RECEIVED,
                LF_END
        );
    }

    public static Boolean canLf(String code) {
        return isCodeIn(
                code,
                WAITING_ISSUE,
                ISSUED,
                WAITING_SMELTING,
                CONVERTER_PREPARATION,
                CONVERTER_START,
                CONVERTER_END
        );
    }

    public static Boolean canSwap(String code) {
        return isCodeIn(
                code,
                WAITING_ISSUE,
                ISSUED,
                WAITING_SMELTING,
                CONVERTER_PREPARATION,
                CONVERTER_START,
                CONVERTER_END
        );
    }
}