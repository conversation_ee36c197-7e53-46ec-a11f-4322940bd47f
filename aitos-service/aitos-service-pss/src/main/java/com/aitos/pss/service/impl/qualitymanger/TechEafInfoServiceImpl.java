package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssTechEafInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechEafInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechEafInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssTechEafInfo;
import com.aitos.pss.mapper.qualitymanger.TPssTechEafInfoMapper;
import com.aitos.pss.service.qualitymanger.ITechEafInfoService;
import com.aitos.pss.vo.qualitymanger.TPssTechEafInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechEafInfoVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TechEafInfoServiceImpl extends ServiceImpl<TPssTechEafInfoMapper, TPssTechEafInfo> implements ITechEafInfoService {

    @Override
    public PageOutput<TPssTechEafInfoPageVo> queryPage(TPssTechEafInfoPageDto dto) {
        LambdaQueryWrapper<TPssTechEafInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getOrderId()),TPssTechEafInfo::getOrderId,dto.getOrderId())
                .eq(Objects.nonNull(dto.getCQualId()),TPssTechEafInfo::getCQualId,dto.getCQualId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssTechEafInfo::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssTechEafInfo::getNEnabledMark,dto.getNEnabledMark())
                .orderByDesc(TPssTechEafInfo::getNId)
                .select(TPssTechEafInfo.class,x -> VoToColumnUtil.fieldsToColumns(TPssTechEafInfoPageVo.class).contains(x.getProperty()));
        IPage<TPssTechEafInfo> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssTechEafInfoPageVo.class);
    }

    @Override
    public TPssTechEafInfoVo queryInfo(Long id) {
        TPssTechEafInfo tPssTechEafInfo = this.baseMapper.selectById(id);
        if (tPssTechEafInfo == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssTechEafInfo, TPssTechEafInfoVo.class);
    }

    @Override
    public TPssTechEafInfoVo add(AddTPssTechEafInfoDto dto) {
        TPssTechEafInfo tPssTechEafInfo = BeanUtil.toBean(dto, TPssTechEafInfo.class);
        this.baseMapper.insert(tPssTechEafInfo);

        return BeanUtil.copyProperties(tPssTechEafInfo, TPssTechEafInfoVo.class);
    }

    @Override
    public TPssTechEafInfoVo update(UpdateTPssTechEafInfoDto dto) {
        TPssTechEafInfo tPssTechEafInfo = BeanUtil.toBean(dto, TPssTechEafInfo.class);
        this.baseMapper.updateById(tPssTechEafInfo);

        return BeanUtil.copyProperties(tPssTechEafInfo, TPssTechEafInfoVo.class);
    }
}
