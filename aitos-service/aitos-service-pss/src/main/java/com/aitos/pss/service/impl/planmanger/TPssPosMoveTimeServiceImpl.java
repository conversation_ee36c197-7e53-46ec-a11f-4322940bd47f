package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssPosMoveTimeDto;
import com.aitos.pss.dto.planmanger.TPssPosMoveTimePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssPosMoveTimeDto;
import com.aitos.pss.entity.planmanger.TPssPosMoveTime;
import com.aitos.pss.mapper.planmanger.TPssPosMoveTimeMapper;
import com.aitos.pss.service.planmanger.ITPssPosMoveTimeService;
import com.aitos.pss.vo.planmanger.TPssPosMoveTimePageVo;
import com.aitos.pss.vo.planmanger.TPssPosMoveTimeVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssPosMoveTimeServiceImpl extends ServiceImpl<TPssPosMoveTimeMapper, TPssPosMoveTime> implements ITPssPosMoveTimeService {

    @Override
    public PageOutput<TPssPosMoveTimePageVo> queryPage(TPssPosMoveTimePageDto dto) {
        LambdaQueryWrapper<TPssPosMoveTime> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCProLine()),TPssPosMoveTime::getCProLine,dto.getCProLine())
                .eq(Objects.nonNull(dto.getCEndProcess()),TPssPosMoveTime::getCEndProcess,dto.getCEndProcess())
                .eq(Objects.nonNull(dto.getCStartProcess()),TPssPosMoveTime::getCStartProcess,dto.getCStartProcess())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssPosMoveTime::getNEnabledMark,dto.getNEnabledMark())
                .eq(ObjectUtil.isNotNull(dto.getNMoveTime()),TPssPosMoveTime::getNMoveTime,dto.getNMoveTime())
                .orderByDesc(TPssPosMoveTime::getNId)
                .select(TPssPosMoveTime.class,x -> VoToColumnUtil.fieldsToColumns(TPssPosMoveTimePageVo.class).contains(x.getProperty()));
        IPage<TPssPosMoveTime> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssPosMoveTimePageVo.class);
    }

    @Override
    public TPssPosMoveTimeVo queryInfo(Long id) {
        TPssPosMoveTime tPssPosMoveTime = this.baseMapper.selectById(id);
        if (tPssPosMoveTime == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssPosMoveTime, TPssPosMoveTimeVo.class);
    }

    @Override
    public TPssPosMoveTimeVo add(AddTPssPosMoveTimeDto dto) {
        TPssPosMoveTime tPssPosMoveTime = BeanUtil.toBean(dto, TPssPosMoveTime.class);
        this.baseMapper.insert(tPssPosMoveTime);

        return BeanUtil.copyProperties(tPssPosMoveTime, TPssPosMoveTimeVo.class);
    }

    @Override
    public TPssPosMoveTimeVo update(UpdateTPssPosMoveTimeDto dto) {
        TPssPosMoveTime tPssPosMoveTime = BeanUtil.toBean(dto, TPssPosMoveTime.class);
        this.baseMapper.updateById(tPssPosMoveTime);

        return BeanUtil.copyProperties(tPssPosMoveTime, TPssPosMoveTimeVo.class);
    }
}
