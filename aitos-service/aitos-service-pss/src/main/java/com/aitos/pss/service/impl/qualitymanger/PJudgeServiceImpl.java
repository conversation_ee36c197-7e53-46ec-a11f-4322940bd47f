package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssPJudgeDto;
import com.aitos.pss.dto.qualitymanger.TPssPJudgePageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssPJudgeDto;
import com.aitos.pss.entity.qualitymanger.TPssPJudge;
import com.aitos.pss.mapper.qualitymanger.TPssPJudgeMapper;
import com.aitos.pss.service.qualitymanger.IPJudgeService;
import com.aitos.pss.vo.qualitymanger.TPssPJudgePageVo;
import com.aitos.pss.vo.qualitymanger.TPssPJudgeVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-05
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class PJudgeServiceImpl extends ServiceImpl<TPssPJudgeMapper, TPssPJudge> implements IPJudgeService {

    @Override
    public PageOutput<TPssPJudgePageVo> queryPage(TPssPJudgePageDto dto) {
        LambdaQueryWrapper<TPssPJudge> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCPlateId()),TPssPJudge::getCPlateId,dto.getCPlateId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateEnd()),TPssPJudge::getDtCreateDateTime,dto.getDtCreateDateStart(),dto.getDtCreateDateEnd())
                .orderByDesc(TPssPJudge::getNId)
                .select(TPssPJudge.class, x -> VoToColumnUtil.fieldsToColumns(TPssPJudgePageVo.class).contains(x.getProperty()));
        IPage<TPssPJudge> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssPJudgePageVo.class);
    }

    @Override
    public TPssPJudgeVo queryInfo(Long id) {
        TPssPJudge tPssPJudge = this.baseMapper.selectById(id);
        if (tPssPJudge == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssPJudge, TPssPJudgeVo.class);
    }

    @Override
    public TPssPJudgeVo add(AddTPssPJudgeDto dto) {
        TPssPJudge tPssPJudge = BeanUtil.toBean(dto, TPssPJudge.class);
        this.baseMapper.insert(tPssPJudge);

        return BeanUtil.copyProperties(tPssPJudge, TPssPJudgeVo.class);
    }

    @Override
    public TPssPJudgeVo update(UpdateTPssPJudgeDto dto) {
        TPssPJudge tPssPJudge = BeanUtil.toBean(dto, TPssPJudge.class);
        this.baseMapper.updateById(tPssPJudge);

        return BeanUtil.copyProperties(tPssPJudge, TPssPJudgeVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {

        List<TPssPJudgePageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssPJudgePageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssPJudge.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssPJudgePageDto dto, Boolean isTemplate) {
        List<TPssPJudgePageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssPJudgePageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "PJudge" + ExcelTypeEnum.XLSX.getValue());
    }
}
