package com.aitos.pss.enums;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import java.util.Objects;

/**
 * <AUTHOR>
 *
 * 性能单项等级
 *
 * @version 1.0.0
 * @date 2025/6/10 14:57
 */
@Getter
@Log4j2
public enum PssQPSEnum {

    /** 低于标准下限 */
    BELOW_LIMIT("1", "超下限"),

    /** 符合标准 */
    QUALIFIED("2", "合格"),

    /** 超出标准上限 */
    ABOVE_LIMIT("3", "超上限"),
    ;

    private final String code;
    private final String description;

    PssQPSEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 安全查询方法（不抛异常）
    public static PssQPSEnum getByCode(String code) {

        for (PssQPSEnum result : values()) {
            if (Objects.equals(result.code, code)) {
                return result;
            }
        }

        log.warn("无效的检测结果编码: {}", code);
        return null;
    }

    // 安全查询方法（不抛异常）
    public static PssQPSEnum getByDescription(String description) {

        for (PssQPSEnum result : values()) {
            if (result.description.equals(description)) {
                return result;
            }
        }

        log.warn("无效的检测结果描述: {}", description);
        return null;
    }

}
