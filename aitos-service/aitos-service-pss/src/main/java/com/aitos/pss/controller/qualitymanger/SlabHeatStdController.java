package com.aitos.pss.controller.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabHeatStdDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabHeatStdPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabHeatStdDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabHeatStd;
import com.aitos.pss.service.qualitymanger.ISlabHeatStdService;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdPageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 轧钢工艺参数管理-加热工艺参数
* <AUTHOR>
* @Date: 2025-05-21
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/slabheatstd")
@Tag(name = "/pss"  + "/slabheatstd",description = "轧钢工艺参数管理-加热工艺参数代码")
@AllArgsConstructor
public class SlabHeatStdController {


    private final ISlabHeatStdService slabHeatStdService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssSlabHeatStd列表(分页)")
    public RT<PageOutput<TPssSlabHeatStdPageVo>> page(@Valid TPssSlabHeatStdPageDto dto){

        return RT.ok(slabHeatStdService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssSlabHeatStd信息")
    public RT<TPssSlabHeatStdVo> info(@RequestParam Long id){

        return RT.ok(slabHeatStdService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssSlabHeatStd")
    @AitLog(value = "轧钢工艺参数管理-加热工艺参数新增数据")
    public RT<TPssSlabHeatStdVo> add(@Valid @RequestBody AddTPssSlabHeatStdDto dto){

        return RT.ok(slabHeatStdService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssSlabHeatStd")
    @AitLog(value = "轧钢工艺参数管理-加热工艺参数修改数据")
    public RT<TPssSlabHeatStdVo> update(@Valid @RequestBody UpdateTPssSlabHeatStdDto dto){

        return RT.ok(slabHeatStdService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "轧钢工艺参数管理-加热工艺参数删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(slabHeatStdService.removeBatchByIds(ids));
    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "轧钢工艺参数管理-加热工艺参数导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        slabHeatStdService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "轧钢工艺参数管理-加热工艺参数导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssSlabHeatStdPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return slabHeatStdService.exportData(dto,isTemplate);
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssSlabHeatStdDto dto){
        TPssSlabHeatStd tPssSlabHeatStd = BeanUtil.toBean(dto, TPssSlabHeatStd.class);

        return RT.ok(slabHeatStdService.updateById(tPssSlabHeatStd));
    }
}