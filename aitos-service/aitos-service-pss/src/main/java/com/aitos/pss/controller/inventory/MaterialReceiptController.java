package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPssMaterialReceiptDto;
import com.aitos.pss.dto.inventory.TPssMaterialReceiptPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMaterialReceiptDto;
import com.aitos.pss.service.inventory.IMaterialReceiptService;
import com.aitos.pss.vo.inventory.TPssMaterialReceiptPageVo;
import com.aitos.pss.vo.inventory.TPssMaterialReceiptVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 原辅料入库管理
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/materialreceipt")
@Tag(name = "/pss"  + "/materialreceipt",description = "原辅料入库管理代码")
@AllArgsConstructor
public class MaterialReceiptController {


    private final IMaterialReceiptService materialReceiptService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssMaterialReceipt列表(分页)")
    @SaCheckPermission(value = "materialreceipt:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssMaterialReceiptPageVo>> page(@Valid TPssMaterialReceiptPageDto dto){
        return RT.ok(materialReceiptService.page(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssMaterialReceipt信息")
    @SaCheckPermission(value = "materialreceipt:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssMaterialReceiptVo> info(@RequestParam Long id){
        return RT.ok(materialReceiptService.info(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssMaterialReceipt")
    @SaCheckPermission(value = "materialreceipt:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料入库管理新增数据")
    public RT<Long> add(@Valid @RequestBody AddTPssMaterialReceiptDto dto){
        return RT.ok(materialReceiptService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssMaterialReceipt")
    @SaCheckPermission(value = "materialreceipt:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料入库管理修改数据")
    public RT<Boolean> update(@Valid @RequestBody UpdateTPssMaterialReceiptDto dto){
        return RT.ok(materialReceiptService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "materialreceipt:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料入库管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(materialReceiptService.removeBatchByIds(ids));
    }

}