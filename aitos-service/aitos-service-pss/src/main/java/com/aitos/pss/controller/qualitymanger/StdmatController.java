package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssStdmatDto;
import com.aitos.pss.dto.qualitymanger.TPssStdmatPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdmatDto;
import com.aitos.pss.service.qualitymanger.IStdmatService;
import com.aitos.pss.vo.qualitymanger.TPssStdmatPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdmatVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 质量设计性能结果查看
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/stdmat")
@Tag(name = "/pss"  + "/stdmat",description = "质量设计性能结果查看代码")
@AllArgsConstructor
public class StdmatController {


    private final IStdmatService stdmatService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssStdmat列表(分页)")
    public RT<PageOutput<TPssStdmatPageVo>> page(@Valid TPssStdmatPageDto dto){

        return RT.ok(stdmatService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssStdmat信息")
    public RT<TPssStdmatVo> info(@RequestParam Long id){

        return RT.ok(stdmatService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssStdmat")
    @AitLog(value = "质量设计性能结果查看新增数据")
    public RT<TPssStdmatVo> add(@Valid @RequestBody AddTPssStdmatDto dto){

        return RT.ok(stdmatService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssStdmat")
    @AitLog(value = "质量设计性能结果查看修改数据")
    public RT<TPssStdmatVo> update(@Valid @RequestBody UpdateTPssStdmatDto dto){

        return RT.ok(stdmatService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "质量设计性能结果查看删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(stdmatService.removeBatchByIds(ids));
    }

}