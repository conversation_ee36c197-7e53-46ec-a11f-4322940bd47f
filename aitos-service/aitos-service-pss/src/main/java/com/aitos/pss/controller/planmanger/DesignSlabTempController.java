package com.aitos.pss.controller.planmanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssFinalDesignSlabTempDto;
import com.aitos.pss.dto.planmanger.TPssFinalDesignSlabTempPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssFinalDesignSlabTempDto;
import com.aitos.pss.service.planmanger.IDesignSlabTempService;
import com.aitos.pss.vo.planmanger.TPssFinalDesignSlabTempPageVo;
import com.aitos.pss.vo.planmanger.TPssFinalDesignSlabTempVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 坯料设计临时表
* <AUTHOR>
* @Date: 2025-07-21
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/designslabtemp")
@Tag(name = "/pss"  + "/designslabtemp",description = "坯料设计临时表代码")
@AllArgsConstructor
public class DesignSlabTempController {


    private final IDesignSlabTempService designSlabTempService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssFinalDesignSlabTemp列表(分页)")
    @SaCheckPermission(value = "designslabtemp:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssFinalDesignSlabTempPageVo>> page(@Valid TPssFinalDesignSlabTempPageDto dto){

        return RT.ok(designSlabTempService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssFinalDesignSlabTemp信息")
    @SaCheckPermission(value = "designslabtemp:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssFinalDesignSlabTempVo> info(@RequestParam Long id){

        return RT.ok(designSlabTempService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssFinalDesignSlabTemp")
    @SaCheckPermission(value = "designslabtemp:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "坯料设计临时表新增数据")
    public RT<TPssFinalDesignSlabTempVo> add(@Valid @RequestBody AddTPssFinalDesignSlabTempDto dto){

        return RT.ok(designSlabTempService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssFinalDesignSlabTemp")
    @SaCheckPermission(value = "designslabtemp:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "坯料设计临时表修改数据")
    public RT<TPssFinalDesignSlabTempVo> update(@Valid @RequestBody UpdateTPssFinalDesignSlabTempDto dto){

        return RT.ok(designSlabTempService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "designslabtemp:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "坯料设计临时表删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(designSlabTempService.removeBatchByIds(ids));
    }

}