package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPssMatOutboundRecordDto;
import com.aitos.pss.dto.inventory.TPssMatOutboundRecordPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMatOutboundRecordDto;
import com.aitos.pss.service.inventory.IMatOutboundRecordService;
import com.aitos.pss.vo.inventory.TPssMatOutboundRecordPageVo;
import com.aitos.pss.vo.inventory.TPssMatOutboundRecordVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 原辅料出库管理
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/matoutboundrecord")
@Tag(name = "/pss"  + "/matoutboundrecord",description = "原辅料出库管理代码")
@AllArgsConstructor
public class MatOutboundRecordController {


    private final IMatOutboundRecordService matOutboundRecordService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssMatOutboundRecord列表(分页)")
    @SaCheckPermission(value = "matoutboundrecord:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssMatOutboundRecordPageVo>> page(@Valid TPssMatOutboundRecordPageDto dto){

        return RT.ok(matOutboundRecordService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssMatOutboundRecord信息")
    @SaCheckPermission(value = "matoutboundrecord:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssMatOutboundRecordVo> info(@RequestParam Long id){

        return RT.ok(matOutboundRecordService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssMatOutboundRecord")
    @SaCheckPermission(value = "matoutboundrecord:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料出库管理新增数据")
    public RT<TPssMatOutboundRecordVo> add(@Valid @RequestBody AddTPssMatOutboundRecordDto dto){

        return RT.ok(matOutboundRecordService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssMatOutboundRecord")
    @SaCheckPermission(value = "matoutboundrecord:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料出库管理修改数据")
    public RT<TPssMatOutboundRecordVo> update(@Valid @RequestBody UpdateTPssMatOutboundRecordDto dto){

        return RT.ok(matOutboundRecordService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "matoutboundrecord:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料出库管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(matOutboundRecordService.removeBatchByIds(ids));
    }

}