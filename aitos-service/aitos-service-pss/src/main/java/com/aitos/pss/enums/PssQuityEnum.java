package com.aitos.pss.enums;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import java.util.Objects;

/**
 * <AUTHOR>
 *
 * 质量等级
 *
 * @version 1.0.0
 * @date 2025/6/10 14:35
 */
@Getter
@Log4j2
public enum PssQuityEnum {

    /** 符合内部标准 */
    INTERNAL_STANDARD("1", "合内控"),

    /** 符合判定标准 */
    JUDGMENT_STANDARD("2", "合判定"),

    /** 符合国家标准 */
    NATIONAL_STANDARD("3", "合国标"),

    /** 成分让步接受 */
    COMPONENT_CONCESSION("4", "成分让步"),

    /** 不合格 */
    FAILED("5", "不合格"),
    ;

    private final String code;
    private final String description;

    PssQuityEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举
     */
    public static PssQuityEnum getByCode(String code) {
        for (PssQuityEnum result : values()) {
            if (Objects.equals(result.code, code)) {
                return result;
            }
        }

        return null;
    }

    /**
     * 根据description获取枚举
     */
    public static PssQuityEnum getByDescription(String description) {
        for (PssQuityEnum result : values()) {
            if (result.description.equals(description)) {
                return result;
            }
        }

        return null;
    }

}
