package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssPFaceDto;
import com.aitos.pss.dto.qualitymanger.TPssPFacePageDto;
import com.aitos.pss.service.qualitymanger.ITPssPFaceService;
import com.aitos.pss.vo.qualitymanger.TPssPFacePageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/pss" + "/pface")
@Tag(name = "/pss"  + "/pface",description = "轧钢表尺判定管理")
@AllArgsConstructor
public class TPssPFaceController {

    private final ITPssPFaceService tPssPFaceService;

    @GetMapping(value = "/page")
    @Operation(summary = "列表(分页)")
    public RT<PageOutput<TPssPFacePageVo>> page(@Valid TPssPFacePageDto dto){

        return RT.ok(tPssPFaceService.queryPage(dto));
    }

    @Operation(summary =  "表面判定")
    @AitLog(value = "表面判定")
    @PostMapping("/fact-decision")
    public RT<Boolean> factDecision(@Valid @RequestBody List<AddTPssPFaceDto> dtoList){

        return RT.ok(tPssPFaceService.factDecision(dtoList));
    }

}