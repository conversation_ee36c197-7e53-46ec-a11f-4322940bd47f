
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssHsecSmpstdDto;
import com.aitos.pss.dto.qualitymanger.TPssHsecSmpstdPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssHsecSmpstdDto;
import com.aitos.pss.entity.qualitymanger.TPssHsecSmpstd;
import com.aitos.pss.vo.qualitymanger.TPssHsecSmpstdPageVo;
import com.aitos.pss.vo.qualitymanger.TPssHsecSmpstdVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-17
* @Version 1.0
*/

public interface IHsecSmpstdService extends IService<TPssHsecSmpstd> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssHsecSmpstdPageVo> queryPage(@Valid TPssHsecSmpstdPageDto dto);

    /**
     * query indo
     * @param id
     * @return
     */
    TPssHsecSmpstdVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssHsecSmpstdVo add(@Valid AddTPssHsecSmpstdDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssHsecSmpstdVo update(@Valid UpdateTPssHsecSmpstdDto dto);

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssHsecSmpstdPageDto dto, Boolean isTemplate);
}
