package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPssSteelReturnRecordDto;
import com.aitos.pss.dto.inventory.TPssSteelReturnRecordPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssSteelReturnRecordDto;
import com.aitos.pss.service.inventory.ISteelReturnRecordService;
import com.aitos.pss.vo.inventory.TPssSteelReturnRecordPageVo;
import com.aitos.pss.vo.inventory.TPssSteelReturnRecordVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 退货管理子表
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/steelreturnrecord")
@Tag(name = "/pss"  + "/steelreturnrecord",description = "退货管理子表代码")
@AllArgsConstructor
public class SteelReturnRecordController {


    private final ISteelReturnRecordService steelReturnRecordService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssSteelReturnRecord列表(分页)")
    @SaCheckPermission(value = "steelreturnrecord:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssSteelReturnRecordPageVo>> page(@Valid TPssSteelReturnRecordPageDto dto){

        return RT.ok(steelReturnRecordService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssSteelReturnRecord信息")
    @SaCheckPermission(value = "steelreturnrecord:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssSteelReturnRecordVo> info(@RequestParam Long id){

        return RT.ok(steelReturnRecordService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssSteelReturnRecord")
    @SaCheckPermission(value = "steelreturnrecord:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "退货管理子表新增数据")
    public RT<TPssSteelReturnRecordVo> add(@Valid @RequestBody AddTPssSteelReturnRecordDto dto){

        return RT.ok(steelReturnRecordService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssSteelReturnRecord")
    @SaCheckPermission(value = "steelreturnrecord:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "退货管理子表修改数据")
    public RT<TPssSteelReturnRecordVo> update(@Valid @RequestBody UpdateTPssSteelReturnRecordDto dto){

        return RT.ok(steelReturnRecordService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "steelreturnrecord:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "退货管理子表删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(steelReturnRecordService.removeBatchByIds(ids));
    }

}