
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPsssYMatInfoDto;
import com.aitos.pss.dto.inventory.TPsssYMatInfoPageDto;
import com.aitos.pss.dto.inventory.UpdateTPsssYMatInfoDto;
import com.aitos.pss.entity.inventory.TPsssYMatInfo;
import com.aitos.pss.vo.inventory.TPsssYMatInfoPageVo;
import com.aitos.pss.vo.inventory.TPsssYMatInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/

public interface IYMatIinfoService extends IService<TPsssYMatInfo> {
    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPsssYMatInfoPageVo> queryPage(TPsssYMatInfoPageDto dto);

    /**
     * 详情查询
     * @param id
     * @return
     */
    TPsssYMatInfoVo queryInfo(Long id);

    /**
     * 新增
     * @param dto
     * @return
     */

    Long add(AddTPsssYMatInfoDto dto);

    /**
     * 更新
     * @param dto
     * @return
     */
    Boolean update(UpdateTPsssYMatInfoDto dto);
}
