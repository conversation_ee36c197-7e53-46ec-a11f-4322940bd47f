package com.aitos.pss.controller.costmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.costmanger.AddTPssFcsMatCdDto;
import com.aitos.pss.dto.costmanger.TPssFcsMatCdPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsMatCdDto;
import com.aitos.pss.service.costmanger.IPssFcsMatcdService;
import com.aitos.pss.vo.costmanger.TPssFcsMatCdPageVo;
import com.aitos.pss.vo.costmanger.TPssFcsMatCdVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 成本科目配置下表
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssfcsmatcd")
@Tag(name = "/pss"  + "/pssfcsmatcd",description = "成本科目配置下表代码")
@AllArgsConstructor
public class PssFcsMatcdController {


    private final IPssFcsMatcdService pssFcsMatcdService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssFcsMatCd列表(分页)")
    public RT<PageOutput<TPssFcsMatCdPageVo>> page(@Valid TPssFcsMatCdPageDto dto){

        return RT.ok(pssFcsMatcdService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssFcsMatCd信息")
    public RT<TPssFcsMatCdVo> info(@RequestParam Long id){

        return RT.ok(pssFcsMatcdService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssFcsMatCd")
    @AitLog(value = "成本科目配置下表新增数据")
    public RT<TPssFcsMatCdVo> add(@Valid @RequestBody AddTPssFcsMatCdDto dto){

        return RT.ok(pssFcsMatcdService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssFcsMatCd")
    @AitLog(value = "成本科目配置下表修改数据")
    public RT<TPssFcsMatCdVo> update(@Valid @RequestBody UpdateTPssFcsMatCdDto dto){

        return RT.ok(pssFcsMatcdService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "成本科目配置下表删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssFcsMatcdService.removeBatchByIds(ids));
    }

}