package com.aitos.pss.controller.planmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssOrderInfoDto;
import com.aitos.pss.dto.planmanger.TPssOrderInfoPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssOrderInfoDto;
import com.aitos.pss.service.planmanger.IOrderManagerService;
import com.aitos.pss.vo.planmanger.TPssOrderInfoPageVo;
import com.aitos.pss.vo.planmanger.TPssOrderInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 订单管理
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/ordermanagement")
@Tag(name = "/pss"  + "/ordermanagement",description = "订单管理代码")
@AllArgsConstructor
public class OrdermanagementController {


    private final IOrderManagerService ordermanagementService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssOrderInfo列表(分页)")
    public RT<PageOutput<TPssOrderInfoPageVo>> queryPage(@Valid TPssOrderInfoPageDto dto){

        return RT.ok(ordermanagementService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssOrderInfo信息")
    public RT<TPssOrderInfoVo> queryInfo(@RequestParam Long id){

        return RT.ok(ordermanagementService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssOrderInfo")
    @AitLog(value = "订单管理新增数据")
    public RT<TPssOrderInfoVo> add(@Valid @RequestBody AddTPssOrderInfoDto dto){

        return RT.ok(ordermanagementService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssOrderInfo")
    @AitLog(value = "订单管理修改数据")
    public RT<TPssOrderInfoVo> update(@Valid @RequestBody UpdateTPssOrderInfoDto dto){

        return RT.ok(ordermanagementService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "订单管理删除数据")
    public RT<Boolean> deleteBathById(@Valid @RequestBody List<Long> ids){

        return RT.ok(ordermanagementService.deleteBathById(ids));
    }

    @PostMapping(value = "/qualitySJ")
    @Operation(summary = "订单管理质量设计")
    @AitLog(value = "订单管理质量设计")
    public RT<Boolean> qualitySJ(@Valid @RequestBody List<UpdateTPssOrderInfoDto> dto){

        return RT.ok(ordermanagementService.qualitySJ(dto));
    }

    @PostMapping(value = "/qualityQXSJ")
    @Operation(summary = "订单管理质量设计取消")
    @AitLog(value = "订单管理质量设计取消")
    public RT<Boolean> qualityQXSJ(@Valid @RequestBody List<UpdateTPssOrderInfoDto> dto){

        return RT.ok(ordermanagementService.qualityQXSJ(dto));
    }

    @PostMapping(value = "/order-merge")
    @Operation(summary = "订单合并")
    @AitLog(value = "订单合并")
    public RT<Boolean> orderMerge(@Valid @RequestBody List<UpdateTPssOrderInfoDto> dtoList){

        return RT.ok(ordermanagementService.orderMerge(dtoList));
    }

    @PostMapping(value = "/order-split")
    @Operation(summary = "订单拆分")
    @AitLog(value = "订单拆分")
    public RT<Boolean> orderSplit(@Valid @RequestBody UpdateTPssOrderInfoDto dto){

        return RT.ok(ordermanagementService.orderSplit(dto));
    }

    @PostMapping(value = "/complete-prediction")
    @Operation(summary = "交期预测")
    @AitLog(value = "交期预测")
    public RT<TPssOrderInfoVo> completePrediction(@Valid @RequestBody UpdateTPssOrderInfoDto dto){

        return RT.ok(ordermanagementService.completePrediction(dto));
    }

    @PostMapping(value = "/mate-substitution")
    @Operation(summary = "坯料替代")
    @AitLog(value = "坯料替代")
    public RT<TPssOrderInfoVo> mateSubstitution(@Valid @RequestBody UpdateTPssOrderInfoDto dto){

        return RT.ok(ordermanagementService.mateSubstitution(dto));
    }
}