package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.constant.GlobalConstant;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssQualStdLibMainDto;
import com.aitos.pss.dto.qualitymanger.TPssQualStdLibMainPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualStdLibMainDto;
import com.aitos.pss.entity.qualitymanger.TPssQualStdLibMain;
import com.aitos.pss.mapper.qualitymanger.TPssQualStdLibMainMapper;
import com.aitos.pss.service.qualitymanger.IQualStdLibMainService;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibMainPageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibMainVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class QualStdLibMainServiceImpl extends MPJBaseServiceImpl<TPssQualStdLibMainMapper, TPssQualStdLibMain> implements IQualStdLibMainService {

    @Override
    public PageOutput<TPssQualStdLibMainPageVo> queryPage(TPssQualStdLibMainPageDto dto) {
        LambdaQueryWrapper<TPssQualStdLibMain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCStdcode()),TPssQualStdLibMain::getCStdcode,dto.getCStdcode())
                .like(StrUtil.isNotBlank(dto.getCStdname()),TPssQualStdLibMain::getCStdname,dto.getCStdname())
                .eq(Objects.nonNull(dto.getCProLine()),TPssQualStdLibMain::getCProLine,dto.getCProLine())
                .eq(StrUtil.isNotBlank(dto.getCStdtype()),TPssQualStdLibMain::getCStdtype,dto.getCStdtype())
                .orderByDesc(TPssQualStdLibMain::getNId)
                .select(TPssQualStdLibMain.class,x -> VoToColumnUtil.fieldsToColumns(TPssQualStdLibMainPageVo.class).contains(x.getProperty()));
        IPage<TPssQualStdLibMain> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssQualStdLibMainPageVo.class);
    }

    @Override
    public TPssQualStdLibMainVo queryInfo(Long id) {
        TPssQualStdLibMain tPssQualStdLibMain = this.baseMapper.selectById(id);
        if (tPssQualStdLibMain == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssQualStdLibMain, TPssQualStdLibMainVo.class);
    }

    @Override
    @Transactional
    public TPssQualStdLibMainVo add(AddTPssQualStdLibMainDto dto) {
        TPssQualStdLibMain tPssQualStdLibMain = BeanUtil.toBean(dto, TPssQualStdLibMain.class);
        Long dbCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssQualStdLibMain>lambdaQuery()
                                .eq(TPssQualStdLibMain::getCStdcode, dto.getCStdcode())
                );
        if (dbCount > 0) throw new MyException("编码不能重复");


        this.baseMapper.insert(tPssQualStdLibMain);

        return BeanUtil.copyProperties(tPssQualStdLibMain, TPssQualStdLibMainVo.class);
    }

    @Override
    @Transactional
    public TPssQualStdLibMainVo update(UpdateTPssQualStdLibMainDto dto) {
        TPssQualStdLibMain tPssQualStdLibMain = BeanUtil.toBean(dto, TPssQualStdLibMain.class);
        Long dbCount =
                this.baseMapper.selectCount(
                        Wrappers.<TPssQualStdLibMain>lambdaQuery()
                                .eq(TPssQualStdLibMain::getCStdcode, dto.getCStdcode())
                                .ne(TPssQualStdLibMain::getNId, dto.getNId())
                );
        if (dbCount > 0) throw new MyException("编码不能重复");

        this.baseMapper.updateById(tPssQualStdLibMain);

        return BeanUtil.copyProperties(tPssQualStdLibMain, TPssQualStdLibMainVo.class);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean delete(List<Long> ids) {
        this.baseMapper.deleteBatchIds(ids);

        return Boolean.TRUE;
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssQualStdLibMainPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssQualStdLibMainPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssQualStdLibMain.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssQualStdLibMainPageDto dto, Boolean isTemplate) {
        List<TPssQualStdLibMainPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssQualStdLibMainPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "QualStdLibMain" + ExcelTypeEnum.XLSX.getValue());
    }

    @Override
    public Boolean distribute(List<UpdateTPssQualStdLibMainDto> dtoList) {
        Set<Long> idSet = dtoList.stream().map(UpdateTPssQualStdLibMainDto::getNId).collect(Collectors.toSet());
        Long dbnUedCount = this.baseMapper.selectCount(Wrappers.<TPssQualStdLibMain>lambdaQuery().in(TPssQualStdLibMain::getNId, idSet).eq(TPssQualStdLibMain::getNIssued, GlobalConstant.NUMBER_ONE));
        if (dbnUedCount > 0) throw new MyException("已下发的数据不能重复下发");
        for (UpdateTPssQualStdLibMainDto dto : dtoList) {
            dto.setNIssued(GlobalConstant.NUMBER_ONE);
        }
        List<TPssQualStdLibMain> updateQualStdLibMainList = BeanUtil.copyToList(dtoList, TPssQualStdLibMain.class);
        this.baseMapper.updateById(updateQualStdLibMainList);

        return Boolean.TRUE;
    }

    @Override
    public List<TPssQualStdLibMainPageVo> queryList(TPssQualStdLibMainPageDto dto) {
        LambdaQueryWrapper<TPssQualStdLibMain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCStdcode()),TPssQualStdLibMain::getCStdcode,dto.getCStdcode())
                .like(StrUtil.isNotBlank(dto.getCStdname()),TPssQualStdLibMain::getCStdname,dto.getCStdname())
                .eq(Objects.nonNull(dto.getCProLine()),TPssQualStdLibMain::getCProLine,dto.getCProLine())
                .eq(StrUtil.isNotBlank(dto.getCStdtype()),TPssQualStdLibMain::getCStdtype,dto.getCStdtype())
                .orderByDesc(TPssQualStdLibMain::getNId)
                .select(TPssQualStdLibMain.class,x -> VoToColumnUtil.fieldsToColumns(TPssQualStdLibMainPageVo.class).contains(x.getProperty()));
        List<TPssQualStdLibMain> qualStdLibMainList = this.baseMapper.selectList(queryWrapper);

        return BeanUtil.copyToList(qualStdLibMainList, TPssQualStdLibMainPageVo.class);
    }
}
