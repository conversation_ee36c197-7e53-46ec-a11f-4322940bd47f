package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabHeatStdInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabHeatStdInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabHeatStdInfoDto;
import com.aitos.pss.service.qualitymanger.ISlabHeatStdInfoService;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 加热工艺质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/slabheatstdinfo")
@Tag(name = "/pss"  + "/slabheatstdinfo",description = "加热工艺质量设计结果代码")
@AllArgsConstructor
public class SlabHeatStdInfoController {


    private final ISlabHeatStdInfoService slabHeatStdInfoService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssSlabHeatStdInfo列表(分页)")
    public RT<PageOutput<TPssSlabHeatStdInfoPageVo>> page(@Valid TPssSlabHeatStdInfoPageDto dto){

        return RT.ok(slabHeatStdInfoService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssSlabHeatStdInfo信息")
    public RT<TPssSlabHeatStdInfoVo> info(@RequestParam Long id){

        return RT.ok(slabHeatStdInfoService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssSlabHeatStdInfo")
    @AitLog(value = "加热工艺质量设计结果新增数据")
    public RT<TPssSlabHeatStdInfoVo> add(@Valid @RequestBody AddTPssSlabHeatStdInfoDto dto){

        return RT.ok(slabHeatStdInfoService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssSlabHeatStdInfo")
    @AitLog(value = "加热工艺质量设计结果修改数据")
    public RT<TPssSlabHeatStdInfoVo> update(@Valid @RequestBody UpdateTPssSlabHeatStdInfoDto dto){

        return RT.ok(slabHeatStdInfoService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "加热工艺质量设计结果删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(slabHeatStdInfoService.removeBatchByIds(ids));

    }

}