package com.aitos.pss.controller.qualitymanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssTechLfDto;
import com.aitos.pss.dto.qualitymanger.TPssTechLfPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechLfDto;
import com.aitos.pss.entity.qualitymanger.TPssTechLf;
import com.aitos.pss.service.qualitymanger.ITechLfService;
import com.aitos.pss.vo.qualitymanger.TPssTechLfPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechLfVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 炼钢工艺参数管理-精炼工艺参数
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/techlf")
@Tag(name = "/pss"  + "/techlf",description = "炼钢工艺参数管理-精炼工艺参数代码")
@AllArgsConstructor
public class TechLfController {


    private final ITechLfService techLfService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssTechLf列表(分页)")
    @SaCheckPermission(value = "techlf:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssTechLfPageVo>> page(@Valid TPssTechLfPageDto dto){

        return RT.ok(techLfService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssTechLf信息")
    @SaCheckPermission(value = "techlf:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssTechLfVo> info(@RequestParam Long id){

        return RT.ok(techLfService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssTechLf")
    @SaCheckPermission(value = "techlf:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-精炼工艺参数新增数据")
    public RT<TPssTechLfVo> add(@Valid @RequestBody AddTPssTechLfDto dto){

        return RT.ok(techLfService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssTechLf")
    @SaCheckPermission(value = "techlf:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-精炼工艺参数修改数据")
    public RT<TPssTechLfVo> update(@Valid @RequestBody UpdateTPssTechLfDto dto){

        return RT.ok(techLfService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "techlf:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-精炼工艺参数删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(techLfService.removeBatchByIds(ids));
    }

    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "炼钢工艺参数管理-精炼工艺参数导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        techLfService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "炼钢工艺参数管理-精炼工艺参数导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssTechLfPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return techLfService.exportData(dto,isTemplate);
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssTechLfDto dto){

        TPssTechLf tPssTechLf = BeanUtil.toBean(dto, TPssTechLf.class);
        return RT.ok(techLfService.updateById(tPssTechLf));

    }
}