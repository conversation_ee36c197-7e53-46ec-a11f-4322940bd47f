
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssStdChemNkProDto;
import com.aitos.pss.dto.qualitymanger.TPssStdChemNkProPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdChemNkProDto;
import com.aitos.pss.entity.qualitymanger.TPssStdChemNkPro;
import com.aitos.pss.vo.qualitymanger.TPssStdChemNkProPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdChemNkProVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/

public interface IPssStdChemNkProService extends IService<TPssStdChemNkPro> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssStdChemNkProPageVo> queryPage(@Valid TPssStdChemNkProPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssStdChemNkProVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssStdChemNkProVo add(@Valid AddTPssStdChemNkProDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssStdChemNkProVo update(@Valid UpdateTPssStdChemNkProDto dto);
}
