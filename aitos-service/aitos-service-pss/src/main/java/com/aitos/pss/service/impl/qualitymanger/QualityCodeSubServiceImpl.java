package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssQualityCodeSubDto;
import com.aitos.pss.dto.qualitymanger.TPssQualityCodeSubPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualityCodeSubDto;
import com.aitos.pss.entity.qualitymanger.TPssQualityCodeSub;
import com.aitos.pss.mapper.qualitymanger.TPssQualityCodeSubMapper;
import com.aitos.pss.service.qualitymanger.IQualityCodeSubService;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodeSubPageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodeSubVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/13 14:54
 */
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class QualityCodeSubServiceImpl extends ServiceImpl<TPssQualityCodeSubMapper, TPssQualityCodeSub> implements IQualityCodeSubService {

    @Override
    public PageOutput<TPssQualityCodeSubPageVo> queryPage(TPssQualityCodeSubPageDto dto) {
        LambdaQueryWrapper<TPssQualityCodeSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StrUtil.isNotBlank(dto.getCQualCode()), TPssQualityCodeSub::getCQualCode, dto.getCQualCode())
                .orderByDesc(TPssQualityCodeSub::getNId)
                .select(TPssQualityCodeSub.class, x -> VoToColumnUtil.fieldsToColumns(TPssQualityCodeSubPageVo.class).contains(x.getProperty()));
        IPage<TPssQualityCodeSub> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssQualityCodeSubPageVo.class);
    }

    @Override
    public TPssQualityCodeSubVo queryInfo(Long id) {
        TPssQualityCodeSub tPssQualityCodeSub = this.baseMapper.selectById(id);
        if (tPssQualityCodeSub == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.toBean(tPssQualityCodeSub, TPssQualityCodeSubVo.class);
    }

    @Override
    public TPssQualityCodeSubVo add(AddTPssQualityCodeSubDto dto) {
        TPssQualityCodeSub tPssQualityCodeSub = BeanUtil.copyProperties(dto, TPssQualityCodeSub.class);
        this.baseMapper.insert(tPssQualityCodeSub);

        return BeanUtil.copyProperties(tPssQualityCodeSub, TPssQualityCodeSubVo.class);
    }

    @Override
    public TPssQualityCodeSubVo update(UpdateTPssQualityCodeSubDto dto) {
        TPssQualityCodeSub tPssQualityCodeSub = BeanUtil.copyProperties(dto, TPssQualityCodeSub.class);
        this.baseMapper.updateById(tPssQualityCodeSub);

        return BeanUtil.copyProperties(tPssQualityCodeSub, TPssQualityCodeSubVo.class);
    }
}