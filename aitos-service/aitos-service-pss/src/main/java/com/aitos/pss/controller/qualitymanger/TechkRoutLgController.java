package com.aitos.pss.controller.qualitymanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssTechkRoutLgDto;
import com.aitos.pss.dto.qualitymanger.TPssTechkRoutLgPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechkRoutLgDto;
import com.aitos.pss.entity.qualitymanger.TPssTechkRoutLg;
import com.aitos.pss.service.qualitymanger.ITechkRoutLgService;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 炼钢工艺参数管理-炼钢工艺路径
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/techkroutlg")
@Tag(name = "/pss"  + "/techkroutlg",description = "炼钢工艺参数管理-炼钢工艺路径代码")
@AllArgsConstructor
public class TechkRoutLgController {


    private final ITechkRoutLgService techkRoutLgService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssTechkRoutLg列表(分页)")
    @SaCheckPermission(value = "techkroutlg:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssTechkRoutLgPageVo>> page(@Valid TPssTechkRoutLgPageDto dto){

        return RT.ok(techkRoutLgService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssTechkRoutLg信息")
    @SaCheckPermission(value = "techkroutlg:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssTechkRoutLgVo> info(@RequestParam Long id){

        return RT.ok(techkRoutLgService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssTechkRoutLg")
    @SaCheckPermission(value = "techkroutlg:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-炼钢工艺路径新增数据")
    public RT<TPssTechkRoutLgVo> add(@Valid @RequestBody AddTPssTechkRoutLgDto dto){

        return RT.ok(techkRoutLgService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssTechkRoutLg")
    @SaCheckPermission(value = "techkroutlg:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-炼钢工艺路径修改数据")
    public RT<TPssTechkRoutLgVo> update(@Valid @RequestBody UpdateTPssTechkRoutLgDto dto){

        return RT.ok(techkRoutLgService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "techkroutlg:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "炼钢工艺参数管理-炼钢工艺路径删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(techkRoutLgService.removeBatchByIds(ids));
    }

    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "炼钢工艺参数管理-炼钢工艺路径导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        techkRoutLgService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "炼钢工艺参数管理-炼钢工艺路径导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssTechkRoutLgPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return techkRoutLgService.exportData(dto,isTemplate);
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssTechkRoutLgDto dto){
        TPssTechkRoutLg tPssTechkRoutLg = BeanUtil.toBean(dto, TPssTechkRoutLg.class);
        return RT.ok(techkRoutLgService.updateById(tPssTechkRoutLg));
    }
}