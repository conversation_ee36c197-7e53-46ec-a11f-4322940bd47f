
package com.aitos.pss.service.prodmonit;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.prodmonit.AddTPssIndicatorsDto;
import com.aitos.pss.dto.prodmonit.TPssIndicatorsPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssIndicatorsDto;
import com.aitos.pss.entity.prodmonit.TPssIndicators;
import com.aitos.pss.vo.prodmonit.TPssIndicatorsPageVo;
import com.aitos.pss.vo.prodmonit.TPssIndicatorsVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/

public interface IIndicatorConfigService extends IService<TPssIndicators> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssIndicatorsPageVo> queryPage(TPssIndicatorsPageDto dto);

    /**
     * query list
     * @param dto
     * @return
     */
    List<TPssIndicatorsPageVo> queryList(@Valid TPssIndicatorsPageDto dto);

    /**
     * 详情查询
     * @param id
     * @return
     */
    TPssIndicatorsVo queryInfo(Long id);

    /**
     * 新增
     * @param dto
     * @return
     */
    TPssIndicatorsVo add(AddTPssIndicatorsDto dto);

    /**
     * 更新
     * @param dto
     * @return
     */

    TPssIndicatorsVo update(UpdateTPssIndicatorsDto dto);

    /**
     * 数据导入
     * @param file
     * @throws IOException
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * 数据导出
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(TPssIndicatorsPageDto dto, Boolean isTemplate);
}
