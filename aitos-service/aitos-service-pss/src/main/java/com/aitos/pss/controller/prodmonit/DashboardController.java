package com.aitos.pss.controller.prodmonit;

import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.prodmonit.DashboardAggregationDto;
import com.aitos.pss.dto.prodmonit.QueryDashboardDto;
import com.aitos.pss.service.prodmonit.IDashboardService;
import com.aitos.pss.vo.chart.ChartVo;
import com.aitos.pss.vo.chart.LineChartSeries;
import com.aitos.pss.vo.chart.MultiLineChartSeries;
import com.aitos.pss.vo.chart.PieChartItem;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @title 监控仪表盘
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/29 09:52
 */
@RestController
@RequestMapping("/pss" + "/dashboard")
@Tag(name = "/pss"  + "/dashboard",description = "监控仪表盘代码")
@AllArgsConstructor
public class DashboardController {

    private final IDashboardService dashboardService;

    @GetMapping(value = "/query-aggregation")
    @Operation(summary = "聚合查询")
    public RT<DashboardAggregationDto> queryAggregation(@Valid QueryDashboardDto dto){

        return RT.ok(dashboardService.queryAggregation(dto));
    }

    @GetMapping(value = "/query-pcr")
    @Operation(summary = "生产指标实时监控")
    public RT<ChartVo<List<LineChartSeries>>> queryPCR(@Valid QueryDashboardDto dto){

        return RT.ok(dashboardService.queryPCR(dto));
    }

    @GetMapping(value = "/query-equipment-status")
    @Operation(summary = "设备状态")
    public RT<ChartVo<List<PieChartItem>>> queryEquipmentStatus(@Valid QueryDashboardDto dto){

        return RT.ok(dashboardService.queryEquipmentStatus(dto));
    }

    @GetMapping(value = "/query-temperature-trend")
    @Operation(summary = "温度趋势")
    public RT<ChartVo<List<MultiLineChartSeries>>> queryTemperatureTrend(@Valid QueryDashboardDto dto){

        return RT.ok(dashboardService.queryTemperatureTrend(dto));
    }

}
