
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPssWareHouseLocDto;
import com.aitos.pss.dto.inventory.TPssWareHouseLocPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssWareHouseLocDto;
import com.aitos.pss.entity.inventory.TPssWareHouseLoc;
import com.aitos.pss.vo.inventory.TPssWareHouseLocPageVo;
import com.aitos.pss.vo.inventory.TPssWareHouseLocVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/

public interface IWareHouseLocService extends IService<TPssWareHouseLoc> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssWareHouseLocPageVo> queryPage(@Valid TPssWareHouseLocPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssWareHouseLocVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssWareHouseLocVo add(@Valid AddTPssWareHouseLocDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssWareHouseLocVo update(@Valid UpdateTPssWareHouseLocDto dto);
}
