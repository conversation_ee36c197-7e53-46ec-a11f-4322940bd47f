package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.constant.GlobalConstant;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssMatResDto;
import com.aitos.pss.dto.planmanger.UpdateTPssMatResDto;
import com.aitos.pss.dto.qualitymanger.TPssMatResPageDto;
import com.aitos.pss.entity.planmanger.TPssMatRes;
import com.aitos.pss.entity.qualitymanger.TPssPFace;
import com.aitos.pss.entity.qualitymanger.TPssPSize;
import com.aitos.pss.enums.PssSlabMatStatusEnum;
import com.aitos.pss.mapper.planmanger.TPssMatResMapper;
import com.aitos.pss.service.qualitymanger.IMatResService;
import com.aitos.pss.service.qualitymanger.ITPssPFaceService;
import com.aitos.pss.service.qualitymanger.ITPssPSizeService;
import com.aitos.pss.vo.planmanger.TPssMatResVo;
import com.aitos.pss.vo.qualitymanger.TPssMatResPageVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class MatResServiceImpl extends MPJBaseServiceImpl<TPssMatResMapper, TPssMatRes> implements IMatResService {
    private static final BigDecimal PI = new BigDecimal("3.14159265358979323846");

    private final ITPssPFaceService pFaceService;

    private final ITPssPSizeService pSizeService;

    @Override
    public PageOutput<TPssMatResPageVo> queryPage(TPssMatResPageDto dto) {
        LambdaQueryWrapper<TPssMatRes> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCMatId()),TPssMatRes::getCMatId,dto.getCMatId())
                .like(StrUtil.isNotBlank(dto.getCHeatId()),TPssMatRes::getCHeatId,dto.getCHeatId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssMatRes::getCStlGrdCd,dto.getCStlGrdCd())
                .between(
                        ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),
                        TPssMatRes::getDtCreateDateTime,
                        dto.getDtCreateDateTimeStart(),
                        dto.getDtCreateDateTimeEnd())
                .between(
                        ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),
                        TPssMatRes::getDtModifyDateTime,
                        dto.getDtModifyDateTimeStart(),
                        dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssMatRes::getNId)
                .select(TPssMatRes.class,x -> VoToColumnUtil.fieldsToColumns(TPssMatResPageVo.class).contains(x.getProperty()));
        if (StrUtil.isNotBlank(dto.getCHeatIdList())) {
            queryWrapper.in(TPssMatRes::getCHeatId,dto.getCHeatIdList().split(","));
        }
        IPage<TPssMatRes> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssMatResPageVo.class);
    }

    @Override
    public PageOutput<TPssMatResPageVo> mainMatePage(TPssMatResPageDto dto) {
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getCHeatIdList())) {
            return null;
        }

        LambdaQueryWrapper<TPssMatRes> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCMatId()),TPssMatRes::getCMatId,dto.getCMatId())
                .like(StrUtil.isNotBlank(dto.getCHeatId()),TPssMatRes::getCHeatId,dto.getCHeatId())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssMatRes::getCStlGrdCd,dto.getCStlGrdCd())
                .orderByDesc(TPssMatRes::getNId)
                .select(TPssMatRes.class,x -> VoToColumnUtil.fieldsToColumns(TPssMatResPageVo.class).contains(x.getProperty()));
        if (StrUtil.isNotBlank(dto.getCHeatIdList())) {
            queryWrapper.in(TPssMatRes::getCHeatId,dto.getCHeatIdList().split(","));
        }
        IPage<TPssMatRes> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssMatResPageVo.class);
    }

    @Override
    public TPssMatResVo queryInfo(Long id) {
        TPssMatRes tPssMatRes = getByIdDeep(id);
        if (tPssMatRes == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssMatRes, TPssMatResVo.class);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public TPssMatResVo add(AddTPssMatResDto dto) {
        TPssMatRes tPssMatRes = BeanUtil.toBean(dto, TPssMatRes.class);
        this.baseMapper.insert(tPssMatRes);
        for (TPssPFace tPssPFace : tPssMatRes.getTPssPFaceList()) {
            tPssPFace.setCPlateId(tPssMatRes.getCPlanMatId());
            pFaceService.save(tPssPFace);
        }
        for (TPssPSize tPssPSize : tPssMatRes.getTPssPSizeList()) {
            tPssPSize.setCPlateId(tPssMatRes.getCPlanMatId());
            pSizeService.save(tPssPSize);
        }

        return BeanUtil.copyProperties(tPssMatRes, TPssMatResVo.class);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean update(TPssMatRes tPssMatRes) {
        this.baseMapper.updateById(tPssMatRes);
        //********************************* TPssPFace  增删改  开始 *******************************************/
        {
            // 查出所有子级的id
            List<TPssPFace> tPssPFaceList = pFaceService.list(Wrappers.lambdaQuery(TPssPFace.class).eq(TPssPFace::getCPlateId, tPssMatRes.getCPlanMatId()).select(TPssPFace::getNId));
            List<Long> tPssPFaceIds = tPssPFaceList.stream().map(TPssPFace::getNId).collect(Collectors.toList());
            //原有子表单 没有被删除的主键
            List<Long> tPssPFaceOldIds = tPssMatRes.getTPssPFaceList().stream().map(TPssPFace::getNId).filter(Objects::nonNull).collect(Collectors.toList());
            //找到需要删除的id
            List<Long> tPssPFaceRemoveIds = tPssPFaceIds.stream().filter(item -> !tPssPFaceOldIds.contains(item)).collect(Collectors.toList());

            for (TPssPFace tPssPFace : tPssMatRes.getTPssPFaceList()) {
                //如果不等于空则修改
                if (tPssPFace.getNId() != null) {
                    pFaceService.updateById(tPssPFace);
                }
                //如果等于空 则新增
                else {
                    //已经不存在的id 删除
                    tPssPFace.setCPlateId(tPssMatRes.getCPlanMatId());
                    pFaceService.save(tPssPFace);
                }
            }
            //已经不存在的id 删除
            if(tPssPFaceRemoveIds.size() > 0){
                pFaceService.removeBatchByIds(tPssPFaceRemoveIds);
            }
        }
        //********************************* TPssPFace  增删改  结束 *******************************************/

        //********************************* TPssPSize  增删改  开始 *******************************************/
        {
            // 查出所有子级的id
            List<TPssPSize> tPssPSizeList = pSizeService.list(Wrappers.lambdaQuery(TPssPSize.class).eq(TPssPSize::getCPlateId, tPssMatRes.getCPlanMatId()).select(TPssPSize::getNId));
            List<Long> tPssPSizeIds = tPssPSizeList.stream().map(TPssPSize::getNId).collect(Collectors.toList());
            //原有子表单 没有被删除的主键
            List<Long> tPssPSizeOldIds = tPssMatRes.getTPssPSizeList().stream().map(TPssPSize::getNId).filter(Objects::nonNull).collect(Collectors.toList());
            //找到需要删除的id
            List<Long> tPssPSizeRemoveIds = tPssPSizeIds.stream().filter(item -> !tPssPSizeOldIds.contains(item)).collect(Collectors.toList());

            for (TPssPSize tPssPSize : tPssMatRes.getTPssPSizeList()) {
                //如果不等于空则修改
                if (tPssPSize.getNId() != null) {
                    pSizeService.updateById(tPssPSize);
                }
                //如果等于空 则新增
                else {
                    //已经不存在的id 删除
                    tPssPSize.setCPlateId(tPssMatRes.getCPlanMatId());
                    pSizeService.save(tPssPSize);
                }
            }
            //已经不存在的id 删除
            if(tPssPSizeRemoveIds.size() > 0){
                pSizeService.removeBatchByIds(tPssPSizeRemoveIds);
            }
        }
        //********************************* TPssPSize  增删改  结束 *******************************************/

        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean delete(List<Long> ids) {
        this.baseMapper.deleteByIds(ids);
        pFaceService.remove(Wrappers.lambdaQuery(TPssPFace.class).in(TPssPFace::getCPlateId, ids));
        pSizeService.remove(Wrappers.lambdaQuery(TPssPSize.class).in(TPssPSize::getCPlateId, ids));

        return true;
    }

    @Override
    public TPssMatResVo sampleConfirm(UpdateTPssMatResDto dto) {
        TPssMatRes matRes = BeanUtil.copyProperties(dto, TPssMatRes.class);
        matRes.setCSmpFl(String.valueOf(GlobalConstant.NUMBER_ONE));
        this.baseMapper.updateById(matRes);

        // TODO 生成委托单功能后续完善，目前没有相应菜单

        return BeanUtil.copyProperties(matRes, TPssMatResVo.class);
    }

    @Override
    public Boolean editJudge(UpdateTPssMatResDto dto) {
        TPssMatRes matRes = BeanUtil.copyProperties(dto, TPssMatRes.class);
        TPssMatRes dbMatRes = this.baseMapper.selectById(matRes.getNId());

        if (Objects.equals(matRes.getCMatCode(), dbMatRes.getCMatCode())) {
            throw new MyException("改判前后物料编码不能相同");
        }

        // 重新计算重量
        BigDecimal nMatWid = matRes.getNMatWid();
        BigDecimal area = calculateAreaFromDiameter(nMatWid);
        BigDecimal volume = area.multiply(matRes.getNMatLth());
        matRes.setNMatWgtCal(volume.multiply(NumberUtil.toBigDecimal("0.00000000785")));

        // 修改物料状态
        matRes.setCStatus(PssSlabMatStatusEnum.RAW_MATERIAL_PENDING_STORAGE.getCode());

        // TODO 库存维护

        this.baseMapper.updateById(matRes);

        return Boolean.TRUE;
    }

    @Override
    public Boolean judgeAbolish(List<UpdateTPssMatResDto> dtoList) {
        for (UpdateTPssMatResDto updateTPssMatResDto : dtoList) {
            updateTPssMatResDto.setCStatus(PssSlabMatStatusEnum.INVALID_BILET.getCode());
        }

        return updateBatchById(BeanUtil.copyToList(dtoList, TPssMatRes.class));
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssMatResPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssMatResPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssMatRes.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssMatResPageDto dto, Boolean isTemplate) {
        List<TPssMatResPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssMatResPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "MatRes" + ExcelTypeEnum.XLSX.getValue());
    }

    /**
     * 计算面积
     * @param diameter
     * @return
     */
    public static BigDecimal calculateAreaFromDiameter(BigDecimal diameter) {
        // 验证输入是否有效
        if (diameter == null || diameter.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("直径必须为正数");
        }

        // 计算半径 = 直径/2
        BigDecimal radius = diameter.divide(BigDecimal.valueOf(2), MathContext.DECIMAL128);

        // 计算面积 = π * r²
        BigDecimal area = PI.multiply(radius.pow(2), MathContext.DECIMAL128);

        return area;
    }
}
