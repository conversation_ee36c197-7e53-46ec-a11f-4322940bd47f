
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPssMaterialReceiptDto;
import com.aitos.pss.dto.inventory.TPssMaterialReceiptPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMaterialReceiptDto;
import com.aitos.pss.entity.inventory.TPssMaterialReceipt;
import com.aitos.pss.vo.inventory.TPssMaterialReceiptPageVo;
import com.aitos.pss.vo.inventory.TPssMaterialReceiptVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/

public interface IMaterialReceiptService extends IService<TPssMaterialReceipt> {
    /**
     * 分页查询
     * @param dto
	 * @return
     */
    PageOutput<TPssMaterialReceiptPageVo> page(TPssMaterialReceiptPageDto dto);

    /**
     * 详情查询
     * @param id
     * @return
     */
    TPssMaterialReceiptVo info(Long id);

    /**
     * 新增
     * @param dto
     * @return
     */
    Long add(AddTPssMaterialReceiptDto dto);

    /**
     * 更新
     * @param dto
     * @return
     */
    Boolean update(UpdateTPssMaterialReceiptDto dto);
}
