package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssStdchemDto;
import com.aitos.pss.dto.qualitymanger.TPssStdchemPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdchemDto;
import com.aitos.pss.entity.qualitymanger.TPssStdchem;
import com.aitos.pss.mapper.qualitymanger.TPssStdchemMapper;
import com.aitos.pss.service.qualitymanger.IStdchemService;
import com.aitos.pss.vo.qualitymanger.TPssStdchemPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdchemVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class StdchemServiceImpl extends ServiceImpl<TPssStdchemMapper, TPssStdchem> implements IStdchemService {

    @Override
    public PageOutput<TPssStdchemPageVo> queryPage(TPssStdchemPageDto dto) {
        LambdaQueryWrapper<TPssStdchem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getOrderId()),TPssStdchem::getOrderId,dto.getOrderId())
                .like(Objects.nonNull(dto.getCQualId()),TPssStdchem::getCQualId,dto.getCQualId())
                .like(StrUtil.isNotBlank(dto.getCChemCompCd()),TPssStdchem::getCChemCompCd,dto.getCChemCompCd())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssStdchem::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssStdchem::getNEnabledMark,dto.getNEnabledMark())
                .orderByDesc(TPssStdchem::getNId)
                .select(TPssStdchem.class,x -> VoToColumnUtil.fieldsToColumns(TPssStdchemPageVo.class).contains(x.getProperty()));
        IPage<TPssStdchem> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssStdchemPageVo.class);
    }

    @Override
    public TPssStdchemVo queryInfo(Long id) {
        TPssStdchem tPssStdchem = this.baseMapper.selectById(id);
        if (tPssStdchem == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssStdchem, TPssStdchemVo.class);
    }

    @Override
    public TPssStdchemVo add(AddTPssStdchemDto dto) {
        TPssStdchem tPssStdchem = BeanUtil.toBean(dto, TPssStdchem.class);
        this.baseMapper.insert(tPssStdchem);

        return BeanUtil.copyProperties(tPssStdchem, TPssStdchemVo.class);
    }

    @Override
    public TPssStdchemVo update(UpdateTPssStdchemDto dto) {
        TPssStdchem tPssStdchem = BeanUtil.toBean(dto, TPssStdchem.class);
        this.baseMapper.updateById(tPssStdchem);

        return BeanUtil.copyProperties(tPssStdchem, TPssStdchemVo.class);
    }
}
