package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.AddTPssMatOutboundRecordDto;
import com.aitos.pss.dto.inventory.TPssMatOutboundRecordPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMatOutboundRecordDto;
import com.aitos.pss.entity.inventory.TPssMatOutboundRecord;
import com.aitos.pss.mapper.inventory.TPssMatOutboundRecordMapper;
import com.aitos.pss.service.inventory.IMatOutboundRecordService;
import com.aitos.pss.vo.inventory.TPssMatOutboundRecordPageVo;
import com.aitos.pss.vo.inventory.TPssMatOutboundRecordVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-04
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class MatOutboundRecordServiceImpl extends ServiceImpl<TPssMatOutboundRecordMapper, TPssMatOutboundRecord> implements IMatOutboundRecordService {

    @Override
    public PageOutput<TPssMatOutboundRecordPageVo> queryPage(TPssMatOutboundRecordPageDto dto) {
        LambdaQueryWrapper<TPssMatOutboundRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getNMaterialId()),TPssMatOutboundRecord::getNMaterialId,dto.getNMaterialId())
                .eq(ObjectUtil.isNotNull(dto.getNAdjustFactor()),TPssMatOutboundRecord::getNAdjustFactor,dto.getNAdjustFactor())
                .eq(ObjectUtil.isNotNull(dto.getNBeforeStock()),TPssMatOutboundRecord::getNBeforeStock,dto.getNBeforeStock())
                .between(ObjectUtil.isNotNull(dto.getDtOutboundTimeStart()) && ObjectUtil.isNotNull(dto.getDtOutboundTimeEnd()),TPssMatOutboundRecord::getDtOutboundTime,dto.getDtOutboundTimeStart(),dto.getDtOutboundTimeEnd())
                .like(StrUtil.isNotBlank(dto.getCOperationType()),TPssMatOutboundRecord::getCOperationType,dto.getCOperationType())
                .like(StrUtil.isNotBlank(dto.getCFurnaceNo()),TPssMatOutboundRecord::getCFurnaceNo,dto.getCFurnaceNo())
                .eq(ObjectUtil.isNotNull(dto.getNTheoreticalAmount()),TPssMatOutboundRecord::getNTheoreticalAmount,dto.getNTheoreticalAmount())
                .eq(ObjectUtil.isNotNull(dto.getNActualAmount()),TPssMatOutboundRecord::getNActualAmount,dto.getNActualAmount())
                .eq(ObjectUtil.isNotNull(dto.getNAfterStock()),TPssMatOutboundRecord::getNAfterStock,dto.getNAfterStock())
                .like(StrUtil.isNotBlank(dto.getCStatus()),TPssMatOutboundRecord::getCStatus,dto.getCStatus())
                .like(StrUtil.isNotBlank(dto.getCRemark()),TPssMatOutboundRecord::getCRemark,dto.getCRemark())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssMatOutboundRecord::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssMatOutboundRecord::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssMatOutboundRecord::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssMatOutboundRecord::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssMatOutboundRecord::getDtCreateDateTime)
                .select(TPssMatOutboundRecord.class,x -> VoToColumnUtil.fieldsToColumns(TPssMatOutboundRecordPageVo.class).contains(x.getProperty()));
        IPage<TPssMatOutboundRecord> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssMatOutboundRecordPageVo.class);
    }

    @Override
    public TPssMatOutboundRecordVo queryInfo(Long id) {
        TPssMatOutboundRecord tPssMatOutboundRecord = this.baseMapper.selectById(id);
        if (tPssMatOutboundRecord == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssMatOutboundRecord, TPssMatOutboundRecordVo.class);
    }

    @Override
    public TPssMatOutboundRecordVo add(AddTPssMatOutboundRecordDto dto) {
        TPssMatOutboundRecord tPssMatOutboundRecord = BeanUtil.toBean(dto, TPssMatOutboundRecord.class);
        this.baseMapper.insert(tPssMatOutboundRecord);

        return BeanUtil.copyProperties(tPssMatOutboundRecord, TPssMatOutboundRecordVo.class);
    }

    @Override
    public TPssMatOutboundRecordVo update(UpdateTPssMatOutboundRecordDto dto) {
        TPssMatOutboundRecord tPssMatOutboundRecord = BeanUtil.toBean(dto, TPssMatOutboundRecord.class);
        this.baseMapper.updateById(tPssMatOutboundRecord);

        return BeanUtil.copyProperties(tPssMatOutboundRecord, TPssMatOutboundRecordVo.class);
    }
}
