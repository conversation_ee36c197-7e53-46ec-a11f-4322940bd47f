package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.TPssBatchSamplingPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssBatchSamplingDto;
import com.aitos.pss.entity.qualitymanger.TPssBatchSampling;
import com.aitos.pss.vo.qualitymanger.TPssBatchSamplingPageVo;
import com.aitos.pss.vo.qualitymanger.TPssBatchSamplingVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

public interface IBatchSamplingService extends IService<TPssBatchSampling> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssBatchSamplingPageVo> queryPage(@Valid TPssBatchSamplingPageDto dto);

    /**
     * 复检
     * @param dtoList
     * @return
     */
    Boolean recheck(@Valid List<UpdateTPssBatchSamplingDto> dtoList);

    /**
     * query info
     * @param id
     * @return
     */
    TPssBatchSamplingVo queryInfo(Long id);
}