package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssStlMingleDto;
import com.aitos.pss.dto.planmanger.TPssStlMinglePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssStlMingleDto;
import com.aitos.pss.entity.planmanger.TPssStlMingle;
import com.aitos.pss.mapper.planmanger.TPssStlMingleMapper;
import com.aitos.pss.service.planmanger.ITPssStlMingleService;
import com.aitos.pss.vo.planmanger.TPssStlMinglePageVo;
import com.aitos.pss.vo.planmanger.TPssStlMingleVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssStlMingleServiceImpl extends ServiceImpl<TPssStlMingleMapper, TPssStlMingle> implements ITPssStlMingleService {

    @Override
    public PageOutput<TPssStlMinglePageVo> queryPage(TPssStlMinglePageDto dto) {
        LambdaQueryWrapper<TPssStlMingle> queryWrapper =
                Wrappers.<TPssStlMingle>lambdaQuery()
                        .eq(Objects.nonNull(dto.getCProLine()),TPssStlMingle::getCProLine,dto.getCProLine())
                        .eq(StrUtil.isNotBlank(dto.getCStlGrdCdB()),TPssStlMingle::getCStlGrdCdB,dto.getCStlGrdCdB())
                        .eq(StrUtil.isNotBlank(dto.getCStlGrdCdA()),TPssStlMingle::getCStlGrdCdA,dto.getCStlGrdCdA())
                        .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssStlMingle::getNEnabledMark,dto.getNEnabledMark())
                        .eq(ObjectUtil.isNotNull(dto.getNCastNum()),TPssStlMingle::getNCastNum,dto.getNCastNum())
                        .orderByDesc(TPssStlMingle::getNId)
                        .select(TPssStlMingle.class,x -> VoToColumnUtil.fieldsToColumns(TPssStlMinglePageVo.class).contains(x.getProperty()));
        IPage<TPssStlMingle> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssStlMinglePageVo.class);
    }

    @Override
    public TPssStlMingleVo queryInfo(Long id) {
        TPssStlMingle tPssStlMingle = this.baseMapper.selectById(id);
        if (tPssStlMingle == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssStlMingle, TPssStlMingleVo.class);
    }

    @Override
    public TPssStlMingleVo add(AddTPssStlMingleDto dto) {
        TPssStlMingle tPssStlMingle = BeanUtil.toBean(dto, TPssStlMingle.class);
        this.baseMapper.insert(tPssStlMingle);

        return BeanUtil.copyProperties(tPssStlMingle, TPssStlMingleVo.class);
    }

    @Override
    public TPssStlMingleVo update(UpdateTPssStlMingleDto dto) {
        TPssStlMingle tPssStlMingle = BeanUtil.toBean(dto, TPssStlMingle.class);
        this.baseMapper.updateById(tPssStlMingle);

        return BeanUtil.copyProperties(tPssStlMingle, TPssStlMingleVo.class);
    }
}
