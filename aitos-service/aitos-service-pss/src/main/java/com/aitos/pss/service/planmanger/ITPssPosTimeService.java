package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssPosTimeDto;
import com.aitos.pss.dto.planmanger.TPssPosTimePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssPosTimeDto;
import com.aitos.pss.entity.planmanger.TPssPosTime;
import com.aitos.pss.vo.planmanger.TPssPosTimePageVo;
import com.aitos.pss.vo.planmanger.TPssPosTimeVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/

public interface ITPssPosTimeService extends IService<TPssPosTime> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssPosTimePageVo> queryPage(TPssPosTimePageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssPosTimeVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssPosTimeVo add(@Valid AddTPssPosTimeDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssPosTimeVo update(@Valid UpdateTPssPosTimeDto dto);
}
