package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.AddTPsssYMatInfoDto;
import com.aitos.pss.dto.inventory.TPsssYMatInfoPageDto;
import com.aitos.pss.dto.inventory.UpdateTPsssYMatInfoDto;
import com.aitos.pss.entity.inventory.TPsssYMatInfo;
import com.aitos.pss.mapper.inventory.TPsssYMatInfoMapper;
import com.aitos.pss.service.inventory.IYMatIinfoService;
import com.aitos.pss.vo.inventory.TPsssYMatInfoPageVo;
import com.aitos.pss.vo.inventory.TPsssYMatInfoVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class YMatIinfoServiceImpl extends ServiceImpl<TPsssYMatInfoMapper, TPsssYMatInfo> implements IYMatIinfoService {

    @Override
    public PageOutput<TPsssYMatInfoPageVo> queryPage(TPsssYMatInfoPageDto dto) {
        LambdaQueryWrapper<TPsssYMatInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCMaterialName()),TPsssYMatInfo::getCMaterialName,dto.getCMaterialName())
                .like(StrUtil.isNotBlank(dto.getCSpecification()),TPsssYMatInfo::getCSpecification,dto.getCSpecification())
                .eq(ObjectUtil.isNotNull(dto.getNMaxStock()),TPsssYMatInfo::getNMaxStock,dto.getNMaxStock())
                .eq(ObjectUtil.isNotNull(dto.getNCurrentStock()),TPsssYMatInfo::getNCurrentStock,dto.getNCurrentStock())
                .like(StrUtil.isNotBlank(dto.getCMaterialCode()),TPsssYMatInfo::getCMaterialCode,dto.getCMaterialCode())
                .like(StrUtil.isNotBlank(dto.getCMaterialType()),TPsssYMatInfo::getCMaterialType,dto.getCMaterialType())
                .like(StrUtil.isNotBlank(dto.getCUnit()),TPsssYMatInfo::getCUnit,dto.getCUnit())
                .eq(ObjectUtil.isNotNull(dto.getNMinStock()),TPsssYMatInfo::getNMinStock,dto.getNMinStock())
                .like(StrUtil.isNotBlank(dto.getCRemark()),TPsssYMatInfo::getCRemark,dto.getCRemark())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPsssYMatInfo::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPsssYMatInfo::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPsssYMatInfo::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPsssYMatInfo::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                    .orderByDesc(TPsssYMatInfo::getDtCreateDateTime)
                .select(TPsssYMatInfo.class,x -> VoToColumnUtil.fieldsToColumns(TPsssYMatInfoPageVo.class).contains(x.getProperty()));
        IPage<TPsssYMatInfo> page = baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        PageOutput<TPsssYMatInfoPageVo> pageOutput = ConventPage.getPageOutput(page, TPsssYMatInfoPageVo.class);
        return pageOutput;
    }

    @Override
    public TPsssYMatInfoVo queryInfo(Long id) {
        TPsssYMatInfo tPsssYMatInfo = this.baseMapper.selectById(id);
        if (tPsssYMatInfo == null) {
            throw new MyException("找不到此数据");
        }
        return BeanUtil.toBean(tPsssYMatInfo, TPsssYMatInfoVo.class);
    }

    @Override
    public Long add(AddTPsssYMatInfoDto dto) {
        TPsssYMatInfo tPsssYMatInfo = BeanUtil.toBean(dto, TPsssYMatInfo.class);
        this.baseMapper.insert(tPsssYMatInfo);
        return tPsssYMatInfo.getNId();
    }

    @Override
    public Boolean update(UpdateTPsssYMatInfoDto dto) {
        TPsssYMatInfo tPsssYMatInfo = BeanUtil.toBean(dto, TPsssYMatInfo.class);
        this.baseMapper.updateById(tPsssYMatInfo);
        return Boolean.TRUE;
    }
}
