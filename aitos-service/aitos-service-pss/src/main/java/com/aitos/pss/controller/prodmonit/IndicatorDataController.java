package com.aitos.pss.controller.prodmonit;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.prodmonit.AddTPssIndicatorDataDto;
import com.aitos.pss.dto.prodmonit.TPssIndicatorDataPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssIndicatorDataDto;
import com.aitos.pss.service.prodmonit.IIndicatorDataService;
import com.aitos.pss.vo.prodmonit.TPssIndicatorDataPageVo;
import com.aitos.pss.vo.prodmonit.TPssIndicatorDataVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 指标数据
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/indicatordata")
@Tag(name = "/pss"  + "/indicatordata",description = "指标数据代码")
@AllArgsConstructor
public class IndicatorDataController {


    private final IIndicatorDataService indicatorDataService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssIndicatorData列表(分页)")
    @SaCheckPermission(value = "indicatordata:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssIndicatorDataPageVo>> page(@Valid TPssIndicatorDataPageDto dto){

        return RT.ok(indicatorDataService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssIndicatorData信息")
    @SaCheckPermission(value = "indicatordata:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssIndicatorDataVo> info(@RequestParam Long id){

        return RT.ok(indicatorDataService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssIndicatorData")
    @SaCheckPermission(value = "indicatordata:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "指标数据新增数据")
    public RT<TPssIndicatorDataVo> add(@Valid @RequestBody AddTPssIndicatorDataDto dto){

        return RT.ok(indicatorDataService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssIndicatorData")
    @SaCheckPermission(value = "indicatordata:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "指标数据修改数据")
    public RT<TPssIndicatorDataVo> update(@Valid @RequestBody UpdateTPssIndicatorDataDto dto){

        return RT.ok(indicatorDataService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "indicatordata:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "指标数据删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(indicatorDataService.removeBatchByIds(ids));

    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "指标数据导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        return RT.ok(indicatorDataService.importData(file));
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "指标数据导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssIndicatorDataPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return indicatorDataService.exportData(dto, isTemplate);
    }
}