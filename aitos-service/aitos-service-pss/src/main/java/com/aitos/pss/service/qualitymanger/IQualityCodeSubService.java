package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssQualityCodeSubDto;
import com.aitos.pss.dto.qualitymanger.TPssQualityCodeSubPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualityCodeSubDto;
import com.aitos.pss.entity.qualitymanger.TPssQualityCodeSub;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodeSubPageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualityCodeSubVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/13 14:53
 */
public interface IQualityCodeSubService extends IService<TPssQualityCodeSub> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageOutput<TPssQualityCodeSubPageVo> queryPage(@Valid TPssQualityCodeSubPageDto dto);

    /**
     * 查询详情
     * @param id
     * @return
     */
    TPssQualityCodeSubVo queryInfo(Long id);

    /**
     * 新增
     * @param dto
     * @return
     */
    TPssQualityCodeSubVo add(@Valid AddTPssQualityCodeSubDto dto);

    /**
     * 更新数据
     * @param dto
     * @return
     */
    TPssQualityCodeSubVo update(@Valid UpdateTPssQualityCodeSubDto dto);
}