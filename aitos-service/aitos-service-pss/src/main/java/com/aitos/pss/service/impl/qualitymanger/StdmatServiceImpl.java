package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssStdmatDto;
import com.aitos.pss.dto.qualitymanger.TPssStdmatPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdmatDto;
import com.aitos.pss.entity.qualitymanger.TPssStdmat;
import com.aitos.pss.mapper.qualitymanger.TPssStdmatMapper;
import com.aitos.pss.service.qualitymanger.IStdmatService;
import com.aitos.pss.vo.qualitymanger.TPssStdmatPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdmatVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class StdmatServiceImpl extends ServiceImpl<TPssStdmatMapper, TPssStdmat> implements IStdmatService {

    @Override
    public PageOutput<TPssStdmatPageVo> queryPage(TPssStdmatPageDto dto) {
        LambdaQueryWrapper<TPssStdmat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssStdmat::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(Objects.nonNull(dto.getCVerifyItemId()),TPssStdmat::getCVerifyItemId,dto.getCVerifyItemId())
                .eq(Objects.nonNull(dto.getCQualId()),TPssStdmat::getCQualId,dto.getCQualId())
                .eq(Objects.nonNull(dto.getNEnabledMark()),TPssStdmat::getNEnabledMark,dto.getNEnabledMark())
                .like(StrUtil.isNotBlank(dto.getOrderId()),TPssStdmat::getOrderId,dto.getOrderId())
                .like(StrUtil.isNotBlank(dto.getCOpStdName()),TPssStdmat::getCOpStdName,dto.getCOpStdName())
                .orderByDesc(TPssStdmat::getNId)
                .select(TPssStdmat.class,x -> VoToColumnUtil.fieldsToColumns(TPssStdmatPageVo.class).contains(x.getProperty()));
        IPage<TPssStdmat> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssStdmatPageVo.class);
    }

    @Override
    public TPssStdmatVo queryInfo(Long id) {
        TPssStdmat tPssStdmat = this.baseMapper.selectById(id);
        if (tPssStdmat == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssStdmat, TPssStdmatVo.class);
    }

    @Override
    public TPssStdmatVo add(AddTPssStdmatDto dto) {
        TPssStdmat tPssStdmat = BeanUtil.toBean(dto, TPssStdmat.class);
        this.baseMapper.insert(tPssStdmat);

        return BeanUtil.copyProperties(tPssStdmat, TPssStdmatVo.class);
    }

    @Override
    public TPssStdmatVo update(UpdateTPssStdmatDto dto) {
        TPssStdmat tPssStdmat = BeanUtil.toBean(dto, TPssStdmat.class);
        this.baseMapper.updateById(tPssStdmat);

        return BeanUtil.copyProperties(tPssStdmat, TPssStdmatVo.class);
    }
}
