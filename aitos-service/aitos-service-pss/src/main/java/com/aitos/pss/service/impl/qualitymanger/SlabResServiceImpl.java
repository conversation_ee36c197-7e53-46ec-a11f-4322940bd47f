package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabResDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabResPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabResDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabJudge;
import com.aitos.pss.entity.qualitymanger.TPssSlabRes;
import com.aitos.pss.enums.PssSlabMatStatusEnum;
import com.aitos.pss.mapper.qualitymanger.TPssSlabResMapper;
import com.aitos.pss.service.qualitymanger.ISlabJudgeService;
import com.aitos.pss.service.qualitymanger.ISlabResService;
import com.aitos.pss.vo.qualitymanger.TPssSlabResPageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabResVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class SlabResServiceImpl extends MPJBaseServiceImpl<TPssSlabResMapper, TPssSlabRes> implements ISlabResService {
    private static final BigDecimal PI = new BigDecimal("3.14159265358979323846");
    
    private final ISlabJudgeService slabJudgeService;

    @Override
    public PageOutput<TPssSlabResPageVo> queryPage(TPssSlabResPageDto dto) {
        LambdaQueryWrapper<TPssSlabRes> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCMatId()),TPssSlabRes::getCMatId,dto.getCMatId())
                .like(StrUtil.isNotBlank(dto.getCHeatId()),TPssSlabRes::getCHeatId,dto.getCHeatId())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssSlabRes::getCStlGrdCd,dto.getCStlGrdCd())
                .between(
                        ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),
                        TPssSlabRes::getDtCreateDateTime,
                        dto.getDtCreateDateTimeStart(),
                        dto.getDtCreateDateTimeEnd())
                .between(
                        ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),
                        TPssSlabRes::getDtModifyDateTime,
                        dto.getDtModifyDateTimeStart(),
                        dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssSlabRes::getNId)
                .select(TPssSlabRes.class,x -> VoToColumnUtil.fieldsToColumns(TPssSlabResPageVo.class).contains(x.getProperty()));
        if (StrUtil.isNotBlank(dto.getCHeatIdList())) {
            queryWrapper
                    .in(StrUtil.isNotBlank(dto.getCHeatIdList()),TPssSlabRes::getCHeatId,dto.getCHeatIdList().split(","));
        }
        IPage<TPssSlabRes> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssSlabResPageVo.class);
    }

    @Override
    public TPssSlabResVo queryInfo(Long id) {
        TPssSlabRes tPssSlabRes = this.baseMapper.selectById(id);
        if (tPssSlabRes == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssSlabRes, TPssSlabResVo.class);
    }

    @Override
    @Transactional
    public TPssSlabResVo add(AddTPssSlabResDto dto) {
        TPssSlabRes tPssSlabRes = BeanUtil.toBean(dto, TPssSlabRes.class);
        this.baseMapper.insert(tPssSlabRes);

        return BeanUtil.copyProperties(tPssSlabRes, TPssSlabResVo.class);
    }

    @Override
    @Transactional
    public Boolean update(TPssSlabRes tPssSlabRes) {
        this.baseMapper.updateById(tPssSlabRes);

        return Boolean.TRUE;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean delete(List<Long> ids) {
        this.baseMapper.deleteByIds(ids);

        return Boolean.TRUE;
    }

    @Override
    public TPssSlabResVo sampleConfirm(UpdateTPssSlabResDto dto) {
        TPssSlabRes slabRes = BeanUtil.copyProperties(dto, TPssSlabRes.class);
        slabRes.setCSmpFl("1");
        this.baseMapper.updateById(slabRes);

        // TODO       根据检验批号查询主表关联的信息，将信息插入到t_pss_test_crd委托单主表和委托单子表t_pss_test_crd_dtl中。 目前此菜单还未开发

        return BeanUtil.copyProperties(slabRes, TPssSlabResVo.class);
    }

    @Override
    public TPssSlabResVo editJudge(UpdateTPssSlabResDto dto) {
        TPssSlabRes slabRes = BeanUtil.copyProperties(dto, TPssSlabRes.class);
        if (!Objects.equals(slabRes.getCStatus(), PssSlabMatStatusEnum.STEEL_MAKING_IN_STORAGE.getCode())) {
            throw new MyException("只能改判在库状态胚料");
        }

        // 修改钢坯综判
        String cMatId = slabRes.getCMatId();
        TPssSlabJudge slabJudge = null;
        if (StringUtils.isNotBlank(cMatId)) {
            slabJudge = slabJudgeService.getOne(Wrappers.<TPssSlabJudge>lambdaQuery().eq(TPssSlabJudge::getCSlabId, cMatId));
        }


        // 重新计算重量
        BigDecimal nMatWid = slabRes.getNMatWid();
        BigDecimal area = calculateAreaFromDiameter(nMatWid);
        BigDecimal volume = area.multiply(slabRes.getNMatLth());
        slabRes.setNMatWgtCal(volume.multiply(NumberUtil.toBigDecimal("0.00000000785")));

        // 修改物料状态
        slabRes.setCStatus(PssSlabMatStatusEnum.STEEL_MAKING_PENDING_STORAGE.getCode());

        // 修改钢坯综判表
        if (Objects.nonNull(slabJudge) && !Objects.equals(slabJudge.getCNewMatCode(),slabRes.getCMatCode())) {
            // 修改物料编码
            slabJudge.setCOldMatCode(slabJudge.getCNewMatCode());
            slabJudge.setCNewMatCode(slabRes.getCMatCode());
        }

        if (Objects.nonNull(slabJudge) && !Objects.equals(slabJudge.getCNewStlGrdCd(),slabRes.getCStlGrdCd())) {
            // 修改钢种编码
            slabJudge.setCOldStlGrdCd(slabJudge.getCNewStlGrdCd());
            slabJudge.setCNewStlGrdCd(slabRes.getCStlGrdCd());
        }

        // TODO 库存维护

        this.updateById(slabRes);
        if (Objects.nonNull(slabJudge)) {
            slabJudgeService.updateById(slabJudge);
        }

        return BeanUtil.copyProperties(slabRes, TPssSlabResVo.class);
    }

    @Override
    public Boolean judgeAbolish(List<UpdateTPssSlabResDto> dtoList) {
        for (UpdateTPssSlabResDto updateTPssSlabResDto : dtoList) {
            updateTPssSlabResDto.setCStatus(PssSlabMatStatusEnum.INVALID_BILET.getCode());
        }

        return updateBatchById(BeanUtil.copyToList(dtoList, TPssSlabRes.class));
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssSlabResPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssSlabResPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssSlabRes.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssSlabResPageDto dto, Boolean isTemplate) {
        List<TPssSlabResPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssSlabResPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "SlabRes" + ExcelTypeEnum.XLSX.getValue());
    }

    /**
     * 计算面积
     * @param diameter
     * @return
     */
    public static BigDecimal calculateAreaFromDiameter(BigDecimal diameter) {
        // 验证输入是否有效
        if (diameter == null || diameter.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("直径必须为正数");
        }

        // 计算半径 = 直径/2
        BigDecimal radius = diameter.divide(BigDecimal.valueOf(2), MathContext.DECIMAL128);

        // 计算面积 = π * r²
        BigDecimal area = PI.multiply(radius.pow(2), MathContext.DECIMAL128);

        return area;
    }
}
