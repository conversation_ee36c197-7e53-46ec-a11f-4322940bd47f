package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.FormatUtil;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.*;
import com.aitos.pss.dto.qualitymanger.*;
import com.aitos.pss.entity.planmanger.TPssAggregatePlan;
import com.aitos.pss.entity.planmanger.TPssOrderCombin;
import com.aitos.pss.entity.planmanger.TPssOrderInfo;
import com.aitos.pss.entity.qualitymanger.*;
import com.aitos.pss.enums.OrderStatusEnum;
import com.aitos.pss.mapper.planmanger.TPssOrderInfoMapper;
import com.aitos.pss.service.planmanger.IAggregatePlanService;
import com.aitos.pss.service.planmanger.IOrderCombinService;
import com.aitos.pss.service.planmanger.IOrderManagerService;
import com.aitos.pss.service.qualitymanger.*;
import com.aitos.pss.vo.planmanger.TPssOrderInfoPageVo;
import com.aitos.pss.vo.planmanger.TPssOrderInfoVo;
import com.aitos.system.client.v2.ICodeRuleClientV2;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class OrderManagerServiceImpl extends ServiceImpl<TPssOrderInfoMapper, TPssOrderInfo> implements IOrderManagerService {

    private final ICodeRuleClientV2 codeRuleClientV2;

    private final IPssStdchemLgService stdchemLgService;

    private final IStdchemService stdchemService;

    private final ITechEafService techEafService;

    private final ITechEafInfoService techEafInfoService;

    private final ITechLfService techLfService;

    private final ITechIfInfoService techIfInfoService;

    private final ITechCcmService techCcmService;

    private final ITechCcmInfoService techCcmInfoService;

    private final ITechkRoutLgService techkRoutLgService;

    private final ITechkRoutLgInfoService techkRoutLgInfoService;

    private final IStdmatZgService stdmatZgService;

    private final IStdmatService stdmatService;

    private final ISlabHeatStdService slabHeatStdService;

    private final ISlabHeatStdInfoService slabHeatStdInfoService;

    private final IRollStdService rollStdService;

    private final IRollStdInfoService rollStdInfoService;

    private final IOrderCombinService orderCombinService;

    private final IAggregatePlanService aggregatePlanService;

    @Override
    public PageOutput<TPssOrderInfoPageVo> queryPage(TPssOrderInfoPageDto dto) {
        LambdaQueryWrapper<TPssOrderInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCProductLineId()),TPssOrderInfo::getCProductLineId,dto.getCProductLineId())
                .eq(Objects.nonNull(dto.getCQualId()),TPssOrderInfo::getCQualId,dto.getCQualId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssOrderInfo::getCStlGrdCd,dto.getCStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCOrderNo()),TPssOrderInfo::getCOrderNo,dto.getCOrderNo())
                .like(StrUtil.isNotBlank(dto.getCContractNo()),TPssOrderInfo::getCContractNo,dto.getCContractNo())
                .like(StrUtil.isNotBlank(dto.getCOrderState()),TPssOrderInfo::getCOrderState,dto.getCOrderState())
                .like(StrUtil.isNotBlank(dto.getCOrderType()),TPssOrderInfo::getCOrderType,dto.getCOrderType())
                .like(StrUtil.isNotBlank(dto.getCProductType()),TPssOrderInfo::getCProductType,dto.getCProductType())
                .like(StrUtil.isNotBlank(dto.getCAggregatePlanId()),TPssOrderInfo::getCAggregatePlanId,dto.getCAggregatePlanId())
                .like(StrUtil.isNotBlank(dto.getCMatItem()),TPssOrderInfo::getCMatItem,dto.getCMatItem())
                .like(StrUtil.isNotBlank(dto.getCProductTaskListId()),TPssOrderInfo::getCProductTaskListId,dto.getCProductTaskListId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssOrderInfo::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .orderByDesc(TPssOrderInfo::getNId)
                .select(TPssOrderInfo.class,x -> VoToColumnUtil.fieldsToColumns(TPssOrderInfoPageVo.class).contains(x.getProperty()));
        IPage<TPssOrderInfo> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssOrderInfoPageVo.class);
    }

    @Override
    public TPssOrderInfoVo queryInfo(Long id) {
        TPssOrderInfo tPssOrderInfo = this.baseMapper.selectById(id);
        if (tPssOrderInfo == null) {
            throw new MyException("数据不存在");
        }

        return BeanUtil.toBean(tPssOrderInfo, TPssOrderInfoVo.class);
    }

    @Override
    @GlobalTransactional
    public TPssOrderInfoVo add(AddTPssOrderInfoDto dto) {
        TPssOrderInfo tPssOrderInfo = BeanUtil.toBean(dto, TPssOrderInfo.class);
        String cItemCd = dto.getCItemCd();
        if (StringUtils.isBlank(cItemCd)) throw new MyException("规格必填");
        String[] splitCItemCd = cItemCd.split("\\*");
        String nProdThk = splitCItemCd[0];
        String nProdWid = splitCItemCd[1];
        tPssOrderInfo.setNProdThk(new BigDecimal(nProdThk));
        tPssOrderInfo.setNProdWid(new BigDecimal(nProdWid));

        String orderCode = codeRuleClientV2.generateAndUse("orderManger", dto.getCOrderType()).getDataOrThrow();
        tPssOrderInfo.setCOrderNo(orderCode);
        this.baseMapper.insert(tPssOrderInfo);

        return BeanUtil.copyProperties(tPssOrderInfo, TPssOrderInfoVo.class);
    }

    @Override
    public TPssOrderInfoVo update(UpdateTPssOrderInfoDto dto) {
        TPssOrderInfo tPssOrderInfo = BeanUtil.toBean(dto, TPssOrderInfo.class);
        String cItemCd = dto.getCItemCd();
        if (StringUtils.isBlank(cItemCd)) throw new MyException("规格必填");
        String[] splitCItemCd = cItemCd.split("\\*");
        String nProdThk = splitCItemCd[0];
        String nProdWid = splitCItemCd[1];
        tPssOrderInfo.setNProdThk(new BigDecimal(nProdThk));
        tPssOrderInfo.setNProdWid(new BigDecimal(nProdWid));

        this.baseMapper.updateById(tPssOrderInfo);

        return BeanUtil.copyProperties(tPssOrderInfo, TPssOrderInfoVo.class);
    }

    @Override
    public Boolean deleteBathById(List<Long> ids) {
        this.baseMapper.deleteByIds(ids);

        return Boolean.TRUE;
    }

    @Override
    @Transactional
    public Boolean qualitySJ(List<UpdateTPssOrderInfoDto> dtoList) {
        Set<String> notNewOrderSet =
                dtoList
                        .stream()
                        .map(UpdateTPssOrderInfoDto::getCOrderState)
                        .filter(cOrderState -> !Objects.equals(cOrderState, OrderStatusEnum.NEW_ORDER.getCode()))
                        .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(notNewOrderSet)) {
            throw new MyException("只有`新建订单`才能做质量设计");
        }

        Set<Long> nIdSet = dtoList.stream().map(UpdateTPssOrderInfoDto::getNId).collect(Collectors.toSet());
        List<TPssOrderInfo> orderInfoList = this.baseMapper.selectByIds(nIdSet);

        // 根据订单号和当前质量编码做成分质量设计，根据质量编码查询出成分标准（t_pss_stdchem_lg），
        // 根据质量等级当前质量编码的成分标准分为国标、内控、判定三种，将查询到的信息插入到t_pss_stdchem，订单号别忘了插入
        saveQualityDesign(orderInfoList);

        // 根据订单号和当前质量编码做炼钢工艺参数质量设计，根据质量编码查询出电炉、精炼、连铸工艺参数、炼钢工艺路径（t_pss_tech_eaf、t_pss_tech_lf、t_pss_tech_ccm、t_pss_techk_rout_lg）
        // 将数据插入到t_pss_tech_eaf_info、t_pss_tech_lf_info、t_pss_tech_ccm_info、t_pss_techk_rout_lg_info
        saveProcessParam(orderInfoList);

        // 根据订单号和当前质量编码做性能质量设计（如果是炼钢订单就不需要做），根据质量编码查询出轧钢性能参数表t_pss_stdmat_zg，将数据插入到t_pss_stdmat
        savePerformanceQuality(orderInfoList);

        // 根据订单号和当前质量编码做加热工艺参数质量设计，根据质量编码查询出坯料加热工艺参数、轧制工艺参数（t_pss_slab_heat_std、t_pss_roll_std）
        // 将数据插入到t_pss_slab_heat_std_info、t_pss_roll_std_info
        saveHeatProcessParam(orderInfoList);

        // 生成销售计划号（合并订单表中c_sales_plan_id的最大值+1，9位00000000001），
        // 当选中多条订单时，c_is_combined字段更新为Y，
        // 每条计划生成一条合并计划表（t_pss_order_combin）的数据，合并计划表c_is_add_plan状态为0，插入t_pss_order_combin，更新订单表
        // 更新订单表状态c_order_state为质量设计确认
        // 修改订单表相关字段：旧状态、综合生产计划号、综合生产计划序号、状态变更时间、订单操作说明、上一次的操作记录
        // 根据合并计划创建综合计划t_pss_aggregate_plan，计划坯料总数先设置为0
        saveOtherDataAndUpdateOrder(orderInfoList);


        return Boolean.TRUE;
    }

    private void saveQualityDesign(List<TPssOrderInfo> orderInfoList) {
        Set<String> cQualCodeSet = orderInfoList.stream().map(TPssOrderInfo::getCQualCode).collect(Collectors.toSet());
        List<TPssStdchemLg> stdchemLgList =
                stdchemLgService.list(Wrappers.<TPssStdchemLg>lambdaQuery().in(TPssStdchemLg::getCQualityCode, cQualCodeSet));
        Map<String, List<TPssStdchemLg>> cQualityCodeAndStdchemLgsMap =
                stdchemLgList.stream().collect(Collectors.groupingBy(TPssStdchemLg::getCQualityCode));

        List<AddTPssStdchemDto> addStdchemList = Lists.newArrayList();
        for (TPssOrderInfo orderInfo : orderInfoList) {
            List<TPssStdchemLg> tPssStdchemLgList = cQualityCodeAndStdchemLgsMap.get(orderInfo.getCQualCode());
            if(CollectionUtils.isEmpty(tPssStdchemLgList)) continue;
            for (TPssStdchemLg tPssStdchemLg : tPssStdchemLgList) {
                AddTPssStdchemDto addStdchem = BeanUtil.copyProperties(tPssStdchemLg, AddTPssStdchemDto.class);
                addStdchem.setOrderId(orderInfo.getCOrderNo());
                addStdchem.setCQualId(tPssStdchemLg.getCQualityId());
                addStdchem.setCQualCode(tPssStdchemLg.getCQualityCode());
                addStdchem.setCQualCodeName(tPssStdchemLg.getCQualityCodeName());
                addStdchemList.add(addStdchem);
            }
        }
        stdchemService.saveBatch(BeanUtil.copyToList(addStdchemList, TPssStdchem.class));
    }

    private void saveProcessParam(List<TPssOrderInfo> orderInfoList) {
        Set<String> cQualCodeSet = orderInfoList.stream().map(TPssOrderInfo::getCQualCode).collect(Collectors.toSet());

        List<TPssTechEaf> techEafList =
                techEafService.list(Wrappers.<TPssTechEaf>lambdaQuery().in(TPssTechEaf::getCQualityCode, cQualCodeSet));
        Map<String, List<TPssTechEaf>> cQualityCodeAndTPssTechEafsMap =
                techEafList.stream().collect(Collectors.groupingBy(TPssTechEaf::getCQualityCode));

        List<TPssTechLf> techLfList =
                techLfService.list(Wrappers.<TPssTechLf>lambdaQuery().in(TPssTechLf::getCQualityCode, cQualCodeSet));
        Map<String, List<TPssTechLf>> cQualityCodeAndTPssTechLfsMap =
                techLfList.stream().collect(Collectors.groupingBy(TPssTechLf::getCQualityCode));

        List<TPssTechCcm> techCcmList =
                techCcmService.list(Wrappers.<TPssTechCcm>lambdaQuery().in(TPssTechCcm::getCQualityCode, cQualCodeSet));
        Map<String, List<TPssTechCcm>> cQualityCodeAndTPssTechCcmsMap =
                techCcmList.stream().collect(Collectors.groupingBy(TPssTechCcm::getCQualityCode));

        List<TPssTechkRoutLg> techkRoutLgList =
                techkRoutLgService.list(Wrappers.<TPssTechkRoutLg>lambdaQuery().in(TPssTechkRoutLg::getCQualityCode, cQualCodeSet));
        Map<String, List<TPssTechkRoutLg>> cQualityCodeAndTPssTechkRoutLgsMap =
                techkRoutLgList.stream().collect(Collectors.groupingBy(TPssTechkRoutLg::getCQualityCode));

        List<AddTPssTechEafInfoDto> addTechEafInfoList = Lists.newArrayList();
        List<AddTPssTechLfInfoDto> addTechLfInfoList = Lists.newArrayList();
        List<AddTPssTechCcmInfoDto> addTechCcmInfoList = Lists.newArrayList();
        List<AddTPssTechkRoutLgInfoDto> addTechkRoutLgInfoList = Lists.newArrayList();
        for (TPssOrderInfo orderInfo : orderInfoList) {
            String cQualCode = orderInfo.getCQualCode();
            List<TPssTechEaf> techEafListGet = cQualityCodeAndTPssTechEafsMap.get(cQualCode);
            if(CollectionUtils.isNotEmpty(techEafListGet)) {
                for (TPssTechEaf tPssTechEaf : techEafListGet) {
                    AddTPssTechEafInfoDto addTechEafInfo = BeanUtil.copyProperties(tPssTechEaf, AddTPssTechEafInfoDto.class);
                    addTechEafInfo.setOrderId(orderInfo.getCOrderNo());
                    addTechEafInfo.setCQualId(tPssTechEaf.getCQualityId());
                    addTechEafInfo.setCQualCode(tPssTechEaf.getCQualityCode());
                    addTechEafInfo.setCQualCodeName(tPssTechEaf.getCQualityCodeName());
                    addTechEafInfoList.add(addTechEafInfo);
                }
            }

            List<TPssTechLf> techLfListGet = cQualityCodeAndTPssTechLfsMap.get(cQualCode);
            if(CollectionUtils.isNotEmpty(techLfListGet)) {
                for (TPssTechLf tPssTechLf : techLfListGet) {
                    AddTPssTechLfInfoDto addTechLfInfo = BeanUtil.copyProperties(tPssTechLf, AddTPssTechLfInfoDto.class);
                    addTechLfInfo.setOrderId(orderInfo.getCOrderNo());
                    addTechLfInfo.setCQualId(tPssTechLf.getCQualityId());
                    addTechLfInfo.setCQualCode(tPssTechLf.getCQualityCode());
                    addTechLfInfo.setCQualCodeName(tPssTechLf.getCQualityCodeName());
                    addTechLfInfoList.add(addTechLfInfo);
                }
            }

            List<TPssTechCcm> techCcmListGet = cQualityCodeAndTPssTechCcmsMap.get(cQualCode);
            if(CollectionUtils.isNotEmpty(techCcmListGet)) {
                for (TPssTechCcm tPssTechCcm : techCcmListGet) {
                    AddTPssTechCcmInfoDto addTechCcmInfo = BeanUtil.copyProperties(tPssTechCcm, AddTPssTechCcmInfoDto.class);
                    addTechCcmInfo.setOrderId(orderInfo.getCOrderNo());
                    addTechCcmInfo.setCQualId(tPssTechCcm.getCQualityId());
                    addTechCcmInfo.setCQualCode(tPssTechCcm.getCQualityCode());
                    addTechCcmInfo.setCQualCodeName(tPssTechCcm.getCQualityCodeName());
                    addTechCcmInfoList.add(addTechCcmInfo);
                }
            }

            List<TPssTechkRoutLg> techkRoutLgListGet = cQualityCodeAndTPssTechkRoutLgsMap.get(cQualCode);
            if(CollectionUtils.isNotEmpty(techkRoutLgListGet)) {
                for (TPssTechkRoutLg tPssTechkRoutLg : techkRoutLgListGet) {
                    AddTPssTechkRoutLgInfoDto addTechkRoutLgInfo = BeanUtil.copyProperties(tPssTechkRoutLg, AddTPssTechkRoutLgInfoDto.class);
                    addTechkRoutLgInfo.setOrderId(orderInfo.getCOrderNo());
                    addTechkRoutLgInfo.setCQualId(tPssTechkRoutLg.getCQualityId());
                    addTechkRoutLgInfo.setCQualCode(tPssTechkRoutLg.getCQualityCode());
                    addTechkRoutLgInfo.setCQualCodeName(tPssTechkRoutLg.getCQualityCodeName());
                    addTechkRoutLgInfoList.add(addTechkRoutLgInfo);
                }
            }

        }

        techEafInfoService.saveBatch(BeanUtil.copyToList(addTechEafInfoList, TPssTechEafInfo.class));
        techIfInfoService.saveBatch(BeanUtil.copyToList(addTechLfInfoList, TPssTechLfInfo.class));
        techCcmInfoService.saveBatch(BeanUtil.copyToList(addTechCcmInfoList, TPssTechCcmInfo.class));
        techkRoutLgInfoService.saveBatch(BeanUtil.copyToList(addTechkRoutLgInfoList, TPssTechkRoutLgInfo.class));
    }

    private void savePerformanceQuality(List<TPssOrderInfo> orderInfoList) {
        Set<String> cQualCodeSet = orderInfoList.stream().map(TPssOrderInfo::getCQualCode).collect(Collectors.toSet());
        List<TPssStdmatZg> stdmatZgList =
                stdmatZgService.list(Wrappers.<TPssStdmatZg>lambdaQuery().in(TPssStdmatZg::getCQualityCode, cQualCodeSet));
        Map<String, List<TPssStdmatZg>> cQualityCodeAndTPssStdmatZgsMap =
                stdmatZgList.stream().collect(Collectors.groupingBy(TPssStdmatZg::getCQualityCode));

        List<AddTPssStdmatDto> addStdmatList = Lists.newArrayList();
        for (TPssOrderInfo orderInfo : orderInfoList) {
            // TODO 后续维护字典枚举
            if (Objects.equals(orderInfo.getCOrderType(),"炼钢订单")) continue;

            List<TPssStdmatZg> tPssStdmatZgList = cQualityCodeAndTPssStdmatZgsMap.get(orderInfo.getCQualCode());
            if(CollectionUtils.isNotEmpty(tPssStdmatZgList)) {
                for (TPssStdmatZg tPssStdmatZg : tPssStdmatZgList) {
                    AddTPssStdmatDto addStdmat = BeanUtil.copyProperties(tPssStdmatZg, AddTPssStdmatDto.class);
                    addStdmat.setOrderId(orderInfo.getCOrderNo());
                    addStdmatList.add(addStdmat);
                }
            }
        }

        stdmatService.saveBatch(BeanUtil.copyToList(addStdmatList, TPssStdmat.class));
    }

    private void saveHeatProcessParam(List<TPssOrderInfo> orderInfoList) {
        Set<String> cQualCodeSet = orderInfoList.stream().map(TPssOrderInfo::getCQualCode).collect(Collectors.toSet());
        List<TPssSlabHeatStd> slabHeatStdList =
                slabHeatStdService.list(Wrappers.<TPssSlabHeatStd>lambdaQuery().in(TPssSlabHeatStd::getCQualityCode, cQualCodeSet));
        Map<String, List<TPssSlabHeatStd>> cQualityCodeAndSlabHeatStdsMap =
                slabHeatStdList.stream().collect(Collectors.groupingBy(TPssSlabHeatStd::getCQualityCode));

        List<TPssRollStd> rollStdList =
                rollStdService.list(Wrappers.<TPssRollStd>lambdaQuery().in(TPssRollStd::getCQualityCode, cQualCodeSet));
        Map<String, List<TPssRollStd>> cQualityCodeAndRollStdMap =
                rollStdList.stream().collect(Collectors.groupingBy(TPssRollStd::getCQualityCode));

        List<AddTPssSlabHeatStdInfoDto> addSlabHeatStdInfoList = Lists.newArrayList();
        List<AddTPssRollStdInfoDto> addRollStdInfoList = Lists.newArrayList();
        for (TPssOrderInfo orderInfo : orderInfoList) {
            String cQualCode = orderInfo.getCQualCode();
            List<TPssSlabHeatStd> slabHeatStdListGet = cQualityCodeAndSlabHeatStdsMap.get(cQualCode);
            if(CollectionUtils.isNotEmpty(slabHeatStdListGet)) {
                for (TPssSlabHeatStd tPssSlabHeatStd : slabHeatStdListGet) {
                    AddTPssSlabHeatStdInfoDto addSlabHeatStdInfo = BeanUtil.copyProperties(tPssSlabHeatStd, AddTPssSlabHeatStdInfoDto.class);
                    addSlabHeatStdInfo.setCOrderNo(orderInfo.getCOrderNo());
                    addSlabHeatStdInfo.setCQualId(tPssSlabHeatStd.getCQualityId());
                    addSlabHeatStdInfo.setCQualCode(tPssSlabHeatStd.getCQualityCode());
                    addSlabHeatStdInfo.setCQualCodeName(tPssSlabHeatStd.getCQualityCodeName());
                    addSlabHeatStdInfoList.add(addSlabHeatStdInfo);
                }
            }

            List<TPssRollStd> rollStdListGet = cQualityCodeAndRollStdMap.get(cQualCode);
            if(CollectionUtils.isNotEmpty(rollStdListGet)) {
                for (TPssRollStd tPssRollStd : rollStdListGet) {
                    AddTPssRollStdInfoDto addRollStdInfo = BeanUtil.copyProperties(tPssRollStd, AddTPssRollStdInfoDto.class);
                    addRollStdInfo.setCOrderNo(orderInfo.getCOrderNo());
                    addRollStdInfo.setCQualId(tPssRollStd.getCQualityId());
                    addRollStdInfo.setCQualCode(tPssRollStd.getCQualityCode());
                    addRollStdInfo.setCQualCodeName(tPssRollStd.getCQualityCodeName());
                    addRollStdInfoList.add(addRollStdInfo);
                }
            }
        }

        slabHeatStdInfoService.saveBatch(BeanUtil.copyToList(addSlabHeatStdInfoList, TPssSlabHeatStdInfo.class));
        rollStdInfoService.saveBatch(BeanUtil.copyToList(addRollStdInfoList, TPssRollStdInfo.class));
    }

    private void saveOtherDataAndUpdateOrder(List<TPssOrderInfo> orderInfoList) {
        Integer maxCSalesPlanId = 0;
        TPssOrderCombin maxOrderCombin =
                orderCombinService.getOne(
                        Wrappers.<TPssOrderCombin>lambdaQuery()
                                .orderByDesc(TPssOrderCombin::getCSalesPlanId)
                                .last("LIMIT 1")
                );
        if (Objects.nonNull(maxOrderCombin)) {
            maxCSalesPlanId = Integer.parseInt(maxOrderCombin.getCSalesPlanId());
        }

        List<AddTPssOrderCombinDto> addOrderCombinList = Lists.newArrayList();
        List<AddTPssAggregatePlanDto> addAggregatePlanList = Lists.newArrayList();
        for (TPssOrderInfo orderInfo : orderInfoList) {
            if (orderInfoList.size() > 1) orderInfo.setCIsCombined("Y");
            AddTPssOrderCombinDto addOrderCombin = BeanUtil.copyProperties(orderInfo, AddTPssOrderCombinDto.class);
            addOrderCombin.setCSalesPlanId(FormatUtil.formatNumber(String.valueOf(++maxCSalesPlanId),"000000000"));
            addOrderCombin.setCCombinState(OrderStatusEnum.QUALITY_DESIGN_COMPLETED.getCode());
            addOrderCombin.setCAggregatePlanId("AP" + addOrderCombin.getCSalesPlanId());
            addOrderCombin.setCOperationNote("质量设计");
            addOrderCombin.setDtStateChangeTime(LocalDateTime.now());
            addOrderCombin.setCOldOperationNote(orderInfo.getCOperationNote());
            addOrderCombin.setCOldState(orderInfo.getCOrderState());
            addOrderCombin.setCStlGrdDesc(orderInfo.getCStlGrdName());
            addOrderCombin.setCProLine(orderInfo.getCProductLineId());
            addOrderCombin.setCProLineCode(orderInfo.getCProductLineCd());
            addOrderCombin.setCProLineName(orderInfo.getCProductLineName());
            addOrderCombin.setCStdSpec(orderInfo.getCStdcode());
            addOrderCombinList.add(addOrderCombin);

            AddTPssAggregatePlanDto addAggregatePlan = BeanUtil.copyProperties(addOrderCombin, AddTPssAggregatePlanDto.class);
            addAggregatePlan.setCMatQulId(addOrderCombin.getCQualId());
            addAggregatePlan.setCMatQulCode(addOrderCombin.getCQualCode());
            addAggregatePlan.setCMatQulName(addOrderCombin.getCQualCodeName());
            addAggregatePlan.setNPlanWgt(addOrderCombin.getNOrderWgt());
            addAggregatePlan.setNProdLenth(addOrderCombin.getNProdLenth());
            addAggregatePlan.setNProdWid(addOrderCombin.getNProdWid());
            addAggregatePlan.setNProdThk(addOrderCombin.getNProdThk());
            addAggregatePlan.setCPlanState(OrderStatusEnum.QUALITY_DESIGN_COMPLETED.getCode());
            addAggregatePlanList.add(addAggregatePlan);

            orderInfo.setCOldState(orderInfo.getCOrderState());
            orderInfo.setCAggregatePlanId(addOrderCombin.getCAggregatePlanId());
            orderInfo.setDtStateChangeTime(LocalDateTime.now());
            orderInfo.setCOldOperationNote(orderInfo.getCOperationNote());
            orderInfo.setCOperationNote(addOrderCombin.getCOperationNote());
            orderInfo.setCOrderState(OrderStatusEnum.QUALITY_DESIGN_COMPLETED.getCode());
        }

        orderCombinService.saveBatch(BeanUtil.copyToList(addOrderCombinList, TPssOrderCombin.class));
        aggregatePlanService.saveBatch(BeanUtil.copyToList(addAggregatePlanList, TPssAggregatePlan.class));
        this.baseMapper.updateById(orderInfoList);
    }

    @Override
    public Boolean qualityQXSJ(List<UpdateTPssOrderInfoDto> dtoList) {
        Set<String> orderNoSet = dtoList.stream().map(UpdateTPssOrderInfoDto::getCOrderNo).collect(Collectors.toSet());
        List<TPssAggregatePlan> notPA0010PlanList =
                aggregatePlanService.list(
                        Wrappers.<TPssAggregatePlan>lambdaQuery()
                                .in(TPssAggregatePlan::getCOrderNo, orderNoSet)
                                .ne(TPssAggregatePlan::getCPlanState, OrderStatusEnum.QUALITY_DESIGN_COMPLETED.getCode())
                );
        if (CollectionUtils.isNotEmpty(notPA0010PlanList)) throw new MyException("只有状态为`质量设计完成`的订单才能取消质量设计");

        for (UpdateTPssOrderInfoDto updateTPssOrderInfoDto : dtoList) {
            updateTPssOrderInfoDto.setCOrderState(OrderStatusEnum.NEW_ORDER.getCode());
            updateTPssOrderInfoDto.setCAggregatePlanId(null);
            updateTPssOrderInfoDto.setDtStateChangeTime(null);
        }
        this.baseMapper.updateById(BeanUtil.copyToList(dtoList, TPssOrderInfo.class));

        stdchemService.remove(Wrappers.<TPssStdchem>lambdaQuery().in(TPssStdchem::getOrderId,orderNoSet));

        techEafInfoService.remove(Wrappers.<TPssTechEafInfo>lambdaQuery().in(TPssTechEafInfo::getOrderId,orderNoSet));

        techIfInfoService.remove(Wrappers.<TPssTechLfInfo>lambdaQuery().in(TPssTechLfInfo::getOrderId,orderNoSet));

        techCcmInfoService.remove(Wrappers.<TPssTechCcmInfo>lambdaQuery().in(TPssTechCcmInfo::getOrderId,orderNoSet));

        techkRoutLgInfoService.remove(Wrappers.<TPssTechkRoutLgInfo>lambdaQuery().in(TPssTechkRoutLgInfo::getOrderId,orderNoSet));

        stdmatService.remove(Wrappers.<TPssStdmat>lambdaQuery().in(TPssStdmat::getOrderId,orderNoSet));

        slabHeatStdInfoService.remove(Wrappers.<TPssSlabHeatStdInfo>lambdaQuery().in(TPssSlabHeatStdInfo::getCOrderNo,orderNoSet));

        rollStdInfoService.remove(Wrappers.<TPssRollStdInfo>lambdaQuery().in(TPssRollStdInfo::getCOrderNo,orderNoSet));

        orderCombinService.remove(Wrappers.<TPssOrderCombin>lambdaQuery().in(TPssOrderCombin::getCOrderNo,orderNoSet));

        aggregatePlanService.remove(Wrappers.<TPssAggregatePlan>lambdaQuery().in(TPssAggregatePlan::getCOrderNo,orderNoSet));

        return Boolean.TRUE;
    }

    @Override
    public Boolean orderMerge(List<UpdateTPssOrderInfoDto> dtoList) {
        // 订单类型必须相同
        checkFieldUnique(dtoList,UpdateTPssOrderInfoDto::getCOrderType,"订单类型必须相同");
        // 产品类型必须相同
        checkFieldUnique(dtoList,UpdateTPssOrderInfoDto::getCProductType,"产品类型必须相同");
        // 质量编码必须相同
        checkFieldUnique(dtoList,UpdateTPssOrderInfoDto::getCQualId,"质量编码必须相同");
        // 定尺类型必须相同
        checkFieldUnique(dtoList,UpdateTPssOrderInfoDto::getCSizeProperty,"定尺类型必须相同");
        // 产线目标必须相同
        checkFieldUnique(dtoList,UpdateTPssOrderInfoDto::getCProductLineId,"产线目标必须相同");
        // 钢坯长度目标必须相同
        checkFieldUnique(dtoList,UpdateTPssOrderInfoDto::getNProdLenth,"钢坯长度目标必须相同");
        // 钢坯长度必须相同
//        checkFieldUnique(dtoList,UpdateTPssOrderInfoDto::getCOrderType,"产品类型必须相同");
        // 钢坯厚度必须相同
        checkFieldUnique(dtoList,UpdateTPssOrderInfoDto::getNProdThk,"钢坯厚度必须相同");
        // 钢坯宽度必须相同
        checkFieldUnique(dtoList,UpdateTPssOrderInfoDto::getNProdWid,"钢坯宽度必须相同");


        UpdateTPssOrderInfoDto tmpOrderInfoDto = dtoList.get(0);
        TPssOrderInfo dbOrderInfo = this.baseMapper.selectById(tmpOrderInfoDto.getNId());

        AddTPssOrderInfoDto addOrderInfoDto = BeanUtil.copyProperties(tmpOrderInfoDto, AddTPssOrderInfoDto.class);
        addOrderInfoDto.setNOrderWgt(BigDecimal.ZERO);
        String orderCode = codeRuleClientV2.generateAndUse("orderManger", dbOrderInfo.getCOrderType()).getDataOrThrow();
        addOrderInfoDto.setCOrderNo(orderCode);
        addOrderInfoDto.setCOrderType(dbOrderInfo.getCOrderType());
        addOrderInfoDto.setCProductType(dbOrderInfo.getCProductType());

        for (UpdateTPssOrderInfoDto tPssOrderInfoDto : dtoList) {
            addOrderInfoDto.setNOrderWgt(addOrderInfoDto.getNOrderWgt().add(tPssOrderInfoDto.getNOrderWgt()));
        }

        this.baseMapper.insert(BeanUtil.copyProperties(addOrderInfoDto, TPssOrderInfo.class));

        this.baseMapper.deleteByIds(dtoList.stream().map(UpdateTPssOrderInfoDto::getNId).collect(Collectors.toSet()));

        return Boolean.TRUE;
    }

    /**
     * 校验数组同一属性是否全部相等
     * @param dtoList
     * @param getter
     * @param exceptionMsg
     * @param <T>
     */
    private <T> void checkFieldUnique(List<UpdateTPssOrderInfoDto> dtoList, Function<UpdateTPssOrderInfoDto, T> getter, String exceptionMsg) {
        if (CollectionUtils.isEmpty(dtoList)) return;

        Set<T> valueSet = dtoList.stream().map(getter).collect(Collectors.toSet());

        if (valueSet.size() > 1) {
            throw new MyException(exceptionMsg);
        }
    }


    @Override
    @GlobalTransactional
    public Boolean orderSplit(UpdateTPssOrderInfoDto dto) {
        BigDecimal nSplitOrderWgt = dto.getNSplitOrderWgt();
        BigDecimal nOrderWgt = dto.getNOrderWgt();
        if (nOrderWgt.compareTo(nSplitOrderWgt) < 0) {
            throw new MyException("拆分重量不能大于订单重量");
        }

        TPssOrderInfo dbOrderInfo = this.baseMapper.selectById(dto.getNId());

        List<TPssOrderInfo> saveDataList = Lists.newArrayList();

        AddTPssOrderInfoDto splitOne = BeanUtil.copyProperties(dto, AddTPssOrderInfoDto.class);
        splitOne.setNOrderWgt(nSplitOrderWgt);
        String orderCodeOne = codeRuleClientV2.generateAndUse("orderManger", dbOrderInfo.getCOrderType()).getDataOrThrow();
        splitOne.setCOrderNo(orderCodeOne);
        splitOne.setCOrderType(dbOrderInfo.getCOrderType());
        splitOne.setCProductType(dbOrderInfo.getCProductType());
        saveDataList.add(BeanUtil.copyProperties(splitOne, TPssOrderInfo.class));

        AddTPssOrderInfoDto splitTwo = BeanUtil.copyProperties(dto, AddTPssOrderInfoDto.class);
        splitTwo.setNOrderWgt(nOrderWgt.subtract(nSplitOrderWgt));
        String orderCodeTwo = codeRuleClientV2.generateAndUse("orderManger", dbOrderInfo.getCOrderType()).getDataOrThrow();
        splitTwo.setCOrderNo(orderCodeTwo);
        splitTwo.setCOrderType(dbOrderInfo.getCOrderType());
        splitTwo.setCProductType(dbOrderInfo.getCProductType());
        saveDataList.add(BeanUtil.copyProperties(splitTwo, TPssOrderInfo.class));

        this.baseMapper.insert(saveDataList);
        this.baseMapper.deleteById(BeanUtil.copyProperties(dto, TPssOrderInfo.class));

        return Boolean.TRUE;
    }

    @Override
    public TPssOrderInfoVo completePrediction(UpdateTPssOrderInfoDto dto) {

        // TODO 交期预测
        return BeanUtil.copyProperties(dto, TPssOrderInfoVo.class);
    }

    @Override
    public TPssOrderInfoVo mateSubstitution(UpdateTPssOrderInfoDto dto) {
        // TODO 坯料替代

        return BeanUtil.copyProperties(dto, TPssOrderInfoVo.class);
    }

}
