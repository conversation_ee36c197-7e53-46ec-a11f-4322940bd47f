package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabResDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabResPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabResDto;
import com.aitos.pss.service.qualitymanger.ISlabResService;
import com.aitos.pss.vo.qualitymanger.TPssSlabResPageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabResVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 炼钢表尺判定管理
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/slabres")
@Tag(name = "/pss"  + "/slabres",description = "炼钢表尺判定管理代码")
@AllArgsConstructor
public class SlabResController {


    private final ISlabResService slabResService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssSlabRes列表(分页)")
    public RT<PageOutput<TPssSlabResPageVo>> page(@Valid TPssSlabResPageDto dto){

        return RT.ok(slabResService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssSlabRes信息")
    public RT<TPssSlabResVo> info(@RequestParam Long id){

        return RT.ok(slabResService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssSlabRes")
    @AitLog(value = "炼钢表尺判定管理新增数据")
    public RT<TPssSlabResVo> add(@Valid @RequestBody AddTPssSlabResDto dto){

        return RT.ok(slabResService.add((dto)));
    }

    @PutMapping
    @Operation(summary = "修改TPssSlabRes")
    @AitLog(value = "炼钢表尺判定管理修改数据")
    public RT<TPssSlabResVo> update(@Valid @RequestBody UpdateTPssSlabResDto dto){

        return RT.ok(slabResService.editJudge(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "炼钢表尺判定管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(slabResService.delete(ids));

    }

    @PostMapping("/sample-confirm")
    @Operation(summary = "取样确认")
    @AitLog(value = "取样确认")
    public RT<TPssSlabResVo> sampleConfirm(@Valid @RequestBody UpdateTPssSlabResDto dto){
        return RT.ok(slabResService.sampleConfirm(dto));

    }

    @PostMapping("/judge-abolish")
    @Operation(summary = "判废")
    @AitLog(value = "判废")
    public RT<Boolean> judgeAbolish(@Valid @RequestBody List<UpdateTPssSlabResDto> dtoList){
        return RT.ok(slabResService.judgeAbolish(dtoList));

    }

    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "炼钢表尺判定管理导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        slabResService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "炼钢表尺判定管理导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssSlabResPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return slabResService.exportData(dto,isTemplate);
    }
}