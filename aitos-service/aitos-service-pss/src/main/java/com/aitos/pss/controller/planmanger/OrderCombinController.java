package com.aitos.pss.controller.planmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssOrderCombinDto;
import com.aitos.pss.dto.planmanger.TPssOrderCombinPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssOrderCombinDto;
import com.aitos.pss.service.planmanger.IOrderCombinService;
import com.aitos.pss.vo.planmanger.TPssOrderCombinPageVo;
import com.aitos.pss.vo.planmanger.TPssOrderCombinVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 合并计划表
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/ordercombin")
@Tag(name = "/pss"  + "/ordercombin",description = "合并计划表代码")
@AllArgsConstructor
public class OrderCombinController {


    private final IOrderCombinService orderCombinService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssOrderCombin列表(分页)")
    public RT<PageOutput<TPssOrderCombinPageVo>> page(@Valid TPssOrderCombinPageDto dto){

        return RT.ok(orderCombinService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssOrderCombin信息")
    public RT<TPssOrderCombinVo> info(@RequestParam Long id){

        return RT.ok(orderCombinService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssOrderCombin")
    @AitLog(value = "合并计划表新增数据")
    public RT<TPssOrderCombinVo> add(@Valid @RequestBody AddTPssOrderCombinDto dto){

        return RT.ok(orderCombinService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssOrderCombin")
    @AitLog(value = "合并计划表修改数据")
    public RT<TPssOrderCombinVo> update(@Valid @RequestBody UpdateTPssOrderCombinDto dto){

        return RT.ok(orderCombinService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "合并计划表删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(orderCombinService.removeBatchByIds(ids));

    }

}