package com.aitos.pss.enums;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import java.util.Objects;

/**
 * <AUTHOR>
 *
 * 综判等级
 *
 * @version 1.0.0
 * @date 2025/6/10 14:54
 */
@Getter
@Log4j2
public enum PssQCEnum {

    /** 待判 */
    PENDING("1", "待判"),

    /** 合格品 */
    QUALIFIED("2", "合格品"),

    /** 不合格 */
    UNQUALIFIED("3", "不合格"),

    /** 二级品 */
    SECONDARY("4", "二级品"),

    /** 废品 */
    REJECTED("5", "废品"),

    /** 无主合格品 */
    UNCLAIMED_QUALIFIED("6", "无主合格品"),
    ;

    private final String code;
    private final String description;

    PssQCEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 安全查询方法（不抛异常）
    public static PssQCEnum getByCode(String code) {

        for (PssQCEnum status : values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }

        log.warn("无效的质检状态编码: {}", code);
        return null;
    }

    // 安全查询方法（不抛异常）
    public static PssQCEnum getByDescription(String description) {

        for (PssQCEnum status : values()) {
            if (status.description.equals(description)) {
                return status;
            }
        }

        log.warn("无效的质检状态描述: {}", description);
        return null;
    }

}
