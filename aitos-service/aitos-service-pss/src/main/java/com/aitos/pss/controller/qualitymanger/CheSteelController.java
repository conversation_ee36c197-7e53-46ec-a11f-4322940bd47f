package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssCheSteelDto;
import com.aitos.pss.dto.qualitymanger.TPssCheSteelPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssCheSteelDto;
import com.aitos.pss.service.qualitymanger.ICheSteelService;
import com.aitos.pss.vo.qualitymanger.TPssCheSteelPageVo;
import com.aitos.pss.vo.qualitymanger.TPssCheSteelVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 炼钢化学成分
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/chesteel")
@Tag(name = "/pss"  + "/chesteel",description = "炼钢化学成分代码")
@AllArgsConstructor
public class CheSteelController {


    private final ICheSteelService cheSteelService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssCheSteel列表(分页)")
    public RT<PageOutput<TPssCheSteelPageVo>> page(@Valid TPssCheSteelPageDto dto){

        return RT.ok(cheSteelService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssCheSteel信息")
    public RT<TPssCheSteelVo> info(@RequestParam Long id){

        return RT.ok(cheSteelService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssCheSteel")
    @AitLog(value = "炼钢化学成分新增数据")
    public RT<TPssCheSteelVo> add(@Valid @RequestBody AddTPssCheSteelDto dto){

        return RT.ok(cheSteelService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssCheSteel")
    @AitLog(value = "炼钢化学成分修改数据")
    public RT<TPssCheSteelVo> update(@Valid @RequestBody UpdateTPssCheSteelDto dto){

        return RT.ok(cheSteelService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "炼钢化学成分删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(cheSteelService.removeBatchByIds(ids));

    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "炼钢化学成分导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {
        cheSteelService.importData(file);

        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "炼钢化学成分导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssCheSteelPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return cheSteelService.exportData(dto,isTemplate);
    }
}