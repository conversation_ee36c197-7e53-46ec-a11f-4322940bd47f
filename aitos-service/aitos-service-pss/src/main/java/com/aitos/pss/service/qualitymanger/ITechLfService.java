
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssTechLfDto;
import com.aitos.pss.dto.qualitymanger.TPssTechLfPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechLfDto;
import com.aitos.pss.entity.qualitymanger.TPssTechLf;
import com.aitos.pss.vo.qualitymanger.TPssTechLfPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechLfVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/

public interface ITechLfService extends IService<TPssTechLf> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssTechLfPageVo> queryPage(@Valid TPssTechLfPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssTechLfVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssTechLfVo add(@Valid AddTPssTechLfDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssTechLfVo update(@Valid UpdateTPssTechLfDto dto);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssTechLfPageDto dto, Boolean isTemplate);
}
