package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPssMatYardinLgDto;
import com.aitos.pss.dto.inventory.TPssMatYardinLgPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMatYardinLgDto;
import com.aitos.pss.service.inventory.IMatYardinLgService;
import com.aitos.pss.vo.inventory.TPssMatYardinLgPageVo;
import com.aitos.pss.vo.inventory.TPssMatYardinLgVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 铸坯入库管理
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/matyardinlg")
@Tag(name = "/pss"  + "/matyardinlg",description = "铸坯入库管理代码")
@AllArgsConstructor
public class MatYardinLgController {


    private final IMatYardinLgService matYardinLgService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssMatYardinLg列表(分页)")
    @SaCheckPermission(value = "matyardinlg:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssMatYardinLgPageVo>> page(@Valid TPssMatYardinLgPageDto dto){

        return RT.ok(matYardinLgService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssMatYardinLg信息")
    @SaCheckPermission(value = "matyardinlg:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssMatYardinLgVo> info(@RequestParam Long id){

        return RT.ok(matYardinLgService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssMatYardinLg")
    @SaCheckPermission(value = "matyardinlg:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "铸坯入库管理新增数据")
    public RT<TPssMatYardinLgVo> add(@Valid @RequestBody AddTPssMatYardinLgDto dto){

        return RT.ok(matYardinLgService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssMatYardinLg")
    @SaCheckPermission(value = "matyardinlg:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "铸坯入库管理修改数据")
    public RT<TPssMatYardinLgVo> update(@Valid @RequestBody UpdateTPssMatYardinLgDto dto){

        return RT.ok(matYardinLgService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "matyardinlg:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "铸坯入库管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(matYardinLgService.removeBatchByIds(ids));
    }

}