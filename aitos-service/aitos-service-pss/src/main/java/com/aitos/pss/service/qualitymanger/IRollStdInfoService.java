
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssRollStdInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssRollStdInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssRollStdInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssRollStdInfo;
import com.aitos.pss.vo.qualitymanger.TPssRollStdInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssRollStdInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/

public interface IRollStdInfoService extends IService<TPssRollStdInfo> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssRollStdInfoPageVo> queryPage(@Valid TPssRollStdInfoPageDto dto);

    /**
     *
     * @param id
     * @return
     */
    TPssRollStdInfoVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssRollStdInfoVo add(@Valid AddTPssRollStdInfoDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssRollStdInfoVo update(@Valid UpdateTPssRollStdInfoDto dto);
}
