package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssWgtOneMaterDto;
import com.aitos.pss.dto.planmanger.TPssWgtOneMaterPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssWgtOneMaterDto;
import com.aitos.pss.entity.planmanger.TPssWgtOneMater;
import com.aitos.pss.mapper.planmanger.TPssWgtOneMaterMapper;
import com.aitos.pss.service.planmanger.ITPssWgtOneMaterService;
import com.aitos.pss.vo.planmanger.TPssWgtOneMaterPageVo;
import com.aitos.pss.vo.planmanger.TPssWgtOneMaterVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TPssWgtOneMaterServiceImpl extends ServiceImpl<TPssWgtOneMaterMapper, TPssWgtOneMater> implements ITPssWgtOneMaterService {

    @Override
    public PageOutput<TPssWgtOneMaterPageVo> queryPage(TPssWgtOneMaterPageDto dto) {
        LambdaQueryWrapper<TPssWgtOneMater> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssWgtOneMater::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(ObjectUtil.isNotNull(dto.getNDensity()),TPssWgtOneMater::getNDensity,dto.getNDensity())
                .like(StrUtil.isNotBlank(dto.getCCalculationMethod()),TPssWgtOneMater::getCCalculationMethod,dto.getCCalculationMethod())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssWgtOneMater::getNEnabledMark,dto.getNEnabledMark())
                .like(StrUtil.isNotBlank(dto.getCSpec()),TPssWgtOneMater::getCSpec,dto.getCSpec())
                .eq(ObjectUtil.isNotNull(dto.getNWgtOneMater()),TPssWgtOneMater::getNWgtOneMater,dto.getNWgtOneMater())
                .eq(Objects.nonNull(dto.getCCcmId()),TPssWgtOneMater::getCCcmId,dto.getCCcmId())
                .orderByDesc(TPssWgtOneMater::getNId)
                .select(TPssWgtOneMater.class,x -> VoToColumnUtil.fieldsToColumns(TPssWgtOneMaterPageVo.class).contains(x.getProperty()));
        IPage<TPssWgtOneMater> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssWgtOneMaterPageVo.class);
    }

    @Override
    public TPssWgtOneMaterVo queryInfo(Long id) {
        TPssWgtOneMater tPssWgtOneMater = this.baseMapper.selectById(id);
        if (tPssWgtOneMater == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssWgtOneMater, TPssWgtOneMaterVo.class);
    }

    @Override
    public TPssWgtOneMaterVo add(AddTPssWgtOneMaterDto dto) {
        TPssWgtOneMater tPssWgtOneMater = BeanUtil.toBean(dto, TPssWgtOneMater.class);
        this.baseMapper.insert(tPssWgtOneMater);

        return BeanUtil.copyProperties(tPssWgtOneMater, TPssWgtOneMaterVo.class);
    }

    @Override
    public TPssWgtOneMaterVo update(UpdateTPssWgtOneMaterDto dto) {
        TPssWgtOneMater tPssWgtOneMater = BeanUtil.toBean(dto, TPssWgtOneMater.class);
        this.baseMapper.updateById(tPssWgtOneMater);

        return BeanUtil.copyProperties(tPssWgtOneMater, TPssWgtOneMaterVo.class);
    }
}
