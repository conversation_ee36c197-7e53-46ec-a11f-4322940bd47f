package com.aitos.pss.service.impl.planmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.planmanger.AddTPssOrderCombinDto;
import com.aitos.pss.dto.planmanger.TPssOrderCombinPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssOrderCombinDto;
import com.aitos.pss.entity.planmanger.TPssOrderCombin;
import com.aitos.pss.mapper.planmanger.TPssOrderCombinMapper;
import com.aitos.pss.service.planmanger.IOrderCombinService;
import com.aitos.pss.vo.planmanger.TPssOrderCombinPageVo;
import com.aitos.pss.vo.planmanger.TPssOrderCombinVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-25
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class OrderCombinServiceImpl extends ServiceImpl<TPssOrderCombinMapper, TPssOrderCombin> implements IOrderCombinService {

    @Override
    public PageOutput<TPssOrderCombinPageVo> queryPage(TPssOrderCombinPageDto dto) {
        LambdaQueryWrapper<TPssOrderCombin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getCQualId()),TPssOrderCombin::getCQualId,dto.getCQualId())
                .eq(ObjectUtil.isNotNull(dto.getCStlGrdCd()),TPssOrderCombin::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(ObjectUtil.isNotNull(dto.getCProLine()),TPssOrderCombin::getCProLine,dto.getCProLine())
                .like(StrUtil.isNotBlank(dto.getCOrderNo()),TPssOrderCombin::getCOrderNo,dto.getCOrderNo())
                .like(StrUtil.isNotBlank(dto.getCSalesPlanId()),TPssOrderCombin::getCSalesPlanId,dto.getCSalesPlanId())
                .like(StrUtil.isNotBlank(dto.getCAggregatePlanId()),TPssOrderCombin::getCAggregatePlanId,dto.getCAggregatePlanId())
                .like(StrUtil.isNotBlank(dto.getCProductTaskListId()),TPssOrderCombin::getCProductTaskListId,dto.getCProductTaskListId())
                .orderByDesc(TPssOrderCombin::getNId)
                .select(TPssOrderCombin.class,x -> VoToColumnUtil.fieldsToColumns(TPssOrderCombinPageVo.class).contains(x.getProperty()));
        IPage<TPssOrderCombin> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssOrderCombinPageVo.class);
    }

    @Override
    public TPssOrderCombinVo queryInfo(Long id) {
        TPssOrderCombin tPssOrderCombin = this.baseMapper.selectById(id);
        if (tPssOrderCombin == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssOrderCombin, TPssOrderCombinVo.class);
    }

    @Override
    public TPssOrderCombinVo add(AddTPssOrderCombinDto dto) {
        TPssOrderCombin tPssOrderCombin = BeanUtil.toBean(dto, TPssOrderCombin.class);
        this.baseMapper.insert(tPssOrderCombin);

        return BeanUtil.copyProperties(tPssOrderCombin, TPssOrderCombinVo.class);
    }

    @Override
    public TPssOrderCombinVo update(UpdateTPssOrderCombinDto dto) {
        TPssOrderCombin tPssOrderCombin = BeanUtil.toBean(dto, TPssOrderCombin.class);
        this.baseMapper.updateById(tPssOrderCombin);

        return BeanUtil.copyProperties(tPssOrderCombin, TPssOrderCombinVo.class);
    }
}
