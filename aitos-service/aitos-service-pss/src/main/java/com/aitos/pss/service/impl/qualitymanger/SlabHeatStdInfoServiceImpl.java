package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabHeatStdInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabHeatStdInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabHeatStdInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabHeatStdInfo;
import com.aitos.pss.mapper.qualitymanger.TPssSlabHeatStdInfoMapper;
import com.aitos.pss.service.qualitymanger.ISlabHeatStdInfoService;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabHeatStdInfoVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class SlabHeatStdInfoServiceImpl extends ServiceImpl<TPssSlabHeatStdInfoMapper, TPssSlabHeatStdInfo> implements ISlabHeatStdInfoService {

    @Override
    public PageOutput<TPssSlabHeatStdInfoPageVo> queryPage(TPssSlabHeatStdInfoPageDto dto) {
        LambdaQueryWrapper<TPssSlabHeatStdInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualId()),TPssSlabHeatStdInfo::getCQualId,dto.getCQualId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssSlabHeatStdInfo::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssSlabHeatStdInfo::getNEnabledMark,dto.getNEnabledMark())
                .orderByDesc(TPssSlabHeatStdInfo::getNId)
                .select(TPssSlabHeatStdInfo.class,x -> VoToColumnUtil.fieldsToColumns(TPssSlabHeatStdInfoPageVo.class).contains(x.getProperty()));
        IPage<TPssSlabHeatStdInfo> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssSlabHeatStdInfoPageVo.class);
    }

    @Override
    public TPssSlabHeatStdInfoVo queryInfo(Long id) {
        TPssSlabHeatStdInfo tPssSlabHeatStdInfo = this.baseMapper.selectById(id);
        if (tPssSlabHeatStdInfo == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssSlabHeatStdInfo, TPssSlabHeatStdInfoVo.class);
    }

    @Override
    public TPssSlabHeatStdInfoVo add(AddTPssSlabHeatStdInfoDto dto) {
        TPssSlabHeatStdInfo tPssSlabHeatStdInfo = BeanUtil.toBean(dto, TPssSlabHeatStdInfo.class);
        this.baseMapper.insert(tPssSlabHeatStdInfo);

        return BeanUtil.copyProperties(tPssSlabHeatStdInfo, TPssSlabHeatStdInfoVo.class);
    }

    @Override
    public TPssSlabHeatStdInfoVo update(UpdateTPssSlabHeatStdInfoDto dto) {
        TPssSlabHeatStdInfo tPssSlabHeatStdInfo = BeanUtil.toBean(dto, TPssSlabHeatStdInfo.class);
        this.baseMapper.updateById(tPssSlabHeatStdInfo);

        return BeanUtil.copyProperties(tPssSlabHeatStdInfo, TPssSlabHeatStdInfoVo.class);
    }
}
