package com.aitos.pss.enums;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import java.util.Objects;

/**
 * <AUTHOR>
 *
 * 表面等级
 *
 * @version 1.0.0
 * @date 2025/6/10 14:42
 */
@Getter
@Log4j2
public enum PssQFaceEnum {

    /** 未表判 */
    PENDING("1", "未表判"),

    /** 合格品 */
    QUALIFIED("2", "合格品"),

    /** 二级品 */
    SECONDARY("3", "二级品"),

    /** 废品 */
    REJECTED("4", "废品"),

    /** 暂留 */
    TEMPORARY_HOLD("5", "暂留"),

    /** 再处理 */
    REPROCESS("6", "再处理"),

    /** 无主合格品 */
    UNCLAIMED_QUALIFIED("7", "无主合格品"),
    ;


    private final String code;
    private final String description;

    PssQFaceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举（找不到时返回null并记录warn日志）
     */
    public static PssQFaceEnum getByCode(String code) {
        for (PssQFaceEnum result : values()) {
            if (Objects.equals(result.code, code)) {
                return result;
            }
        }

        log.warn("无效枚举值: {}", code);
        return null;
    }

    /**
     * 根据description获取枚举（找不到时返回null并记录warn日志）
     */
    public static PssQFaceEnum getByDescription(String description) {

        for (PssQFaceEnum result : values()) {
            if (result.description.equals(description)) {
                return result;
            }
        }

        log.warn("不存在枚举值: {}", description);
        return null;
    }

}
