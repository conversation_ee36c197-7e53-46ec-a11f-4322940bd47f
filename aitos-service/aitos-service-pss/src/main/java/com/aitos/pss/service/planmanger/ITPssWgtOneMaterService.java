package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssWgtOneMaterDto;
import com.aitos.pss.dto.planmanger.TPssWgtOneMaterPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssWgtOneMaterDto;
import com.aitos.pss.entity.planmanger.TPssWgtOneMater;
import com.aitos.pss.vo.planmanger.TPssWgtOneMaterPageVo;
import com.aitos.pss.vo.planmanger.TPssWgtOneMaterVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/

public interface ITPssWgtOneMaterService extends IService<TPssWgtOneMater> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssWgtOneMaterPageVo> queryPage(@Valid TPssWgtOneMaterPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssWgtOneMaterVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssWgtOneMaterVo add(@Valid AddTPssWgtOneMaterDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssWgtOneMaterVo update(@Valid UpdateTPssWgtOneMaterDto dto);
}
