
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssTechkRoutLgInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechkRoutLgInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechkRoutLgInfoDto;
import com.aitos.pss.entity.qualitymanger.TPssTechkRoutLgInfo;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechkRoutLgInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/

public interface ITechkRoutLgInfoService extends IService<TPssTechkRoutLgInfo> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssTechkRoutLgInfoPageVo> queryPage(@Valid TPssTechkRoutLgInfoPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssTechkRoutLgInfoVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssTechkRoutLgInfoVo add(@Valid AddTPssTechkRoutLgInfoDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssTechkRoutLgInfoVo update(@Valid UpdateTPssTechkRoutLgInfoDto dto);
}
