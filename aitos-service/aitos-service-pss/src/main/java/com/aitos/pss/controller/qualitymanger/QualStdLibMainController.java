package com.aitos.pss.controller.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssQualStdLibMainDto;
import com.aitos.pss.dto.qualitymanger.TPssQualStdLibMainPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssQualStdLibMainDto;
import com.aitos.pss.entity.qualitymanger.TPssQualStdLibMain;
import com.aitos.pss.service.qualitymanger.IQualStdLibMainService;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibMainPageVo;
import com.aitos.pss.vo.qualitymanger.TPssQualStdLibMainVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 质检标准库管理
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/qualstdlibmain")
@Tag(name = "/pss"  + "/qualstdlibmain",description = "质检标准库管理代码")
@AllArgsConstructor
public class QualStdLibMainController {


    private final IQualStdLibMainService qualStdLibMainService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssQualStdLibMain列表(分页)")
    public RT<PageOutput<TPssQualStdLibMainPageVo>> page(@Valid TPssQualStdLibMainPageDto dto){

        return RT.ok(qualStdLibMainService.queryPage(dto));
    }

    @GetMapping(value = "/query-list")
    @Operation(summary = "TPssQualStdLibMain列表")
    public RT<List<TPssQualStdLibMainPageVo>> queryList(@Valid TPssQualStdLibMainPageDto dto){

        return RT.ok(qualStdLibMainService.queryList(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssQualStdLibMain信息")
    public RT<TPssQualStdLibMainVo> info(@RequestParam Long id){

        return RT.ok(qualStdLibMainService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssQualStdLibMain")
    @AitLog(value = "质检标准库管理新增数据")
    public RT<TPssQualStdLibMainVo> add(@Valid @RequestBody AddTPssQualStdLibMainDto dto){

        return RT.ok(qualStdLibMainService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssQualStdLibMain")
    @AitLog(value = "质检标准库管理修改数据")
    public RT<TPssQualStdLibMainVo> update(@Valid @RequestBody UpdateTPssQualStdLibMainDto dto){

        return RT.ok(qualStdLibMainService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "质检标准库管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(qualStdLibMainService.removeBatchByIds(ids));
    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "质检标准库管理导入数据")
    public RT<Boolean> importData(@RequestParam MultipartFile file) throws IOException {

        qualStdLibMainService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "质检标准库管理导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssQualStdLibMainPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return qualStdLibMainService.exportData(dto,isTemplate);
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssQualStdLibMainDto dto){
        TPssQualStdLibMain tPssQualStdLibMain = BeanUtil.toBean(dto, TPssQualStdLibMain.class);

        return RT.ok(qualStdLibMainService.updateById(tPssQualStdLibMain));
    }

    @PostMapping("/distribute")
    @Operation(summary = "下发数据")
    @AitLog(value = "下发数据")
    public RT<Boolean> distribute(@Valid @RequestBody List<UpdateTPssQualStdLibMainDto> dtoList){

        return RT.ok(qualStdLibMainService.distribute(dtoList));
    }
}