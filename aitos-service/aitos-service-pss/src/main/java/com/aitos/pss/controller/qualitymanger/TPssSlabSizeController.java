package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabSizeDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabSizePageDto;
import com.aitos.pss.service.qualitymanger.ITPssSlabSizeService;
import com.aitos.pss.vo.qualitymanger.TPssSlabSizePageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabSizeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/pss" + "/labsize")
@Tag(name = "/pss"  + "/labsize",description = "炼钢表尺判定管理代码")
@AllArgsConstructor
public class TPssSlabSizeController {

    private final ITPssSlabSizeService slabSizeService;

    @GetMapping(value = "/page")
    @Operation(summary = "(分页)")
    public RT<PageOutput<TPssSlabSizePageVo>> page(@Valid TPssSlabSizePageDto dto){

        return RT.ok(slabSizeService.queryPage(dto));
    }

    @PostMapping
    @Operation(summary =  "新增")
    @AitLog(value = "新增")
    public RT<TPssSlabSizeVo> add(@Valid @RequestBody AddTPssSlabSizeDto dto){

        return RT.ok(slabSizeService.add(dto));
    }

    @Operation(summary =  "尺寸判定")
    @AitLog(value = "尺寸判定")
    @PostMapping("/size-decision")
    public RT<Boolean> sizeDecision(@Valid @RequestBody List<AddTPssSlabSizeDto> dtoList){

        return RT.ok(slabSizeService.sizeDecision(dtoList));
    }

}