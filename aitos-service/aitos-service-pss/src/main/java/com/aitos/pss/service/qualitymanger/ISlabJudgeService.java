
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabJudgeDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabJudgePageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabJudgeDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabJudge;
import com.aitos.pss.vo.qualitymanger.TPssSlabJudgePageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabJudgeVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/

public interface ISlabJudgeService extends IService<TPssSlabJudge> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssSlabJudgePageVo> queryPage(@Valid TPssSlabJudgePageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssSlabJudgeVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssSlabJudgeVo add(@Valid AddTPssSlabJudgeDto dto);

    /**
     * 编辑数据
     * @param dto
     * @return
     */
    TPssSlabJudgeVo update(@Valid UpdateTPssSlabJudgeDto dto);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssSlabJudgePageDto dto, Boolean isTemplate);
}
