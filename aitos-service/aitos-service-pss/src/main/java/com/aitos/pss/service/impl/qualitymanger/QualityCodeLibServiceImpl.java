package com.aitos.pss.service.impl.qualitymanger;

import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.entity.qualitymanger.TPssQualityCodeLib;
import com.aitos.pss.mapper.qualitymanger.TPssQualityCodeLibMapper;
import com.aitos.pss.service.qualitymanger.ITPssQualityCodeLibService;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class QualityCodeLibServiceImpl extends ServiceImpl<TPssQualityCodeLibMapper, TPssQualityCodeLib> implements ITPssQualityCodeLibService {

}