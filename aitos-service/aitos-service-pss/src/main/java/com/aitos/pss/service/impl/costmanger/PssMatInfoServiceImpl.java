package com.aitos.pss.service.impl.costmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.costmanger.AddTPssMatInfoDto;
import com.aitos.pss.dto.costmanger.TPssMatInfoPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssMatInfoDto;
import com.aitos.pss.entity.costmanger.TPssMatInfo;
import com.aitos.pss.mapper.costmanger.TPssMatInfoMapper;
import com.aitos.pss.service.costmanger.IPssMatInfoService;
import com.aitos.pss.vo.costmanger.TPssMatInfoPageVo;
import com.aitos.pss.vo.costmanger.TPssMatInfoVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-01
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class PssMatInfoServiceImpl extends ServiceImpl<TPssMatInfoMapper, TPssMatInfo> implements IPssMatInfoService {

    @Override
    public PageOutput<TPssMatInfoPageVo> queryPage(TPssMatInfoPageDto dto) {
        LambdaQueryWrapper<TPssMatInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getCBofId()), TPssMatInfo::getCBofId, dto.getCBofId())
                .eq(ObjectUtil.isNotNull(dto.getCProcId()), TPssMatInfo::getCProcId, dto.getCProcId())
                .eq(ObjectUtil.isNotNull(dto.getCProLine()), TPssMatInfo::getCProLine, dto.getCProLine())
                .orderByDesc(TPssMatInfo::getNCreateUserId)
                .select(TPssMatInfo.class,x -> VoToColumnUtil.fieldsToColumns(TPssMatInfoPageVo.class).contains(x.getProperty()));
        IPage<TPssMatInfo> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssMatInfoPageVo.class);
    }

    @Override
    public TPssMatInfoVo queryInfo(Long id) {
        TPssMatInfo tPssMatInfo = this.baseMapper.selectById(id);
        if (tPssMatInfo == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssMatInfo, TPssMatInfoVo.class);
    }

    @Override
    public TPssMatInfoVo add(AddTPssMatInfoDto dto) {
        TPssMatInfo tPssMatInfo = BeanUtil.toBean(dto, TPssMatInfo.class);
        this.baseMapper.insert(tPssMatInfo);

        return BeanUtil.copyProperties(tPssMatInfo, TPssMatInfoVo.class);
    }

    @Override
    public TPssMatInfoVo update(UpdateTPssMatInfoDto dto) {
        TPssMatInfo tPssMatInfo = BeanUtil.toBean(dto, TPssMatInfo.class);
        this.baseMapper.updateById(tPssMatInfo);

        return BeanUtil.copyProperties(tPssMatInfo, TPssMatInfoVo.class);
    }
}
