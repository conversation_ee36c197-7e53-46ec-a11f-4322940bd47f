package com.aitos.pss.controller.prodmonit;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.prodmonit.AddTPssEquipmentsDto;
import com.aitos.pss.dto.prodmonit.TPssEquipmentsPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssEquipmentsDto;
import com.aitos.pss.service.prodmonit.IDeviceManageService;
import com.aitos.pss.vo.prodmonit.TPssEquipmentsPageVo;
import com.aitos.pss.vo.prodmonit.TPssEquipmentsVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: 设备管理
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/devicemanage")
@Tag(name = "/pss"  + "/devicemanage",description = "设备管理代码")
@AllArgsConstructor
public class DeviceManageController {


    private final IDeviceManageService deviceManageService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssEquipments列表(分页)")
    @SaCheckPermission(value = "devicemanage:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssEquipmentsPageVo>> page(@Valid TPssEquipmentsPageDto dto){
        return RT.ok(deviceManageService.queryPage(dto));
    }

    @GetMapping(value = "/list")
    @Operation(summary = "TPssEquipments列表")
    @SaCheckPermission(value = "devicemanage:detail", orRole = RoleConstants.ADMIN)
    public RT<List<TPssEquipmentsVo>> queryList(@Valid TPssEquipmentsPageDto dto){
        return RT.ok(deviceManageService.queryList(dto));
    }


    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssEquipments信息")
    @SaCheckPermission(value = "devicemanage:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssEquipmentsVo> info(@RequestParam Long id){

        return RT.ok(deviceManageService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssEquipments")
    @SaCheckPermission(value = "devicemanage:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "设备管理新增数据")
    public RT<TPssEquipmentsVo> add(@Valid @RequestBody AddTPssEquipmentsDto dto){

        return RT.ok(deviceManageService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssEquipments")
    @SaCheckPermission(value = "devicemanage:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "设备管理修改数据")
    public RT<TPssEquipmentsVo> update(@Valid @RequestBody UpdateTPssEquipmentsDto dto){

        return RT.ok(deviceManageService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "devicemanage:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "设备管理删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(deviceManageService.removeBatchByIds(ids));
    }
    @PostMapping("/import")
    @Operation(summary = "导入")
    @AitLog(value = "设备管理导入数据")
    public RT<Void> importData(@RequestParam MultipartFile file) throws IOException {

        deviceManageService.importData(file);
        return RT.ok();
    }

    @GetMapping("/export")
    @Operation(summary = "导出")
    @AitLog(value = "设备管理导出数据")
    public ResponseEntity<byte[]> exportData(@Valid TPssEquipmentsPageDto dto, @RequestParam(defaultValue = "false") Boolean isTemplate) {

        return deviceManageService.exportData(dto,isTemplate);
    }
}
