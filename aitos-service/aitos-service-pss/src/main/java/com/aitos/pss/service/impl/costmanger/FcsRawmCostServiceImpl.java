package com.aitos.pss.service.impl.costmanger;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.costmanger.AddTPssFcsRawmCostDto;
import com.aitos.pss.dto.costmanger.TPssFcsRawmCostPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsRawmCostDto;
import com.aitos.pss.entity.costmanger.TPssFcsRawmCost;
import com.aitos.pss.mapper.costmanger.TPssFcsRawmCostMapper;
import com.aitos.pss.service.costmanger.IFcsRawmCostService;
import com.aitos.pss.vo.costmanger.TPssFcsRawmCostPageVo;
import com.aitos.pss.vo.costmanger.TPssFcsRawmCostVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class FcsRawmCostServiceImpl extends ServiceImpl<TPssFcsRawmCostMapper, TPssFcsRawmCost> implements IFcsRawmCostService {

    @Override
    public PageOutput<TPssFcsRawmCostPageVo> queryPage(TPssFcsRawmCostPageDto dto) {
        LambdaQueryWrapper<TPssFcsRawmCost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssFcsRawmCost::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(Objects.nonNull(dto.getCProcId()),TPssFcsRawmCost::getCProcId,dto.getCProcId())
                .like(StrUtil.isNotBlank(dto.getCCostItemName()),TPssFcsRawmCost::getCCostItemName,dto.getCCostItemName())
                .like(StrUtil.isNotBlank(dto.getCMatCode()),TPssFcsRawmCost::getCMatCode,dto.getCMatCode())
                .eq(ObjectUtil.isNotNull(dto.getCPlanPrice()),TPssFcsRawmCost::getCPlanPrice,dto.getCPlanPrice())
                .eq(ObjectUtil.isNotNull(dto.getCMatQty()),TPssFcsRawmCost::getCMatQty,dto.getCMatQty())
                .like(StrUtil.isNotBlank(dto.getCCrew()),TPssFcsRawmCost::getCCrew,dto.getCCrew())
                .between(ObjectUtil.isNotNull(dto.getDtUseDateEndStart()) && ObjectUtil.isNotNull(dto.getDtUseDateEndEnd()),TPssFcsRawmCost::getDtUseDateEnd,dto.getDtUseDateEndStart(),dto.getDtUseDateEndEnd())
                .like(StrUtil.isNotBlank(dto.getCType()),TPssFcsRawmCost::getCType,dto.getCType())
                .eq(ObjectUtil.isNotNull(dto.getCRatio()),TPssFcsRawmCost::getCRatio,dto.getCRatio())
                .between(ObjectUtil.isNotNull(dto.getCCostDateStart()) && ObjectUtil.isNotNull(dto.getCCostDateEnd()),TPssFcsRawmCost::getCCostDate,dto.getCCostDateStart(),dto.getCCostDateEnd())
                .like(Objects.nonNull(dto.getCProEmp()),TPssFcsRawmCost::getCProEmp,dto.getCProEmp())
                .like(StrUtil.isNotBlank(dto.getCMonthFlag()),TPssFcsRawmCost::getCMonthFlag,dto.getCMonthFlag())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssFcsRawmCost::getNEnabledMark,dto.getNEnabledMark())
                .like(StrUtil.isNotBlank(dto.getCUnitCode()),TPssFcsRawmCost::getCUnitCode,dto.getCUnitCode())
                .like(StrUtil.isNotBlank(dto.getCHeatId()),TPssFcsRawmCost::getCHeatId,dto.getCHeatId())
                .eq(Objects.nonNull(dto.getCProLine()),TPssFcsRawmCost::getCProLine,dto.getCProLine())
                .like(StrUtil.isNotBlank(dto.getCCostItemCode()),TPssFcsRawmCost::getCCostItemCode,dto.getCCostItemCode())
                .eq(ObjectUtil.isNotNull(dto.getCMatId()),TPssFcsRawmCost::getCMatId,dto.getCMatId())
                .like(StrUtil.isNotBlank(dto.getCMatName()),TPssFcsRawmCost::getCMatName,dto.getCMatName())
                .eq(ObjectUtil.isNotNull(dto.getCActPrice()),TPssFcsRawmCost::getCActPrice,dto.getCActPrice())
                .between(ObjectUtil.isNotNull(dto.getDtUseDateStart()) && ObjectUtil.isNotNull(dto.getDtUseDateEnd()),TPssFcsRawmCost::getDtUseDate,dto.getDtUseDateStart(),dto.getDtUseDateEnd())
                .like(StrUtil.isNotBlank(dto.getCShift()),TPssFcsRawmCost::getCShift,dto.getCShift())
                .eq(ObjectUtil.isNotNull(dto.getNCostSeq()),TPssFcsRawmCost::getNCostSeq,dto.getNCostSeq())
                .eq(ObjectUtil.isNotNull(dto.getCQuota()),TPssFcsRawmCost::getCQuota,dto.getCQuota())
                .eq(Objects.nonNull(dto.getCSeat()),TPssFcsRawmCost::getCSeat,dto.getCSeat())
                .like(Objects.nonNull(dto.getCState()),TPssFcsRawmCost::getCState,dto.getCState())
                .between(ObjectUtil.isNotNull(dto.getDtProTimeStart()) && ObjectUtil.isNotNull(dto.getDtProTimeEnd()),TPssFcsRawmCost::getDtProTime,dto.getDtProTimeStart(),dto.getDtProTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getCUnitId()),TPssFcsRawmCost::getCUnitId,dto.getCUnitId())
                .like(StrUtil.isNotBlank(dto.getCUnitName()),TPssFcsRawmCost::getCUnitName,dto.getCUnitName())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssFcsRawmCost::getNModifyUserId,dto.getNModifyUserId())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssFcsRawmCost::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssFcsRawmCost::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssFcsRawmCost::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssFcsRawmCost::getDtCreateDateTime)
                .select(TPssFcsRawmCost.class,x -> VoToColumnUtil.fieldsToColumns(TPssFcsRawmCostPageVo.class).contains(x.getProperty()));
        IPage<TPssFcsRawmCost> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssFcsRawmCostPageVo.class);
    }

    @Override
    public TPssFcsRawmCostVo queryInfo(Long id) {
        TPssFcsRawmCost tPssFcsRawmCost = this.baseMapper.selectById(id);
        if (tPssFcsRawmCost == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssFcsRawmCost, TPssFcsRawmCostVo.class);
    }

    @Override
    public TPssFcsRawmCostVo add(AddTPssFcsRawmCostDto dto) {
        TPssFcsRawmCost tPssFcsRawmCost = BeanUtil.toBean(dto, TPssFcsRawmCost.class);
        this.baseMapper.insert(tPssFcsRawmCost);

        return BeanUtil.copyProperties(tPssFcsRawmCost, TPssFcsRawmCostVo.class);
    }

    @Override
    public TPssFcsRawmCostVo update(UpdateTPssFcsRawmCostDto dto) {
        TPssFcsRawmCost tPssFcsRawmCost = BeanUtil.toBean(dto, TPssFcsRawmCost.class);
        this.baseMapper.updateById(tPssFcsRawmCost);

        return BeanUtil.copyProperties(tPssFcsRawmCost, TPssFcsRawmCostVo.class);
    }

    @Override
    public Boolean approve(List<UpdateTPssFcsRawmCostDto> dtoList) {
        Set<Integer> cStateSet =
                dtoList
                        .stream()
                        .filter(item -> Objects.equals(item.getCState(), 1))
                        .map(UpdateTPssFcsRawmCostDto::getCState)
                        .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(cStateSet)) throw new MyException("已审核数据不能重复审核");

        Long userId = StpUtil.getLoginIdAsLong();
        LocalDateTime now = LocalDateTime.now();
        for (UpdateTPssFcsRawmCostDto updateTPssFcsRawmCostDto : dtoList) {
            updateTPssFcsRawmCostDto.setCState(1);
            updateTPssFcsRawmCostDto.setCProEmp(userId);
            updateTPssFcsRawmCostDto.setDtProTime(now);
        }
        List<TPssFcsRawmCost> tPssFcsRawmCostList = BeanUtil.copyToList(dtoList, TPssFcsRawmCost.class);
        this.baseMapper.updateById(tPssFcsRawmCostList);

        return Boolean.TRUE;
    }

    @Override
    public Boolean approveCancel(List<UpdateTPssFcsRawmCostDto> dtoList) {
        Set<Integer> cStateSet =
                dtoList
                        .stream()
                        .filter(item -> Objects.equals(item.getCState(), 0))
                        .map(UpdateTPssFcsRawmCostDto::getCState)
                        .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(cStateSet)) throw new MyException("未审核数据不能取消审核");

        for (UpdateTPssFcsRawmCostDto updateTPssFcsRawmCostDto : dtoList) {
            updateTPssFcsRawmCostDto.setCState(1);
        }
        List<TPssFcsRawmCost> tPssFcsRawmCostList = BeanUtil.copyToList(dtoList, TPssFcsRawmCost.class);
        this.baseMapper.updateById(tPssFcsRawmCostList);

        return Boolean.TRUE;
    }
}
