package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssRollStdInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssRollStdInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssRollStdInfoDto;
import com.aitos.pss.service.qualitymanger.IRollStdInfoService;
import com.aitos.pss.vo.qualitymanger.TPssRollStdInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssRollStdInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 轧制工艺质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/rollstdinfo")
@Tag(name = "/pss"  + "/rollstdinfo",description = "轧制工艺质量设计结果代码")
@AllArgsConstructor
public class RollStdInfoController {


    private final IRollStdInfoService rollStdInfoService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssRollStdInfo列表(分页)")
    public RT<PageOutput<TPssRollStdInfoPageVo>> page(@Valid TPssRollStdInfoPageDto dto){

        return RT.ok(rollStdInfoService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssRollStdInfo信息")
    public RT<TPssRollStdInfoVo> info(@RequestParam Long id){

        return RT.ok(rollStdInfoService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssRollStdInfo")
    @AitLog(value = "轧制工艺质量设计结果新增数据")
    public RT<TPssRollStdInfoVo> add(@Valid @RequestBody AddTPssRollStdInfoDto dto){

        return RT.ok(rollStdInfoService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssRollStdInfo")
    @AitLog(value = "轧制工艺质量设计结果修改数据")
    public RT<TPssRollStdInfoVo> update(@Valid @RequestBody UpdateTPssRollStdInfoDto dto){

        return RT.ok(rollStdInfoService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "轧制工艺质量设计结果删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(rollStdInfoService.removeBatchByIds(ids));

    }

}