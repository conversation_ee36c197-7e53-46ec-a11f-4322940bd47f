
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.qualitymanger.AddTPssCheSteelDto;
import com.aitos.pss.dto.qualitymanger.TPssCheSteelPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssCheSteelDto;
import com.aitos.pss.entity.qualitymanger.TPssCheSteel;
import com.aitos.pss.vo.qualitymanger.TPssCheSteelPageVo;
import com.aitos.pss.vo.qualitymanger.TPssCheSteelVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/

public interface ICheSteelService extends IService<TPssCheSteel> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssCheSteelPageVo> queryPage(@Valid TPssCheSteelPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssCheSteelVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssCheSteelVo add(@Valid AddTPssCheSteelDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssCheSteelVo update(@Valid UpdateTPssCheSteelDto dto);

    /**
     * import
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssCheSteelPageDto dto, Boolean isTemplate);
}
