package com.aitos.pss.controller.qualitymanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssTechEafInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechEafInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechEafInfoDto;
import com.aitos.pss.service.qualitymanger.ITechEafInfoService;
import com.aitos.pss.vo.qualitymanger.TPssTechEafInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechEafInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 电炉工艺参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/techeafinfo")
@Tag(name = "/pss"  + "/techeafinfo",description = "电炉工艺参数质量设计结果代码")
@AllArgsConstructor
public class TechEafInfoController {


    private final ITechEafInfoService techEafInfoService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssTechEafInfo列表(分页)")
    public RT<PageOutput<TPssTechEafInfoPageVo>> page(@Valid TPssTechEafInfoPageDto dto){

        return RT.ok(techEafInfoService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssTechEafInfo信息")
    public RT<TPssTechEafInfoVo> info(@RequestParam Long id){

        return RT.ok(techEafInfoService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssTechEafInfo")
    @AitLog(value = "电炉工艺参数质量设计结果新增数据")
    public RT<TPssTechEafInfoVo> add(@Valid @RequestBody AddTPssTechEafInfoDto dto){

        return RT.ok(techEafInfoService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssTechEafInfo")
    @AitLog(value = "电炉工艺参数质量设计结果修改数据")
    public RT<TPssTechEafInfoVo> update(@Valid @RequestBody UpdateTPssTechEafInfoDto dto){

        return RT.ok(techEafInfoService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "techeafinfo:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "电炉工艺参数质量设计结果删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(techEafInfoService.removeBatchByIds(ids));
    }

}