package com.aitos.pss.controller.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssStdChemNkProDto;
import com.aitos.pss.dto.qualitymanger.TPssStdChemNkProPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdChemNkProDto;
import com.aitos.pss.entity.qualitymanger.TPssStdChemNkPro;
import com.aitos.pss.service.qualitymanger.IPssStdChemNkProService;
import com.aitos.pss.vo.qualitymanger.TPssStdChemNkProPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdChemNkProVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 成分标准管理-工序成分标准
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssstdchemnkpro")
@Tag(name = "/pss"  + "/pssstdchemnkpro",description = "成分标准管理-工序成分标准代码")
@AllArgsConstructor
public class PssStdChemNkProController {


    private final IPssStdChemNkProService pssStdChemNkProService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssStdChemNkPro列表(分页)")
    public RT<PageOutput<TPssStdChemNkProPageVo>> page(@Valid TPssStdChemNkProPageDto dto){

        return RT.ok(pssStdChemNkProService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssStdChemNkPro信息")
    public RT<TPssStdChemNkProVo> info(@RequestParam Long id){

        return RT.ok(pssStdChemNkProService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssStdChemNkPro")
    @AitLog(value = "成分标准管理-工序成分标准新增数据")
    public RT<TPssStdChemNkProVo> add(@Valid @RequestBody AddTPssStdChemNkProDto dto){

        return RT.ok(pssStdChemNkProService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssStdChemNkPro")
    @AitLog(value = "成分标准管理-工序成分标准修改数据")
    public RT<TPssStdChemNkProVo> update(@Valid @RequestBody UpdateTPssStdChemNkProDto dto){

        return RT.ok(pssStdChemNkProService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "成分标准管理-工序成分标准删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssStdChemNkProService.removeBatchByIds(ids));
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssStdChemNkProDto dto){
        TPssStdChemNkPro tPssStdChemNkPro = BeanUtil.toBean(dto, TPssStdChemNkPro.class);

        return RT.ok(pssStdChemNkProService.updateById(tPssStdChemNkPro));
    }
}