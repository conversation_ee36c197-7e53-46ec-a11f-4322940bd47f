package com.aitos.pss.controller.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssWgtOneMaterDto;
import com.aitos.pss.dto.planmanger.TPssWgtOneMaterPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssWgtOneMaterDto;
import com.aitos.pss.entity.planmanger.TPssWgtOneMater;
import com.aitos.pss.service.planmanger.ITPssWgtOneMaterService;
import com.aitos.pss.vo.planmanger.TPssWgtOneMaterPageVo;
import com.aitos.pss.vo.planmanger.TPssWgtOneMaterVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 熔量重量标准-钢种单米重
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/psswgtonemater")
@Tag(name = "/pss"  + "/psswgtonemater",description = "熔量重量标准-钢种单米重代码")
@AllArgsConstructor
public class TPssWgtOneMaterController {


    private final ITPssWgtOneMaterService pssWgtOneMaterService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssWgtOneMater列表(分页)")
    public RT<PageOutput<TPssWgtOneMaterPageVo>> page(@Valid TPssWgtOneMaterPageDto dto){

        return RT.ok(pssWgtOneMaterService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssWgtOneMater信息")
    public RT<TPssWgtOneMaterVo> info(@RequestParam Long id){

        return RT.ok(pssWgtOneMaterService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssWgtOneMater")
    @AitLog(value = "熔量重量标准-钢种单米重新增数据")
    public RT<TPssWgtOneMaterVo> add(@Valid @RequestBody AddTPssWgtOneMaterDto dto){

        return RT.ok(pssWgtOneMaterService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssWgtOneMater")
    @AitLog(value = "熔量重量标准-钢种单米重修改数据")
    public RT<TPssWgtOneMaterVo> update(@Valid @RequestBody UpdateTPssWgtOneMaterDto dto){

        return RT.ok(pssWgtOneMaterService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "熔量重量标准-钢种单米重删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssWgtOneMaterService.removeBatchByIds(ids));
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssWgtOneMaterDto dto){
        TPssWgtOneMater tPssWgtOneMater = BeanUtil.toBean(dto, TPssWgtOneMater.class);

        return RT.ok(pssWgtOneMaterService.updateById(tPssWgtOneMater));
    }
}