
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPssSteelReturnRecordDto;
import com.aitos.pss.dto.inventory.TPssSteelReturnRecordPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssSteelReturnRecordDto;
import com.aitos.pss.entity.inventory.TPssSteelReturnRecord;
import com.aitos.pss.vo.inventory.TPssSteelReturnRecordPageVo;
import com.aitos.pss.vo.inventory.TPssSteelReturnRecordVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/

public interface ISteelReturnRecordService extends IService<TPssSteelReturnRecord> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssSteelReturnRecordPageVo> queryPage(@Valid TPssSteelReturnRecordPageDto dto);

    /**
     *
     * @param id
     * @return
     */
    TPssSteelReturnRecordVo queryInfo(Long id);

    /**
     *
     * @param dto
     * @return
     */
    TPssSteelReturnRecordVo add(@Valid AddTPssSteelReturnRecordDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssSteelReturnRecordVo update(@Valid UpdateTPssSteelReturnRecordDto dto);
}
