package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.AddTPssMatStockLimitDto;
import com.aitos.pss.dto.inventory.TPssMatStockLimitPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssMatStockLimitDto;
import com.aitos.pss.entity.inventory.TPssMatStockLimit;
import com.aitos.pss.mapper.inventory.TPssMatStockLimitMapper;
import com.aitos.pss.service.inventory.IPssMatStockLimitService;
import com.aitos.pss.vo.inventory.TPssMatStockLimitPageVo;
import com.aitos.pss.vo.inventory.TPssMatStockLimitVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class PssMatStockLimitServiceImpl extends ServiceImpl<TPssMatStockLimitMapper, TPssMatStockLimit> implements IPssMatStockLimitService {

    @Override
    public TPssMatStockLimitVo queryInfo(Long id) {
        TPssMatStockLimit tPssMatStockLimit = this.baseMapper.selectById(id);
        if (tPssMatStockLimit == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.toBean(tPssMatStockLimit, TPssMatStockLimitVo.class);
    }

    @Override
    public Long add(AddTPssMatStockLimitDto dto) {
        if (dto.getNMinStock().compareTo(dto.getNMaxStock()) >= 0){
             throw new MyException("下限大于等于上限");
        }
        if (dto.getNMaxStock().compareTo(BigDecimal.ZERO) < 0 || dto.getNMinStock().compareTo(BigDecimal.ZERO) < 0){
            throw new MyException("下限和上限中存在负数");
        }
        TPssMatStockLimit tPssMatStockLimit = BeanUtil.toBean(dto, TPssMatStockLimit.class);
        this.baseMapper.insert(tPssMatStockLimit);
        return tPssMatStockLimit.getNId();
    }

    @Override
    public Boolean update(UpdateTPssMatStockLimitDto dto) {
        if (dto.getNMinStock().compareTo(dto.getNMaxStock()) >= 0){
            throw new MyException("下限大于等于上限");
        }
        if (dto.getNMaxStock().compareTo(BigDecimal.ZERO) <0 || dto.getNMinStock().compareTo(BigDecimal.ZERO) < 0){
            throw new MyException("下限和上限中存在负数");
        }
        TPssMatStockLimit tPssMatStockLimit = BeanUtil.toBean(dto, TPssMatStockLimit.class);
        this.baseMapper.updateById(tPssMatStockLimit);
        return Boolean.TRUE;
    }

    @Override
    public PageOutput<TPssMatStockLimitPageVo> queryPage(TPssMatStockLimitPageDto dto) {
        LambdaQueryWrapper<TPssMatStockLimit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCMaterialName()),TPssMatStockLimit::getCMaterialName,dto.getCMaterialName())
                .like(StrUtil.isNotBlank(dto.getCMaterialType()),TPssMatStockLimit::getCMaterialType,dto.getCMaterialType())
                .like(dto.getCMaterialTypeId() != null,TPssMatStockLimit::getCMaterialTypeId,dto.getCMaterialTypeId())
                .eq(ObjectUtil.isNotNull(dto.getNMinStock()),TPssMatStockLimit::getNMinStock,dto.getNMinStock())
                .like(StrUtil.isNotBlank(dto.getCUnit()),TPssMatStockLimit::getCUnit,dto.getCUnit())
                .like(StrUtil.isNotBlank(dto.getCMaterialCode()),TPssMatStockLimit::getCMaterialCode,dto.getCMaterialCode())
                .eq(ObjectUtil.isNotNull(dto.getNCurrentStock()),TPssMatStockLimit::getNCurrentStock,dto.getNCurrentStock())
                .eq(ObjectUtil.isNotNull(dto.getNMaxStock()),TPssMatStockLimit::getNMaxStock,dto.getNMaxStock())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssMatStockLimit::getNEnabledMark,dto.getNEnabledMark())
                .orderByDesc(TPssMatStockLimit::getDtCreateDateTime)
                .select(TPssMatStockLimit.class,x -> VoToColumnUtil.fieldsToColumns(TPssMatStockLimitPageVo.class).contains(x.getProperty()));
        IPage<TPssMatStockLimit> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        return ConventPage.getPageOutput(page, TPssMatStockLimitPageVo.class);
    }
}
