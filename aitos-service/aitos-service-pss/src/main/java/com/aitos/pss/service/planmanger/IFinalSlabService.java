
package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssFinalSlabDto;
import com.aitos.pss.dto.planmanger.TPssFinalSlabPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssFinalSlabDto;
import com.aitos.pss.entity.planmanger.TPssFinalSlab;
import com.aitos.pss.vo.planmanger.TPssFinalSlabPageVo;
import com.aitos.pss.vo.planmanger.TPssFinalSlabVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-22
* @Version 1.0
*/

public interface IFinalSlabService extends IService<TPssFinalSlab> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssFinalSlabPageVo> queryPage(@Valid TPssFinalSlabPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssFinalSlabVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssFinalSlabVo add(@Valid AddTPssFinalSlabDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssFinalSlabVo update(@Valid UpdateTPssFinalSlabDto dto);
}
