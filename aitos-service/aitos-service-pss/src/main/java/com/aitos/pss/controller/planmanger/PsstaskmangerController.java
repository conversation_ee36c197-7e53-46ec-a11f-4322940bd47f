package com.aitos.pss.controller.planmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssAggregatePlanDto;
import com.aitos.pss.dto.planmanger.TPssAggregatePlanPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssAggregatePlanDto;
import com.aitos.pss.service.planmanger.IAggregatePlanService;
import com.aitos.pss.vo.planmanger.TPssAggregatePlanPageVo;
import com.aitos.pss.vo.planmanger.TPssAggregatePlanVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 生产任务单管理
* <AUTHOR>
* @Date: 2025-05-27
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/psstaskmanger")
@Tag(name = "/pss"  + "/psstaskmanger",description = "生产任务单管理代码")
@AllArgsConstructor
public class PsstaskmangerController {


    private final IAggregatePlanService psstaskmangerService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssAggregatePlan列表(分页)")
    public RT<PageOutput<TPssAggregatePlanPageVo>> queryPage(@Valid TPssAggregatePlanPageDto dto){

        return RT.ok(psstaskmangerService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssAggregatePlan信息")
    public RT<TPssAggregatePlanVo> queryInfo(@RequestParam Long id){

        return RT.ok(psstaskmangerService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssAggregatePlan")
    @AitLog(value = "生产任务单管理新增数据")
    public RT<TPssAggregatePlanVo> add(@Valid @RequestBody AddTPssAggregatePlanDto dto){

        return RT.ok(psstaskmangerService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssAggregatePlan")
    @AitLog(value = "生产任务单管理修改数据")
    public RT<TPssAggregatePlanVo> update(@Valid @RequestBody UpdateTPssAggregatePlanDto dto){

        return RT.ok(psstaskmangerService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "生产任务单管理删除数据")
    public RT<Boolean> deleteBathId(@Valid @RequestBody List<Long> ids){

        return RT.ok(psstaskmangerService.deleteBathId(ids));
    }

    @PostMapping("/pline-allocation")
    @Operation(summary = "产线分配")
    @AitLog(value = "产线分配")
    public RT<Boolean> plineAllocation(@Valid @RequestBody UpdateTPssAggregatePlanDto dto){

        return RT.ok(psstaskmangerService.plineAllocation(dto));
    }

}