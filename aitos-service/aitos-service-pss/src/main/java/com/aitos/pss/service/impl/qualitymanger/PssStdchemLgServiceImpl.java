package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssStdchemLgDto;
import com.aitos.pss.dto.qualitymanger.TPssStdchemLgPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdchemLgDto;
import com.aitos.pss.entity.qualitymanger.TPssStdchemLg;
import com.aitos.pss.mapper.qualitymanger.TPssStdchemLgMapper;
import com.aitos.pss.service.qualitymanger.IPssStdchemLgService;
import com.aitos.pss.vo.qualitymanger.TPssStdchemLgPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdchemLgVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class PssStdchemLgServiceImpl extends ServiceImpl<TPssStdchemLgMapper, TPssStdchemLg> implements IPssStdchemLgService {

    @Override
    public PageOutput<TPssStdchemLgPageVo> queryPage(TPssStdchemLgPageDto dto) {
        LambdaQueryWrapper<TPssStdchemLg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCQualityCode()),TPssStdchemLg::getCQualityCode,dto.getCQualityCode())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssStdchemLg::getCStlGrdCd,dto.getCStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCChemCompCd()),TPssStdchemLg::getCChemCompCd,dto.getCChemCompCd())
                .eq(ObjectUtil.isNotNull(dto.getCChemCompMin()),TPssStdchemLg::getCChemCompMin,dto.getCChemCompMin())
                .eq(ObjectUtil.isNotNull(dto.getCChemCompMax()),TPssStdchemLg::getCChemCompMax,dto.getCChemCompMax())
                .eq(Objects.nonNull(dto.getCQualityId()),TPssStdchemLg::getCQualityId,dto.getCQualityId())
                .like(StrUtil.isNotBlank(dto.getCCompCodeMin()),TPssStdchemLg::getCCompCodeMin,dto.getCCompCodeMin())
                .like(StrUtil.isNotBlank(dto.getCCompCodeMax()),TPssStdchemLg::getCCompCodeMax,dto.getCCompCodeMax())
                .like(StrUtil.isNotBlank(dto.getCStdClass()),TPssStdchemLg::getCStdClass,dto.getCStdClass())
                .orderByDesc(TPssStdchemLg::getCQualityCode)
                .select(TPssStdchemLg.class,x -> VoToColumnUtil.fieldsToColumns(TPssStdchemLgPageVo.class).contains(x.getProperty()));
        IPage<TPssStdchemLg> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssStdchemLgPageVo.class);
    }

    @Override
    public TPssStdchemLgVo queryInfo(Long id) {
        TPssStdchemLg tPssStdchemLg = this.baseMapper.selectById(id);
        if (tPssStdchemLg == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssStdchemLg, TPssStdchemLgVo.class);
    }

    @Override
    public TPssStdchemLgVo add(AddTPssStdchemLgDto dto) {
        TPssStdchemLg tPssStdchemLg = BeanUtil.toBean(dto, TPssStdchemLg.class);
        if (Objects.isNull(dto.getCChemCompMin()) || Objects.isNull(dto.getCChemCompMax())) throw new MyException("化学成分最大最小值不能为空");
        if (dto.getCChemCompMax().compareTo(dto.getCChemCompMin()) < 0) throw new MyException("化学成分最大值不能小于最小值");

        this.baseMapper.insert(tPssStdchemLg);

        return BeanUtil.copyProperties(tPssStdchemLg, TPssStdchemLgVo.class);
    }

    @Override
    public TPssStdchemLgVo update(UpdateTPssStdchemLgDto dto) {
        TPssStdchemLg tPssStdchemLg = BeanUtil.toBean(dto, TPssStdchemLg.class);
        this.baseMapper.updateById(tPssStdchemLg);

        return BeanUtil.copyProperties(tPssStdchemLg, TPssStdchemLgVo.class);
    }
}
