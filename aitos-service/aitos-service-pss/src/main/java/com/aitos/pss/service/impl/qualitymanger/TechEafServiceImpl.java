package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssTechEafDto;
import com.aitos.pss.dto.qualitymanger.TPssTechEafPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechEafDto;
import com.aitos.pss.entity.qualitymanger.TPssTechEaf;
import com.aitos.pss.mapper.qualitymanger.TPssTechEafMapper;
import com.aitos.pss.service.qualitymanger.ITechEafService;
import com.aitos.pss.vo.qualitymanger.TPssTechEafPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechEafVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TechEafServiceImpl extends ServiceImpl<TPssTechEafMapper, TPssTechEaf> implements ITechEafService {

    @Override
    public PageOutput<TPssTechEafPageVo> queryPage(TPssTechEafPageDto dto) {
        LambdaQueryWrapper<TPssTechEaf> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualityId()),TPssTechEaf::getCQualityId,dto.getCQualityId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssTechEaf::getCStlGrdCd,dto.getCStlGrdCd())
                .orderByDesc(TPssTechEaf::getNId)
                .select(TPssTechEaf.class,x -> VoToColumnUtil.fieldsToColumns(TPssTechEafPageVo.class).contains(x.getProperty()));
        IPage<TPssTechEaf> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssTechEafPageVo.class);
    }

    @Override
    public TPssTechEafVo queryInfo(Long id) {
        TPssTechEaf tPssTechEaf = this.baseMapper.selectById(id);
        if (tPssTechEaf == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssTechEaf,TPssTechEafVo.class);
    }

    @Override
    public TPssTechEafVo add(AddTPssTechEafDto dto) {
        TPssTechEaf tPssTechEaf = BeanUtil.toBean(dto, TPssTechEaf.class);
        this.baseMapper.insert(tPssTechEaf);

        return BeanUtil.copyProperties(tPssTechEaf,TPssTechEafVo.class);
    }

    @Override
    public TPssTechEafVo update(UpdateTPssTechEafDto dto) {
        TPssTechEaf tPssTechEaf = BeanUtil.toBean(dto, TPssTechEaf.class);
        this.baseMapper.updateById(tPssTechEaf);

        return BeanUtil.copyProperties(tPssTechEaf,TPssTechEafVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssTechEafPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssTechEafPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssTechEaf.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssTechEafPageDto dto, Boolean isTemplate) {
        List<TPssTechEafPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssTechEafPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "TechEaf" + ExcelTypeEnum.XLSX.getValue());
    }
}
