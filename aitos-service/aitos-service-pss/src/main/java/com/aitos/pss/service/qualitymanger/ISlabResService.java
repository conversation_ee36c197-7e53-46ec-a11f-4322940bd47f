
package com.aitos.pss.service.qualitymanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.mybatis.base.MPJDeepAndJoinBaseService;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabResDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabResPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabResDto;
import com.aitos.pss.entity.qualitymanger.TPssSlabRes;
import com.aitos.pss.vo.qualitymanger.TPssSlabResPageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabResVo;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-03
* @Version 1.0
*/

public interface ISlabResService extends MPJDeepAndJoinBaseService<TPssSlabRes> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssSlabResPageVo> queryPage(@Valid TPssSlabResPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssSlabResVo queryInfo(Long id);

    /**
    * 新增
    *
    * @param dto
    * @return
    */
    TPssSlabResVo add(AddTPssSlabResDto dto);

    /**
    * 更新
    *
    * @param tPssSlabRes
    * @return
    */
    Boolean update(TPssSlabRes tPssSlabRes);

    /**
    * 删除
    *
    * @param ids
    * @return
    */
    Boolean delete(List<Long> ids);

    /**
     * 取样确认
     * @param dto
     * @return
     */
    TPssSlabResVo sampleConfirm(@Valid UpdateTPssSlabResDto dto);

    /**
     * 改判
     * @param dto
     * @return
     */
    TPssSlabResVo editJudge(@Valid UpdateTPssSlabResDto dto);

    /**
     * 判废
     * @param dtoList
     * @return
     */
    Boolean judgeAbolish(@Valid List<UpdateTPssSlabResDto> dtoList);

    /**
     * importData
     * @param file
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssSlabResPageDto dto, Boolean isTemplate);
}
