package com.aitos.pss.controller.inventory;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.inventory.AddTPssYMatStockRecordDto;
import com.aitos.pss.dto.inventory.TPssYMatStockRecordPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssYMatStockRecordDto;
import com.aitos.pss.service.inventory.IMatStockRecordService;
import com.aitos.pss.vo.inventory.TPssYMatStockRecordPageVo;
import com.aitos.pss.vo.inventory.TPssYMatStockRecordVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 原辅料库存信息查询子表
* <AUTHOR>
* @Date: 2025-07-03
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/matstockrecord")
@Tag(name = "/pss"  + "/matstockrecord",description = "原辅料库存信息查询子表代码")
@AllArgsConstructor
public class MatStockRecordController {


    private final IMatStockRecordService matStockRecordService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssYMatStockRecord列表(分页)")
    @SaCheckPermission(value = "matstockrecord:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssYMatStockRecordPageVo>> page(@Valid TPssYMatStockRecordPageDto dto){
        return RT.ok(matStockRecordService.ueryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssYMatStockRecord信息")
    @SaCheckPermission(value = "matstockrecord:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssYMatStockRecordVo> info(@RequestParam Long id){
        return RT.ok(   matStockRecordService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssYMatStockRecord")
    @SaCheckPermission(value = "matstockrecord:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料库存信息查询子表新增数据")
    public RT<Long> add(@Valid @RequestBody AddTPssYMatStockRecordDto dto){
        return RT.ok(     matStockRecordService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssYMatStockRecord")
    @SaCheckPermission(value = "matstockrecord:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料库存信息查询子表修改数据")
    public RT<Boolean> update(@Valid @RequestBody UpdateTPssYMatStockRecordDto dto){
        return RT.ok( matStockRecordService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "matstockrecord:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "原辅料库存信息查询子表删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){
        return RT.ok(matStockRecordService.removeBatchByIds(ids));
    }

}
