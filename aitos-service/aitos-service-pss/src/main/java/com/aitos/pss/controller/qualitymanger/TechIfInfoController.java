package com.aitos.pss.controller.qualitymanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.qualitymanger.AddTPssTechLfInfoDto;
import com.aitos.pss.dto.qualitymanger.TPssTechLfInfoPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechLfInfoDto;
import com.aitos.pss.service.qualitymanger.ITechIfInfoService;
import com.aitos.pss.vo.qualitymanger.TPssTechLfInfoPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechLfInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 精炼工艺参数质量设计结果
* <AUTHOR>
* @Date: 2025-05-26
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/techifinfo")
@Tag(name = "/pss"  + "/techifinfo",description = "精炼工艺参数质量设计结果代码")
@AllArgsConstructor
public class TechIfInfoController {


    private final ITechIfInfoService techIfInfoService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssTechLfInfo列表(分页)")
    public RT<PageOutput<TPssTechLfInfoPageVo>> page(@Valid TPssTechLfInfoPageDto dto){

        return RT.ok(techIfInfoService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssTechLfInfo信息")
    public RT<TPssTechLfInfoVo> info(@RequestParam Long id){

        return RT.ok(techIfInfoService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssTechLfInfo")
    @AitLog(value = "精炼工艺参数质量设计结果新增数据")
    public RT<TPssTechLfInfoVo> add(@Valid @RequestBody AddTPssTechLfInfoDto dto){

        return RT.ok(techIfInfoService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssTechLfInfo")
    @AitLog(value = "精炼工艺参数质量设计结果修改数据")
    public RT<TPssTechLfInfoVo> update(@Valid @RequestBody UpdateTPssTechLfInfoDto dto){

        return RT.ok(techIfInfoService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "精炼工艺参数质量设计结果删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(techIfInfoService.removeBatchByIds(ids));
    }

}