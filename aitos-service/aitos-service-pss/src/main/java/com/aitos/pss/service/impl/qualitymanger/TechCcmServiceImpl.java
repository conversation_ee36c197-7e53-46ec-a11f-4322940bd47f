package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssTechCcmDto;
import com.aitos.pss.dto.qualitymanger.TPssTechCcmPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssTechCcmDto;
import com.aitos.pss.entity.qualitymanger.TPssTechCcm;
import com.aitos.pss.mapper.qualitymanger.TPssTechCcmMapper;
import com.aitos.pss.service.qualitymanger.ITechCcmService;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmPageVo;
import com.aitos.pss.vo.qualitymanger.TPssTechCcmVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-20
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class TechCcmServiceImpl extends ServiceImpl<TPssTechCcmMapper, TPssTechCcm> implements ITechCcmService {

    @Override
    public PageOutput<TPssTechCcmPageVo> queryPage(TPssTechCcmPageDto dto) {
        LambdaQueryWrapper<TPssTechCcm> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualityId()),TPssTechCcm::getCQualityId,dto.getCQualityId())
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssTechCcm::getCStlGrdCd,dto.getCStlGrdCd())
                .orderByDesc(TPssTechCcm::getNId)
                .select(TPssTechCcm.class,x -> VoToColumnUtil.fieldsToColumns(TPssTechCcmPageVo.class).contains(x.getProperty()));
        IPage<TPssTechCcm> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssTechCcmPageVo.class);
    }

    @Override
    public TPssTechCcmVo queryInfo(Long id) {
        TPssTechCcm tPssTechCcm = this.baseMapper.selectById(id);
        if (tPssTechCcm == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssTechCcm, TPssTechCcmVo.class);
    }

    @Override
    public TPssTechCcmVo add(AddTPssTechCcmDto dto) {
        TPssTechCcm tPssTechCcm = BeanUtil.toBean(dto, TPssTechCcm.class);
        this.baseMapper.insert(tPssTechCcm);

        return BeanUtil.copyProperties(tPssTechCcm, TPssTechCcmVo.class);
    }

    @Override
    public TPssTechCcmVo update(UpdateTPssTechCcmDto dto) {
        TPssTechCcm tPssTechCcm = BeanUtil.toBean(dto, TPssTechCcm.class);
        this.baseMapper.updateById(tPssTechCcm);

        return BeanUtil.copyProperties(tPssTechCcm, TPssTechCcmVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssTechCcmPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssTechCcmPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssTechCcm.class));
    }
    @Override
    public ResponseEntity<byte[]> exportData(TPssTechCcmPageDto dto, Boolean isTemplate) {
        List<TPssTechCcmPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssTechCcmPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "TechCcm" + ExcelTypeEnum.XLSX.getValue());
    }
}
