package com.aitos.pss.service.planmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.planmanger.AddTPssPosMoveTimeDto;
import com.aitos.pss.dto.planmanger.TPssPosMoveTimePageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssPosMoveTimeDto;
import com.aitos.pss.entity.planmanger.TPssPosMoveTime;
import com.aitos.pss.vo.planmanger.TPssPosMoveTimePageVo;
import com.aitos.pss.vo.planmanger.TPssPosMoveTimeVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/

public interface ITPssPosMoveTimeService extends IService<TPssPosMoveTime> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssPosMoveTimePageVo> queryPage(@Valid TPssPosMoveTimePageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssPosMoveTimeVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssPosMoveTimeVo add(@Valid AddTPssPosMoveTimeDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssPosMoveTimeVo update(@Valid UpdateTPssPosMoveTimeDto dto);
}
