package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssStdChemNkProDto;
import com.aitos.pss.dto.qualitymanger.TPssStdChemNkProPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssStdChemNkProDto;
import com.aitos.pss.entity.qualitymanger.TPssStdChemNkPro;
import com.aitos.pss.mapper.qualitymanger.TPssStdChemNkProMapper;
import com.aitos.pss.service.qualitymanger.IPssStdChemNkProService;
import com.aitos.pss.vo.qualitymanger.TPssStdChemNkProPageVo;
import com.aitos.pss.vo.qualitymanger.TPssStdChemNkProVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-19
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class PssStdChemNkProServiceImpl extends ServiceImpl<TPssStdChemNkProMapper, TPssStdChemNkPro> implements IPssStdChemNkProService {

    @Override
    public PageOutput<TPssStdChemNkProPageVo> queryPage(TPssStdChemNkProPageDto dto) {
        LambdaQueryWrapper<TPssStdChemNkPro> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssStdChemNkPro::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(Objects.nonNull(dto.getCQualityId()),TPssStdChemNkPro::getCQualityId,dto.getCQualityId())
                .like(StrUtil.isNotBlank(dto.getCCompCodeMax()),TPssStdChemNkPro::getCCompCodeMax,dto.getCCompCodeMax())
                .like(StrUtil.isNotBlank(dto.getCChemComp()),TPssStdChemNkPro::getCChemComp,dto.getCChemComp())
                .like(ObjectUtil.isNotNull(dto.getCChemCompMax()),TPssStdChemNkPro::getCChemCompMax,dto.getCChemCompMax())
                .like(StrUtil.isNotBlank(dto.getCQualityCodeAbbreviation()),TPssStdChemNkPro::getCQualityCodeAbbreviation,dto.getCQualityCodeAbbreviation())
                .like(StrUtil.isNotBlank(dto.getCOpStdName()),TPssStdChemNkPro::getCOpStdName,dto.getCOpStdName())
                .like(StrUtil.isNotBlank(dto.getCCompCodeMin()),TPssStdChemNkPro::getCCompCodeMin,dto.getCCompCodeMin())
                .like(StrUtil.isNotBlank(dto.getCChemCompCd()),TPssStdChemNkPro::getCChemCompCd,dto.getCChemCompCd())
                .like(ObjectUtil.isNotNull(dto.getCChemCompMin()),TPssStdChemNkPro::getCChemCompMin,dto.getCChemCompMin())
                .like(StrUtil.isNotBlank(dto.getCQualityCode()),TPssStdChemNkPro::getCQualityCode,dto.getCQualityCode())
                .like(StrUtil.isNotBlank(dto.getCMacCode()),TPssStdChemNkPro::getCMacCode,dto.getCMacCode())
                .like(StrUtil.isNotBlank(dto.getCMacName()),TPssStdChemNkPro::getCMacName,dto.getCMacName())
                .orderByDesc(TPssStdChemNkPro::getNId)
                .select(TPssStdChemNkPro.class,x -> VoToColumnUtil.fieldsToColumns(TPssStdChemNkProPageVo.class).contains(x.getProperty()));
        IPage<TPssStdChemNkPro> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssStdChemNkProPageVo.class);
    }

    @Override
    public TPssStdChemNkProVo queryInfo(Long id) {
        TPssStdChemNkPro tPssStdChemNkPro = this.baseMapper.selectById(id);
        if (tPssStdChemNkPro == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssStdChemNkPro, TPssStdChemNkProVo.class);
    }

    @Override
    public TPssStdChemNkProVo add(AddTPssStdChemNkProDto dto) {
        if (dto.getCChemCompMin().compareTo(dto.getCChemCompMax()) == 1){
            throw new MyException("下限不能大于上限");
        }
        TPssStdChemNkPro tPssStdChemNkPro = BeanUtil.toBean(dto, TPssStdChemNkPro.class);
        this.baseMapper.insert(tPssStdChemNkPro);

        return BeanUtil.copyProperties(tPssStdChemNkPro, TPssStdChemNkProVo.class);
    }

    @Override
    public TPssStdChemNkProVo update(UpdateTPssStdChemNkProDto dto) {
        if (dto.getCChemCompMin().compareTo(dto.getCChemCompMax()) == 1){
            throw new MyException("下限不能大于上限");
        }
        TPssStdChemNkPro tPssStdChemNkPro = BeanUtil.toBean(dto, TPssStdChemNkPro.class);
        this.baseMapper.updateById(tPssStdChemNkPro);

        return BeanUtil.copyProperties(dto, TPssStdChemNkProVo.class);
    }
}
