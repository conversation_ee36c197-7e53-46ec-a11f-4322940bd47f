package com.aitos.pss.controller.planmanger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.constant.RoleConstants;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssFinalSlabDto;
import com.aitos.pss.dto.planmanger.TPssFinalSlabPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssFinalSlabDto;
import com.aitos.pss.service.planmanger.IFinalSlabService;
import com.aitos.pss.vo.planmanger.TPssFinalSlabPageVo;
import com.aitos.pss.vo.planmanger.TPssFinalSlabVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 最终计划钢坯
* <AUTHOR>
* @Date: 2025-07-22
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/finalslab")
@Tag(name = "/pss"  + "/finalslab",description = "最终计划钢坯代码")
@AllArgsConstructor
public class FinalSlabController {


    private final IFinalSlabService finalSlabService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssFinalSlab列表(分页)")
    @SaCheckPermission(value = "finalslab:detail", orRole = RoleConstants.ADMIN)
    public RT<PageOutput<TPssFinalSlabPageVo>> page(@Valid TPssFinalSlabPageDto dto){

        return RT.ok(finalSlabService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssFinalSlab信息")
    @SaCheckPermission(value = "finalslab:detail", orRole = RoleConstants.ADMIN)
    public RT<TPssFinalSlabVo> info(@RequestParam Long id){

        return RT.ok(finalSlabService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssFinalSlab")
    @SaCheckPermission(value = "finalslab:add", orRole = RoleConstants.ADMIN)
    @AitLog(value = "最终计划钢坯新增数据")
    public RT<TPssFinalSlabVo> add(@Valid @RequestBody AddTPssFinalSlabDto dto){

        return RT.ok(finalSlabService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssFinalSlab")
    @SaCheckPermission(value = "finalslab:edit", orRole = RoleConstants.ADMIN)
    @AitLog(value = "最终计划钢坯修改数据")
    public RT<TPssFinalSlabVo> update(@Valid @RequestBody UpdateTPssFinalSlabDto dto){

        return RT.ok(finalSlabService.update(dto));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @SaCheckPermission(value = "finalslab:delete", orRole = RoleConstants.ADMIN)
    @AitLog(value = "最终计划钢坯删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(finalSlabService.removeBatchByIds(ids));
    }

}