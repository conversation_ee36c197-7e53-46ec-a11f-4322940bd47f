package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssSlabJudgeDto;
import com.aitos.pss.dto.qualitymanger.TPssSlabJudgePageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssSlabJudgeDto;
import com.aitos.pss.entity.qualitymanger.*;
import com.aitos.pss.mapper.qualitymanger.TPssSlabFaceMapper;
import com.aitos.pss.mapper.qualitymanger.TPssSlabJudgeMapper;
import com.aitos.pss.mapper.qualitymanger.TPssSlabResMapper;
import com.aitos.pss.mapper.qualitymanger.TPssSlabSizeMapper;
import com.aitos.pss.service.qualitymanger.ICheSteelService;
import com.aitos.pss.service.qualitymanger.ISlabJudgeService;
import com.aitos.pss.vo.qualitymanger.TPssSlabJudgePageVo;
import com.aitos.pss.vo.qualitymanger.TPssSlabJudgeVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-04
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class SlabJudgeServiceImpl extends ServiceImpl<TPssSlabJudgeMapper, TPssSlabJudge> implements ISlabJudgeService {

    private final ICheSteelService iCheSteelService;

    private final TPssSlabFaceMapper slabFaceMapper;

    private final TPssSlabSizeMapper slabSizeMapper;

    private final TPssSlabResMapper slabResMapper;

    @Override
    public PageOutput<TPssSlabJudgePageVo> queryPage(TPssSlabJudgePageDto dto) {
        LambdaQueryWrapper<TPssSlabJudge> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCSlabId()),TPssSlabJudge::getCSlabId,dto.getCSlabId())
                .eq(StrUtil.isNotBlank(dto.getCNewStlGrdCd()),TPssSlabJudge::getCNewStlGrdCd,dto.getCNewStlGrdCd())
                .like(StrUtil.isNotBlank(dto.getCHeatId()),TPssSlabJudge::getCHeatId,dto.getCHeatId())
                .between(Objects.nonNull(dto.getDtModifyDateTimeStart()) && Objects.nonNull(dto.getDtModifyDateTimeEnd()),
                        TPssSlabJudge::getDtModifyDateTime,
                        dto.getDtModifyDateTimeStart(),
                        dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssSlabJudge::getNId)
                .select(TPssSlabJudge.class,x -> VoToColumnUtil.fieldsToColumns(TPssSlabJudgePageVo.class).contains(x.getProperty()));
        IPage<TPssSlabJudge> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssSlabJudgePageVo.class);
    }

    @Override
    public TPssSlabJudgeVo queryInfo(Long id) {
        TPssSlabJudge tPssSlabJudge = this.baseMapper.selectById(id);
        if (tPssSlabJudge == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssSlabJudge, TPssSlabJudgeVo.class);
    }

    @Override
    public TPssSlabJudgeVo add(AddTPssSlabJudgeDto dto) {
        TPssSlabJudge tPssSlabJudge = BeanUtil.toBean(dto, TPssSlabJudge.class);
        this.baseMapper.insert(tPssSlabJudge);

        return BeanUtil.copyProperties(tPssSlabJudge, TPssSlabJudgeVo.class);
    }

    @Override
    @Transactional
    public TPssSlabJudgeVo update(UpdateTPssSlabJudgeDto dto) {
        TPssSlabJudge tPssSlabJudge = BeanUtil.toBean(dto, TPssSlabJudge.class);

        // 修改成分表
        if (StringUtils.isNoneBlank(tPssSlabJudge.getCHeatId())) {
            iCheSteelService.update(
                    Wrappers.<TPssCheSteel>lambdaUpdate()
                            .eq(TPssCheSteel::getCSampleid,tPssSlabJudge.getCHeatId())
                            .set(TPssCheSteel::getCJudge,tPssSlabJudge.getCJudgeRlt())
            );
        }

        // 修改表面判定表，尺寸判定，炼钢坯料表
        if (StringUtils.isNoneBlank(tPssSlabJudge.getCSlabId())) {
            slabFaceMapper.update(
                    Wrappers.<TPssSlabFace>lambdaUpdate()
                            .eq(TPssSlabFace::getCSlabId,tPssSlabJudge.getCSlabId())
                            .set(TPssSlabFace::getCFaceRlt,tPssSlabJudge.getCFaceRlt())
            );

            slabSizeMapper.update(
                    Wrappers.<TPssSlabSize>lambdaUpdate()
                            .eq(TPssSlabSize::getCSlabId,tPssSlabJudge.getCSlabId())
                            .set(TPssSlabSize::getCSizeRlt,tPssSlabJudge.getCSizeRlt())
            );

            slabResMapper.update(
                    Wrappers.<TPssSlabRes>lambdaUpdate()
                            .eq(TPssSlabRes::getCMatId,tPssSlabJudge.getCSlabId())
                            .set(TPssSlabRes::getCChemGrd,tPssSlabJudge.getCChemRlt())
                            .set(TPssSlabRes::getCSizeGrd,tPssSlabJudge.getCSizeRlt())
                            .set(TPssSlabRes::getCSurfGrd,tPssSlabJudge.getCBodyRlt())
                            .set(TPssSlabRes::getCProdGrd,tPssSlabJudge.getCJudgeRlt())
            );
        }

        this.baseMapper.updateById(tPssSlabJudge);

        return BeanUtil.copyProperties(tPssSlabJudge, TPssSlabJudgeVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssSlabJudgePageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssSlabJudgePageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssSlabJudge.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssSlabJudgePageDto dto, Boolean isTemplate) {
        List<TPssSlabJudgePageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssSlabJudgePageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "SlabJudge" + ExcelTypeEnum.XLSX.getValue());
    }
}
