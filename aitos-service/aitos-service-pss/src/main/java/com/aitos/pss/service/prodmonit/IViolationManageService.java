
package com.aitos.pss.service.prodmonit;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.prodmonit.AddTPssViolationsDto;
import com.aitos.pss.dto.prodmonit.TPssViolationsPageDto;
import com.aitos.pss.dto.prodmonit.UpdateTPssViolationsDto;
import com.aitos.pss.entity.prodmonit.TPssViolations;
import com.aitos.pss.vo.prodmonit.TPssViolationsPageVo;
import com.aitos.pss.vo.prodmonit.TPssViolationsVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-28
* @Version 1.0
*/

public interface IViolationManageService extends IService<TPssViolations> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssViolationsPageVo> queryPage(@Valid TPssViolationsPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssViolationsVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssViolationsVo add(@Valid AddTPssViolationsDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssViolationsVo update(@Valid UpdateTPssViolationsDto dto);

    /**
     * importData
     * @param file
     * @return
     */
    Void importData(MultipartFile file) throws IOException;

    /**
     * exportData
     * @param dto
     * @param isTemplate
     * @return
     */
    ResponseEntity<byte[]> exportData(@Valid TPssViolationsPageDto dto, Boolean isTemplate);
}
