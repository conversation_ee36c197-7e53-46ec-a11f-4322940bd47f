package com.aitos.pss.service.impl.qualitymanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.advice.utils.ExcelTransUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.qualitymanger.AddTPssRollStdDto;
import com.aitos.pss.dto.qualitymanger.TPssRollStdPageDto;
import com.aitos.pss.dto.qualitymanger.UpdateTPssRollStdDto;
import com.aitos.pss.entity.qualitymanger.TPssRollStd;
import com.aitos.pss.mapper.qualitymanger.TPssRollStdMapper;
import com.aitos.pss.service.qualitymanger.IRollStdService;
import com.aitos.pss.vo.qualitymanger.TPssRollStdPageVo;
import com.aitos.pss.vo.qualitymanger.TPssRollStdVo;
import com.aitos.system.utils.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-05-21
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class RollStdServiceImpl extends ServiceImpl<TPssRollStdMapper, TPssRollStd> implements IRollStdService {

    @Override
    public PageOutput<TPssRollStdPageVo> queryPage(TPssRollStdPageDto dto) {
        LambdaQueryWrapper<TPssRollStd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCQualityId()),TPssRollStd::getCQualityId,dto.getCQualityId())
                .like(StrUtil.isNotBlank(dto.getCStlGrdCd()),TPssRollStd::getCStlGrdCd,dto.getCStlGrdCd())
                .eq(Objects.nonNull(dto.getNEnabledMark()),TPssRollStd::getNEnabledMark,dto.getNEnabledMark())
                .orderByDesc(TPssRollStd::getNId)
                .select(TPssRollStd.class,x -> VoToColumnUtil.fieldsToColumns(TPssRollStdPageVo.class).contains(x.getProperty()));
        IPage<TPssRollStd> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssRollStdPageVo.class);
    }

    @Override
    public TPssRollStdVo queryInfo(Long id) {
        TPssRollStd tPssRollStd = this.baseMapper.selectById(id);
        if (tPssRollStd == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssRollStd, TPssRollStdVo.class);
    }

    @Override
    public TPssRollStdVo add(AddTPssRollStdDto dto) {
        TPssRollStd tPssRollStd = BeanUtil.toBean(dto, TPssRollStd.class);
        this.baseMapper.insert(tPssRollStd);

        return BeanUtil.copyProperties(tPssRollStd, TPssRollStdVo.class);
    }

    @Override
    public TPssRollStdVo update(UpdateTPssRollStdDto dto) {
        TPssRollStd tPssRollStd = BeanUtil.toBean(dto, TPssRollStd.class);
        this.baseMapper.updateById(tPssRollStd);

        return BeanUtil.copyProperties(tPssRollStd, TPssRollStdVo.class);
    }

    @Override
    public void importData(MultipartFile file) throws IOException {
        List<TPssRollStdPageVo> savedDataList = EasyExcel.read(file.getInputStream()).head(TPssRollStdPageVo.class).sheet().doReadSync();
        ExcelTransUtil.transExcelData(savedDataList, true);
        this.baseMapper.insert(BeanUtil.copyToList(savedDataList, TPssRollStd.class));
    }

    @Override
    public ResponseEntity<byte[]> exportData(TPssRollStdPageDto dto, Boolean isTemplate) {
        List<TPssRollStdPageVo> customerList = isTemplate != null && isTemplate ? new ArrayList<>() : queryPage(dto).getList();
        ExcelTransUtil.transExcelData(customerList, false);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot, TPssRollStdPageVo.class).automaticMergeHead(false).excelType(ExcelTypeEnum.XLSX).sheet().doWrite(customerList);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);

        return RT.fileStream(resultBot.toByteArray(), "RollStd" + ExcelTypeEnum.XLSX.getValue());
    }
}
