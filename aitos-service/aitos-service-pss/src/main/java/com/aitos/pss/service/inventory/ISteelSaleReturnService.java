
package com.aitos.pss.service.inventory;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.inventory.AddTPssSteelSaleReturnDto;
import com.aitos.pss.dto.inventory.TPssSteelSaleReturnPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssSteelSaleReturnDto;
import com.aitos.pss.entity.inventory.TPssSteelSaleReturn;
import com.aitos.pss.vo.inventory.TPssSteelSaleReturnPageVo;
import com.aitos.pss.vo.inventory.TPssSteelSaleReturnVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/

public interface ISteelSaleReturnService extends IService<TPssSteelSaleReturn> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssSteelSaleReturnPageVo> queryPage(@Valid TPssSteelSaleReturnPageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssSteelSaleReturnVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssSteelSaleReturnVo add(@Valid AddTPssSteelSaleReturnDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssSteelSaleReturnVo update(@Valid UpdateTPssSteelSaleReturnDto dto);
}
