package com.aitos.pss.service.impl.costmanger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.costmanger.AddTPssFcsCdDto;
import com.aitos.pss.dto.costmanger.TPssFcsCdPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsCdDto;
import com.aitos.pss.entity.costmanger.TPssFcsCd;
import com.aitos.pss.entity.costmanger.TPssFcsMatCd;
import com.aitos.pss.mapper.costmanger.TPssFcsCdMapper;
import com.aitos.pss.service.costmanger.IPssFcsMatcdService;
import com.aitos.pss.service.costmanger.IPssFcscdService;
import com.aitos.pss.vo.costmanger.TPssFcsCdPageVo;
import com.aitos.pss.vo.costmanger.TPssFcsCdVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class PssFcscdServiceImpl extends ServiceImpl<TPssFcsCdMapper, TPssFcsCd>
    implements IPssFcscdService {

    private final IPssFcsMatcdService pssFcsMatcdService;

    @Override
    public PageOutput<TPssFcsCdPageVo> queryPage(TPssFcsCdPageDto dto) {
        LambdaQueryWrapper<TPssFcsCd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getNId()),TPssFcsCd::getNId,dto.getNId())
                .like(StrUtil.isNotBlank(dto.getCMatTypeBName()),TPssFcsCd::getCMatTypeBName,dto.getCMatTypeBName())
                .like(StrUtil.isNotBlank(dto.getCCostItemName()),TPssFcsCd::getCCostItemName,dto.getCCostItemName())
                .like(Objects.nonNull(dto.getCProLine()),TPssFcsCd::getCProLine,dto.getCProLine())
                .like(StrUtil.isNotBlank(dto.getCMemo()),TPssFcsCd::getCMemo,dto.getCMemo())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssFcsCd::getNCreateUserId,dto.getNCreateUserId())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssFcsCd::getNModifyUserId,dto.getNModifyUserId())
                .like(StrUtil.isNotBlank(dto.getCMatTypeBCode()),TPssFcsCd::getCMatTypeBCode,dto.getCMatTypeBCode())
                .like(StrUtil.isNotBlank(dto.getCCostItemCode()),TPssFcsCd::getCCostItemCode,dto.getCCostItemCode())
                .like(StrUtil.isNotBlank(dto.getCMatUnitId()),TPssFcsCd::getCMatUnitId,dto.getCMatUnitId())
                .eq(ObjectUtil.isNotNull(dto.getCCostSeq()),TPssFcsCd::getCCostSeq,dto.getCCostSeq())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssFcsCd::getNEnabledMark,dto.getNEnabledMark())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssFcsCd::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssFcsCd::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssFcsCd::getDtCreateDateTime)
                .select(TPssFcsCd.class,x -> VoToColumnUtil.fieldsToColumns(TPssFcsCdPageVo.class).contains(x.getProperty()));
        IPage<TPssFcsCd> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssFcsCdPageVo.class);
    }

    @Override
    public TPssFcsCdVo queryInfo(Long id) {
        TPssFcsCd tPssFcsCd = this.baseMapper.selectById(id);
        if (tPssFcsCd == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssFcsCd, TPssFcsCdVo.class);
    }

  @Override
  public TPssFcsCdVo add(AddTPssFcsCdDto dto) {
    TPssFcsCd tPssFcsCd = BeanUtil.toBean(dto, TPssFcsCd.class);
    Long dbCostItemCodeCount =
        this.baseMapper.selectCount(
            Wrappers.<TPssFcsCd>lambdaQuery()
                .eq(TPssFcsCd::getCCostItemCode, dto.getCCostItemCode()));
    if (dbCostItemCodeCount > 0) throw new MyException("项目小类编码不能重复");

    Long dbCostSeqCount =
        this.baseMapper.selectCount(
            Wrappers.<TPssFcsCd>lambdaQuery()
                .eq(TPssFcsCd::getCCostSeq, dto.getCCostSeq()));
    if (dbCostSeqCount > 0) throw new MyException("顺序不能重复");

    this.baseMapper.insert(tPssFcsCd);

    return BeanUtil.copyProperties(tPssFcsCd, TPssFcsCdVo.class);
  }

  @Override
  public TPssFcsCdVo update(UpdateTPssFcsCdDto dto) {
    TPssFcsCd tPssFcsCd = BeanUtil.toBean(dto, TPssFcsCd.class);
      Long dbCostItemCodeCount =
              this.baseMapper.selectCount(
                      Wrappers.<TPssFcsCd>lambdaQuery()
                              .eq(TPssFcsCd::getCCostItemCode, dto.getCCostItemCode())
                              .ne(TPssFcsCd::getNId, dto.getNId())
              );
      if (dbCostItemCodeCount > 0) throw new MyException("项目小类编码不能重复");

      Long dbCostSeqCount =
              this.baseMapper.selectCount(
                      Wrappers.<TPssFcsCd>lambdaQuery()
                              .eq(TPssFcsCd::getCCostSeq, dto.getCCostSeq())
                              .ne(TPssFcsCd::getNId, dto.getNId())
              );
      if (dbCostSeqCount > 0) throw new MyException("顺序不能重复");

    this.baseMapper.updateById(tPssFcsCd);

    return BeanUtil.copyProperties(tPssFcsCd, TPssFcsCdVo.class);
  }

    @Override
    public Boolean removeBatch(List<Long> ids) {
        List<TPssFcsCd> tPssFcsCdList = this.baseMapper.selectByIds(ids);
        Set<String> costItemCodeSet = tPssFcsCdList.stream().map(TPssFcsCd::getCCostItemCode).collect(Collectors.toSet());

        Long dbCostItemCodeCount =
                pssFcsMatcdService.count(Wrappers.<TPssFcsMatCd>lambdaQuery().in(TPssFcsMatCd::getCCostItemCode, costItemCodeSet));
        if (dbCostItemCodeCount > 0) throw new MyException("删除内容包含子项，请先将子项删除");

        this.baseMapper.deleteBatchIds(ids);

        return Boolean.TRUE;
    }
}
