package com.aitos.pss.service.impl.inventory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.exception.MyException;
import com.aitos.common.core.uitls.VoToColumnUtil;
import com.aitos.common.mybatis.utils.ConventPage;
import com.aitos.pss.constant.PssConstant;
import com.aitos.pss.dto.inventory.AddTPssSteelReturnRecordDto;
import com.aitos.pss.dto.inventory.TPssSteelReturnRecordPageDto;
import com.aitos.pss.dto.inventory.UpdateTPssSteelReturnRecordDto;
import com.aitos.pss.entity.inventory.TPssSteelReturnRecord;
import com.aitos.pss.mapper.inventory.TPssSteelReturnRecordMapper;
import com.aitos.pss.service.inventory.ISteelReturnRecordService;
import com.aitos.pss.vo.inventory.TPssSteelReturnRecordPageVo;
import com.aitos.pss.vo.inventory.TPssSteelReturnRecordVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-05
* @Version 1.0
*/
@Service
@AllArgsConstructor
@DS(PssConstant.PSS)
public class SteelReturnRecordServiceImpl extends ServiceImpl<TPssSteelReturnRecordMapper, TPssSteelReturnRecord> implements ISteelReturnRecordService {

    @Override
    public PageOutput<TPssSteelReturnRecordPageVo> queryPage(TPssSteelReturnRecordPageDto dto) {
        LambdaQueryWrapper<TPssSteelReturnRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getCStockNo()),TPssSteelReturnRecord::getCStockNo,dto.getCStockNo())
                .like(StrUtil.isNotBlank(dto.getCMaterialNo()),TPssSteelReturnRecord::getCMaterialNo,dto.getCMaterialNo())
                .like(StrUtil.isNotBlank(dto.getCSpec()),TPssSteelReturnRecord::getCSpec,dto.getCSpec())
                .eq(ObjectUtil.isNotNull(dto.getNReturnQty()),TPssSteelReturnRecord::getNReturnQty,dto.getNReturnQty())
                .between(ObjectUtil.isNotNull(dto.getDtReturnTimeStart()) && ObjectUtil.isNotNull(dto.getDtReturnTimeEnd()),TPssSteelReturnRecord::getDtReturnTime,dto.getDtReturnTimeStart(),dto.getDtReturnTimeEnd())
                .like(StrUtil.isNotBlank(dto.getCReturnReason()),TPssSteelReturnRecord::getCReturnReason,dto.getCReturnReason())
                .like(StrUtil.isNotBlank(dto.getCStatus()),TPssSteelReturnRecord::getCStatus,dto.getCStatus())
                .eq(ObjectUtil.isNotNull(dto.getNEnabledMark()),TPssSteelReturnRecord::getNEnabledMark,dto.getNEnabledMark())
                .like(StrUtil.isNotBlank(dto.getCDeliveryNo()),TPssSteelReturnRecord::getCDeliveryNo,dto.getCDeliveryNo())
                .like(StrUtil.isNotBlank(dto.getCBatchNo()),TPssSteelReturnRecord::getCBatchNo,dto.getCBatchNo())
                .like(StrUtil.isNotBlank(dto.getCSteelGrade()),TPssSteelReturnRecord::getCSteelGrade,dto.getCSteelGrade())
                .eq(ObjectUtil.isNotNull(dto.getNWeight()),TPssSteelReturnRecord::getNWeight,dto.getNWeight())
                .eq(ObjectUtil.isNotNull(dto.getNReturnWeight()),TPssSteelReturnRecord::getNReturnWeight,dto.getNReturnWeight())
                .like(StrUtil.isNotBlank(dto.getCReturnUser()),TPssSteelReturnRecord::getCReturnUser,dto.getCReturnUser())
                .like(StrUtil.isNotBlank(dto.getCOutNo()),TPssSteelReturnRecord::getCOutNo,dto.getCOutNo())
                .like(StrUtil.isNotBlank(dto.getCRemark()),TPssSteelReturnRecord::getCRemark,dto.getCRemark())
                .eq(ObjectUtil.isNotNull(dto.getNCreateUserId()),TPssSteelReturnRecord::getNCreateUserId,dto.getNCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getDtCreateDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtCreateDateTimeEnd()),TPssSteelReturnRecord::getDtCreateDateTime,dto.getDtCreateDateTimeStart(),dto.getDtCreateDateTimeEnd())
                .eq(ObjectUtil.isNotNull(dto.getNModifyUserId()),TPssSteelReturnRecord::getNModifyUserId,dto.getNModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getDtModifyDateTimeStart()) && ObjectUtil.isNotNull(dto.getDtModifyDateTimeEnd()),TPssSteelReturnRecord::getDtModifyDateTime,dto.getDtModifyDateTimeStart(),dto.getDtModifyDateTimeEnd())
                .orderByDesc(TPssSteelReturnRecord::getDtCreateDateTime)
                .select(TPssSteelReturnRecord.class,x -> VoToColumnUtil.fieldsToColumns(TPssSteelReturnRecordPageVo.class).contains(x.getProperty()));
        IPage<TPssSteelReturnRecord> page = this.baseMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(page, TPssSteelReturnRecordPageVo.class);
    }

    @Override
    public TPssSteelReturnRecordVo queryInfo(Long id) {
        TPssSteelReturnRecord tPssSteelReturnRecord = this.baseMapper.selectById(id);
        if (tPssSteelReturnRecord == null) {
            throw new MyException("找不到此数据！");
        }

        return BeanUtil.copyProperties(tPssSteelReturnRecord, TPssSteelReturnRecordVo.class);
    }

    @Override
    public TPssSteelReturnRecordVo add(AddTPssSteelReturnRecordDto dto) {
        TPssSteelReturnRecord tPssSteelReturnRecord = BeanUtil.toBean(dto, TPssSteelReturnRecord.class);
        this.baseMapper.insert(tPssSteelReturnRecord);

        return BeanUtil.copyProperties(tPssSteelReturnRecord, TPssSteelReturnRecordVo.class);
    }

    @Override
    public TPssSteelReturnRecordVo update(UpdateTPssSteelReturnRecordDto dto) {
        TPssSteelReturnRecord tPssSteelReturnRecord = BeanUtil.toBean(dto, TPssSteelReturnRecord.class);
        this.baseMapper.updateById(tPssSteelReturnRecord);

        return BeanUtil.copyProperties(tPssSteelReturnRecord, TPssSteelReturnRecordVo.class);
    }
}
