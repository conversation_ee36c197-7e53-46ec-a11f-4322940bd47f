package com.aitos.pss.controller.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssCastCnvtsDto;
import com.aitos.pss.dto.planmanger.TPssCastCnvtsPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssCastCnvtsDto;
import com.aitos.pss.entity.planmanger.TPssCastCnvts;
import com.aitos.pss.service.planmanger.ITPssCastCnvtsService;
import com.aitos.pss.vo.planmanger.TPssCastCnvtsPageVo;
import com.aitos.pss.vo.planmanger.TPssCastCnvtsVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 浇次标准-炉次浇次数量编制标准
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/psscastcnvts")
@Tag(name = "/pss"  + "/psscastcnvts",description = "浇次标准-炉次浇次数量编制标准代码")
@AllArgsConstructor
public class TPssCastCnvtsController {


    private final ITPssCastCnvtsService pssCastCnvtsService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssCastCnvts列表(分页)")
    public RT<PageOutput<TPssCastCnvtsPageVo>> queryPage(@Valid TPssCastCnvtsPageDto dto){

        return RT.ok(pssCastCnvtsService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssCastCnvts信息")
    public RT<TPssCastCnvtsVo> info(@RequestParam Long id){

        return RT.ok(pssCastCnvtsService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssCastCnvts")
    @AitLog(value = "浇次标准-炉次浇次数量编制标准新增数据")
    public RT<TPssCastCnvtsVo> add(@Valid @RequestBody AddTPssCastCnvtsDto dto){

        return RT.ok(pssCastCnvtsService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssCastCnvts")
    @AitLog(value = "浇次标准-炉次浇次数量编制标准修改数据")
    public RT<TPssCastCnvtsVo> update(@Valid @RequestBody UpdateTPssCastCnvtsDto dto){

        return RT.ok(pssCastCnvtsService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "浇次标准-炉次浇次数量编制标准删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssCastCnvtsService.removeBatchByIds(ids));
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssCastCnvtsDto dto){
        TPssCastCnvts tPssCastCnvts = BeanUtil.copyProperties(dto, TPssCastCnvts.class);

        return RT.ok(pssCastCnvtsService.updateById(tPssCastCnvts));
    }

}