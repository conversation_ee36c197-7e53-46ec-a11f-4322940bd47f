
package com.aitos.pss.service.inventory;

import com.aitos.pss.dto.inventory.TPssMatLocmapLgPageDto;
import com.aitos.pss.entity.inventory.TPssMatLocmapLg;
import com.aitos.pss.vo.inventory.TPssMatLocmapLgPageVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-07-16
* @Version 1.0
*/

public interface IMatLocmapLgService extends IService<TPssMatLocmapLg> {
    /**
     * 分组查询
     * @param dto
     * @return
     */
    Map<String, List<TPssMatLocmapLgPageVo>> queryPage(TPssMatLocmapLgPageDto dto);
}
