package com.aitos.pss.controller.planmanger;

import cn.hutool.core.bean.BeanUtil;
import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.planmanger.AddTPssRateStdDto;
import com.aitos.pss.dto.planmanger.TPssRateStdPageDto;
import com.aitos.pss.dto.planmanger.UpdateTPssRateStdDto;
import com.aitos.pss.entity.planmanger.TPssRateStd;
import com.aitos.pss.service.planmanger.ITPssRateStdService;
import com.aitos.pss.vo.planmanger.TPssRateStdPageVo;
import com.aitos.pss.vo.planmanger.TPssRateStdVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 熔量重量标准-钢种收得率
* <AUTHOR>
* @Date: 2025-05-16
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssratestd")
@Tag(name = "/pss"  + "/pssratestd",description = "熔量重量标准-钢种收得率代码")
@AllArgsConstructor
public class TPssRateStdController {


    private final ITPssRateStdService pssRateStdService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssRateStd列表(分页)")
    public RT<PageOutput<TPssRateStdPageVo>> page(@Valid TPssRateStdPageDto dto){

        return RT.ok(pssRateStdService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssRateStd信息")
    public RT<TPssRateStdVo> info(@RequestParam Long id){

        return RT.ok(pssRateStdService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssRateStd")
    @AitLog(value = "熔量重量标准-钢种收得率新增数据")
    public RT<TPssRateStdVo> add(@Valid @RequestBody AddTPssRateStdDto dto){

        return RT.ok(pssRateStdService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssRateStd")
    @AitLog(value = "熔量重量标准-钢种收得率修改数据")
    public RT<TPssRateStdVo> update(@Valid @RequestBody UpdateTPssRateStdDto dto){

        return RT.ok(pssRateStdService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "熔量重量标准-钢种收得率删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssRateStdService.removeBatchByIds(ids));
    }

    @PostMapping("/update-enabled")
    @Operation(summary = "更新启用状态")
    @AitLog(value = "更新启用状态")
    public RT<Boolean> updateEnabled(@Valid @RequestBody UpdateTPssRateStdDto dto){
        TPssRateStd tPssRateStd = BeanUtil.toBean(dto, TPssRateStd.class);

        return RT.ok(pssRateStdService.updateById(tPssRateStd));
    }

    @GetMapping("/check-single")
    @Operation(summary = "判断当前钢种收得率是否唯一")
    @AitLog(value = "判断当前钢种收得率是否唯一")
    public RT<Boolean> checkSingle(@Valid UpdateTPssRateStdDto dto){

        return RT.ok(pssRateStdService.checkSingle(dto));
    }

}