
package com.aitos.pss.service.costmanger;

import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.pss.dto.costmanger.AddTPssFcsMatPriceDto;
import com.aitos.pss.dto.costmanger.TPssFcsMatPricePageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsMatPriceDto;
import com.aitos.pss.entity.costmanger.TPssFcsMatPrice;
import com.aitos.pss.vo.costmanger.TPssFcsMatPricePageVo;
import com.aitos.pss.vo.costmanger.TPssFcsMatPriceVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/

public interface IFcsMatpriceService extends IService<TPssFcsMatPrice> {

    /**
     * query page
     * @param dto
     * @return
     */
    PageOutput<TPssFcsMatPricePageVo> queryPage(@Valid TPssFcsMatPricePageDto dto);

    /**
     * query info
     * @param id
     * @return
     */
    TPssFcsMatPriceVo queryInfo(Long id);

    /**
     * add
     * @param dto
     * @return
     */
    TPssFcsMatPriceVo add(@Valid AddTPssFcsMatPriceDto dto);

    /**
     * update
     * @param dto
     * @return
     */
    TPssFcsMatPriceVo update(@Valid UpdateTPssFcsMatPriceDto dto);
}
