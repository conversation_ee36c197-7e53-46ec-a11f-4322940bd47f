package com.aitos.pss.controller.costmanger;

import com.aitos.common.core.annotation.AitLog;
import com.aitos.common.core.domain.page.PageOutput;
import com.aitos.common.core.domain.result.RT;
import com.aitos.pss.dto.costmanger.AddTPssFcsCdDto;
import com.aitos.pss.dto.costmanger.TPssFcsCdPageDto;
import com.aitos.pss.dto.costmanger.UpdateTPssFcsCdDto;
import com.aitos.pss.service.costmanger.IPssFcscdService;
import com.aitos.pss.vo.costmanger.TPssFcsCdPageVo;
import com.aitos.pss.vo.costmanger.TPssFcsCdVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: 成本科目配置上表
* <AUTHOR>
* @Date: 2025-06-30
* @Version 1.0
*/
@RestController
@RequestMapping("/pss" + "/pssfcscd")
@Tag(name = "/pss"  + "/pssfcscd",description = "成本科目配置上表代码")
@AllArgsConstructor
public class PssFcscdController {


    private final IPssFcscdService pssFcscdService;

    @GetMapping(value = "/page")
    @Operation(summary = "TPssFcsCd列表(分页)")
    public RT<PageOutput<TPssFcsCdPageVo>> page(@Valid TPssFcsCdPageDto dto){

        return RT.ok(pssFcscdService.queryPage(dto));
    }

    @GetMapping(value = "/info")
    @Operation(summary = "根据id查询TPssFcsCd信息")
    public RT<TPssFcsCdVo> info(@RequestParam Long id){

        return RT.ok(pssFcscdService.queryInfo(id));
    }


    @PostMapping
    @Operation(summary =  "新增TPssFcsCd")
    @AitLog(value = "成本科目配置上表新增数据")
    public RT<TPssFcsCdVo> add(@Valid @RequestBody AddTPssFcsCdDto dto){

        return RT.ok(pssFcscdService.add(dto));
    }

    @PutMapping
    @Operation(summary = "修改TPssFcsCd")
    @AitLog(value = "成本科目配置上表修改数据")
    public RT<TPssFcsCdVo> update(@Valid @RequestBody UpdateTPssFcsCdDto dto){

        return RT.ok(pssFcscdService.update(dto));

    }

    @DeleteMapping
    @Operation(summary = "删除")
    @AitLog(value = "成本科目配置上表删除数据")
    public RT<Boolean> delete(@Valid @RequestBody List<Long> ids){

        return RT.ok(pssFcscdService.removeBatch(ids));
    }

}