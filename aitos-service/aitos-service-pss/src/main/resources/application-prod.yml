server:
  port: 4001

spring:
  application:
    name: aitos-pss-service
  main:
    allow-bean-definition-overriding: true
  config:
    import:
      - nacos:global-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:datasource-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:mybatis-plus-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:sa-token-client-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:redis-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:seata-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
  cloud:
    # nacos监控
    nacos:
      discovery:
        server-addr: 192.168.11.111:8848
        namespace: prod
      config:
        server-addr: 192.168.11.111:8848
        namespace: prod
    # sentinel dashboard 地址
    sentinel:
      transport:
        dashboard: 192.168.11.111:8080
        # 默认端口，  如果 被占用，会一直+1  直到未被占用为止
        port: 8719




