<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 从 Spring 上下文获取应用名 -->
    <springProperty scope="context" name="appName" source="spring.application.name"
                    defaultValue="aitos-service-pss"/>
    <springProperty scope="context" name="logbackLevel" source="logging.level.root" defaultValue="info"/>

    <!-- 定义日志文件存储路径，在项目同级目录下的 logs 文件夹 -->
    <!--    <property name="LOG_PATH" value="${LOG_PATH:-${user.dir}/logs}"/>-->
    <!--    <property name="LOG_PATH" value="logs"/>-->
    <!-- 手动设置或获取服务器 IP，可通过系统属性传入 -->
    <define name="serverIp" class="com.aitos.common.core.log.LogbackIpPropertyDefiner"/>
    <define name="LOG_PATH" class="com.aitos.common.core.log.LogbackDirPropertyDefiner"/>


    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- JDBC 日志滚动文件输出，按大小滚动，超出 200MB 切割 -->
    <appender name="JDBC_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${appName}-jdbc-${serverIp}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${appName}-jdbc-${serverIp}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <cleanHistoryOnStart>false</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>%d{ISO8601} [%thread] %-5level %logger{36} - %msg [%X{traceId:-}]%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 普通日志滚动文件输出，按大小滚动，超出 200MB 切割 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${appName}-stdout.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${appName}-stdout-${serverIp}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <cleanHistoryOnStart>false</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>%d{ISO8601} [%thread] %-5level %logger{36} - %msg [%X{traceId:-}]%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- Error 日志滚动文件输出，按大小滚动，超出 200MB 切割 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${appName}-stderr.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${appName}-stderr.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <cleanHistoryOnStart>false</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>%d{ISO8601} [%thread] %-5level %logger{36} - %msg [%X{traceId:-}]%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 根日志 -->
    <root level="${logbackLevel}">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="JDBC_FILE"/>
    </root>
</configuration>