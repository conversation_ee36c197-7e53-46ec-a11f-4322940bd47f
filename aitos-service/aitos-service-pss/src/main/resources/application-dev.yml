server:
  port: 4001

spring:
  application:
    name: aitos-pss-service-dyx
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/
  main:
    allow-bean-definition-overriding: true
  config:
    import:
      - nacos:global-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:datasource-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:mybatis-plus-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:sa-token-client-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:redis-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:seata-config.yml?group=DEFAULT_GROUP&refreshEnabled=true
  cloud:
    nacos: #nacos监控
      discovery:
        server-addr: 192.168.11.111:8848
#        namespace: aitos-cloud
      config:
        server-addr: 192.168.11.111:8848 # nacos 配置中心地址
        namespace: aitos-cloud
    sentinel:
      transport:
        dashboard: 192.168.11.111:8080 #sentinel dashboard 地址
        port: 8719  #默认端口，  如果 被占用，会一直+1  直到未被占用为止

springdoc:
  swagger-ui:
    path: /swagger-ui.html  # Swagger UI访问路径
    enabled: true          # 启用Swagger UI
    tags-sorter: alpha     # 标签排序方式
    operations-sorter: alpha # 接口排序方式
    config-url: /v3/api-docs/swagger-config  # 必须与api-docs.path一致
    url: /v3/api-docs  # 必须明确指定
  api-docs:
    path: /v3/api-docs     # OpenAPI文档路径
  default-consumes-media-type: application/json
  default-produces-media-type: application/json
  default-flat-param-object: false
  group-configs:
    - group: 'default'
      paths-to-match: '/pss/**'
      packages-to-scan: com.aitos.pss

knife4j:
  enable: true
  setting:
    enable-footer: false
    enable-swagger-models: true




