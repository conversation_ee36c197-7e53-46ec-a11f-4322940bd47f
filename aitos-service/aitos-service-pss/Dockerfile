#penjdk:8-jre 为基础镜像，来构建此镜像，可以理解为运行的需要基础环境
FROM openjdk:11
#WORKDIR指令用于指定容器的一个目录， 容器启动时执行的命令会在该目录下执行。
WORKDIR /opt/docker/images/metabase/
#将当前metabase.jar 复制到容器根目录下
ADD target/aitos-service-pss.jar aitos-service-pss.jar
#将依赖包 复制到容器根目录/libs下,metabase.jar已不再需要添加其它jar包
#暴露容器端口为3000 Docker镜像告知Docker宿主机应用监听了3000端口
EXPOSE 4001
# 接收构建时传递的环境变量 ENVVARIABLE
ARG ENV_VARIABLE
# 设置环境变量，以便在容器启动时可以使用
ENV ENV_VARIABLE ${ENV_VARIABLE}
#容器启动时执行的命令
CMD java -Dspring.profiles.active=$ENV_VARIABLE -jar aitos-service-pss.jar


